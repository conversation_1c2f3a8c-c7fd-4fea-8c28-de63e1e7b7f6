SEARCH_RPC_REDIS_HOST=localhost
<PERSON>ARCH_RPC_REDIS_PORT=6379
SEARCH_RPC_REDIS_USERNAME=
SEARCH_RPC_REDIS_PASSWORD=
SEARCH_RPC_SIGNING_KEY=keyboardcat

ACCOUNT_RPC_REDIS_HOST=localhost
ACCOUNT_RPC_REDIS_PORT=6379
ACCOUNT_RPC_REDIS_USERNAME=
ACCOUNT_RPC_REDIS_PASSWORD=
ACCOUNT_RPC_SIGNING_KEY=keyboardcat

PIPELINE_RPC_REDIS_HOST=localhost
PIPELINE_RPC_REDIS_PORT=6379
PIPELINE_RPC_REDIS_USERNAME=
PIPELINE_RPC_REDIS_PASSWORD=
PIPELINE_RPC_SIGNING_KEY=keyboardcat

HEALTH_PORT=9004

LOG_LEVEL=debug
NODE_ENV=local

JWT_KEY=keyboardcat

ELASTIC_HOST=
ELASTIC_HOST_V2=
ELASTIC_PEOPLE_INDEX=

AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=

SEARCH_SUPPORTED_LANGUAGES=eng,cmn,jpn

ELASTIC_CLINICAL_TRIALS_HOST=
ELASTIC_CLINICAL_TRIALS_INDEX=trials
ELASTIC_INSTITUTIONS_INDEX=institutions

MESH_KEYWORD_EXCLUDE_WORDS="adult,biochemistry,business,china,cloud computing,computer network,computer network communication,distribute computing,dynamic source route,hardware architecture,health care,history 20th century,human,incidence,large hadron collider,law,lepton,longitudinal study,medicine,netherland,node network,patient ,physics,political science,rotterdam study,software,static route,survey questionnaire,theoretical computer science,treatment outcome,unite state,wireless ad hoc network,wireless network,wireless sensor network,internal medicine,cell biology,male,female,middle aged,aged 80,aged,clinical trial,young adult,patient,disease,cell,biology,general medicine,catalysis"

QUERY_UNDERSTANDING_SERVICE_HOST=localhost
QUERY_UNDERSTANDING_SERVICE_SERVICE_PORT=8050
QUERY_UNDERSTANDING_SERVICE_FEATURE_FLAG=false
LAUNCH_DARKLY_CLIENT_ID=

ELASTIC_CLINICAL_TRIALS_HOST_V2=
ELASTIC_CLINICAL_TRIALS_INDEX_V2=
ELASTIC_CLINICAL_TRIALS_HOST_V3=
ELASTIC_CLINICAL_TRIALS_INDEX_V3=
ELASTIC_CLINICAL_TRIALS_HOST_V4=
ELASTIC_CLINICAL_TRIALS_INDEX_V4=
ELASTIC_CLINICAL_TRIALS_INDEX_WITH_CTMS=

ELASTIC_CTMS_FACILITY_AGGS_INDEX=
ELASTIC_CTMS_INVESTIGATORS_AGGS_INDEX=

ELASTIC_CONGRESS_INDEX=congress
ELASTIC_CONGRESS_SESSIONS_INDEX=congress_sessions

SEARCH_RESOURCE_SERVER_SERVICE_HOST=localhost
SEARCH_RESOURCE_SERVER_SERVICE_PORT_GRPC=4141

# specify to use simple authentication
# ELASTIC_USERNAME=
# ELASTIC_PASSWORD=

PIPELINE_POSTGRES_HOST=db-for-reports.data.h1insights.com
PIPELINE_POSTGRES_PORT=5432
PIPELINE_POSTGRES_DB=pipeline
PIPELINE_POSTGRES_USERNAME=postgres
#get password from aws secret manager
PIPELINE_POSTGRES_PASSWORD=

NOTIFICATIONS_RPC_REDIS_HOST=localhost
NOTIFICATIONS_RPC_REDIS_PORT=6379
NOTIFICATIONS_RPC_SIGNING_KEY=keyboardcat


AZURE_OPEN_AI_API_KEY=
AZURE_OPEN_AI_ENDPOINT=""
AZURE_OPEN_AI_DEPLOYMENT=
AZURE_OPEN_AI_API_VERSON=
