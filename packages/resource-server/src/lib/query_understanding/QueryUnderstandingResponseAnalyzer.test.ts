import { faker } from "@faker-js/faker";
import { QueryIntent } from "../../proto/query_intent_pb";
import { QueryUnderstandingServiceResponse } from "../../proto/query_understanding_service_pb";
import { Synonym } from "../../proto/synonym_pb";
import {
  getQueryIntents,
  getQuerySynonymList
} from "./QueryUnderstandingResponseAnalyzer";
import { QueryIntent as QueryIntentEnum } from "@h1nyc/search-sdk/dist/interfaces/elasticDocuments";

describe("tests getQuerySynonymList", () => {
  test("qus response has no synonym info", () => {
    const query = faker.datatype.string();
    const response = new QueryUnderstandingServiceResponse();
    expect(getQuerySynonymList(query, response)).toStrictEqual([]);
  });

  test("qus response has zero synonyms", () => {
    const query = faker.datatype.string();
    const response = new QueryUnderstandingServiceResponse();
    // This query has no synonyms
    const synonym = new Synonym();
    synonym.setOrig(query);
    response.addUnigramSynonym(synonym);
    expect(getQuerySynonymList(query, response)).toStrictEqual([]);
  });

  test("qus response has synonyms", () => {
    const query = faker.datatype.string();
    const expectedSynonyms = [faker.datatype.string(), faker.datatype.string()];
    expectedSynonyms.sort();
    const response = new QueryUnderstandingServiceResponse();
    // populate response with expected synonyms
    for (const expectedSynonym of expectedSynonyms) {
      const synonym = new Synonym();
      synonym.setOrig(query);
      const synAndScore = new Synonym.SynScore();
      synAndScore.setSyn(expectedSynonym);
      synAndScore.setScore(1.0);
      synonym.addContextFreeSyn(synAndScore);
      response.addUnigramSynonym(synonym);
    }

    expect(getQuerySynonymList(query, response)).toStrictEqual(
      expectedSynonyms
    );
  });

  test("query has no intent", () => {
    const query = faker.datatype.string();
    const response = new QueryUnderstandingServiceResponse();
    expect(getQueryIntents(query, response)).toStrictEqual([]);
  });

  test("query has intents", () => {
    const query = faker.datatype.string();
    const personIntent = new QueryIntent.Intent();
    personIntent.setIntentType(QueryIntent.IntentType.PERSON_NAME);
    personIntent.setScore(1.0);
    const queryIntent = new QueryIntent();
    queryIntent.setPersonNameIntent(personIntent);

    const diseaseIntent = new QueryIntent.Intent();
    diseaseIntent.setIntentType(QueryIntent.IntentType.DISEASE);
    diseaseIntent.setScore(1.0);
    queryIntent.setDiseaseIntent(diseaseIntent);

    const institutionIntent = new QueryIntent.Intent();
    institutionIntent.setIntentType(QueryIntent.IntentType.INSTITUTION);
    institutionIntent.setScore(1.0);
    queryIntent.setInstitutionIntent(institutionIntent);

    const specialistIntent = new QueryIntent.Intent();
    specialistIntent.setIntentType(QueryIntent.IntentType.SPECIALIST);
    specialistIntent.setScore(1.0);
    queryIntent.setSpecialistIntent(specialistIntent);

    const conferenceIntent = new QueryIntent.Intent();
    conferenceIntent.setIntentType(QueryIntent.IntentType.CONFERENCE_NAME);
    conferenceIntent.setScore(1.0);
    queryIntent.setConferenceIntent(conferenceIntent);

    const response = new QueryUnderstandingServiceResponse();
    response.setQueryIntent(queryIntent);
    const expectedIntents = [
      QueryIntentEnum.PERSON,
      QueryIntentEnum.DISEASE,
      QueryIntentEnum.INSTITUTION,
      QueryIntentEnum.SPECIALIST,
      QueryIntentEnum.CONFERENCE
    ];
    expect(getQueryIntents(query, response)).toStrictEqual(expectedIntents);
  });
});
