import { QueryIntent as QueryIntentEnum } from "@h1nyc/search-sdk";
import { QueryUnderstandingServiceResponse } from "../../proto/query_understanding_service_pb";
import { QueryIntent } from "../../proto/query_intent_pb";

/**
 * Returns a list of phrasal synonyms (i.e.g synonyms of the whole query) obtained for the query
 * @param query search query
 * @param response proto response of the query understanding service
 * @returns a list of synonyms retrieved for the user query
 */
export function getQuerySynonymList(
  query?: string,
  response?: QueryUnderstandingServiceResponse
): string[] {
  if (query && response) {
    const retrievedSynonyms: string[] = [];
    for (const synonym of response.getUnigramSynonymList()) {
      // only adding phrasal synonyms for now
      if (
        synonym.getOrig().toLowerCase() === query.toLowerCase() &&
        synonym.getContextFreeSynList()?.[0]?.hasSyn()
      ) {
        retrievedSynonyms.push(synonym.getContextFreeSynList()[0].getSyn()!);
      }
    }
    return retrievedSynonyms.sort();
  }

  return [];
}

export function getQueryIntents(
  query?: string,
  response?: QueryUnderstandingServiceResponse
): QueryIntentEnum[] {
  if (query && response?.hasQueryIntent()) {
    const queryIntents: QueryIntent = response.getQueryIntent()!;
    const intents: QueryIntentEnum[] = [];
    if (
      queryIntents.hasPersonNameIntent() &&
      queryIntents.getPersonNameIntent()?.getScore() === 1.0
    ) {
      intents.push(QueryIntentEnum.PERSON);
    }
    if (
      queryIntents.hasDiseaseIntent() &&
      queryIntents.getDiseaseIntent()?.getScore() === 1.0
    ) {
      intents.push(QueryIntentEnum.DISEASE);
    }
    if (
      queryIntents.hasInstitutionIntent() &&
      queryIntents.getInstitutionIntent()?.getScore() === 1.0
    ) {
      intents.push(QueryIntentEnum.INSTITUTION);
    }
    if (
      queryIntents.hasSpecialistIntent() &&
      queryIntents.getSpecialistIntent()?.getScore() === 1.0
    ) {
      intents.push(QueryIntentEnum.SPECIALIST);
    }

    if (
      queryIntents.hasLlmIntent() &&
      queryIntents.getLlmIntent()?.getScore() === 1.0 
    ) {
      intents.push(QueryIntentEnum.NATURAL_LANGUAGE);
    }

    const conferenceIntentScore = queryIntents
      .getConferenceIntent()
      ?.getScore();

    if (conferenceIntentScore && conferenceIntentScore >= 0.8) {
      intents.push(QueryIntentEnum.CONFERENCE);
    }
    return intents;
  }
  return [];
}
