import { QueryDslQueryContainer } from "@elastic/elasticsearch/lib/api/types";
import { ProfileFilterValue, ProfileFilterValueType } from "@h1nyc/search-sdk";
import { faker } from "@faker-js/faker";
import { PublicationProfileAsset } from "./PublicationProfileAsset";

const expectedNested = { path: "publications" };

describe("buildIdsAggregation()", () => {
  const expectedAggs = {
    ids: { terms: { size: 10000, field: "publications.id" } }
  };

  it("throws an error when both terms and filters are missing", () => {
    const asset = new PublicationProfileAsset();
    expect(() => asset.buildIdsAggregation()).toThrowError();
  });

  it("adds term filter clause for publication type filter", () => {
    const asset = new PublicationProfileAsset();
    const filterValues: ProfileFilterValue[] = [
      {
        type: ProfileFilterValueType.PublicationType,
        value: "Editorial"
      }
    ];
    const aggs = asset.buildIdsAggregation(undefined, filterValues);

    const expectedFilters = [
      {
        bool: {
          must: [
            {
              term: { "publications.type_eng": "Editorial" }
            }
          ]
        }
      }
    ];

    expect(aggs.publications).toHaveProperty("nested", expectedNested);
    expect(aggs.publications.aggs.publicationFilters.aggs).toEqual(
      expectedAggs
    );

    expect(aggs.publications.aggs.publicationFilters.filters.filters).toEqual(
      expectedFilters
    );
  });

  it("adds terms filter clause for journal filter", () => {
    const asset = new PublicationProfileAsset();
    const filterValues: ProfileFilterValue[] = [
      {
        type: ProfileFilterValueType.PublicationJournal,
        value: ["blood", "guts"]
      }
    ];
    const aggs = asset.buildIdsAggregation(undefined, filterValues);

    const expectedFilters = [
      {
        bool: {
          must: [
            {
              terms: { "publications.journalName_eng": ["blood", "guts"] }
            }
          ]
        }
      }
    ];

    expect(aggs.publications).toHaveProperty("nested", expectedNested);
    expect(aggs.publications.aggs.publicationFilters.aggs).toEqual(
      expectedAggs
    );

    expect(aggs.publications.aggs.publicationFilters.filters.filters).toEqual(
      expectedFilters
    );
  });

  it("adds range filter clause for min date filter", () => {
    const asset = new PublicationProfileAsset();
    const now = new Date().getTime();
    const filterValues: ProfileFilterValue[] = [
      {
        type: ProfileFilterValueType.PublicationDate,
        value: {
          min: now
        }
      }
    ];
    const aggs = asset.buildIdsAggregation(undefined, filterValues);

    const expectedFilters = [
      {
        bool: {
          must: [
            {
              range: {
                "publications.datePublished": { gte: now }
              }
            }
          ]
        }
      }
    ];

    expect(aggs.publications).toHaveProperty("nested", expectedNested);
    expect(aggs.publications.aggs.publicationFilters.aggs).toEqual(
      expectedAggs
    );

    expect(aggs.publications.aggs.publicationFilters.filters.filters).toEqual(
      expectedFilters
    );
  });

  it("adds range filter clause for max date filter", () => {
    const asset = new PublicationProfileAsset();
    const now = new Date().getTime();
    const filterValues: ProfileFilterValue[] = [
      {
        type: ProfileFilterValueType.PublicationDate,
        value: {
          max: now
        }
      }
    ];
    const aggs = asset.buildIdsAggregation(undefined, filterValues);

    const expectedFilters = [
      {
        bool: {
          must: [
            {
              range: {
                "publications.datePublished": { lte: now }
              }
            }
          ]
        }
      }
    ];

    expect(aggs.publications).toHaveProperty("nested", expectedNested);
    expect(aggs.publications.aggs.publicationFilters.aggs).toEqual(
      expectedAggs
    );

    expect(aggs.publications.aggs.publicationFilters.filters.filters).toEqual(
      expectedFilters
    );
  });

  it("adds range filter clause for min and max date filter", () => {
    const asset = new PublicationProfileAsset();
    const now = new Date().getTime();
    const later = now + 1000;
    const filterValues: ProfileFilterValue[] = [
      {
        type: ProfileFilterValueType.PublicationDate,
        value: {
          min: now,
          max: later
        }
      }
    ];
    const aggs = asset.buildIdsAggregation(undefined, filterValues);

    const expectedFilters = [
      {
        bool: {
          must: [
            {
              range: {
                "publications.datePublished": { gte: now, lte: later }
              }
            }
          ]
        }
      }
    ];

    expect(aggs.publications).toHaveProperty("nested", expectedNested);
    expect(aggs.publications.aggs.publicationFilters.aggs).toEqual(
      expectedAggs
    );

    expect(aggs.publications.aggs.publicationFilters.filters.filters).toEqual(
      expectedFilters
    );
  });

  it("adds term filter clause for publication isFirstOrder filter", () => {
    const asset = new PublicationProfileAsset();
    const filterValues: ProfileFilterValue[] = [
      {
        type: ProfileFilterValueType.PublicationIsFirstOrder,
        value: true
      }
    ];
    const aggs = asset.buildIdsAggregation(undefined, filterValues);

    const expectedFilters = [
      {
        bool: {
          must: [
            {
              bool: {
                should: [
                  {
                    term: { "publications.isFirstOrder": true }
                  }
                ],
                minimum_should_match: 1
              }
            }
          ]
        }
      }
    ];

    expect(aggs.publications).toHaveProperty("nested", expectedNested);
    expect(aggs.publications.aggs.publicationFilters.aggs).toEqual(
      expectedAggs
    );

    expect(aggs.publications.aggs.publicationFilters.filters.filters).toEqual(
      expectedFilters
    );
  });

  it("adds term filter clause for publication isLastOrder filter", () => {
    const asset = new PublicationProfileAsset();
    const filterValues: ProfileFilterValue[] = [
      {
        type: ProfileFilterValueType.PublicationIsLastOrder,
        value: true
      }
    ];
    const aggs = asset.buildIdsAggregation(undefined, filterValues);

    const expectedFilters = [
      {
        bool: {
          must: [
            {
              bool: {
                should: [
                  {
                    term: { "publications.isLastOrder": true }
                  }
                ],
                minimum_should_match: 1
              }
            }
          ]
        }
      }
    ];

    expect(aggs.publications).toHaveProperty("nested", expectedNested);
    expect(aggs.publications.aggs.publicationFilters.aggs).toEqual(
      expectedAggs
    );

    expect(aggs.publications.aggs.publicationFilters.filters.filters).toEqual(
      expectedFilters
    );
  });

  it("adds term filter clause for publication isLastOrder filter and isFirstOrder", () => {
    const asset = new PublicationProfileAsset();
    const filterValues: ProfileFilterValue[] = [
      {
        type: ProfileFilterValueType.PublicationIsLastOrder,
        value: true
      },
      {
        type: ProfileFilterValueType.PublicationIsFirstOrder,
        value: true
      }
    ];
    const aggs = asset.buildIdsAggregation(undefined, filterValues);

    const expectedFilters = [
      {
        bool: {
          must: [
            {
              bool: {
                should: [
                  {
                    term: { "publications.isFirstOrder": true }
                  },
                  {
                    term: { "publications.isLastOrder": true }
                  }
                ],
                minimum_should_match: 1
              }
            }
          ]
        }
      }
    ];

    expect(aggs.publications).toHaveProperty("nested", expectedNested);
    expect(aggs.publications.aggs.publicationFilters.aggs).toEqual(
      expectedAggs
    );

    expect(aggs.publications.aggs.publicationFilters.filters.filters).toEqual(
      expectedFilters
    );
  });
});

describe("buildMetricsAggregation", () => {
  it("should return unfiltered aggregations when neither terms nor filters are supplied", () => {
    const publicationProfileAsset = new PublicationProfileAsset();

    const query = publicationProfileAsset.buildMetricsAggregation();

    expect(query).toEqual({
      publications: {
        nested: {
          path: "publications"
        },
        aggs: {
          yearTotals: {
            date_histogram: {
              field: "publications.datePublished",
              calendar_interval: "year"
            }
          },
          citationCount: {
            sum: {
              field: "publications.citationCount"
            }
          },
          guidelineCount: {
            filter: {
              bool: {
                should: [
                  {
                    terms: {
                      "publications.type_eng": [
                        "Guideline",
                        "Practice Guideline"
                      ]
                    }
                  },
                  {
                    simple_query_string: {
                      query: [
                        '"practice update"',
                        '"clinical practice guideline"',
                        '"clinical practice guidelines"'
                      ].join(" | "),
                      fields: ["publications.keywords_eng"],
                      default_operator: "OR",
                      flags: "PHRASE"
                    }
                  },
                  {
                    bool: {
                      must: [
                        {
                          simple_query_string: {
                            query: '"standard of care"',
                            fields: ["publications.keywords_eng"],
                            flags: "PHRASE"
                          }
                        }
                      ],
                      must_not: [
                        {
                          terms: {
                            "publications.type_eng": [
                              "Randomized Controlled Trial",
                              "Comparative Study",
                              "Controlled Clinical Trial",
                              "Clinical Trial",
                              "Phase I",
                              " Phase I",
                              "Phase II",
                              " Phase II",
                              "Phase III",
                              " Phase III"
                            ]
                          }
                        }
                      ]
                    }
                  }
                ],
                minimum_should_match: 1
              }
            }
          },
          microBloggingCount: {
            sum: {
              field: "publications.microBloggingCount"
            }
          }
        }
      }
    });
  });

  it("should include all supplied filters in the query", () => {
    const publicationProfileAsset = new PublicationProfileAsset();

    const minDate = new Date(faker.date.past()).getTime();
    const maxDate = new Date(faker.date.past()).getTime();
    const journals = [faker.datatype.string(), faker.datatype.string()];
    const journalType = faker.datatype.string();

    const filters: Array<ProfileFilterValue> = [
      {
        type: ProfileFilterValueType.PublicationDate,
        value: {
          min: minDate,
          max: maxDate
        }
      },
      {
        type: ProfileFilterValueType.PublicationJournal,
        value: journals
      },
      {
        type: ProfileFilterValueType.PublicationType,
        value: journalType
      }
    ];

    const query = publicationProfileAsset.buildMetricsAggregation(
      undefined,
      filters
    );

    expect(query).toEqual({
      publications: {
        nested: {
          path: "publications"
        },
        aggs: {
          publicationFilters: {
            filters: {
              filters: [
                {
                  bool: {
                    must: [
                      {
                        range: {
                          "publications.datePublished": {
                            gte: minDate,
                            lte: maxDate
                          }
                        }
                      },
                      {
                        terms: {
                          "publications.journalName_eng": journals
                        }
                      },
                      {
                        term: {
                          "publications.type_eng": journalType
                        }
                      }
                    ]
                  }
                }
              ]
            },
            aggs: {
              yearTotals: {
                date_histogram: {
                  field: "publications.datePublished",
                  calendar_interval: "year"
                }
              },
              citationCount: {
                sum: {
                  field: "publications.citationCount"
                }
              },
              guidelineCount: {
                filter: {
                  bool: {
                    should: [
                      {
                        terms: {
                          "publications.type_eng": [
                            "Guideline",
                            "Practice Guideline"
                          ]
                        }
                      },
                      {
                        simple_query_string: {
                          query: [
                            '"practice update"',
                            '"clinical practice guideline"',
                            '"clinical practice guidelines"'
                          ].join(" | "),
                          fields: ["publications.keywords_eng"],
                          default_operator: "OR",
                          flags: "PHRASE"
                        }
                      },
                      {
                        bool: {
                          must: [
                            {
                              simple_query_string: {
                                query: '"standard of care"',
                                fields: ["publications.keywords_eng"],
                                flags: "PHRASE"
                              }
                            }
                          ],
                          must_not: [
                            {
                              terms: {
                                "publications.type_eng": [
                                  "Randomized Controlled Trial",
                                  "Comparative Study",
                                  "Controlled Clinical Trial",
                                  "Clinical Trial",
                                  "Phase I",
                                  " Phase I",
                                  "Phase II",
                                  " Phase II",
                                  "Phase III",
                                  " Phase III"
                                ]
                              }
                            }
                          ]
                        }
                      }
                    ],
                    minimum_should_match: 1
                  }
                }
              },
              microBloggingCount: {
                sum: {
                  field: "publications.microBloggingCount"
                }
              }
            }
          }
        }
      }
    });
  });

  it("should not apply any aggregation filters when filters is an empty array", () => {
    const publicationProfileAsset = new PublicationProfileAsset();

    const query = publicationProfileAsset.buildMetricsAggregation(
      undefined,
      []
    );

    expect(query).toEqual({
      publications: {
        nested: {
          path: "publications"
        },
        aggs: {
          yearTotals: {
            date_histogram: {
              field: "publications.datePublished",
              calendar_interval: "year"
            }
          },
          citationCount: {
            sum: {
              field: "publications.citationCount"
            }
          },
          guidelineCount: {
            filter: {
              bool: {
                should: [
                  {
                    terms: {
                      "publications.type_eng": [
                        "Guideline",
                        "Practice Guideline"
                      ]
                    }
                  },
                  {
                    simple_query_string: {
                      query: [
                        '"practice update"',
                        '"clinical practice guideline"',
                        '"clinical practice guidelines"'
                      ].join(" | "),
                      fields: ["publications.keywords_eng"],
                      default_operator: "OR",
                      flags: "PHRASE"
                    }
                  },
                  {
                    bool: {
                      must: [
                        {
                          simple_query_string: {
                            query: '"standard of care"',
                            fields: ["publications.keywords_eng"],
                            flags: "PHRASE"
                          }
                        }
                      ],
                      must_not: [
                        {
                          terms: {
                            "publications.type_eng": [
                              "Randomized Controlled Trial",
                              "Comparative Study",
                              "Controlled Clinical Trial",
                              "Clinical Trial",
                              "Phase I",
                              " Phase I",
                              "Phase II",
                              " Phase II",
                              "Phase III",
                              " Phase III"
                            ]
                          }
                        }
                      ]
                    }
                  }
                ],
                minimum_should_match: 1
              }
            }
          },
          microBloggingCount: {
            sum: {
              field: "publications.microBloggingCount"
            }
          }
        }
      }
    });
  });

  it("should only include filter for terms when no filters are supplied", () => {
    const publicationProfileAsset = new PublicationProfileAsset();

    const queryContainer: QueryDslQueryContainer = {
      term: {
        [faker.database.column()]: faker.datatype.string()
      }
    };

    const query =
      publicationProfileAsset.buildMetricsAggregation(queryContainer);

    expect(query).toEqual({
      publications: {
        nested: {
          path: "publications"
        },
        aggs: {
          publicationFilters: {
            filters: {
              filters: [
                {
                  bool: {
                    must: [queryContainer]
                  }
                }
              ]
            },
            aggs: {
              yearTotals: {
                date_histogram: {
                  field: "publications.datePublished",
                  calendar_interval: "year"
                }
              },
              citationCount: {
                sum: {
                  field: "publications.citationCount"
                }
              },
              guidelineCount: {
                filter: {
                  bool: {
                    should: [
                      {
                        terms: {
                          "publications.type_eng": [
                            "Guideline",
                            "Practice Guideline"
                          ]
                        }
                      },
                      {
                        simple_query_string: {
                          query: [
                            '"practice update"',
                            '"clinical practice guideline"',
                            '"clinical practice guidelines"'
                          ].join(" | "),
                          fields: ["publications.keywords_eng"],
                          default_operator: "OR",
                          flags: "PHRASE"
                        }
                      },
                      {
                        bool: {
                          must: [
                            {
                              simple_query_string: {
                                query: '"standard of care"',
                                fields: ["publications.keywords_eng"],
                                flags: "PHRASE"
                              }
                            }
                          ],
                          must_not: [
                            {
                              terms: {
                                "publications.type_eng": [
                                  "Randomized Controlled Trial",
                                  "Comparative Study",
                                  "Controlled Clinical Trial",
                                  "Clinical Trial",
                                  "Phase I",
                                  " Phase I",
                                  "Phase II",
                                  " Phase II",
                                  "Phase III",
                                  " Phase III"
                                ]
                              }
                            }
                          ]
                        }
                      }
                    ],
                    minimum_should_match: 1
                  }
                }
              },
              microBloggingCount: {
                sum: {
                  field: "publications.microBloggingCount"
                }
              }
            }
          }
        }
      }
    });
  });

  it("should apply all supplied filters in addition to terms", () => {
    const publicationProfileAsset = new PublicationProfileAsset();

    const queryContainer: QueryDslQueryContainer = {
      term: {
        [faker.database.column()]: faker.datatype.string()
      }
    };

    const minDate = new Date(faker.date.past()).getTime();
    const maxDate = new Date(faker.date.past()).getTime();
    const journals = [faker.datatype.string(), faker.datatype.string()];
    const journalType = faker.datatype.string();

    const filters: Array<ProfileFilterValue> = [
      {
        type: ProfileFilterValueType.PublicationDate,
        value: {
          min: minDate,
          max: maxDate
        }
      },
      {
        type: ProfileFilterValueType.PublicationJournal,
        value: journals
      },
      {
        type: ProfileFilterValueType.PublicationType,
        value: journalType
      }
    ];

    const query = publicationProfileAsset.buildMetricsAggregation(
      queryContainer,
      filters
    );

    expect(query).toEqual({
      publications: {
        nested: {
          path: "publications"
        },
        aggs: {
          publicationFilters: {
            filters: {
              filters: [
                {
                  bool: {
                    must: [
                      queryContainer,
                      {
                        range: {
                          "publications.datePublished": {
                            gte: minDate,
                            lte: maxDate
                          }
                        }
                      },
                      {
                        terms: {
                          "publications.journalName_eng": journals
                        }
                      },
                      {
                        term: {
                          "publications.type_eng": journalType
                        }
                      }
                    ]
                  }
                }
              ]
            },
            aggs: {
              yearTotals: {
                date_histogram: {
                  field: "publications.datePublished",
                  calendar_interval: "year"
                }
              },
              citationCount: {
                sum: {
                  field: "publications.citationCount"
                }
              },
              guidelineCount: {
                filter: {
                  bool: {
                    should: [
                      {
                        terms: {
                          "publications.type_eng": [
                            "Guideline",
                            "Practice Guideline"
                          ]
                        }
                      },
                      {
                        simple_query_string: {
                          query: [
                            '"practice update"',
                            '"clinical practice guideline"',
                            '"clinical practice guidelines"'
                          ].join(" | "),
                          fields: ["publications.keywords_eng"],
                          default_operator: "OR",
                          flags: "PHRASE"
                        }
                      },
                      {
                        bool: {
                          must: [
                            {
                              simple_query_string: {
                                query: '"standard of care"',
                                fields: ["publications.keywords_eng"],
                                flags: "PHRASE"
                              }
                            }
                          ],
                          must_not: [
                            {
                              terms: {
                                "publications.type_eng": [
                                  "Randomized Controlled Trial",
                                  "Comparative Study",
                                  "Controlled Clinical Trial",
                                  "Clinical Trial",
                                  "Phase I",
                                  " Phase I",
                                  "Phase II",
                                  " Phase II",
                                  "Phase III",
                                  " Phase III"
                                ]
                              }
                            }
                          ]
                        }
                      }
                    ],
                    minimum_should_match: 1
                  }
                }
              },
              microBloggingCount: {
                sum: {
                  field: "publications.microBloggingCount"
                }
              }
            }
          }
        }
      }
    });
  });
});
