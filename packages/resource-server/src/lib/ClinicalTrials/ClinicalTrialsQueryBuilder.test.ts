import {
  createQuery,
  createQ<PERSON>yWithCTMS,
  fieldsToSearchRootTrials,
  QueryVersion
} from "./ClinicalTrialsQueryBuilder";
import { faker } from "@faker-js/faker";
import { trialSourceIdMap, trialsAggregationsV3 } from "./trials-index";
import { createMockInstance } from "../../util/TestUtils";
import { QueryParserService } from "../../services/QueryParserService";
import { ParsedQueryTreeToElasticsearchQueriesService } from "../../services/ParsedQueryTreeToElasticsearchQueries";
import { QueryDslQueryContainer } from "@elastic/elasticsearch/lib/api/types";
import { QueryUnderstandingServiceClient } from "../../services/QueryUnderstandingServiceClient";
import { QueryUnderstandingServiceResponse } from "../../proto/query_understanding_service_pb";
import { ITree } from "../ParserTypes/types";

const expectedKeywordQuery = (
  queryKeyword: string,
  synonymizedQuery?: string
) => {
  return [
    {
      bool: {
        should: [
          {
            nested: {
              path: "facility",
              query: {
                bool: {
                  filter: [
                    {
                      simple_query_string: {
                        query: queryKeyword,
                        default_operator: "AND",
                        fields: [
                          "facility.name",
                          "facility.city",
                          "facility.state",
                          "facility.zip",
                          "facility.country"
                        ]
                      }
                    }
                  ]
                }
              }
            }
          },
          {
            nested: {
              path: "person",
              query: {
                bool: {
                  filter: [
                    {
                      simple_query_string: {
                        query: queryKeyword,
                        default_operator: "AND",
                        fields: ["person.name"]
                      }
                    }
                  ]
                }
              }
            }
          },
          {
            bool: {
              filter: [
                {
                  simple_query_string: {
                    query: synonymizedQuery ?? queryKeyword,
                    default_operator: "AND",
                    fields: [
                      "identifiers.external_uuid",
                      "study.brief_title",
                      "study.official_title",
                      "study.phase",
                      "study.overall_status",
                      "study.design.intervention_model",
                      "study.design.observational_model",
                      "study.design.primary_purpose",
                      "study.design.time_perspective",
                      "study.design.masking",
                      "study.design.masking_description",
                      "study.design.intervention_model_description",
                      "study.designOutcome.outcome_type",
                      "study.designOutcome.measure",
                      "study.designOutcome.time_frame",
                      "study.designOutcome.description",
                      "study.designGroup.group_type",
                      "study.designGroup.title",
                      "study.designGroup.description",
                      "study.conditions",
                      "study.keywords",
                      "study.intervention.intervention_type",
                      "study.intervention.name",
                      "study.intervention.description",
                      "study.intervention.intervention_other_names",
                      "study.sponsor.agency_class",
                      "study.enrollment_type",
                      "study.eligibility.minimum_age"
                    ]
                  }
                }
              ]
            }
          }
        ]
      }
    }
  ];
};

describe("buildClinicalTrialsQuery()", () => {
  it("builds a query with filters and query", async () => {
    const queryKeyword = faker.datatype.string();
    const input = {
      filters: [
        {
          name: "SearchQuery",
          value: queryKeyword
        }
      ],
      limit: 10,
      offset: 0,
      sort: {
        sortBy: "StartDate",
        direction: "Desc"
      }
      //idsOnly?: boolean;
    };

    const query = await createQuery(input);

    const expectedQuery = {
      from: input.offset,
      size: input.limit,
      track_total_hits: true,
      sort: [
        {
          studyStartDate: "desc"
        }
      ],
      query: {
        bool: {
          filter: [],
          must: expectedKeywordQuery(queryKeyword)
        }
      },
      aggs: {}
    };

    expect(query).toEqual(expectedQuery);
  });

  it("builds a query with filters and filter ids", async () => {
    const queryKeyword = faker.datatype.string();
    const input = {
      filters: [
        {
          name: "SearchQuery",
          value: queryKeyword
        }
      ],
      ids: ["1445275"],
      limit: 10,
      offset: 0,
      sort: {
        sortBy: "StartDate",
        direction: "Desc"
      }
      //idsOnly?: boolean;
    };

    const query2 = await createQuery(input);

    const expectedQuery2 = {
      from: input.offset,
      size: input.limit,
      track_total_hits: true,
      sort: [
        {
          studyStartDate: "desc"
        }
      ],
      query: {
        bool: {
          filter: [],
          must: [
            ...expectedKeywordQuery(queryKeyword),
            {
              terms: {
                studyId: input.ids
              }
            }
          ]
        }
      },
      aggs: {}
    };

    expect(query2).toEqual(expectedQuery2);
  });

  it("builds a multi-select query with filters", async () => {
    const sponsors = [faker.random.words(), faker.random.words()];
    const cities = [faker.random.words(), faker.random.words()];
    const conditions = [
      faker.random.words(),
      faker.random.words(),
      faker.random.words()
    ];

    const input = {
      filters: [
        { name: "Sponsor", value: `${JSON.stringify(sponsors)}` },
        { name: "City", value: `${JSON.stringify(cities)}` },
        {
          name: "Conditions",
          value: `${JSON.stringify(conditions)}`
        }
      ],
      limit: 200,
      offset: 0,
      sort: { sortBy: "StartDate", direction: "Desc" }
    };

    const queryMultiSelect = await createQuery(input);
    const expectedQueryMultiSelect = {
      from: 0,
      size: 200,
      track_total_hits: true,
      sort: [
        {
          studyStartDate: "desc"
        }
      ],
      query: {
        bool: {
          must: [
            {
              bool: {
                should: [
                  {
                    query_string: {
                      query: `${sponsors[0]}`,
                      fields: ["sponsorName"]
                    }
                  },
                  {
                    query_string: {
                      query: `${sponsors[1]}`,
                      fields: ["sponsorName"]
                    }
                  }
                ]
              }
            },
            {
              bool: {
                should: [
                  {
                    query_string: {
                      query: `${cities[0]}`,
                      fields: ["facilityCity.autocomplete"]
                    }
                  },
                  {
                    query_string: {
                      query: `${cities[1]}`,
                      fields: ["facilityCity.autocomplete"]
                    }
                  }
                ]
              }
            },
            {
              bool: {
                should: [
                  {
                    simple_query_string: {
                      query: `${conditions[0]}`,
                      fields: ["conditionName"],
                      default_operator: "AND"
                    }
                  },
                  {
                    simple_query_string: {
                      query: `${conditions[1]}`,
                      fields: ["conditionName"],
                      default_operator: "AND"
                    }
                  },
                  {
                    simple_query_string: {
                      query: `${conditions[2]}`,
                      fields: ["conditionName"],
                      default_operator: "AND"
                    }
                  }
                ]
              }
            }
          ],
          filter: []
        }
      },
      aggs: {}
    };

    expect(queryMultiSelect).toEqual(expectedQueryMultiSelect);
  });

  it("builds a query with filters and query 2", async () => {
    const input = {
      filters: [],
      limit: 25,
      offset: 0,
      sort: {
        sortBy: "StudyCompletionDate",
        direction: "Desc"
      }
    };

    const query = await createQuery(input);

    const expectedQuery = {
      from: 0,
      query: {
        match_all: {}
      },
      size: 25,
      sort: [
        {
          studyEndDate: "desc"
        }
      ],
      track_total_hits: true,
      aggs: {}
    };

    expect(query).toEqual(expectedQuery);
  });

  it("builds a query with filters, aggregations and query 3", async () => {
    const input = {
      filters: [
        {
          name: "StudyPhase",
          value: '["\\"Phase 3\\""]'
        }
      ],
      limit: 25,
      offset: 0,
      sort: {
        sortBy: "StartDate",
        direction: "Desc"
      }
    };

    const query = await createQuery(input, QueryVersion.V2);

    const expectedQuery = {
      from: 0,
      size: 25,
      track_total_hits: true,
      sort: [
        {
          "study.start_date": "desc"
        }
      ],
      query: {
        bool: {
          must: [
            {
              bool: {
                should: [
                  {
                    query_string: {
                      query: '"Phase 3"',
                      fields: ["study.phase.keyword"]
                    }
                  },
                  {
                    query_string: {
                      query: '"PHASE3"',
                      fields: ["study.phase.keyword"]
                    }
                  },
                  {
                    query_string: {
                      query: '"Phase III"',
                      fields: ["study.phase.keyword"]
                    }
                  },
                  {
                    query_string: {
                      query: '"Therapeutic confirmatory  (Phase III)"',
                      fields: ["study.phase.keyword"]
                    }
                  },
                  {
                    query_string: {
                      query: '"3"',
                      fields: ["study.phase.keyword"]
                    }
                  },
                  {
                    query_string: {
                      query: '"III"',
                      fields: ["study.phase.keyword"]
                    }
                  }
                ]
              }
            }
          ],
          filter: []
        }
      },
      aggs: expect.objectContaining({
        trial_duration: {
          filter: {
            bool: {
              must: [
                {
                  exists: {
                    field: "study.start_date"
                  }
                },
                {
                  exists: {
                    field: "study.completion_date"
                  }
                }
              ]
            }
          },
          aggs: expect.objectContaining({
            avg_duration: {
              avg: {
                script:
                  "ZonedDateTime d1 = doc['study.completion_date'].value; ZonedDateTime d2 = doc['study.start_date'].value; long differenceInMillis = ChronoUnit.MILLIS.between(d1, d2); return Math.abs(differenceInMillis);"
              }
            }
          })
        },
        facility_count: {
          filter: {
            exists: {
              field: "facility.statusHistory"
            }
          },
          aggs: {
            count: {
              value_count: {
                field: "facility.external_uuid.keyword"
              }
            }
          }
        },
        total_enrollment: {
          filter: {
            range: {
              "study.enrollment": {
                lte: 100000
              }
            }
          },
          aggs: expect.objectContaining({
            enrollment_sum: {
              sum: {
                field: "study.enrollment"
              }
            }
          })
        },
        group_by_person_role: {
          terms: {
            field: "person.roleHistory.role.keyword"
          }
        },
        group_by_country: {
          terms: {
            field: "facility.country.keyword",
            size: 10
          }
        },
        group_by_phase_and_status: {
          terms: {
            field: "study.phase.keyword"
          },
          aggs: {
            group_by_status: {
              terms: {
                field: "study.overall_status.keyword"
              }
            }
          }
        }
      })
    };

    expect(query).toEqual(expect.objectContaining(expectedQuery));
  });

  it("builds a query with ids, with v2 ignores ids not starting with NCT", async () => {
    const input = {
      ids: ["1445275", "NCT123"],
      limit: 25,
      offset: 0,
      sort: {
        sortBy: "StartDate",
        direction: "Desc"
      }
    };

    const query = await createQuery(input, QueryVersion.V2);

    const expectedQuery = {
      from: 0,
      size: 25,
      track_total_hits: true,
      sort: [
        {
          "study.start_date": "desc"
        }
      ],
      query: {
        bool: {
          filter: [],
          must: [
            {
              terms: {
                "identifiers.external_uuid.keyword": ["1445275", "NCT123"]
              }
            }
          ]
        }
      },
      aggs: expect.objectContaining({
        trial_duration: {
          filter: {
            bool: {
              must: [
                {
                  exists: {
                    field: "study.start_date"
                  }
                },
                {
                  exists: {
                    field: "study.completion_date"
                  }
                }
              ]
            }
          },
          aggs: expect.objectContaining({
            avg_duration: {
              avg: {
                script:
                  "ZonedDateTime d1 = doc['study.completion_date'].value; ZonedDateTime d2 = doc['study.start_date'].value; long differenceInMillis = ChronoUnit.MILLIS.between(d1, d2); return Math.abs(differenceInMillis);"
              }
            }
          })
        },
        facility_count: {
          filter: {
            exists: {
              field: "facility.statusHistory"
            }
          },
          aggs: {
            count: {
              value_count: {
                field: "facility.external_uuid.keyword"
              }
            }
          }
        },
        total_enrollment: {
          filter: {
            range: {
              "study.enrollment": {
                lte: 100000
              }
            }
          },
          aggs: expect.objectContaining({
            enrollment_sum: {
              sum: {
                field: "study.enrollment"
              }
            }
          })
        },
        group_by_person_role: {
          terms: {
            field: "person.roleHistory.role.keyword"
          }
        },
        group_by_country: {
          terms: {
            field: "facility.country.keyword",
            size: 10
          }
        },
        group_by_phase_and_status: {
          terms: {
            field: "study.phase.keyword"
          },
          aggs: {
            group_by_status: {
              terms: {
                field: "study.overall_status.keyword"
              }
            }
          }
        }
      })
    };

    expect(query).toEqual(expect.objectContaining(expectedQuery));
  });

  it("builds a query with ids, with v1 includes ids starting with NCT and numbers", async () => {
    const input = {
      ids: ["1445275", "NCT123"],
      limit: 25,
      offset: 0,
      sort: {
        sortBy: "StartDate",
        direction: "Desc"
      }
    };

    const query = await createQuery(input, QueryVersion.V1);

    const expectedQuery = {
      aggs: {},
      from: 0,
      query: {
        bool: {
          filter: [],
          must: [
            {
              terms: {
                studyId: ["1445275"]
              }
            },
            {
              terms: {
                studyNctId: ["NCT123"]
              }
            }
          ]
        }
      },
      size: 25,
      sort: [
        {
          studyStartDate: "desc"
        }
      ],
      track_total_hits: true
    };

    expect(query).toEqual(expectedQuery);
  });

  it("builds a query with nested filters, aggregations and query", async () => {
    const input = {
      filters: [
        {
          name: "Country",
          value: '["\\"Spain\\""]'
        }
      ],
      limit: 25,
      offset: 0,
      sort: {
        sortBy: "StartDate",
        direction: "Desc"
      }
    };

    const query = await createQuery(input, QueryVersion.V3);

    const expectedQuery = {
      from: 0,
      size: 25,
      track_total_hits: true,
      sort: [
        {
          "study.start_date": "desc"
        }
      ],
      query: {
        bool: {
          must: [
            {
              bool: {
                should: [
                  {
                    nested: {
                      path: ["facility"],
                      query: {
                        query_string: {
                          query: '"Spain"',
                          fields: ["facility.country"]
                        }
                      }
                    }
                  }
                ]
              }
            }
          ],
          filter: []
        }
      },
      aggs: expect.objectContaining({
        trial_duration: {
          filter: {
            bool: {
              must: [
                {
                  exists: {
                    field: "study.start_date"
                  }
                },
                {
                  exists: {
                    field: "study.completion_date"
                  }
                }
              ]
            }
          },
          aggs: expect.objectContaining({
            avg_duration: {
              avg: {
                script:
                  "ZonedDateTime d1 = doc['study.completion_date'].value; ZonedDateTime d2 = doc['study.start_date'].value; long differenceInMillis = ChronoUnit.MILLIS.between(d1, d2); return Math.abs(differenceInMillis);"
              }
            }
          })
        },
        facility_count: {
          filter: {
            exists: {
              field: "facility.statusHistory"
            }
          },
          aggs: {
            count: {
              value_count: {
                field: "facility.external_uuid.keyword"
              }
            }
          }
        },
        total_enrollment: {
          filter: {
            range: {
              "study.enrollment": {
                lte: 100000
              }
            }
          },
          aggs: expect.objectContaining({
            enrollment_sum: {
              sum: {
                field: "study.enrollment"
              }
            }
          })
        },
        group_by_person_role: {
          terms: {
            field: "person.roleHistory.role.keyword"
          }
        },
        group_by_country: {
          terms: {
            field: "facility.country.keyword",
            size: 10
          }
        },
        group_by_phase_and_status: {
          terms: {
            field: "study.phase.keyword"
          },
          aggs: {
            group_by_status: {
              terms: {
                field: "study.overall_status.keyword"
              }
            }
          }
        }
      })
    };

    expect(query).toEqual(expect.objectContaining(expectedQuery));
  });

  it("builds a query with acronym filters", async () => {
    const acronymValue = faker.random.word();

    const input = {
      filters: [
        {
          name: "Acronym",
          value: `["\\"${acronymValue}\\""]`
        }
      ],
      limit: 25,
      offset: 0,
      sort: {
        sortBy: "StartDate",
        direction: "Desc"
      }
    };

    const query = await createQuery(input, QueryVersion.V3);

    const expectedQuery = {
      from: 0,
      size: 25,
      track_total_hits: true,
      sort: [
        {
          "study.start_date": "desc"
        }
      ],
      query: {
        bool: {
          must: [
            {
              bool: {
                should: [
                  {
                    query_string: {
                      query: `"${acronymValue}"`,
                      fields: ["study.acronym"]
                    }
                  }
                ]
              }
            }
          ],
          filter: []
        }
      },
      aggs: expect.objectContaining({
        trial_duration: {
          filter: {
            bool: {
              must: [
                {
                  exists: {
                    field: "study.start_date"
                  }
                },
                {
                  exists: {
                    field: "study.completion_date"
                  }
                }
              ]
            }
          },
          aggs: expect.objectContaining({
            avg_duration: {
              avg: {
                script:
                  "ZonedDateTime d1 = doc['study.completion_date'].value; ZonedDateTime d2 = doc['study.start_date'].value; long differenceInMillis = ChronoUnit.MILLIS.between(d1, d2); return Math.abs(differenceInMillis);"
              }
            }
          })
        },
        facility_count: {
          filter: {
            exists: {
              field: "facility.statusHistory"
            }
          },
          aggs: {
            count: {
              value_count: {
                field: "facility.external_uuid.keyword"
              }
            }
          }
        },
        total_enrollment: {
          filter: {
            range: {
              "study.enrollment": {
                lte: 100000
              }
            }
          },
          aggs: expect.objectContaining({
            enrollment_sum: {
              sum: {
                field: "study.enrollment"
              }
            }
          })
        },
        group_by_person_role: {
          terms: {
            field: "person.roleHistory.role.keyword"
          }
        },
        group_by_country: {
          terms: {
            field: "facility.country.keyword",
            size: 10
          }
        },
        group_by_phase_and_status: {
          terms: {
            field: "study.phase.keyword"
          },
          aggs: {
            group_by_status: {
              terms: {
                field: "study.overall_status.keyword"
              }
            }
          }
        }
      })
    };

    expect(query).toEqual(expect.objectContaining(expectedQuery));
  });

  it("correctly builds a multi-select query with should and must not clause for source field", async () => {
    const sources = ["EUDRA", "NCT"];
    const input = {
      filters: [{ name: "Source", value: `${JSON.stringify(sources)}` }],
      limit: 200,
      offset: 0,
      sort: { sortBy: "StartDate", direction: "Desc" }
    };

    const queryMultiSelect = await createQuery(input, 3);
    const expectedQueryMultiSelect = {
      query: {
        bool: {
          must: [
            {
              bool: {
                should: [
                  {
                    term: {
                      "identifiers.collection_source_id": {
                        value: trialSourceIdMap.EUDRA.toString()
                      }
                    }
                  },
                  {
                    term: {
                      "identifiers.collection_source_id": {
                        value: trialSourceIdMap.NCT.toString()
                      }
                    }
                  }
                ],
                must_not: [
                  {
                    terms: {
                      "identifiers.collection_source_id": [
                        trialSourceIdMap.DRKS.toString(),
                        trialSourceIdMap.UMIN.toString(),
                        trialSourceIdMap.ChiCTR.toString(),
                        trialSourceIdMap.ISRCTN.toString(),
                        trialSourceIdMap.CTIS.toString()
                      ]
                    }
                  }
                ],
                must: undefined
              }
            }
          ],
          filter: []
        }
      }
    };

    expect(queryMultiSelect).toEqual(
      expect.objectContaining(expectedQueryMultiSelect)
    );
  });

  it("correctly builds a multi-select query for source when one only value is supplied", async () => {
    const sources = ["EUDRA"];
    const input = {
      filters: [{ name: "Source", value: `${JSON.stringify(sources)}` }],
      limit: 200,
      offset: 0,
      sort: { sortBy: "StartDate", direction: "Desc" }
    };

    const queryMultiSelect = await createQuery(input, 3);
    const expectedQueryMultiSelect = {
      query: {
        bool: {
          must: [
            {
              bool: {
                should: [
                  {
                    term: {
                      "identifiers.collection_source_id": {
                        value: trialSourceIdMap.EUDRA.toString()
                      }
                    }
                  }
                ],
                must_not: [
                  {
                    terms: {
                      "identifiers.collection_source_id": [
                        trialSourceIdMap.NCT.toString(),
                        trialSourceIdMap.DRKS.toString(),
                        trialSourceIdMap.UMIN.toString(),
                        trialSourceIdMap.ChiCTR.toString(),
                        trialSourceIdMap.ISRCTN.toString(),
                        trialSourceIdMap.CTIS.toString()
                      ]
                    }
                  }
                ],
                must: undefined
              }
            }
          ],
          filter: []
        }
      }
    };

    expect(queryMultiSelect).toEqual(
      expect.objectContaining(expectedQueryMultiSelect)
    );
  });

  it("correctly applies sort on the keyword field when EnrollmentType sortBy option is supplied", async () => {
    const input = {
      filters: [],
      limit: 25,
      offset: 0,
      sort: {
        sortBy: "EnrollmentType",
        direction: "Desc"
      }
    };

    const query = await createQuery(input, QueryVersion.V3);

    const expectedQuery = {
      from: 0,
      size: 25,
      track_total_hits: true,
      sort: [
        {
          "study.enrollment_type.keyword": "desc"
        }
      ],
      query: {
        match_all: {}
      },
      aggs: expect.objectContaining({
        trial_duration: {
          filter: {
            bool: {
              must: [
                {
                  exists: {
                    field: "study.start_date"
                  }
                },
                {
                  exists: {
                    field: "study.completion_date"
                  }
                }
              ]
            }
          },
          aggs: {
            avg_duration: {
              avg: {
                script:
                  "ZonedDateTime d1 = doc['study.completion_date'].value; ZonedDateTime d2 = doc['study.start_date'].value; long differenceInMillis = ChronoUnit.MILLIS.between(d1, d2); return Math.abs(differenceInMillis);"
              }
            },
            median_duration: {
              percentiles: {
                script:
                  "ZonedDateTime d1 = doc['study.completion_date'].value; ZonedDateTime d2 = doc['study.start_date'].value; long differenceInMillis = ChronoUnit.MILLIS.between(d1, d2); return Math.abs(differenceInMillis);",
                percents: [50]
              }
            }
          }
        },
        enrollment_duration: {
          filter: {
            bool: {
              must: [
                { exists: { field: "study.start_date" } },
                { exists: { field: "study.primary_completion_date" } }
              ]
            }
          },
          aggs: {
            avg_duration: {
              avg: {
                script:
                  "ZonedDateTime d1 = doc['study.primary_completion_date'].value; ZonedDateTime d2 = doc['study.start_date'].value; long differenceInMillis = ChronoUnit.MILLIS.between(d1, d2); return Math.abs(differenceInMillis);"
              }
            },
            median_duration: {
              percentiles: {
                script:
                  "ZonedDateTime d1 = doc['study.primary_completion_date'].value; ZonedDateTime d2 = doc['study.start_date'].value; long differenceInMillis = ChronoUnit.MILLIS.between(d1, d2); return Math.abs(differenceInMillis);",
                percents: [50]
              }
            }
          }
        },
        facility_count: {
          filter: {
            exists: {
              field: "facility.statusHistory"
            }
          },
          aggs: {
            count: {
              value_count: {
                field: "facility.external_uuid.keyword"
              }
            }
          }
        },
        total_enrollment: {
          filter: {
            range: {
              "study.enrollment": {
                lte: 100000
              }
            }
          },
          aggs: {
            enrollment_sum: {
              sum: {
                field: "study.enrollment"
              }
            },
            median: {
              percentiles: {
                field: "study.enrollment",
                percents: [50]
              }
            }
          }
        },
        group_by_person_role: {
          terms: {
            field: "person.roleHistory.role.keyword"
          }
        },
        group_by_country: {
          terms: {
            field: "facility.country.keyword",
            size: 10
          }
        },
        group_by_phase_and_status: {
          terms: {
            field: "study.phase.keyword"
          },
          aggs: {
            group_by_status: {
              terms: {
                field: "study.overall_status.keyword"
              }
            }
          }
        }
      })
    };

    expect(query).toEqual(expect.objectContaining(expectedQuery));
  });

  it("builds a query with enrollment filter", async () => {
    const input = {
      filters: [
        {
          name: "Enrollment",
          value: '{"min":"2","max":"23"}'
        }
      ],
      limit: 25,
      offset: 0,
      sort: {
        sortBy: "StartDate",
        direction: "Desc"
      }
    };

    const query = await createQuery(
      input,
      QueryVersion.V4,
      trialsAggregationsV3
    );

    const expectedQuery = {
      query: {
        bool: {
          filter: [
            {
              range: {
                "study.enrollment": {
                  gte: "2",
                  lte: "23"
                }
              }
            }
          ]
        }
      },
      aggs: {
        total_enrollment: {
          filter: {
            range: {
              "study.enrollment": {
                gte: "2",
                lte: "23"
              }
            }
          },
          aggs: {
            enrollment_sum: {
              sum: {
                field: "study.enrollment"
              }
            },
            median: {
              percentiles: {
                field: "study.enrollment",
                percents: [50]
              }
            }
          }
        }
      }
    };

    expect(query.query).toEqual(expectedQuery.query);
    expect(query.aggs.total_enrollment).toEqual(
      expectedQuery.aggs.total_enrollment
    );
  });

  it("builds a query with base aggregations for enrollment count when no filter is supplied", async () => {
    const input = {
      filters: [],
      limit: 25,
      offset: 0,
      sort: {
        sortBy: "StartDate",
        direction: "Desc"
      }
    };

    const query = await await createQuery(
      input,
      QueryVersion.V4,
      trialsAggregationsV3
    );

    const expectedQuery = {
      query: {
        match_all: {}
      },
      aggs: {
        total_enrollment: {
          filter: {
            range: {
              "study.enrollment": {
                lte: 100000,
                gt: 0
              }
            }
          },
          aggs: {
            enrollment_sum: {
              sum: {
                field: "study.enrollment"
              }
            },
            median: {
              percentiles: {
                field: "study.enrollment",
                percents: [50]
              }
            }
          }
        }
      }
    };

    expect(query.query).toEqual(expectedQuery.query);
    expect(query.aggs.total_enrollment).toEqual(
      expectedQuery.aggs.total_enrollment
    );
  });
});

describe("clinical trials with CTMS", () => {
  it("builds a request with filters of biomarkerInclusion, aggregations and query", async () => {
    const filterValue = faker.random.word();
    const input = {
      filters: [
        {
          name: "BiomarkerInclusion",
          value: `["\\"${filterValue}\\""]`
        }
      ],
      limit: faker.datatype.number(),
      offset: faker.datatype.number(),
      sort: {
        sortBy: "StartDate",
        direction: "Desc"
      }
    };

    const query = await createQueryWithCTMS(input, trialsAggregationsV3);

    const expectedQuery = {
      from: input.offset,
      size: input.limit,
      track_total_hits: true,
      sort: [
        {
          "study.start_date": "desc"
        }
      ],
      query: {
        bool: {
          must: [
            {
              bool: {
                should: [
                  {
                    query_string: {
                      query: `"${filterValue}"`,
                      fields: ["biomarkerWithSynonyms_inclusion"]
                    }
                  }
                ]
              }
            }
          ],
          filter: [
            {
              bool: {
                should: [
                  {
                    bool: {
                      must_not: [
                        {
                          exists: {
                            field: "project_id"
                          }
                        }
                      ]
                    }
                  }
                ]
              }
            }
          ]
        }
      },
      aggs: expect.objectContaining(trialsAggregationsV3)
    };

    expect(query).toEqual(expect.objectContaining(expectedQuery));
  });

  it("builds a request with filters of biomarkers, aggregations and query", async () => {
    const filterValue = faker.random.word();
    const input = {
      filters: [
        {
          name: "Biomarkers",
          value: `["\\"${filterValue}\\""]`
        }
      ],
      limit: faker.datatype.number(),
      offset: faker.datatype.number(),
      sort: {
        sortBy: "StartDate",
        direction: "Desc"
      }
    };

    const query = await createQueryWithCTMS(input, trialsAggregationsV3);

    const expectedQuery = {
      from: input.offset,
      size: input.limit,
      track_total_hits: true,
      sort: [
        {
          "study.start_date": "desc"
        }
      ],
      query: {
        bool: {
          must: [
            {
              bool: {
                should: [
                  {
                    query_string: {
                      query: `"${filterValue}"`,
                      fields: ["biomarkerWithSynonyms"]
                    }
                  }
                ]
              }
            }
          ],
          filter: [
            {
              bool: {
                should: [
                  {
                    bool: {
                      must_not: [
                        {
                          exists: {
                            field: "project_id"
                          }
                        }
                      ]
                    }
                  }
                ]
              }
            }
          ]
        }
      },
      aggs: expect.objectContaining(trialsAggregationsV3)
    };

    expect(query).toEqual(expect.objectContaining(expectedQuery));
  });

  it("builds a request with filters of biomarkerExclusion, aggregations and query", async () => {
    const filterValue = faker.random.word();
    const input = {
      filters: [
        {
          name: "BiomarkerExclusion",
          value: `["\\"${filterValue}\\""]`
        }
      ],
      limit: faker.datatype.number(),
      offset: faker.datatype.number(),
      sort: {
        sortBy: "StartDate",
        direction: "Desc"
      }
    };

    const query = await createQueryWithCTMS(input, trialsAggregationsV3);

    const expectedQuery = {
      from: input.offset,
      size: input.limit,
      track_total_hits: true,
      sort: [
        {
          "study.start_date": "desc"
        }
      ],
      query: {
        bool: {
          must: [
            {
              bool: {
                should: [
                  {
                    query_string: {
                      query: `"${filterValue}"`,
                      fields: ["biomarkerWithSynonyms_exclusion"]
                    }
                  }
                ]
              }
            }
          ],
          filter: [
            {
              bool: {
                should: [
                  {
                    bool: {
                      must_not: [
                        {
                          exists: {
                            field: "project_id"
                          }
                        }
                      ]
                    }
                  }
                ]
              }
            }
          ]
        }
      },
      aggs: expect.objectContaining(trialsAggregationsV3)
    };

    expect(query).toEqual(expect.objectContaining(expectedQuery));
  });

  it("builds a request with filters of indicationInclusion, aggregations and query", async () => {
    const filterValue = faker.random.word();
    const input = {
      filters: [
        {
          name: "IndicationInclusion",
          value: `["\\"${filterValue}\\""]`
        }
      ],
      limit: faker.datatype.number(),
      offset: faker.datatype.number(),
      sort: {
        sortBy: "StartDate",
        direction: "Desc"
      }
    };

    const query = await createQueryWithCTMS(input, trialsAggregationsV3);

    const expectedQuery = {
      from: input.offset,
      size: input.limit,
      track_total_hits: true,
      sort: [
        {
          "study.start_date": "desc"
        }
      ],
      query: {
        bool: {
          must: [
            {
              bool: {
                should: [
                  {
                    query_string: {
                      query: `"${filterValue}"`,
                      fields: ["indication_inclusions"]
                    }
                  }
                ]
              }
            }
          ],
          filter: [
            {
              bool: {
                should: [
                  {
                    bool: {
                      must_not: [
                        {
                          exists: {
                            field: "project_id"
                          }
                        }
                      ]
                    }
                  }
                ]
              }
            }
          ]
        }
      },
      aggs: expect.objectContaining(trialsAggregationsV3)
    };

    expect(query).toEqual(expect.objectContaining(expectedQuery));
  });

  it("builds a request with filters of indications, aggregations and query", async () => {
    const filterValue = faker.random.word();
    const input = {
      filters: [
        {
          name: "Indications",
          value: `["\\"${filterValue}\\""]`
        }
      ],
      limit: faker.datatype.number(),
      offset: faker.datatype.number(),
      sort: {
        sortBy: "StartDate",
        direction: "Desc"
      }
    };

    const query = await createQueryWithCTMS(input, trialsAggregationsV3);

    const expectedQuery = {
      from: input.offset,
      size: input.limit,
      track_total_hits: true,
      sort: [
        {
          "study.start_date": "desc"
        }
      ],
      query: {
        bool: {
          must: [
            {
              bool: {
                should: [
                  {
                    query_string: {
                      query: `"${filterValue}"`,
                      fields: ["indications"]
                    }
                  }
                ]
              }
            }
          ],
          filter: [
            {
              bool: {
                should: [
                  {
                    bool: {
                      must_not: [
                        {
                          exists: {
                            field: "project_id"
                          }
                        }
                      ]
                    }
                  }
                ]
              }
            }
          ]
        }
      },
      aggs: expect.objectContaining(trialsAggregationsV3)
    };

    expect(query).toEqual(expect.objectContaining(expectedQuery));
  });

  it("builds a request with filters of indicationExclusion, aggregations and query", async () => {
    const filterValue = faker.random.word();
    const input = {
      filters: [
        {
          name: "IndicationExclusion",
          value: `["\\"${filterValue}\\""]`
        }
      ],
      limit: faker.datatype.number(),
      offset: faker.datatype.number(),
      sort: {
        sortBy: "StartDate",
        direction: "Desc"
      }
    };

    const query = await createQueryWithCTMS(input, trialsAggregationsV3);

    const expectedQuery = {
      from: input.offset,
      size: input.limit,
      track_total_hits: true,
      sort: [
        {
          "study.start_date": "desc"
        }
      ],
      query: {
        bool: {
          must: [
            {
              bool: {
                should: [
                  {
                    query_string: {
                      query: `"${filterValue}"`,
                      fields: ["indication_exclusions"]
                    }
                  }
                ]
              }
            }
          ],
          filter: [
            {
              bool: {
                should: [
                  {
                    bool: {
                      must_not: [
                        {
                          exists: {
                            field: "project_id"
                          }
                        }
                      ]
                    }
                  }
                ]
              }
            }
          ]
        }
      },
      aggs: expect.objectContaining(trialsAggregationsV3)
    };

    expect(query).toEqual(expect.objectContaining(expectedQuery));
  });

  it("builds a request with filters for generic drug names, aggregations and query", async () => {
    const filterValue = faker.random.word();

    const input = {
      filters: [
        {
          name: "GenericDrugNames",
          value: `["\\"${filterValue}\\""]`
        }
      ],
      limit: faker.datatype.number(),
      offset: faker.datatype.number(),
      sort: {
        sortBy: "StartDate",
        direction: "Desc"
      }
    };

    const query = await createQueryWithCTMS(input, trialsAggregationsV3);

    const expectedQuery = {
      from: input.offset,
      size: input.limit,
      track_total_hits: true,
      sort: [
        {
          "study.start_date": "desc"
        }
      ],
      query: {
        bool: {
          must: [
            {
              bool: {
                should: [
                  {
                    query_string: {
                      query: `"${filterValue}"`,
                      fields: ["generic_drug_names"]
                    }
                  }
                ]
              }
            }
          ],
          filter: [
            {
              bool: {
                should: [
                  {
                    bool: {
                      must_not: [
                        {
                          exists: {
                            field: "project_id"
                          }
                        }
                      ]
                    }
                  }
                ]
              }
            }
          ]
        }
      },
      aggs: expect.objectContaining(trialsAggregationsV3)
    };

    expect(query).toEqual(expect.objectContaining(expectedQuery));
  });

  it("builds a request with filters of inclusion_generic_drug_names, aggregations and query", async () => {
    const filterValue = faker.random.word();
    const input = {
      filters: [
        {
          name: "GenericDrugNameInclusion",
          value: `["\\"${filterValue}\\""]`
        }
      ],
      limit: faker.datatype.number(),
      offset: faker.datatype.number(),
      sort: {
        sortBy: "StartDate",
        direction: "Desc"
      }
    };

    const query = await createQueryWithCTMS(input, trialsAggregationsV3);

    const expectedQuery = {
      from: input.offset,
      size: input.limit,
      track_total_hits: true,
      sort: [
        {
          "study.start_date": "desc"
        }
      ],
      query: {
        bool: {
          must: [
            {
              bool: {
                should: [
                  {
                    query_string: {
                      query: `"${filterValue}"`,
                      fields: ["inclusion_generic_drug_names"]
                    }
                  }
                ]
              }
            }
          ],
          filter: [
            {
              bool: {
                should: [
                  {
                    bool: {
                      must_not: [
                        {
                          exists: {
                            field: "project_id"
                          }
                        }
                      ]
                    }
                  }
                ]
              }
            }
          ]
        }
      },
      aggs: expect.objectContaining(trialsAggregationsV3)
    };

    expect(query).toEqual(expect.objectContaining(expectedQuery));
  });

  it("builds a request with filters of exclusion_generic_drug_names, aggregations and query", async () => {
    const filterValue = faker.random.word();
    const input = {
      filters: [
        {
          name: "GenericDrugNameExclusion",
          value: `["\\"${filterValue}\\""]`
        }
      ],
      limit: faker.datatype.number(),
      offset: faker.datatype.number(),
      sort: {
        sortBy: "StartDate",
        direction: "Desc"
      }
    };

    const query = await createQueryWithCTMS(input, trialsAggregationsV3);

    const expectedQuery = {
      from: input.offset,
      size: input.limit,
      track_total_hits: true,
      sort: [
        {
          "study.start_date": "desc"
        }
      ],
      query: {
        bool: {
          must: [
            {
              bool: {
                should: [
                  {
                    query_string: {
                      query: `"${filterValue}"`,
                      fields: ["exclusion_generic_drug_names"]
                    }
                  }
                ]
              }
            }
          ],
          filter: [
            {
              bool: {
                should: [
                  {
                    bool: {
                      must_not: [
                        {
                          exists: {
                            field: "project_id"
                          }
                        }
                      ]
                    }
                  }
                ]
              }
            }
          ]
        }
      },
      aggs: expect.objectContaining(trialsAggregationsV3)
    };

    expect(query).toEqual(expect.objectContaining(expectedQuery));
  });

  it("builds a request with filters, aggregations and query", async () => {
    const filterValue = faker.random.word();
    const input = {
      filters: [
        {
          name: "StudyPhase",
          value: `["\\"${filterValue}\\""]`
        }
      ],
      limit: faker.datatype.number(),
      offset: faker.datatype.number(),
      sort: {
        sortBy: "StartDate",
        direction: "Desc"
      }
    };

    const query = await createQueryWithCTMS(input);

    const expectedQuery = {
      from: input.offset,
      size: input.limit,
      track_total_hits: true,
      sort: [
        {
          "study.start_date": "desc"
        }
      ],
      query: {
        bool: {
          must: [
            {
              bool: {
                should: [
                  {
                    query_string: {
                      query: `"${filterValue}"`,
                      fields: ["study.phase.keyword"]
                    }
                  }
                ]
              }
            }
          ],
          filter: [
            {
              bool: {
                should: [
                  {
                    bool: {
                      must_not: [
                        {
                          exists: {
                            field: "project_id"
                          }
                        }
                      ]
                    }
                  }
                ]
              }
            }
          ]
        }
      },
      aggs: expect.objectContaining({
        trial_duration: {
          filter: {
            bool: {
              must: [
                {
                  exists: {
                    field: "study.start_date"
                  }
                },
                {
                  exists: {
                    field: "study.completion_date"
                  }
                }
              ]
            }
          },
          aggs: expect.objectContaining({
            avg_duration: {
              avg: {
                script:
                  "ZonedDateTime d1 = doc['study.completion_date'].value; ZonedDateTime d2 = doc['study.start_date'].value; long differenceInMillis = ChronoUnit.MILLIS.between(d1, d2); return Math.abs(differenceInMillis);"
              }
            }
          })
        },
        facility_count: {
          filter: {
            exists: {
              field: "facility.statusHistory"
            }
          },
          aggs: {
            count: {
              value_count: {
                field: "facility.external_uuid.keyword"
              }
            }
          }
        },
        total_enrollment: {
          filter: {
            range: {
              "study.enrollment": {
                lte: 100000
              }
            }
          },
          aggs: expect.objectContaining({
            enrollment_sum: {
              sum: {
                field: "study.enrollment"
              }
            }
          })
        },
        group_by_person_role: {
          terms: {
            field: "person.roleHistory.role.keyword"
          }
        },
        group_by_country: {
          terms: {
            field: "facility.country.keyword",
            size: 10
          }
        },
        group_by_phase_and_status: {
          terms: {
            field: "study.phase.keyword"
          },
          aggs: {
            group_by_status: {
              terms: {
                field: "study.overall_status.keyword"
              }
            }
          }
        }
      })
    };

    expect(query).toEqual(expect.objectContaining(expectedQuery));
  });

  it("builds a query with ids", async () => {
    const input = {
      ids: [`NCT${faker.datatype.number()}`, faker.datatype.uuid()],
      limit: faker.datatype.number(),
      offset: faker.datatype.number(),
      sort: {
        sortBy: "StartDate",
        direction: "Desc"
      }
    };

    const query = await createQueryWithCTMS(input);

    const expectedQuery = {
      from: input.offset,
      size: input.limit,
      track_total_hits: true,
      sort: [
        {
          "study.start_date": "desc"
        }
      ],
      query: {
        bool: {
          filter: [
            {
              bool: {
                should: [
                  {
                    bool: {
                      must_not: [
                        {
                          exists: {
                            field: "project_id"
                          }
                        }
                      ]
                    }
                  }
                ]
              }
            }
          ],
          must: [
            {
              bool: {
                should: [
                  {
                    terms: {
                      "identifiers.external_uuid.keyword": input.ids
                    }
                  },
                  {
                    terms: {
                      h1dn_clinical_trial_id: input.ids
                    }
                  }
                ]
              }
            }
          ]
        }
      },
      aggs: expect.objectContaining({
        trial_duration: {
          filter: {
            bool: {
              must: [
                {
                  exists: {
                    field: "study.start_date"
                  }
                },
                {
                  exists: {
                    field: "study.completion_date"
                  }
                }
              ]
            }
          },
          aggs: expect.objectContaining({
            avg_duration: {
              avg: {
                script:
                  "ZonedDateTime d1 = doc['study.completion_date'].value; ZonedDateTime d2 = doc['study.start_date'].value; long differenceInMillis = ChronoUnit.MILLIS.between(d1, d2); return Math.abs(differenceInMillis);"
              }
            }
          })
        },
        facility_count: {
          filter: {
            exists: {
              field: "facility.statusHistory"
            }
          },
          aggs: {
            count: {
              value_count: {
                field: "facility.external_uuid.keyword"
              }
            }
          }
        },
        total_enrollment: {
          filter: {
            range: {
              "study.enrollment": {
                lte: 100000
              }
            }
          },
          aggs: expect.objectContaining({
            enrollment_sum: {
              sum: {
                field: "study.enrollment"
              }
            }
          })
        },
        group_by_person_role: {
          terms: {
            field: "person.roleHistory.role.keyword"
          }
        },
        group_by_country: {
          terms: {
            field: "facility.country.keyword",
            size: 10
          }
        },
        group_by_phase_and_status: {
          terms: {
            field: "study.phase.keyword"
          },
          aggs: {
            group_by_status: {
              terms: {
                field: "study.overall_status.keyword"
              }
            }
          }
        }
      })
    };

    expect(query).toEqual(expect.objectContaining(expectedQuery));
  });

  it("builds a query with nested filters, aggregations and query", async () => {
    const input = {
      filters: [
        {
          name: "Country",
          value: '["\\"Spain\\""]'
        }
      ],
      limit: faker.datatype.number(),
      offset: faker.datatype.number(),
      sort: {
        sortBy: "StartDate",
        direction: "Desc"
      }
    };

    const query = await createQueryWithCTMS(input);

    const expectedQuery = {
      from: input.offset,
      size: input.limit,
      track_total_hits: true,
      sort: [
        {
          "study.start_date": "desc"
        }
      ],
      query: {
        bool: {
          must: [
            {
              bool: {
                should: [
                  {
                    nested: {
                      path: ["facility"],
                      query: {
                        query_string: {
                          query: '"Spain"',
                          fields: ["facility.country"]
                        }
                      }
                    }
                  }
                ]
              }
            }
          ],
          filter: [
            {
              bool: {
                should: [
                  {
                    bool: {
                      must_not: [
                        {
                          exists: {
                            field: "project_id"
                          }
                        }
                      ]
                    }
                  }
                ]
              }
            }
          ]
        }
      },
      aggs: expect.objectContaining({
        trial_duration: {
          filter: {
            bool: {
              must: [
                {
                  exists: {
                    field: "study.start_date"
                  }
                },
                {
                  exists: {
                    field: "study.completion_date"
                  }
                }
              ]
            }
          },
          aggs: expect.objectContaining({
            avg_duration: {
              avg: {
                script:
                  "ZonedDateTime d1 = doc['study.completion_date'].value; ZonedDateTime d2 = doc['study.start_date'].value; long differenceInMillis = ChronoUnit.MILLIS.between(d1, d2); return Math.abs(differenceInMillis);"
              }
            }
          })
        },
        facility_count: {
          filter: {
            exists: {
              field: "facility.statusHistory"
            }
          },
          aggs: {
            count: {
              value_count: {
                field: "facility.external_uuid.keyword"
              }
            }
          }
        },
        total_enrollment: {
          filter: {
            range: {
              "study.enrollment": {
                lte: 100000
              }
            }
          },
          aggs: expect.objectContaining({
            enrollment_sum: {
              sum: {
                field: "study.enrollment"
              }
            }
          })
        },
        group_by_person_role: {
          terms: {
            field: "person.roleHistory.role.keyword"
          }
        },
        group_by_country: {
          terms: {
            field: "facility.country.keyword",
            size: 10
          }
        },
        group_by_phase_and_status: {
          terms: {
            field: "study.phase.keyword"
          },
          aggs: {
            group_by_status: {
              terms: {
                field: "study.overall_status.keyword"
              }
            }
          }
        }
      })
    };

    expect(query).toEqual(expect.objectContaining(expectedQuery));
  });

  it("builds a query with acronym filters", async () => {
    const acronymValue = faker.random.word();

    const input = {
      filters: [
        {
          name: "Acronym",
          value: `["\\"${acronymValue}\\""]`
        }
      ],
      limit: faker.datatype.number(),
      offset: faker.datatype.number(),
      sort: {
        sortBy: "StartDate",
        direction: "Desc"
      }
    };

    const query = await createQueryWithCTMS(input);

    const expectedQuery = {
      from: input.offset,
      size: input.limit,
      track_total_hits: true,
      sort: [
        {
          "study.start_date": "desc"
        }
      ],
      query: {
        bool: {
          must: [
            {
              bool: {
                should: [
                  {
                    query_string: {
                      query: `"${acronymValue}"`,
                      fields: ["study.acronym"]
                    }
                  }
                ]
              }
            }
          ],
          filter: [
            {
              bool: {
                should: [
                  {
                    bool: {
                      must_not: [
                        {
                          exists: {
                            field: "project_id"
                          }
                        }
                      ]
                    }
                  }
                ]
              }
            }
          ]
        }
      },
      aggs: expect.objectContaining({
        trial_duration: {
          filter: {
            bool: {
              must: [
                {
                  exists: {
                    field: "study.start_date"
                  }
                },
                {
                  exists: {
                    field: "study.completion_date"
                  }
                }
              ]
            }
          },
          aggs: expect.objectContaining({
            avg_duration: {
              avg: {
                script:
                  "ZonedDateTime d1 = doc['study.completion_date'].value; ZonedDateTime d2 = doc['study.start_date'].value; long differenceInMillis = ChronoUnit.MILLIS.between(d1, d2); return Math.abs(differenceInMillis);"
              }
            }
          })
        },
        facility_count: {
          filter: {
            exists: {
              field: "facility.statusHistory"
            }
          },
          aggs: {
            count: {
              value_count: {
                field: "facility.external_uuid.keyword"
              }
            }
          }
        },
        total_enrollment: {
          filter: {
            range: {
              "study.enrollment": {
                lte: 100000
              }
            }
          },
          aggs: expect.objectContaining({
            enrollment_sum: {
              sum: {
                field: "study.enrollment"
              }
            }
          })
        },
        group_by_person_role: {
          terms: {
            field: "person.roleHistory.role.keyword"
          }
        },
        group_by_country: {
          terms: {
            field: "facility.country.keyword",
            size: 10
          }
        },
        group_by_phase_and_status: {
          terms: {
            field: "study.phase.keyword"
          },
          aggs: {
            group_by_status: {
              terms: {
                field: "study.overall_status.keyword"
              }
            }
          }
        }
      })
    };

    expect(query).toEqual(expect.objectContaining(expectedQuery));
  });

  it("correctly builds a multi-select query with should and must not clause for source field", async () => {
    const sources = ["EUDRA", "NCT"];
    const input = {
      filters: [{ name: "Source", value: `${JSON.stringify(sources)}` }],
      limit: faker.datatype.number(),
      offset: faker.datatype.number(),
      sort: { sortBy: "StartDate", direction: "Desc" }
    };

    const queryMultiSelect = await createQueryWithCTMS(input);
    const expectedQueryMultiSelect = {
      from: input.offset,
      size: input.limit,
      track_total_hits: true,
      sort: [
        {
          "study.start_date": "desc"
        }
      ],
      query: {
        bool: {
          must: [
            {
              bool: {
                should: [
                  {
                    term: {
                      "identifiers.collection_source_id": {
                        value: trialSourceIdMap.EUDRA.toString()
                      }
                    }
                  },
                  {
                    term: {
                      "identifiers.collection_source_id": {
                        value: trialSourceIdMap.NCT.toString()
                      }
                    }
                  }
                ],
                must_not: [
                  {
                    terms: {
                      "identifiers.collection_source_id": [
                        trialSourceIdMap.DRKS.toString(),
                        trialSourceIdMap.UMIN.toString(),
                        trialSourceIdMap.ChiCTR.toString(),
                        trialSourceIdMap.ISRCTN.toString(),
                        trialSourceIdMap.CTIS.toString()
                      ]
                    }
                  }
                ],
                must: undefined
              }
            }
          ],
          filter: [
            {
              bool: {
                should: [
                  {
                    bool: {
                      must_not: [
                        {
                          exists: {
                            field: "project_id"
                          }
                        }
                      ]
                    }
                  }
                ]
              }
            }
          ]
        }
      },
      aggs: expect.anything()
    };

    expect(queryMultiSelect).toEqual(
      expect.objectContaining(expectedQueryMultiSelect)
    );
  });

  it("correctly builds a multi-select query for source when one only value is supplied", async () => {
    const sources = ["EUDRA"];
    const input = {
      filters: [{ name: "Source", value: `${JSON.stringify(sources)}` }],
      limit: faker.datatype.number(),
      offset: faker.datatype.number(),
      sort: { sortBy: "StartDate", direction: "Desc" }
    };

    const queryMultiSelect = await createQueryWithCTMS(input);
    const expectedQueryMultiSelect = {
      from: input.offset,
      size: input.limit,
      track_total_hits: true,
      sort: [
        {
          "study.start_date": "desc"
        }
      ],
      query: {
        bool: {
          must: [
            {
              bool: {
                should: [
                  {
                    term: {
                      "identifiers.collection_source_id": {
                        value: trialSourceIdMap.EUDRA.toString()
                      }
                    }
                  }
                ],
                must_not: [
                  {
                    terms: {
                      "identifiers.collection_source_id": [
                        trialSourceIdMap.NCT.toString(),
                        trialSourceIdMap.DRKS.toString(),
                        trialSourceIdMap.UMIN.toString(),
                        trialSourceIdMap.ChiCTR.toString(),
                        trialSourceIdMap.ISRCTN.toString(),
                        trialSourceIdMap.CTIS.toString()
                      ]
                    }
                  }
                ],
                must: undefined
              }
            }
          ],
          filter: [
            {
              bool: {
                should: [
                  {
                    bool: {
                      must_not: [
                        {
                          exists: {
                            field: "project_id"
                          }
                        }
                      ]
                    }
                  }
                ]
              }
            }
          ]
        }
      },
      aggs: expect.anything()
    };

    expect(queryMultiSelect).toEqual(
      expect.objectContaining(expectedQueryMultiSelect)
    );
  });

  it("correctly applies sort on the keyword field when EnrollmentType sortBy option is supplied", async () => {
    const input = {
      filters: [],
      limit: faker.datatype.number(),
      offset: faker.datatype.number(),
      sort: {
        sortBy: "EnrollmentType",
        direction: "Desc"
      }
    };

    const query = await createQueryWithCTMS(input);

    const expectedQuery = {
      from: input.offset,
      size: input.limit,
      track_total_hits: true,
      sort: [
        {
          "study.enrollment_type.keyword": "desc"
        }
      ],
      query: {
        bool: {
          filter: [
            {
              bool: {
                should: [
                  {
                    bool: {
                      must_not: [
                        {
                          exists: {
                            field: "project_id"
                          }
                        }
                      ]
                    }
                  }
                ]
              }
            }
          ]
        }
      },
      aggs: expect.objectContaining({
        trial_duration: {
          filter: {
            bool: {
              must: [
                {
                  exists: {
                    field: "study.start_date"
                  }
                },
                {
                  exists: {
                    field: "study.completion_date"
                  }
                }
              ]
            }
          },
          aggs: expect.objectContaining({
            avg_duration: {
              avg: {
                script:
                  "ZonedDateTime d1 = doc['study.completion_date'].value; ZonedDateTime d2 = doc['study.start_date'].value; long differenceInMillis = ChronoUnit.MILLIS.between(d1, d2); return Math.abs(differenceInMillis);"
              }
            }
          })
        },
        facility_count: {
          filter: {
            exists: {
              field: "facility.statusHistory"
            }
          },
          aggs: {
            count: {
              value_count: {
                field: "facility.external_uuid.keyword"
              }
            }
          }
        },
        total_enrollment: {
          filter: {
            range: {
              "study.enrollment": {
                lte: 100000
              }
            }
          },
          aggs: expect.objectContaining({
            enrollment_sum: {
              sum: {
                field: "study.enrollment"
              }
            }
          })
        },
        group_by_person_role: {
          terms: {
            field: "person.roleHistory.role.keyword"
          }
        },
        group_by_country: {
          terms: {
            field: "facility.country.keyword",
            size: 10
          }
        },
        group_by_phase_and_status: {
          terms: {
            field: "study.phase.keyword"
          },
          aggs: {
            group_by_status: {
              terms: {
                field: "study.overall_status.keyword"
              }
            }
          }
        }
      })
    };

    expect(query).toEqual(expect.objectContaining(expectedQuery));
  });

  it("builds a query with enrollment filter", async () => {
    const input = {
      filters: [
        {
          name: "Enrollment",
          value: '{"min":"2","max":"23"}'
        }
      ],
      limit: 25,
      offset: 0,
      sort: {
        sortBy: "StartDate",
        direction: "Desc"
      }
    };

    const query = await createQueryWithCTMS(input, trialsAggregationsV3);

    const expectedQuery = {
      from: input.offset,
      size: input.limit,
      track_total_hits: true,
      sort: [
        {
          "study.start_date": "desc"
        }
      ],
      query: {
        bool: {
          filter: [
            {
              range: {
                "study.enrollment": {
                  gte: "2",
                  lte: "23"
                }
              }
            },
            {
              bool: {
                should: [
                  {
                    bool: {
                      must_not: [
                        {
                          exists: {
                            field: "project_id"
                          }
                        }
                      ]
                    }
                  }
                ]
              }
            }
          ]
        }
      },
      aggs: expect.objectContaining({
        total_enrollment: {
          filter: {
            range: {
              "study.enrollment": {
                gte: "2",
                lte: "23"
              }
            }
          },
          aggs: expect.objectContaining({
            enrollment_sum: {
              sum: {
                field: "study.enrollment"
              }
            }
          })
        }
      })
    };

    expect(query).toEqual(expect.objectContaining(expectedQuery));
  });

  it("builds a query with base aggregations for enrollment count when no filter is supplied", async () => {
    const input = {
      filters: [],
      limit: 25,
      offset: 0,
      sort: {
        sortBy: "StartDate",
        direction: "Desc"
      }
    };

    const query = await createQueryWithCTMS(input, trialsAggregationsV3);

    const expectedQuery = {
      from: input.offset,
      size: input.limit,
      track_total_hits: true,
      sort: [
        {
          "study.start_date": "desc"
        }
      ],
      query: {
        bool: {
          filter: [
            {
              bool: {
                should: [
                  {
                    bool: {
                      must_not: [
                        {
                          exists: {
                            field: "project_id"
                          }
                        }
                      ]
                    }
                  }
                ]
              }
            }
          ]
        }
      },
      aggs: expect.objectContaining({
        total_enrollment: {
          filter: {
            range: {
              "study.enrollment": {
                lte: 100000,
                gt: 0
              }
            }
          },
          aggs: {
            enrollment_sum: {
              sum: {
                field: "study.enrollment"
              }
            },
            median: {
              percentiles: {
                field: "study.enrollment",
                percents: [50]
              }
            }
          }
        }
      })
    };

    expect(query).toEqual(expectedQuery);
  });

  it("builds a query with project id filter when provided in input", async () => {
    const input = {
      filters: [],
      limit: faker.datatype.number(),
      offset: faker.datatype.number(),
      sort: {
        sortBy: "StartDate",
        direction: "Desc"
      },
      projectId: faker.datatype.uuid()
    };

    const query = await createQueryWithCTMS(input, trialsAggregationsV3);

    const expectedQuery = {
      from: input.offset,
      size: input.limit,
      track_total_hits: true,
      sort: [
        {
          "study.start_date": "desc"
        }
      ],
      query: {
        bool: {
          filter: [
            {
              bool: {
                should: [
                  {
                    bool: {
                      must_not: [
                        {
                          exists: {
                            field: "project_id"
                          }
                        }
                      ]
                    }
                  },
                  {
                    bool: {
                      must: [
                        {
                          exists: {
                            field: "project_id"
                          }
                        },
                        {
                          term: {
                            project_id: input.projectId
                          }
                        }
                      ]
                    }
                  }
                ]
              }
            }
          ]
        }
      },
      aggs: expect.anything()
    };

    expect(query).toEqual(expectedQuery);
  });

  it("builds a request with hasCTMS filters, aggregations and query", async () => {
    const filterValue = faker.random.word();
    const input = {
      filters: [
        {
          name: "StudyPhase",
          value: `["\\"${filterValue}\\""]`
        },
        {
          name: "HasCTMSData",
          value: `true`
        }
      ],
      limit: faker.datatype.number(),
      offset: faker.datatype.number(),
      sort: {
        sortBy: "StartDate",
        direction: "Desc"
      },
      projectId: faker.datatype.number().toString()
    };

    const query = await createQueryWithCTMS(input);

    const expectedQuery = {
      from: input.offset,
      size: input.limit,
      track_total_hits: true,
      sort: [
        {
          "study.start_date": "desc"
        }
      ],
      query: {
        bool: {
          filter: [
            {
              bool: {
                should: [
                  {
                    bool: {
                      must_not: [
                        {
                          exists: {
                            field: "project_id"
                          }
                        }
                      ]
                    }
                  },
                  {
                    bool: {
                      must: [
                        {
                          exists: {
                            field: "project_id"
                          }
                        },
                        {
                          term: {
                            project_id: input.projectId!
                          }
                        }
                      ]
                    }
                  }
                ]
              }
            }
          ],
          must: [
            {
              nested: {
                path: "ctms",
                query: {
                  term: {
                    "ctms.project_id": input.projectId!
                  }
                }
              }
            },
            {
              bool: {
                should: [
                  {
                    query_string: {
                      query: `"${filterValue}"`,
                      fields: ["study.phase.keyword"]
                    }
                  }
                ]
              }
            }
          ]
        }
      },
      aggs: expect.objectContaining({
        trial_duration: {
          filter: {
            bool: {
              must: [
                {
                  exists: {
                    field: "study.start_date"
                  }
                },
                {
                  exists: {
                    field: "study.completion_date"
                  }
                }
              ]
            }
          },
          aggs: {
            avg_duration: {
              avg: {
                script:
                  "ZonedDateTime d1 = doc['study.completion_date'].value; ZonedDateTime d2 = doc['study.start_date'].value; long differenceInMillis = ChronoUnit.MILLIS.between(d1, d2); return Math.abs(differenceInMillis);"
              }
            },
            median_duration: {
              percentiles: {
                script:
                  "ZonedDateTime d1 = doc['study.completion_date'].value; ZonedDateTime d2 = doc['study.start_date'].value; long differenceInMillis = ChronoUnit.MILLIS.between(d1, d2); return Math.abs(differenceInMillis);",
                percents: [50]
              }
            }
          }
        },
        enrollment_duration: {
          filter: {
            bool: {
              must: [
                { exists: { field: "study.start_date" } },
                { exists: { field: "study.primary_completion_date" } }
              ]
            }
          },
          aggs: {
            avg_duration: {
              avg: {
                script:
                  "ZonedDateTime d1 = doc['study.primary_completion_date'].value; ZonedDateTime d2 = doc['study.start_date'].value; long differenceInMillis = ChronoUnit.MILLIS.between(d1, d2); return Math.abs(differenceInMillis);"
              }
            },
            median_duration: {
              percentiles: {
                script:
                  "ZonedDateTime d1 = doc['study.primary_completion_date'].value; ZonedDateTime d2 = doc['study.start_date'].value; long differenceInMillis = ChronoUnit.MILLIS.between(d1, d2); return Math.abs(differenceInMillis);",
                percents: [50]
              }
            }
          }
        },
        facility_count: {
          filter: {
            exists: {
              field: "facility.statusHistory"
            }
          },
          aggs: {
            count: {
              value_count: {
                field: "facility.external_uuid.keyword"
              }
            }
          }
        },
        total_enrollment: {
          filter: {
            range: {
              "study.enrollment": {
                lte: 100000
              }
            }
          },
          aggs: {
            enrollment_sum: {
              sum: {
                field: "study.enrollment"
              }
            },
            median: {
              percentiles: {
                field: "study.enrollment",
                percents: [50]
              }
            }
          }
        },
        group_by_person_role: {
          terms: {
            field: "person.roleHistory.role.keyword"
          }
        },
        group_by_country: {
          terms: {
            field: "facility.country.keyword",
            size: 10
          }
        },
        group_by_phase_and_status: {
          terms: {
            field: "study.phase.keyword"
          },
          aggs: {
            group_by_status: {
              terms: {
                field: "study.overall_status.keyword"
              }
            }
          }
        }
      })
    };

    expect(query).toEqual(expect.objectContaining(expectedQuery));
  });

  it("builds a query with inclusion filters", async () => {
    const array1Word1 = faker.random.word();
    const array1Word2 = faker.random.word();
    const array2Word1 = faker.random.word();
    const array2Word2 = faker.random.word();
    const input = {
      filters: [
        {
          name: "Inclusion",
          value: `[["\\"${array1Word1}\\"","\\"${array1Word2}\\""],["\\"${array2Word1}\\"","\\"${array2Word2}\\""]]`
        }
      ],
      limit: faker.datatype.number(),
      offset: faker.datatype.number(),
      sort: {
        sortBy: "StartDate",
        direction: "Desc"
      }
    };
    const queryParserService = createMockInstance(QueryParserService);
    const parsedQueryTreeToElasticsearchQueriesService = createMockInstance(
      ParsedQueryTreeToElasticsearchQueriesService
    );
    const expectedParsedQuery: QueryDslQueryContainer = {
      bool: {
        must: [
          {
            bool: {
              must: [
                {
                  bool: {
                    should: [
                      {
                        simple_query_string: {
                          query: `(${array1Word1}) | (${array1Word2})`,
                          default_operator: "AND",
                          fields: [
                            "study.eligibility.inclusion_criteria_parsed"
                          ],
                          flags:
                            "AND|ESCAPE|FUZZY|NEAR|NOT|OR|PHRASE|PRECEDENCE|PREFIX|SLOP"
                        }
                      }
                    ],
                    minimum_should_match: 1
                  }
                }
              ]
            }
          },
          {
            bool: {
              must: [
                {
                  bool: {
                    should: [
                      {
                        simple_query_string: {
                          query: `(${array2Word1}) | (${array2Word2})`,
                          default_operator: "AND",
                          fields: [
                            "study.eligibility.inclusion_criteria_parsed"
                          ],
                          flags:
                            "AND|ESCAPE|FUZZY|NEAR|NOT|OR|PHRASE|PRECEDENCE|PREFIX|SLOP"
                        }
                      }
                    ],
                    minimum_should_match: 1
                  }
                }
              ]
            }
          }
        ]
      }
    };
    parsedQueryTreeToElasticsearchQueriesService.parse.mockReturnValue(
      expectedParsedQuery
    );
    const query = await createQueryWithCTMS(
      input,
      undefined,
      undefined,
      queryParserService,
      parsedQueryTreeToElasticsearchQueriesService
    );

    const expectedQuery = {
      from: input.offset,
      size: input.limit,
      track_total_hits: true,
      sort: [
        {
          "study.start_date": "desc"
        }
      ],
      query: {
        bool: {
          filter: [
            expectedParsedQuery,
            {
              bool: {
                should: [
                  {
                    bool: {
                      must_not: [
                        {
                          exists: {
                            field: "project_id"
                          }
                        }
                      ]
                    }
                  }
                ]
              }
            }
          ]
        }
      },
      aggs: expect.anything()
    };
    expect(parsedQueryTreeToElasticsearchQueriesService.parse).toBeCalledWith(
      undefined,
      ["study.eligibility.inclusion_criteria_parsed"]
    );
    expect(query).toEqual(expect.objectContaining(expectedQuery));
  });

  it("builds a query with exclusion filters", async () => {
    const array1Word1 = faker.random.word();
    const array1Word2 = faker.random.word();
    const array2Word1 = faker.random.word();
    const array2Word2 = faker.random.word();

    const input = {
      filters: [
        {
          name: "Exclusion",
          value: `[["\\"${array1Word1}\\"","\\"${array1Word2}\\""],["\\"${array2Word1}\\"","\\"${array2Word2}\\""]]`
        }
      ],
      limit: faker.datatype.number(),
      offset: faker.datatype.number(),
      sort: {
        sortBy: "StartDate",
        direction: "Desc"
      }
    };
    const queryParserService = createMockInstance(QueryParserService);
    const parsedQueryTreeToElasticsearchQueriesService = createMockInstance(
      ParsedQueryTreeToElasticsearchQueriesService
    );
    const expectedParsedQuery: QueryDslQueryContainer = {
      bool: {
        must: [
          {
            bool: {
              must: [
                {
                  bool: {
                    should: [
                      {
                        simple_query_string: {
                          query: `(${array1Word1}) | (${array1Word2})`,
                          default_operator: "AND",
                          fields: [
                            "study.eligibility.exclusion_criteria_parsed"
                          ],
                          flags:
                            "AND|ESCAPE|FUZZY|NEAR|NOT|OR|PHRASE|PRECEDENCE|PREFIX|SLOP"
                        }
                      }
                    ],
                    minimum_should_match: 1
                  }
                }
              ]
            }
          },
          {
            bool: {
              must: [
                {
                  bool: {
                    should: [
                      {
                        simple_query_string: {
                          query: `(${array2Word1}) | (${array2Word2})`,
                          default_operator: "AND",
                          fields: [
                            "study.eligibility.exclusion_criteria_parsed"
                          ],
                          flags:
                            "AND|ESCAPE|FUZZY|NEAR|NOT|OR|PHRASE|PRECEDENCE|PREFIX|SLOP"
                        }
                      }
                    ],
                    minimum_should_match: 1
                  }
                }
              ]
            }
          }
        ]
      }
    };
    parsedQueryTreeToElasticsearchQueriesService.parse.mockReturnValue(
      expectedParsedQuery
    );
    const query = await createQueryWithCTMS(
      input,
      undefined,
      undefined,
      queryParserService,
      parsedQueryTreeToElasticsearchQueriesService
    );

    const expectedQuery = {
      from: input.offset,
      size: input.limit,
      track_total_hits: true,
      sort: [
        {
          "study.start_date": "desc"
        }
      ],
      query: {
        bool: {
          filter: [
            expectedParsedQuery,
            {
              bool: {
                should: [
                  {
                    bool: {
                      must_not: [
                        {
                          exists: {
                            field: "project_id"
                          }
                        }
                      ]
                    }
                  }
                ]
              }
            }
          ]
        }
      },
      aggs: expect.anything()
    };

    expect(parsedQueryTreeToElasticsearchQueriesService.parse).toBeCalledWith(
      undefined,
      ["study.eligibility.exclusion_criteria_parsed"]
    );
    expect(query).toEqual(expect.objectContaining(expectedQuery));
  });

  it("builds a query with enrollment rate filter", async () => {
    const input = {
      filters: [
        {
          name: "EnrollmentRate",
          value: '{"min":"2","max":"23"}'
        }
      ],
      limit: 25,
      offset: 0,
      sort: {
        sortBy: "StartDate",
        direction: "Desc"
      }
    };

    const query = await createQueryWithCTMS(input, trialsAggregationsV3);

    const expectedQuery = {
      from: input.offset,
      size: input.limit,
      track_total_hits: true,
      sort: [
        {
          "study.start_date": "desc"
        }
      ],
      query: {
        bool: {
          filter: [
            {
              range: {
                "study.patients_per_site_per_month": {
                  gte: "2",
                  lte: "23"
                }
              }
            },
            {
              bool: {
                should: [
                  {
                    bool: {
                      must_not: [
                        {
                          exists: {
                            field: "project_id"
                          }
                        }
                      ]
                    }
                  }
                ]
              }
            }
          ]
        }
      },
      aggs: expect.anything()
    };

    expect(query).toEqual(expect.objectContaining(expectedQuery));
  });

  it("builds a query with eligibility age filter", async () => {
    const input = {
      filters: [
        {
          name: "EligibilityAge",
          value: '{"min":"2","max":"23"}'
        }
      ],
      limit: 25,
      offset: 0,
      sort: {
        sortBy: "StartDate",
        direction: "Desc"
      }
    };

    const query = await createQueryWithCTMS(input, trialsAggregationsV3);

    const expectedQuery = {
      from: input.offset,
      size: input.limit,
      track_total_hits: true,
      sort: [
        {
          "study.start_date": "desc"
        }
      ],
      query: {
        bool: {
          filter: [
            {
              range: {
                "study.eligibility.minimum_age_years": {
                  gte: "2"
                }
              }
            },
            {
              range: {
                "study.eligibility.maximum_age_years": {
                  lte: "23"
                }
              }
            },
            {
              bool: {
                should: [
                  {
                    bool: {
                      must_not: [
                        {
                          exists: {
                            field: "project_id"
                          }
                        }
                      ]
                    }
                  }
                ]
              }
            }
          ]
        }
      },
      aggs: expect.anything()
    };

    expect(query).toEqual(expect.objectContaining(expectedQuery));
  });

  it("builds a query with enrollment duration filter", async () => {
    const input = {
      filters: [
        {
          name: "EnrollmentDuration",
          value: '{"min":"20","max":"23"}'
        }
      ],
      limit: 25,
      offset: 0,
      sort: {
        sortBy: "StartDate",
        direction: "Desc"
      }
    };

    const query = await createQueryWithCTMS(input, trialsAggregationsV3);

    const expectedQuery = {
      from: input.offset,
      size: input.limit,
      track_total_hits: true,
      sort: [
        {
          "study.start_date": "desc"
        }
      ],
      query: {
        bool: {
          filter: [
            {
              range: {
                "study.enrollment_duration_in_months": {
                  gte: "20",
                  lte: "23"
                }
              }
            },
            {
              bool: {
                should: [
                  {
                    bool: {
                      must_not: [
                        {
                          exists: {
                            field: "project_id"
                          }
                        }
                      ]
                    }
                  }
                ]
              }
            }
          ]
        }
      },
      aggs: expect.anything()
    };

    expect(query).toEqual(expect.objectContaining(expectedQuery));
  });

  it("builds a query with site count filter", async () => {
    const input = {
      filters: [
        {
          name: "SiteCount",
          value: '{"min":"10","max":"15"}'
        }
      ],
      limit: 25,
      offset: 0,
      sort: {
        sortBy: "StartDate",
        direction: "Desc"
      }
    };

    const query = await createQueryWithCTMS(input, trialsAggregationsV3);

    const expectedQuery = {
      from: input.offset,
      size: input.limit,
      track_total_hits: true,
      sort: [
        {
          "study.start_date": "desc"
        }
      ],
      query: {
        bool: {
          filter: [
            {
              range: {
                number_of_facilities: {
                  gte: "10",
                  lte: "15"
                }
              }
            },
            {
              bool: {
                should: [
                  {
                    bool: {
                      must_not: [
                        {
                          exists: {
                            field: "project_id"
                          }
                        }
                      ]
                    }
                  }
                ]
              }
            }
          ]
        }
      },
      aggs: expect.anything()
    };

    expect(query).toEqual(expect.objectContaining(expectedQuery));
  });

  it("builds a synonymised query with conditions filter", async () => {
    const conditions = [
      faker.random.words(),
      faker.random.words(),
      faker.random.words()
    ];
    const input = {
      filters: [
        {
          name: "Conditions",
          value: JSON.stringify(conditions)
        }
      ],
      limit: 25,
      offset: 0,
      sort: {
        sortBy: "StartDate",
        direction: "Desc"
      }
    };
    const queryUnderstandingServiceClient = createMockInstance(
      QueryUnderstandingServiceClient
    );
    const condition1Synonyms = `${conditions[0]} | ${faker.random.words()}`;
    const condition2Synonyms = `${conditions[1]} | ${faker.random.words()}`;
    const condition3Synonyms = `${conditions[2]} | ${faker.random.words()}`;
    const queryUnderstandingServiceResponse = createMockInstance(
      QueryUnderstandingServiceResponse
    );
    queryUnderstandingServiceResponse.getAugmentedQuery
      .mockReturnValueOnce(condition1Synonyms)
      .mockReturnValueOnce(condition2Synonyms)
      .mockReturnValueOnce(condition3Synonyms);

    queryUnderstandingServiceClient.analyze.mockResolvedValue(
      queryUnderstandingServiceResponse
    );

    const query = await createQueryWithCTMS(
      input,
      trialsAggregationsV3,
      false,
      undefined,
      undefined,
      queryUnderstandingServiceClient
    );
    const expectedQuery = {
      from: input.offset,
      size: input.limit,
      track_total_hits: true,
      sort: [
        {
          "study.start_date": "desc"
        }
      ],
      query: {
        bool: {
          filter: [
            {
              bool: {
                should: [
                  {
                    bool: {
                      must_not: [
                        {
                          exists: {
                            field: "project_id"
                          }
                        }
                      ]
                    }
                  }
                ]
              }
            }
          ],
          must: [
            {
              bool: {
                should: [
                  {
                    simple_query_string: {
                      query: `${condition1Synonyms}`,
                      fields: ["study.condition.name"],
                      default_operator: "AND"
                    }
                  },
                  {
                    simple_query_string: {
                      query: `${condition2Synonyms}`,
                      fields: ["study.condition.name"],
                      default_operator: "AND"
                    }
                  },
                  {
                    simple_query_string: {
                      query: `${condition3Synonyms}`,
                      fields: ["study.condition.name"],
                      default_operator: "AND"
                    }
                  }
                ]
              }
            }
          ]
        }
      },
      aggs: expect.anything()
    };

    expect(query).toEqual(expect.objectContaining(expectedQuery));
  });

  it("builds a synonymised query with search query", async () => {
    const queryKeyword = faker.random.words();
    const input = {
      filters: [
        {
          name: "SearchQuery",
          value: queryKeyword
        }
      ],
      limit: 10,
      offset: 0,
      sort: {
        sortBy: "StartDate",
        direction: "Desc"
      }
      //idsOnly?: boolean;
    };
    const queryUnderstandingServiceClient = createMockInstance(
      QueryUnderstandingServiceClient
    );
    const querySynonyms = `${queryKeyword} | ${faker.random.words()}`;

    const queryUnderstandingServiceResponse = createMockInstance(
      QueryUnderstandingServiceResponse
    );
    queryUnderstandingServiceResponse.getAugmentedQuery.mockReturnValue(
      querySynonyms
    );

    queryUnderstandingServiceClient.analyze.mockResolvedValue(
      queryUnderstandingServiceResponse
    );

    const query = await createQueryWithCTMS(
      input,
      undefined,
      undefined,
      undefined,
      undefined,
      queryUnderstandingServiceClient
    );

    const expectedQuery = {
      from: input.offset,
      size: input.limit,
      track_total_hits: true,
      sort: [
        {
          "study.start_date": "desc"
        }
      ],
      query: {
        bool: {
          filter: [
            {
              bool: {
                should: [
                  {
                    bool: {
                      must_not: [
                        {
                          exists: {
                            field: "project_id"
                          }
                        }
                      ]
                    }
                  }
                ]
              }
            }
          ],
          must: expectedKeywordQuery(queryKeyword, querySynonyms)
        }
      },
      aggs: expect.anything()
    };

    expect(query).toEqual(expectedQuery);
  });

  it("builds an appropriate query with search query and OR operator", async () => {
    const queryKeyword = `${faker.random.words()} OR ${faker.random.words()}`;
    const input = {
      filters: [
        {
          name: "SearchQuery",
          value: queryKeyword
        }
      ],
      limit: 10,
      offset: 0,
      sort: {
        sortBy: "StartDate",
        direction: "Desc"
      }
      //idsOnly?: boolean;
    };
    const queryUnderstandingServiceClient = createMockInstance(
      QueryUnderstandingServiceClient
    );

    const queryUnderstandingServiceResponse = createMockInstance(
      QueryUnderstandingServiceResponse
    );
    const queryParserService = createMockInstance(QueryParserService);
    const textValue1 = faker.random.word();
    const textValue2 = faker.random.word();
    const parsedTree: ITree = [
      "Or",
      [
        ["Including", ["Text", textValue1]],
        ["Including", ["Text", textValue2]]
      ]
    ];

    queryParserService.parseQuery.mockReturnValue(parsedTree);
    const parsedQueryTreeToElasticsearchQueriesService = createMockInstance(
      ParsedQueryTreeToElasticsearchQueriesService
    );
    const mockParsedQuery: QueryDslQueryContainer = {
      bool: {
        must: {
          bool: {
            should: [
              {
                simple_query_string: {
                  query: `${textValue1} | ${textValue2}`,
                  fields: fieldsToSearchRootTrials
                }
              }
            ]
          }
        }
      }
    };
    parsedQueryTreeToElasticsearchQueriesService.parse.mockReturnValue(
      mockParsedQuery
    );

    queryUnderstandingServiceClient.analyze.mockResolvedValue(
      queryUnderstandingServiceResponse
    );

    const query = await createQueryWithCTMS(
      input,
      undefined,
      undefined,
      queryParserService,
      parsedQueryTreeToElasticsearchQueriesService,
      queryUnderstandingServiceClient
    );

    const expectedQuery = {
      from: input.offset,
      size: input.limit,
      track_total_hits: true,
      sort: [
        {
          "study.start_date": "desc"
        }
      ],
      query: {
        bool: {
          filter: [
            {
              bool: {
                should: [
                  {
                    bool: {
                      must_not: [
                        {
                          exists: {
                            field: "project_id"
                          }
                        }
                      ]
                    }
                  }
                ]
              }
            }
          ],
          must: expect.arrayContaining([
            {
              bool: {
                should: expect.arrayContaining([mockParsedQuery])
              }
            }
          ])
        }
      },
      aggs: expect.anything()
    };
    expect(queryUnderstandingServiceClient.analyze).not.toHaveBeenCalled();
    expect(query).toEqual(expectedQuery);
  });

  it("builds a query with sponsor type filter", async () => {
    const filterValue = faker.random.word();
    const input = {
      filters: [
        {
          name: "SponsorType",
          value: `["\\"${filterValue}\\""]`
        }
      ],
      limit: 25,
      offset: 0,
      sort: {
        sortBy: "StartDate",
        direction: "Desc"
      }
    };

    const query = await createQueryWithCTMS(input, trialsAggregationsV3);

    const expectedQuery = {
      from: input.offset,
      size: input.limit,
      track_total_hits: true,
      sort: [
        {
          "study.start_date": "desc"
        }
      ],
      query: {
        bool: {
          must: [
            {
              bool: {
                should: [
                  {
                    query_string: {
                      query: `"${filterValue}"`,
                      fields: ["study.sponsor.sponsor_type"]
                    }
                  }
                ]
              }
            }
          ],
          filter: [
            {
              bool: {
                should: [
                  {
                    bool: {
                      must_not: [
                        {
                          exists: {
                            field: "project_id"
                          }
                        }
                      ]
                    }
                  }
                ]
              }
            }
          ]
        }
      },
      aggs: expect.anything()
    };

    expect(query).toEqual(expect.objectContaining(expectedQuery));
  });
});
