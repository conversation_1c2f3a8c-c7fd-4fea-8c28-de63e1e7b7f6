import {
  ClinicalTrial,
  ClinicalTrialDocument,
  ClinicalTrialDocumentV2,
  ClinicalTrialDocumentV3,
  ClinicalTrialDocumentV4,
  ClinicalTrialDocumentWithCTMS,
  GroupedByItem,
  GroupedByPhaseAndStatus,
  TrialPersonRole,
  TrialsAggregations,
  GroupedByCountryTrials,
  GroupedByTopCountryAvgEnrollmentRate
} from "@h1nyc/search-sdk";

type ClinicalTrialValue = ClinicalTrial[keyof Partial<ClinicalTrial>];

export type ClinicalTrialMapperType<T> = {
  [key in keyof Partial<ClinicalTrial>]: (doc: T) => ClinicalTrialValue;
};

type ClinicalTrialAggregationValue =
  TrialsAggregations[keyof Partial<TrialsAggregations>];

export type ClinicalTrialAggregationsMapperType<T> = {
  [key in keyof Partial<TrialsAggregations>]: (
    agg: T,
    totalTrialCount?: number
  ) => ClinicalTrialAggregationValue;
};

export interface HitSource<T> {
  _source: T;
}

export interface HitsV1 {
  total: { value: number };
  max_score: number;
  hits: HitSource<ClinicalTrialDocument>[];
}

export interface Hits<TrialDocumentType> {
  total: { value: number };
  max_score: number;
  hits: HitSource<TrialDocumentType>[];
}

export type HitsV2 = Hits<ClinicalTrialDocumentV2>;
export type HitsV3 = Hits<ClinicalTrialDocumentV3>;
export type HitsV4 = Hits<ClinicalTrialDocumentV4>;
export type HitsWithCTMS = Hits<ClinicalTrialDocumentWithCTMS>;

export interface RootAggregation {
  trial_duration: {
    avg_duration: {
      value: number;
    };
    median_duration: {
      values: {
        "50.0": number;
      };
    };
  };
  enrollment_duration: {
    doc_count: number;
    avg_duration: {
      value: number;
    };
    median_duration: {
      values: {
        "50.0": number;
      };
    };
  };
  facility_count: {
    doc_count: number;
    count: {
      value: number;
    };
  };
  total_enrollment: {
    doc_count: number;
    enrollment_sum: {
      value: number;
    };
    median: {
      values: {
        "50.0": number;
      };
    };
  };
  group_by_person_role: {
    buckets: Array<GroupedByItem<TrialPersonRole>>;
  };
  group_by_country: {
    buckets: Array<GroupedByItem<string>>;
  };
  group_by_phase_and_status: {
    buckets: GroupedByPhaseAndStatus;
  };
  median_facility_count: {
    doc_count: number;
    median_facility_count: {
      value: number;
    };
  };
  enrollment_graphs: {
    group_by_enrollment_duration: {
      buckets: Array<GroupedByItem<string>>;
    };
    group_by_patients_per_site_per_month: {
      buckets: Array<GroupedByItem<string>>;
    };
  };
  patients_per_site_per_month: {
    doc_count: number;
    avg_patients_per_site_per_month: {
      value: number;
    };
    median_patients_per_site_per_month: {
      values: {
        "50.0": number;
      };
    };
  };
  enrollment_duration_v2: {
    doc_count: number;
    avg_duration: {
      value: number;
    };
    median_duration: {
      values: {
        "50.0": number;
      };
    };
  };
  group_by_top_countries: {
    group_by_sites: {
      order_by_sites: {
        buckets: Array<GroupedByItem<string>>;
      };
    };
    group_by_trials: {
      order_by_trials: {
        buckets: GroupedByCountryTrials;
      };
    };
    group_by_avg_enrollment_rate: {
      filtered_trials: {
        order_by_avg_enrollment_rate: {
          buckets: GroupedByTopCountryAvgEnrollmentRate;
        };
      };
    };
  };
}

export interface RootAggregationV3
  extends Omit<
    RootAggregation,
    "group_by_person_role" | "facility_count" | "group_by_country"
  > {
  group_by_person_role: {
    group_by_role: {
      buckets: Array<GroupedByItem<TrialPersonRole>>;
    };
  };
  facility_count: {
    doc_count: number;
    filtered_facility_count: {
      doc_count: number;
    };
  };
  group_by_country: {
    country_groups: {
      buckets: Array<GroupedByItem<string>>;
    };
  };
  filtered_facility_count: {
    doc_count: number;
    median: {
      values: {
        "50.0": number;
      };
    };
  };
}

export interface RootObject<TrialDocumentType> {
  took: number;
  timed_out: boolean;
  hits: Hits<TrialDocumentType>;
  aggregations?: RootAggregation;
}

export interface RootObjectV1 {
  took: number;
  timed_out: boolean;
  hits: Hits<ClinicalTrialDocument>;
}
export type RootObjectV2 = RootObject<ClinicalTrialDocumentV2>;
export interface RootObjectV3<TrialDocumentType>
  extends Omit<RootObject<TrialDocumentType>, "aggregations"> {
  aggregations?: RootAggregationV3;
}
