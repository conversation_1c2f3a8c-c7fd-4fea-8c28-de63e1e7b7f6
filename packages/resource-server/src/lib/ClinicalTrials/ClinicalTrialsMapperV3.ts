import {
  ClinicalTrialAggregationsMapperType,
  ClinicalTrialMapperType,
  RootAggregationV3,
  RootObjectV3
} from "./types";
import {
  ClinicalTrialDocumentV3,
  ClinicalTrialInput,
  ClinicalTrialsSearchResponse,
  TrialPersonRole
} from "@h1nyc/search-sdk";
import * as _ from "lodash";
import {
  aggregationsV2Mapper,
  documentV2Mapper
} from "./ClinicalTrialsMapperV2";
import { mapAggregations, mapDocument } from "./ClinicalTrialsBaseMapper";

/**
 * Milliseconds in Seconds *
 * Seconds in Minutes *
 * Minutes in Hours *
 * Hours in Days *
 * Days in Months
 */
const MILLISECONDS_PER_MONTH = 1000 * 60 * 60 * 24 * 30.42;

function notEmpty<T>(value: T | null | undefined): value is T {
  return !(value === null || value === undefined);
}

export const getAvgPatientCount = (aggsDoc: RootAggregationV3) => {
  const totalEnrollment = aggsDoc.total_enrollment.enrollment_sum.value;
  const enrollmentDocCount = aggsDoc.total_enrollment.doc_count;
  return enrollmentDocCount === 0 ? null : totalEnrollment / enrollmentDocCount;
};

export const getAvgTrialDurationInMonths = (aggsDoc: RootAggregationV3) => {
  if (
    Number.isNaN(aggsDoc.trial_duration.avg_duration.value) ||
    aggsDoc.trial_duration.avg_duration.value === 0
  ) {
    return 0;
  }
  const avgTrialDurationInMonths =
    aggsDoc.trial_duration.avg_duration.value / MILLISECONDS_PER_MONTH;
  return avgTrialDurationInMonths;
};

export const mapResultsV3 = (
  root: RootObjectV3<ClinicalTrialDocumentV3>,
  input: ClinicalTrialInput
): ClinicalTrialsSearchResponse => {
  const hits = root.hits;

  return {
    from: input.offset,
    pageSize: input.limit,
    total: hits.total.value,
    results: input.idsOnly
      ? []
      : hits.hits.map((hit) => mapDocument(hit._source, documentV3Mapper)),
    resultIds: input.idsOnly
      ? hits.hits
          .map((hit) => hit._source.identifiers)
          .filter(notEmpty)
          .map((identifiers) => String(identifiers[0].external_uuid))
      : undefined,
    aggregations: root.aggregations
      ? mapAggregations<RootAggregationV3>(
          root.aggregations,
          aggregationsV3Mapper,
          root.hits.total.value
        )
      : undefined
  };
};

export const documentV3Mapper: ClinicalTrialMapperType<ClinicalTrialDocumentV3> =
  {
    ...(documentV2Mapper as Partial<
      ClinicalTrialMapperType<ClinicalTrialDocumentV3>
    >),
    nctId: (doc) => doc.identifiers && doc.identifiers[0].external_uuid
  };

export const aggregationsV3Mapper: ClinicalTrialAggregationsMapperType<RootAggregationV3> =
  {
    ...(aggregationsV2Mapper as Partial<
      ClinicalTrialAggregationsMapperType<RootAggregationV3>
    >),
    avgInvestigatorCount: (
      aggsDoc: RootAggregationV3,
      totalTrialCount?: number
    ) => {
      const investigatorCount = _.sumBy(
        aggsDoc.group_by_person_role.group_by_role.buckets,
        (rolBucket) =>
          [
            TrialPersonRole.PRINCIPAL_INVESTIGATOR,
            TrialPersonRole.SUB_INVESTIGATOR
          ].includes(rolBucket.key)
            ? rolBucket.doc_count
            : 0
      );
      return totalTrialCount === undefined || totalTrialCount === 0
        ? null
        : investigatorCount / totalTrialCount;
    },
    groupByPersonRole: (aggsDoc) =>
      aggsDoc.group_by_person_role.group_by_role.buckets,
    groupByCountry: (aggsDoc) =>
      aggsDoc.group_by_country.country_groups.buckets,
    facilityCount: (aggsDoc) => aggsDoc.facility_count.doc_count,
    avgPatientsPerSite: getAvgPatientPerSite,
    avgPatientsPerSitePerMonth: (aggsDoc) => {
      const avgTrialDurationInMonths = getAvgTrialDurationInMonths(aggsDoc);
      const avgPatientsPerSite = getAvgPatientPerSite(aggsDoc);
      if (avgTrialDurationInMonths === 0) {
        return 0;
      }
      return avgPatientsPerSite / avgTrialDurationInMonths;
    },
    avgFacilityCount: (aggsDoc, totalTrialCount) => {
      const facilityCount = aggsDoc.facility_count.doc_count;
      return !totalTrialCount ? null : facilityCount / totalTrialCount;
    },
    medianFacilityCount: (aggsDoc) => {
      return aggsDoc.filtered_facility_count?.median?.values?.["50.0"] ?? null;
    },
    groupByEnrollmentDuration: (aggDoc) =>
      aggDoc.enrollment_graphs?.group_by_enrollment_duration?.buckets,
    groupByPatientsPerSitePerEnrollmentDuration: (aggDoc) =>
      aggDoc.enrollment_graphs?.group_by_patients_per_site_per_month?.buckets,
    avgPatientsPerSitePerMonthV2: (aggsDoc) =>
      aggsDoc.patients_per_site_per_month?.avg_patients_per_site_per_month
        .value,
    medianPatientsPerSitePerMonthV2: (aggsDoc) =>
      aggsDoc.patients_per_site_per_month?.median_patients_per_site_per_month
        .values["50.0"],
    medianTrialsDuration: (aggsDoc) =>
      aggsDoc.trial_duration.median_duration?.values?.["50.0"], // median duration in ms
    medianPatientCount: (aggsDoc) =>
      aggsDoc.total_enrollment.median?.values?.["50.0"],
    medianEnrollmentDurationV2: (aggsDoc) =>
      aggsDoc.enrollment_duration_v2?.median_duration?.values["50.0"],
    groupByTopCountrySites: (aggDoc) =>
      aggDoc.group_by_top_countries?.group_by_sites?.order_by_sites?.buckets,
    groupByTopCountryTrials: (aggDoc) => {
      return (
        aggDoc.group_by_top_countries?.group_by_trials?.order_by_trials
          ?.buckets || []
      ).map((bucket) => {
        return {
          key: bucket.key,
          doc_count: bucket.trials?.doc_count || 0
        };
      });
    },
    groupByTopCountryAvgEnrollmentRate: (aggDoc) => {
      return (
        aggDoc.group_by_top_countries?.group_by_avg_enrollment_rate
          ?.filtered_trials?.order_by_avg_enrollment_rate?.buckets || []
      )
        .slice(0, 20) // return only top 20
        .map((bucket) => {
          return {
            key: bucket.key,
            doc_count: bucket.avg_patients_per_site_per_month?.value || 0
          };
        });
    }
  };

function getAvgPatientPerSite(aggsDoc: RootAggregationV3) {
  const totalPatientCount = aggsDoc.total_enrollment.enrollment_sum.value;
  const totalFacilityCount = aggsDoc.facility_count.doc_count;
  if (
    Number.isNaN(totalPatientCount) ||
    Number.isNaN(totalFacilityCount) ||
    totalPatientCount === 0 ||
    totalFacilityCount === 0
  ) {
    return 0; // to avoid NAN display at frontend
  }
  const result = totalPatientCount / totalFacilityCount;
  return result;
}
