import { ClinicalTrialDocumentV3, TrialPersonRole } from "@h1nyc/search-sdk";
import fixture1 from "../../services/__fixtures__/GoldToProduct/TrialsV3_replacement_survivors.json";
import { mapResultsV3, aggregationsV3Mapper } from "./ClinicalTrialsMapperV3";
import { RootAggregationV3, RootObjectV3 } from "./types";
import { faker } from "@faker-js/faker";

describe("ClinicalTrialDocumentV3 Test cases", () => {
  describe("avgPatientsPerSitePerMonth calculations", () => {
    const input = {
      filters: [
        {
          name: "StudyPhase",
          value: '["\\"Phase 3\\""]'
        }
      ],
      limit: 25,
      offset: 0,
      sort: {
        sortBy: "StartDate",
        direction: "Desc"
      }
    };
    const initialResponse: RootObjectV3<ClinicalTrialDocumentV3> = fixture1;
    const docCount = 500;
    const avgTrialsDuration = **********0; // 10 Months
    const medianTrialsDuration = 13141440000; // 5 Months
    const facilityCount = 1000;
    const enrollmentDocCount = 2;
    const totalEnrollment = 200;
    const medianEnrollment = 100;
    const medianFacilityCount = 1;
    const avgPatientsPerSitePerMonths = 100;
    const medianPatientsPerSitePerMonth = 10;

    const personRoleBuckets = [
      {
        key: TrialPersonRole.PRINCIPAL_INVESTIGATOR,
        doc_count: 2000
      },
      {
        key: TrialPersonRole.CENTRAL_CONTACT,
        doc_count: 0
      },
      {
        key: TrialPersonRole.SUB_INVESTIGATOR,
        doc_count: 3000
      },
      {
        key: TrialPersonRole.STUDY_CHAIR,
        doc_count: 300
      }
    ];

    const groupByCountry = [...Array(5)].map(() => ({
      key: faker.datatype.string(),
      doc_count: faker.datatype.number()
    }));
    const groupByEnrollmentDuration = [...Array(5)].map(() => ({
      key: faker.datatype.string(),
      doc_count: faker.datatype.number()
    }));
    const groupByPatientsPerSitePerEnrollmentDuration = [...Array(5)].map(
      () => ({
        key: faker.datatype.string(),
        doc_count: faker.datatype.number()
      })
    );
    const statusNames = [...Array(5)].map(() => faker.datatype.string());
    const phaseBuckets = [...Array(5)].map(() => ({
      key: faker.datatype.string(),
      doc_count: faker.datatype.number(),
      group_by_status: {
        buckets: statusNames.map((status) => ({
          key: status,
          doc_count: faker.datatype.number()
        }))
      }
    }));
    const groupByTopCountrySites = [...Array(5)].map(() => ({
      key: faker.datatype.string(),
      doc_count: faker.datatype.number()
    }));
    const groupByTopCountryTrials = [...Array(5)].map(() => ({
      key: faker.datatype.string(),
      doc_count: faker.datatype.number(),
      trials: {
        doc_count: faker.datatype.number()
      }
    }));
    const groupByTopCountryAvgEnrollmentRate = [...Array(5)].map(() => ({
      key: faker.datatype.string(),
      doc_count: faker.datatype.number(),
      trials: {
        doc_count: faker.datatype.number(),
        sum_patients_per_site_per_month: {
          value: faker.datatype.number()
        }
      },
      avg_patients_per_site_per_month: {
        value: faker.datatype.number()
      }
    }));
    const aggregations: RootAggregationV3 = {
      median_facility_count: {
        doc_count: 10000,
        median_facility_count: {
          value: medianFacilityCount
        }
      },
      filtered_facility_count: {
        doc_count: 10000,
        median: {
          values: {
            "50.0": medianFacilityCount
          }
        }
      },
      enrollment_duration: {
        doc_count: 100,
        avg_duration: {
          value: avgTrialsDuration
        },
        median_duration: {
          values: {
            "50.0": medianTrialsDuration
          }
        }
      },
      trial_duration: {
        avg_duration: {
          value: avgTrialsDuration
        },
        median_duration: {
          values: {
            "50.0": medianTrialsDuration
          }
        }
      },
      facility_count: {
        doc_count: facilityCount,
        filtered_facility_count: {
          doc_count: docCount
        }
      },
      group_by_person_role: {
        group_by_role: {
          buckets: personRoleBuckets
        }
      },
      group_by_country: {
        country_groups: {
          buckets: groupByCountry
        }
      },
      group_by_phase_and_status: {
        buckets: phaseBuckets
      },
      total_enrollment: {
        doc_count: enrollmentDocCount,
        enrollment_sum: {
          value: totalEnrollment
        },
        median: {
          values: {
            "50.0": medianEnrollment
          }
        }
      },
      enrollment_graphs: {
        group_by_enrollment_duration: {
          buckets: groupByEnrollmentDuration
        },
        group_by_patients_per_site_per_month: {
          buckets: groupByPatientsPerSitePerEnrollmentDuration
        }
      },
      enrollment_duration_v2: {
        doc_count: 100,
        avg_duration: {
          value: avgTrialsDuration
        },
        median_duration: {
          values: {
            "50.0": medianTrialsDuration
          }
        }
      },
      patients_per_site_per_month: {
        doc_count: 100,
        avg_patients_per_site_per_month: {
          value: avgPatientsPerSitePerMonths
        },
        median_patients_per_site_per_month: {
          values: {
            "50.0": medianPatientsPerSitePerMonth
          }
        }
      },
      group_by_top_countries: {
        group_by_sites: {
          order_by_sites: {
            buckets: groupByTopCountrySites
          }
        },
        group_by_trials: {
          order_by_trials: {
            buckets: groupByTopCountryTrials
          }
        },
        group_by_avg_enrollment_rate: {
          filtered_trials: {
            order_by_avg_enrollment_rate: {
              buckets: groupByTopCountryAvgEnrollmentRate
            }
          }
        }
      }
    };

    it("should return correct aggregation results from search initialResponse", () => {
      initialResponse.aggregations = aggregations;
      const result = mapResultsV3(initialResponse, input);
      expect(result).toEqual(
        expect.objectContaining({
          aggregations: expect.objectContaining({
            avgPatientsPerSitePerMonth: 0.02
          })
        })
      );
    });
    it("should return correct aggregation results from search initialResponse with duration of 1 month", () => {
      const response = {
        ...initialResponse,
        aggregations: {
          ...aggregations,
          trial_duration: {
            ...aggregations.trial_duration,
            avg_duration: {
              value: ********** // 1 month
            }
          }
        }
      };
      const result = mapResultsV3(response, input);
      expect(result).toEqual(
        expect.objectContaining({
          aggregations: expect.objectContaining({
            avgPatientsPerSitePerMonth: 0.2
          })
        })
      );
    });
    it("should return correct aggregation results from search initialResponse with facility count of 1", () => {
      const response = {
        ...initialResponse,
        aggregations: {
          ...aggregations,
          facility_count: {
            doc_count: 1,
            filtered_facility_count: {
              doc_count: 1
            }
          }
        }
      };
      const result = mapResultsV3(response, input);
      expect(result).toEqual(
        expect.objectContaining({
          aggregations: expect.objectContaining({
            avgPatientsPerSitePerMonth: 20
          })
        })
      );
    });
    it("should return avgPatientsPerSitePerMonth 0  from search initialResponse with facility count of 0", () => {
      const response = {
        ...initialResponse,
        aggregations: {
          ...aggregations,
          facility_count: {
            doc_count: 0,
            filtered_facility_count: {
              doc_count: 1
            }
          }
        }
      };
      const result = mapResultsV3(response, input);
      expect(result).toEqual(
        expect.objectContaining({
          aggregations: expect.objectContaining({
            avgPatientsPerSitePerMonth: 0
          })
        })
      );
    });
    it("should return correct aggregation results from search when average patient count is 1", () => {
      const response = {
        ...initialResponse,
        aggregations: {
          ...aggregations,
          total_enrollment: {
            ...aggregations.total_enrollment,
            doc_count: 1,
            enrollment_sum: {
              value: 1
            }
          }
        }
      };
      const result = mapResultsV3(response, input);
      expect(result).toEqual(
        expect.objectContaining({
          aggregations: expect.objectContaining({
            avgPatientsPerSitePerMonth: 0.0001
          })
        })
      );
    });
    it("should return  avgPatientsPerSitePerMonth 0 from search when average patient count is 0", () => {
      const response = {
        ...initialResponse,
        aggregations: {
          ...aggregations,
          total_enrollment: {
            ...aggregations.total_enrollment,
            doc_count: 0,
            enrollment_sum: {
              value: 0
            }
          }
        }
      };
      const result = mapResultsV3(response, input);
      expect(result).toEqual(
        expect.objectContaining({
          aggregations: expect.objectContaining({
            avgPatientsPerSitePerMonth: 0
          })
        })
      );
    });
  });

  describe("avgPatientsPerSite calculation", () => {
    const personRoleBuckets = [
      {
        key: TrialPersonRole.PRINCIPAL_INVESTIGATOR,
        doc_count: 2000
      },
      {
        key: TrialPersonRole.CENTRAL_CONTACT,
        doc_count: 0
      },
      {
        key: TrialPersonRole.SUB_INVESTIGATOR,
        doc_count: 3000
      },
      {
        key: TrialPersonRole.STUDY_CHAIR,
        doc_count: 300
      }
    ];

    const groupByCountry = [...Array(5)].map(() => ({
      key: faker.datatype.string(),
      doc_count: faker.datatype.number()
    }));
    const groupByEnrollmentDuration = [...Array(5)].map(() => ({
      key: faker.datatype.string(),
      doc_count: faker.datatype.number()
    }));
    const groupByPatientsPerSitePerEnrollmentDuration = [...Array(5)].map(
      () => ({
        key: faker.datatype.string(),
        doc_count: faker.datatype.number()
      })
    );
    const statusNames = [...Array(5)].map(() => faker.datatype.string());
    const phaseBuckets = [...Array(5)].map(() => ({
      key: faker.datatype.string(),
      doc_count: faker.datatype.number(),
      group_by_status: {
        buckets: statusNames.map((status) => ({
          key: status,
          doc_count: faker.datatype.number()
        }))
      }
    }));
    const groupByTopCountrySites = [...Array(5)].map(() => ({
      key: faker.datatype.string(),
      doc_count: faker.datatype.number()
    }));
    const groupByTopCountryTrials = [...Array(5)].map(() => ({
      key: faker.datatype.string(),
      doc_count: faker.datatype.number(),
      trials: {
        doc_count: faker.datatype.number()
      }
    }));
    const groupByTopCountryAvgEnrollmentRate = [...Array(5)].map(() => ({
      key: faker.datatype.string(),
      doc_count: faker.datatype.number(),
      trials: {
        doc_count: faker.datatype.number(),
        sum_patients_per_site_per_month: {
          value: faker.datatype.number()
        }
      },
      avg_patients_per_site_per_month: {
        value: faker.datatype.number()
      }
    }));

    it("should return null if totalPatientCount is null from ES", () => {
      const aggregations: RootAggregationV3 = {
        median_facility_count: {
          doc_count: 0,
          median_facility_count: {
            value: 0
          }
        },
        filtered_facility_count: {
          doc_count: 0,
          median: {
            values: {
              "50.0": 0
            }
          }
        },
        enrollment_duration: {
          doc_count: 0,
          avg_duration: {
            value: 0
          },
          median_duration: {
            values: {
              "50.0": 0
            }
          }
        },
        trial_duration: {
          avg_duration: {
            value: 0
          },
          median_duration: {
            values: {
              "50.0": 0
            }
          }
        },
        facility_count: {
          doc_count: 100,
          filtered_facility_count: {
            doc_count: 50
          }
        },
        group_by_person_role: {
          group_by_role: {
            buckets: personRoleBuckets
          }
        },
        group_by_country: {
          country_groups: {
            buckets: groupByCountry
          }
        },
        group_by_phase_and_status: {
          buckets: phaseBuckets
        },
        total_enrollment: {
          doc_count: null as unknown as number,
          enrollment_sum: {
            value: null as unknown as number
          },
          median: {
            values: {
              "50.0": null as unknown as number
            }
          }
        },
        enrollment_graphs: {
          group_by_enrollment_duration: {
            buckets: groupByEnrollmentDuration
          },
          group_by_patients_per_site_per_month: {
            buckets: groupByPatientsPerSitePerEnrollmentDuration
          }
        },
        enrollment_duration_v2: {
          doc_count: 0,
          avg_duration: {
            value: 0
          },
          median_duration: {
            values: {
              "50.0": 0
            }
          }
        },
        patients_per_site_per_month: {
          doc_count: 0,
          avg_patients_per_site_per_month: {
            value: 0
          },
          median_patients_per_site_per_month: {
            values: {
              "50.0": 0
            }
          }
        },
        group_by_top_countries: {
          group_by_sites: {
            order_by_sites: {
              buckets: groupByTopCountrySites
            }
          },
          group_by_trials: {
            order_by_trials: {
              buckets: groupByTopCountryTrials
            }
          },
          group_by_avg_enrollment_rate: {
            filtered_trials: {
              order_by_avg_enrollment_rate: {
                buckets: groupByTopCountryAvgEnrollmentRate
              }
            }
          }
        }
      };
      const count = aggregationsV3Mapper!.avgPatientsPerSite!(aggregations);
      expect(count).toEqual(0);
    });
    it("should return null if facility_count.doc_count is zero from ES", () => {
      const aggregations: RootAggregationV3 = {
        median_facility_count: {
          doc_count: 0,
          median_facility_count: {
            value: 0
          }
        },
        filtered_facility_count: {
          doc_count: 0,
          median: {
            values: {
              "50.0": 0
            }
          }
        },
        enrollment_duration: {
          doc_count: 0,
          avg_duration: {
            value: 0
          },
          median_duration: {
            values: {
              "50.0": 0
            }
          }
        },
        trial_duration: {
          avg_duration: {
            value: 0
          },
          median_duration: {
            values: {
              "50.0": 0
            }
          }
        },
        facility_count: {
          doc_count: 0,
          filtered_facility_count: {
            doc_count: 0
          }
        },
        group_by_person_role: {
          group_by_role: {
            buckets: personRoleBuckets
          }
        },
        group_by_country: {
          country_groups: {
            buckets: groupByCountry
          }
        },
        group_by_phase_and_status: {
          buckets: phaseBuckets
        },
        total_enrollment: {
          doc_count: 100,
          enrollment_sum: {
            value: 100
          },
          median: {
            values: {
              "50.0": 0
            }
          }
        },
        enrollment_graphs: {
          group_by_enrollment_duration: {
            buckets: groupByEnrollmentDuration
          },
          group_by_patients_per_site_per_month: {
            buckets: groupByPatientsPerSitePerEnrollmentDuration
          }
        },
        enrollment_duration_v2: {
          doc_count: 0,
          avg_duration: {
            value: 0
          },
          median_duration: {
            values: {
              "50.0": 0
            }
          }
        },
        patients_per_site_per_month: {
          doc_count: 0,
          avg_patients_per_site_per_month: {
            value: 0
          },
          median_patients_per_site_per_month: {
            values: {
              "50.0": 0
            }
          }
        },
        group_by_top_countries: {
          group_by_sites: {
            order_by_sites: {
              buckets: groupByTopCountrySites
            }
          },
          group_by_trials: {
            order_by_trials: {
              buckets: groupByTopCountryTrials
            }
          },
          group_by_avg_enrollment_rate: {
            filtered_trials: {
              order_by_avg_enrollment_rate: {
                buckets: groupByTopCountryAvgEnrollmentRate
              }
            }
          }
        }
      };
      const count = aggregationsV3Mapper!.avgPatientsPerSite!(aggregations);
      expect(count).toEqual(0);
    });
    it("should return 2 if  total_enrollment is 200 and facility_count.doc_count is 100 from ES", () => {
      const totalPatientCount = 200;
      const totalFacilityCount = 100;
      const expected = totalPatientCount / totalFacilityCount;
      const aggregations: RootAggregationV3 = {
        median_facility_count: {
          doc_count: 0,
          median_facility_count: {
            value: 0
          }
        },
        filtered_facility_count: {
          doc_count: 0,
          median: {
            values: {
              "50.0": 0
            }
          }
        },
        enrollment_duration: {
          doc_count: 0,
          avg_duration: {
            value: 0
          },
          median_duration: {
            values: {
              "50.0": 0
            }
          }
        },
        trial_duration: {
          avg_duration: {
            value: 0
          },
          median_duration: {
            values: {
              "50.0": 0
            }
          }
        },
        facility_count: {
          doc_count: totalFacilityCount,
          filtered_facility_count: {
            doc_count: totalFacilityCount
          }
        },
        group_by_person_role: {
          group_by_role: {
            buckets: personRoleBuckets
          }
        },
        group_by_country: {
          country_groups: {
            buckets: groupByCountry
          }
        },
        group_by_phase_and_status: {
          buckets: phaseBuckets
        },
        total_enrollment: {
          doc_count: totalPatientCount,
          enrollment_sum: {
            value: totalPatientCount
          },
          median: {
            values: {
              "50.0": 0
            }
          }
        },
        enrollment_graphs: {
          group_by_enrollment_duration: {
            buckets: groupByEnrollmentDuration
          },
          group_by_patients_per_site_per_month: {
            buckets: groupByPatientsPerSitePerEnrollmentDuration
          }
        },
        enrollment_duration_v2: {
          doc_count: 0,
          avg_duration: {
            value: 0
          },
          median_duration: {
            values: {
              "50.0": 0
            }
          }
        },
        patients_per_site_per_month: {
          doc_count: 0,
          avg_patients_per_site_per_month: {
            value: 0
          },
          median_patients_per_site_per_month: {
            values: {
              "50.0": 0
            }
          }
        },
        group_by_top_countries: {
          group_by_sites: {
            order_by_sites: {
              buckets: groupByTopCountrySites
            }
          },
          group_by_trials: {
            order_by_trials: {
              buckets: groupByTopCountryTrials
            }
          },
          group_by_avg_enrollment_rate: {
            filtered_trials: {
              order_by_avg_enrollment_rate: {
                buckets: groupByTopCountryAvgEnrollmentRate
              }
            }
          }
        }
      };
      const count = aggregationsV3Mapper!.avgPatientsPerSite!(aggregations);
      expect(count).toEqual(expected);
    });
  });
});
