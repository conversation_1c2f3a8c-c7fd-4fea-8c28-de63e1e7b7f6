import { ClinicalTrialSource, TrialHistogramMetric } from "@h1nyc/search-sdk";
import _ from "lodash";
import { RootAggregation, RootAggregationV3 } from "./types";
import { QueryDslQueryContainer } from "@elastic/elasticsearch/lib/api/types";

export const SearchableTextFields = [
  "studyRegistrySource",
  "studyBriefTitle",
  "studyNctId",
  "studyOfficialTitle",
  "studyPhase",
  "studyOverallStatus",
  "designInterventionModel",
  "designObservationalModel",
  "designPrimaryPurpose",
  "designTimePerspective",
  "designMasking",
  "designMaskingDescription",
  "designInterventionModelDescription",
  "designOutcomeType",
  "designOutcomeMeasure",
  "designOutcomeTimeFrame",
  "designOutcomeDescription",
  "designGroupType",
  "designGroupTitle",
  "designGroupDescription",
  "conditionName",
  "keywordName",
  "interventionType",
  "interventionName",
  "interventionDescription",
  "interventionOtherName",
  "facilityName",
  "facilityCity",
  "facilityState",
  "facilityZip",
  "facilityCountry",
  "facilityInvestigatorRole",
  "facilityInvestigatorName",
  "sponsorName",
  "sponsorAgencyClass",
  "sponsorLeadOrCollaborator",
  "studyEnrollmentType",
  "eligibilityMinimumAgeRaw",
  "centralContactType",
  "centralContactName",
  "centralContactPhone",
  "centralContactEmail",
  "facilityContactType",
  "facilityContactName",
  "facilityContactPhone",
  "facilityContactEmail",
  "overallOfficialRole",
  "overallOfficialName",
  "overallOfficialAffiliation"
];

export const SearchableTextFieldsV2 = [
  "study.brief_title",
  "study.external_uuid",
  "study.official_title",
  "study.phase",
  "study.overall_status",
  "study.design.intervention_model",
  "study.design.observational_model",
  "study.design.primary_purpose",
  "study.design.time_perspective",
  "study.design.masking",
  "study.design.masking_description",
  "study.design.intervention_model_description",
  "study.designOutcome.outcome_type",
  "study.designOutcome.measure",
  "study.designOutcome.time_frame",
  "study.designOutcome.description",
  "study.designGroup.group_type",
  "study.designGroup.title",
  "study.designGroup.description",
  "study.conditions",
  "study.keywords",
  "study.intervention.intervention_type",
  "study.intervention.name",
  "study.intervention.description",
  "study.intervention.intervention_other_names",
  "facility.name",
  "facility.city",
  "facility.state",
  "facility.zip",
  "facility.country",
  "person.name",
  "person.email",
  "person.name",
  "person.roleHistory.email",
  "study.sponsor.agency_class",
  "study.enrollment_type",
  "study.eligibility.minimum_age"
];

export type MappedFilterField = {
  fields: string[];
  isDate?: boolean;
  isMulti?: boolean;
  nestedPath?: string[];
  disabled?: boolean;
  isRange?: boolean;
  // Will be more useful when we switch to ES 7.10 (currently on 7.9), where
  // we have case insensitive wild card searches:
  // https://www.elastic.co/guide/en/elasticsearch/reference/current/query-dsl-wildcard-query.html
  // useWildcard?: boolean;
  useAutocomplete?: boolean;
  isKeywordSort?: boolean;
  isMultiFieldRange?: boolean;
  transformValues?: (values: string[]) => string[];
  addShouldsToMust?: (values: string[], shoulds: any[]) => void;
  scriptFilter?: (values: string[]) => any;
  scriptSort?: (direction: string) => any;
  getQueryComponents?: (values: string[]) => {
    musts?: QueryDslQueryContainer[];
    must_not?: QueryDslQueryContainer[];
    should?: QueryDslQueryContainer[];
  };
  useQueryComponents?: boolean;
};

export type AutocompleteMappedFilterField = {
  fieldsToSearch: string[];
  fieldForAutocomplete: string;
  nestedPath?: string;
};

const QUOTES = /['"]+/g;
const EMPTY_STRING = "";

type KnownClinicalTrialSource = Exclude<ClinicalTrialSource, "Unknown">;

export const trialSourceIdMap: Readonly<
  Record<KnownClinicalTrialSource, number>
> = {
  AACT: 29,
  NCT: 29,
  ChiCTR: 729,
  DRKS: 1335,
  EUDRA: 405,
  UMIN: 10148,
  CTIS: 1048,
  ISRCTN: 10151
};

const studyTypeValueFields: { [key: string]: string } = {
  Interventional: "designInterventionModel",
  Observational: "designObservationalModel"
};

export const filterMapV1: { [key: string]: MappedFilterField } = {
  BriefTitle: {
    fields: ["studyBriefTitle"],
    isMulti: true
  },
  Status: {
    fields: ["studyOverallStatus"],
    isMulti: true
  },
  StartDate: {
    fields: ["studyStartDate"],
    isDate: true
  },
  NctId: {
    fields: ["studyNctId"],
    isMulti: true
  },
  StudyPhase: {
    fields: ["studyPhase"],
    isMulti: true,
    transformValues: (values: string[]) => {
      return values.map((value) => {
        if (value === "Not Applicable") {
          return "N/A";
        }

        return value;
      });
    },
    addShouldsToMust: (values: string[], shoulds: any[]) => {
      // N/A is treated as ["N/A" OR null]
      if (values.indexOf("N/A") > -1) {
        shoulds.push({
          bool: {
            must_not: [
              {
                exists: {
                  field: "studyPhase"
                }
              }
            ]
          }
        });
      }
    }
  },
  Conditions: {
    fields: ["conditionName"],
    isMulti: true,
    isKeywordSort: true
  },
  Interventions: {
    fields: ["interventionName"],
    isKeywordSort: true,
    isMulti: true
  },
  InterventionType: {
    fields: ["interventionType"],
    isMulti: true
  },
  Sponsor: {
    fields: ["sponsorName"],
    isKeywordSort: true,
    isMulti: true
  },
  InterventionOtherNames: {
    fields: ["interventionOtherName"],
    isKeywordSort: true,
    isMulti: true
  },
  EstimatedEndDate: {
    fields: ["studyPrimaryCompletionDate"],
    isDate: true
  },
  PrimaryCompletionDate: {
    fields: ["studyPrimaryCompletionDate"],
    isDate: true
  },
  StudyCompletionDate: {
    fields: ["studyEndDate"],
    isDate: true,
    disabled: true
  },
  StudyType: {
    fields: [],
    isMulti: true,
    scriptFilter: (values: string[]) => {
      // This checks the length of the given field arrays
      const scripts = values.map((value) => {
        try {
          value = JSON.parse(value);
        } catch (err) {
          console.log(`Failed to parse study type value: ${value}`);
        }
        const field = studyTypeValueFields[value];

        if (!field) {
          console.warn(`No field found for value ${value}`);
          return null;
        }

        return `if (doc['${field}'] !== null) { return doc['${field}'].size() > 0 } else { return false }`;
      });

      if (scripts.length) {
        return {
          script: { script: _.compact(scripts).join(" || ") }
        };
      }

      return null;
    },
    scriptSort: (direction: string) => {
      // If direction is ascending, then sort by size of designInterventionModel
      // first. Otherwise, sort by size of designObservationalModel

      const field =
        direction === "Desc"
          ? "designObservationalModel"
          : "designInterventionModel";

      return {
        type: "number",
        script: {
          lang: "painless",
          source: `if (doc['${field}'] !== null) { return doc['${field}'].size() } else { return 0 }`
        },
        order: "desc"
      };
    }
  },
  Enrollment: {
    fields: ["studyEnrollment"],
    useAutocomplete: true
  },
  EnrollmentType: {
    fields: ["studyEnrollmentType"],
    isMulti: true
  },
  EligibilityMinAge: {
    fields: ["eligibilityMinimumAgeRaw"],
    isMulti: true
  },
  Allocation: {
    fields: ["designAllocation"],
    isMulti: true
  },
  InterventionModel: {
    fields: ["designInterventionModel"],
    isMulti: true
  },
  Masking: {
    fields: ["designMasking"],
    useAutocomplete: true
  },
  PrimaryPurpose: {
    fields: ["designPrimaryPurpose"],
    isMulti: true,
    useAutocomplete: true
  },
  Investigators: {
    fields: ["facilityInvestigatorName", "overallOfficialName"],
    isMulti: true,
    isKeywordSort: true
  },
  H1Investigator: {
    fields: [""], // Field?
    disabled: true
  },
  Sites: {
    fields: ["facilityName"],
    isMulti: true,
    useAutocomplete: true
  },
  States: {
    fields: ["facilityState"],
    isMulti: true,
    useAutocomplete: true
  },
  City: {
    fields: ["facilityCity"],
    isMulti: true,
    useAutocomplete: true
  },
  Country: {
    fields: ["facilityCountry"],
    isMulti: true,
    useAutocomplete: true
  },
  ContactNames: {
    fields: ["facilityContactName", "centralContactName"],
    isMulti: true,
    isKeywordSort: true
  },
  SitesCount: {
    fields: ["uniqueCountOfSites"]
  },
  PatientsPerMonth: {
    fields: ["patientsPerMonth"]
  },
  AvgPatientsPerSite: {
    fields: ["avgPatientsPerSite"]
  },
  PatientsPerSitePerMonth: {
    fields: ["patientsPerSitePerMonth"]
  },
  Acronym: {
    fields: ["study.acronym"],
    isKeywordSort: true,
    isMulti: true
  }
};

const phaseMapper: { [key: string]: string[] } = {
  '"Not Applicable"': ['"N/A"', '"NA"', '""', '"Not Specified"'],
  '"Phase 0"': ['"0"', '"PHASE0"', '"Phase 0"', '"0"'],
  '"Early Phase 1"': ['"EARLY_PHASE1"'],
  '"Phase 1"': [
    '"1"',
    '"PHASE1"',
    '"Human Pharmacology (Phase I)- Bioequivalence Study"',
    '"Human Pharmacology (Phase I)- First administration to humans"',
    '"Human Pharmacology (Phase I)-  Other"',
    '"Phase I"',
    '"I"'
  ],
  '"Phase 2"': [
    '"Therapeutic exploratory (Phase II)"',
    '"PHASE2"',
    '"Phase II"',
    '"2"',
    '"II"'
  ],
  '"Phase 3"': [
    '"PHASE3"',
    '"Phase III"',
    '"Therapeutic confirmatory  (Phase III)"',
    '"3"',
    '"III"'
  ],
  '"Phase 4"': [
    '"PHASE4"',
    '"Therapeutic use (Phase IV)"',
    '"Phase IV"',
    '"4"',
    '"IV"'
  ],
  '"Phase 1/Phase 2"': [
    '"PHASE1/PHASE2"',
    '"Phase I and Phase II (Integrated)- Other"',
    '"Phase I and Phase II (Integrated)- First administration to humans"',
    '"Phase I and Phase II (Integrated)- Bioequivalence Study"',
    '"I-II"',
    '"Phase I/II"',
    '"1-2"'
  ],
  '"Phase 2/Phase 3"': [
    '"Phase II and Phase III (Integrated)"',
    '"PHASE2/PHASE3"',
    '"Phase II,III"',
    '"Phase II/III"',
    '"II-III"',
    '"2-3"'
  ],
  '"Phase 3/Phase 4"': [
    '"Phase III and phase IV (Integrated)"',
    '"PHASE3/PHASE4"',
    '"Phase III/IV"',
    '"III-IV"',
    '"Phase III,IV"',
    '"Phase III and IV (Integrated)"'
  ]
};

const studyTypeMapper: { [key: string]: string[] } = {
  '"Interventional"': [
    '"Interventional"',
    '"INTERVENTIONAL"',
    '"Interventional study"'
  ],
  '"Observational"': [
    '"Observational"',
    '"OBSERVATIONAL"',
    '"Observational study"',
    '"Observational [Patient Registry]"'
  ],
  '"Other"': [
    '"Non-interventional"',
    '"Diagnostic test"',
    '"Cause/Relative factors study"',
    '"4"',
    '"5"',
    '"Basic Science"',
    '"Others,meta-analysis etc"',
    '"Expanded Access"',
    '"6"',
    '"Treatment study"',
    '"Epidemilogical research"',
    '"N/A"',
    '"Prognosis study"',
    '"3"',
    '""',
    '"1"',
    '"9"',
    '"Prevention"',
    '"EXPANDED_ACCESS"',
    '"Screening"',
    '"10"',
    '"7"',
    '"Health services reaserch"',
    '"11"',
    '"57"',
    '"2"',
    '"Health Services Research"',
    '"Other"',
    '"8"',
    '"Not Specified"'
  ]
};

const statusMapper: { [key: string]: string[] } = {
  '"Active"': ['"Active, not recruiting"'],
  '"Completed"': ['"/Completed"'],
  '"Not Yet Recruiting"': ['"Not yet recruiting"'],
  '"Terminated or Withdrawn"': ['"Terminated"'],
  '"Unknown"': ['"Unknown Status"', '"UNKNOWN"', '"Not Available"', '""'],
  '"Suspended or Halted"': ['"Suspended"']
};

export const filterMapV2: { [key: string]: MappedFilterField } = {
  BriefTitle: {
    fields: ["study.brief_title"],
    isMulti: true
  },
  Status: {
    fields: ["study.overall_status.keyword"],
    isMulti: true,
    transformValues: (values: string[]) => {
      return values.reduce<string[]>((prev, curr) => {
        prev.push(curr);
        const CTISStatus = statusMapper[curr] ?? [];
        prev.push(...CTISStatus);

        return prev;
      }, []);
    }
  },
  StartDate: {
    fields: ["study.start_date"],
    isDate: true
  },
  NctId: {
    fields: ["study.external_uuid"],
    isMulti: true,
    isKeywordSort: true
  },
  StudyPhase: {
    fields: ["study.phase.keyword"],
    isMulti: true,
    transformValues: (values: string[]) => {
      return values.reduce<string[]>((prev, curr) => {
        prev.push(curr);
        const CTISPhases = phaseMapper[curr] ?? [];
        prev.push(...CTISPhases);

        return prev;
      }, []);
    }
  },
  Conditions: {
    fields: ["study.conditions"],
    isMulti: true,
    isKeywordSort: true
  },
  Interventions: {
    fields: ["study.intervention.name"],
    isKeywordSort: true,
    isMulti: true
  },
  InterventionType: {
    fields: ["study.intervention.intervention_type"],
    isMulti: true,
    isKeywordSort: true
  },
  Sponsor: {
    fields: ["person.name", "facility.name"],
    isKeywordSort: true,
    isMulti: true,
    nestedPath: ["person", "facility"]
  },
  InterventionOtherNames: {
    fields: ["study.intervention"],
    isKeywordSort: true,
    isMulti: true
  },
  EstimatedEndDate: {
    fields: ["study.primary_completion_date"],
    isDate: true
  },
  PrimaryCompletionDate: {
    fields: ["study.primary_completion_date"],
    isDate: true
  },
  StudyCompletionDate: {
    fields: ["study.completion_date"],
    isDate: true,
    disabled: true
  },
  StudyType: {
    fields: ["study.study_type"],
    isMulti: true,
    transformValues: (values: string[]) => {
      return values.reduce<string[]>((prev, curr) => {
        prev.push(curr);
        const studyTypes = studyTypeMapper[curr] ?? [];
        prev.push(...studyTypes);

        return prev;
      }, []);
    }
  },
  Enrollment: {
    fields: ["study.enrollment"],
    useAutocomplete: true
  },
  EnrollmentType: {
    fields: ["study.enrollment_type"],
    isMulti: true,
    isKeywordSort: true
  },
  EligibilityMinAge: {
    fields: ["study.eligibility.minimum_age"],
    isMulti: true
  },
  Allocation: {
    fields: ["study.design.allocation.keyword"],
    isMulti: true
  },
  InterventionModel: {
    fields: ["study.design.intervention_model.keyword"],
    isMulti: true
  },
  Masking: {
    fields: ["study.design.masking.keyword"],
    useAutocomplete: true
  },
  PrimaryPurpose: {
    fields: ["study.design.primary_purpose.keyword"],
    isMulti: true
  },
  Investigators: {
    fields: ["person.name"],
    isMulti: true,
    isKeywordSort: true,
    nestedPath: ["person"]
  },
  H1Investigator: {
    fields: ["person.name"],
    disabled: true,
    isKeywordSort: true,
    nestedPath: ["person"]
  },
  Sites: {
    fields: ["facility.name"],
    isMulti: true,
    isKeywordSort: true,
    nestedPath: ["facility"]
  },
  States: {
    fields: ["facility.state"],
    isMulti: true,
    useAutocomplete: false,
    isKeywordSort: true,
    nestedPath: ["facility"]
  },
  City: {
    fields: ["facility.city"],
    isMulti: true,
    useAutocomplete: false,
    isKeywordSort: true,
    nestedPath: ["facility"]
  },
  Country: {
    fields: ["facility.country"],
    isMulti: true,
    useAutocomplete: false,
    isKeywordSort: true,
    nestedPath: ["facility"]
  },
  ContactNames: {
    fields: ["person.name"],
    isMulti: true,
    isKeywordSort: true,
    nestedPath: ["person"]
  },
  PersonId: {
    fields: ["person.h1_person_id"],
    isMulti: true,
    useAutocomplete: false,
    isKeywordSort: true,
    nestedPath: ["person"]
  },
  SitesCount: {
    fields: []
  },
  PatientsPerMonth: {
    fields: []
  },
  AvgPatientsPerSite: {
    fields: []
  },
  PatientsPerSitePerMonth: {
    fields: []
  },
  InstitutionIds: {
    fields: ["facility.h1_organization_id"],
    isMulti: true,
    nestedPath: ["facility"]
  },
  Source: {
    fields: ["identifiers.collection_source_id"],
    isMulti: true,
    transformValues: (values: string[]): string[] => {
      return values.map((value: string) => {
        // Getting source values as [ '"EUDRA"', '"NCT"' ], so we have to remove the extra quotes from string
        const source = value.replace(
          QUOTES,
          EMPTY_STRING
        ) as KnownClinicalTrialSource;
        return trialSourceIdMap[source]?.toString() ?? source;
      });
    },
    useQueryComponents: true,
    getQueryComponents: (
      values: string[]
    ): {
      should: QueryDslQueryContainer[];
      must_not: QueryDslQueryContainer[];
    } => {
      const should: QueryDslQueryContainer[] = [];
      const must_not: QueryDslQueryContainer[] = [];
      const sourceIds = [
        trialSourceIdMap.EUDRA.toString(),
        trialSourceIdMap.NCT.toString(),
        trialSourceIdMap.DRKS.toString(),
        trialSourceIdMap.UMIN.toString(),
        trialSourceIdMap.ChiCTR.toString(),
        trialSourceIdMap.ISRCTN.toString(),
        trialSourceIdMap.CTIS.toString()
      ];
      values.map((val) => {
        should.push({
          term: {
            "identifiers.collection_source_id": {
              value: val
            }
          }
        });
      });
      must_not.push({
        terms: {
          "identifiers.collection_source_id": _.difference(sourceIds, values)
        }
      });
      return {
        should,
        must_not
      };
    }
  },
  Acronym: {
    fields: ["study.acronym"],
    isKeywordSort: true,
    isMulti: true
  },
  HasCTMSData: {
    fields: []
  }
};

export const filterMapV3: {
  [key: string]: MappedFilterField;
} = {
  ...filterMapV2,
  Biomarkers: {
    fields: ["biomarkerWithSynonyms"],
    isKeywordSort: true,
    isMulti: true
  },
  BiomarkerInclusion: {
    fields: ["biomarkerWithSynonyms_inclusion"],
    isKeywordSort: true,
    isMulti: true
  },
  BiomarkerExclusion: {
    fields: ["biomarkerWithSynonyms_exclusion"],
    isKeywordSort: true,
    isMulti: true
  },
  Indications: {
    fields: ["indications"],
    isKeywordSort: true,
    isMulti: true
  },
  IndicationInclusion: {
    fields: ["indication_inclusions"],
    isKeywordSort: true,
    isMulti: true
  },
  IndicationExclusion: {
    fields: ["indication_exclusions"],
    isKeywordSort: true,
    isMulti: true
  },
  Conditions: {
    fields: ["study.condition.name"],
    isMulti: true,
    isKeywordSort: true
  },
  Sponsor: {
    fields: ["study.sponsor.name"],
    isKeywordSort: true,
    isMulti: true
  },
  SponsorType: {
    fields: ["study.sponsor.sponsor_type"],
    isKeywordSort: true,
    isMulti: true
  },
  Enrollment: {
    fields: ["study.enrollment"],
    isRange: true
  },
  Inclusion: {
    fields: ["study.eligibility.inclusion_criteria_parsed"]
  },
  Exclusion: {
    fields: ["study.eligibility.exclusion_criteria_parsed"]
  },
  EnrollmentRate: {
    fields: ["study.patients_per_site_per_month"],
    isRange: true
  },
  EnrollmentDuration: {
    fields: ["study.enrollment_duration_in_months"],
    isRange: true
  },
  SiteCount: {
    fields: ["number_of_facilities"],
    isRange: true
  },
  EligibilityAge: {
    fields: [
      "study.eligibility.minimum_age_years",
      "study.eligibility.maximum_age_years"
    ],
    isMultiFieldRange: true
  },
  GenericDrugNames: {
    fields: ["generic_drug_names"],
    isKeywordSort: true,
    isMulti: true
  },
  GenericDrugNameInclusion: {
    fields: ["inclusion_generic_drug_names"],
    isKeywordSort: true,
    isMulti: true
  },
  GenericDrugNameExclusion: {
    fields: ["exclusion_generic_drug_names"],
    isKeywordSort: true,
    isMulti: true
  }
};

export const filterMapV4: {
  [key: string]: MappedFilterField;
} = {
  ...filterMapV3,
  Indications: {
    fields: ["allIndications"],
    isKeywordSort: true,
    isMulti: true
  }
};

export const autocompleteFilterMap: {
  [key: string]: AutocompleteMappedFilterField;
} = {
  COUNTRY: {
    fieldsToSearch: ["facility.country"],
    fieldForAutocomplete: "facility.country.keyword",
    nestedPath: "facility"
  },
  STATE: {
    fieldsToSearch: ["facility.state"],
    fieldForAutocomplete: "facility.state.keyword",
    nestedPath: "facility"
  },
  CITY: {
    fieldsToSearch: ["facility.city"],
    fieldForAutocomplete: "facility.city.keyword",
    nestedPath: "facility"
  },
  CONDITIONS: {
    fieldsToSearch: ["study.condition.name"],
    fieldForAutocomplete: "study.condition.name.keyword"
  },
  SPONSORS: {
    fieldsToSearch: ["study.sponsor.name"],
    fieldForAutocomplete: "study.sponsor.name.keyword"
  },
  SPONSOR_TYPES: {
    fieldsToSearch: ["study.sponsor.sponsor_type"],
    fieldForAutocomplete: "study.sponsor.sponsor_type.keyword"
  },
  BIOMARKERS: {
    fieldsToSearch: ["biomarkerWithSynonyms.autocomplete_search"],
    fieldForAutocomplete: "biomarkerWithSynonyms"
  },
  BIOMARKER_INCLUSION: {
    fieldsToSearch: ["biomarkerWithSynonyms_inclusion.autocomplete_search"],
    fieldForAutocomplete: "biomarkerWithSynonyms_inclusion"
  },
  BIOMARKER_EXCLUSION: {
    fieldsToSearch: ["biomarkerWithSynonyms_exclusion.autocomplete_search"],
    fieldForAutocomplete: "biomarkerWithSynonyms_exclusion"
  },
  INDICATIONS: {
    fieldsToSearch: ["indications.autocomplete_search"],
    fieldForAutocomplete: "indications"
  },
  INDICATION_INCLUSION: {
    fieldsToSearch: ["indication_inclusions.autocomplete_search"],
    fieldForAutocomplete: "indication_inclusions"
  },
  INDICATION_EXCLUSION: {
    fieldsToSearch: ["indication_exclusions.autocomplete_search"],
    fieldForAutocomplete: "indication_exclusions"
  },
  GENERIC_DRUG_NAMES: {
    fieldsToSearch: ["generic_drug_names.autocomplete_search"],
    fieldForAutocomplete: "generic_drug_names"
  },
  GENERIC_DRUG_NAMES_INCLUSION: {
    fieldsToSearch: ["inclusion_generic_drug_names"],
    fieldForAutocomplete: "inclusion_generic_drug_names"
  },
  GENERIC_DRUG_NAMES_EXCLUSION: {
    fieldsToSearch: ["exclusion_generic_drug_names"],
    fieldForAutocomplete: "exclusion_generic_drug_names"
  }
};

const EnrollmentDurationRange = [
  { from: 0, to: 8, key: "<8" },
  { from: 8, to: 16, key: "8-16" },
  { from: 16, to: 24, key: "16-24" },
  { from: 24, to: 32, key: "24-32" },
  { from: 32, to: 40, key: "32-40" },
  { from: 40, to: 48, key: "40-48" },
  { from: 48, to: 56, key: "48-56" },
  { from: 56, key: "56+" }
];

const PatientsPerSitePerMonthRange = [
  { from: 0, to: 1, key: "<1" },
  { from: 1, to: 5, key: "1-5" },
  { from: 5, to: 10, key: "5-10" },
  { from: 10, to: 15, key: "10-15" },
  { from: 15, to: 20, key: "15-20" },
  { from: 20, to: 25, key: "20-25" },
  { from: 25, to: 30, key: "25-30" },
  { from: 30, to: 35, key: "30-35" },
  { from: 35, to: 40, key: "35-40" },
  { from: 40, to: 45, key: "40-45" },
  { from: 45, to: 50, key: "45-50" },
  { from: 50, key: "50+" }
];

/**
 * Histogram aggregation configurations for different metrics
 */
export const histogramAggregations = {
  [TrialHistogramMetric.ENROLLMENT]: {
    field: "study.enrollment",
    filter: {
      range: {
        "study.enrollment": {
          gt: 0,
          lte: 100000
        }
      }
    }
  },
  [TrialHistogramMetric.TRIAL_DURATION]: {
    field: "script",
    script:
      "ZonedDateTime d1 = doc['study.completion_date'].value; ZonedDateTime d2 = doc['study.start_date'].value; long differenceInMillis = ChronoUnit.MILLIS.between(d1, d2); return Math.abs(differenceInMillis) / (1000 * 60 * 60 * 24 * 365.25);",
    filter: {
      bool: {
        must: [
          { exists: { field: "study.start_date" } },
          { exists: { field: "study.completion_date" } }
        ]
      }
    }
  },
  [TrialHistogramMetric.ENROLLMENT_DURATION]: {
    field: "study.enrollment_duration_in_months",
    filter: {
      bool: {
        must: {
          range: {
            "study.enrollment_duration_in_months": {
              gt: 0
            }
          }
        },
        must_not: [
          { term: { "study.overall_status.keyword": "Not yet recruiting" } },
          { term: { "study.overall_status.keyword": "Recruiting" } }
        ]
      }
    }
  },
  [TrialHistogramMetric.PATIENTS_PER_SITE_PER_MONTH]: {
    field: "study.patients_per_site_per_month",
    filter: {
      bool: {
        must: {
          range: {
            "study.patients_per_site_per_month": {
              gt: 0
            }
          }
        },
        must_not: [
          { term: { "study.overall_status.keyword": "Not yet recruiting" } },
          { term: { "study.overall_status.keyword": "Recruiting" } }
        ]
      }
    }
  },
  [TrialHistogramMetric.FACILITY_COUNT]: {
    field: "number_of_facilities",
    filter: {
      range: {
        number_of_facilities: {
          gt: 0
        }
      }
    }
  }
};

export const trialsAggregations: Record<
  keyof RootAggregation,
  Record<string, unknown>
> = {
  // todo - simplify this aggregation once `HPES-3414` is complete.
  /**
   * Aggregation for calculating the average duration of a trial, defined as
   * completion_date - start_state
   *
   * Trials without a start or completion date will be filtered out
   *
   */
  trial_duration: {
    filter: {
      bool: {
        must: [
          {
            exists: {
              field: "study.start_date"
            }
          },
          {
            exists: {
              field: "study.completion_date"
            }
          }
        ]
      }
    },
    aggs: {
      avg_duration: {
        avg: {
          script:
            "ZonedDateTime d1 = doc['study.completion_date'].value; ZonedDateTime d2 = doc['study.start_date'].value; long differenceInMillis = ChronoUnit.MILLIS.between(d1, d2); return Math.abs(differenceInMillis);"
        }
      },
      median_duration: {
        percentiles: {
          script:
            "ZonedDateTime d1 = doc['study.completion_date'].value; ZonedDateTime d2 = doc['study.start_date'].value; long differenceInMillis = ChronoUnit.MILLIS.between(d1, d2); return Math.abs(differenceInMillis);",
          percents: [50]
        }
      }
    }
  },
  enrollment_duration: {
    filter: {
      bool: {
        must: [
          { exists: { field: "study.start_date" } },
          { exists: { field: "study.primary_completion_date" } }
        ]
      }
    },
    aggs: {
      avg_duration: {
        avg: {
          script:
            "ZonedDateTime d1 = doc['study.primary_completion_date'].value; ZonedDateTime d2 = doc['study.start_date'].value; long differenceInMillis = ChronoUnit.MILLIS.between(d1, d2); return Math.abs(differenceInMillis);"
        }
      },
      median_duration: {
        percentiles: {
          script:
            "ZonedDateTime d1 = doc['study.primary_completion_date'].value; ZonedDateTime d2 = doc['study.start_date'].value; long differenceInMillis = ChronoUnit.MILLIS.between(d1, d2); return Math.abs(differenceInMillis);",
          percents: [50]
        }
      }
    }
  },
  /**
   * Aggregation for calculating the total number of facilities across all trials
   * contained in the search
   *
   * Filters out facilities without a statusHistory as those are invalid.
   */
  facility_count: {
    filter: {
      exists: {
        field: "facility.statusHistory"
      }
    },
    aggs: {
      count: {
        value_count: {
          field: "facility.external_uuid.keyword"
        }
      }
    }
  },
  /**
   * Aggregation for calculating the total enrollment count across all trials
   * contained in the search
   *
   * Only includes trials with enrollment counts less than or equal to 100,000 (~99.8th percentile).
   * There are several trials that have placeholder values for enrollment count (things like 9999999)
   * So, we want to try and filter out some of the outliers
   */
  total_enrollment: {
    filter: {
      range: {
        "study.enrollment": {
          lte: 100000
        }
      }
    },
    aggs: {
      enrollment_sum: {
        sum: {
          field: "study.enrollment"
        }
      },
      median: {
        percentiles: {
          field: "study.enrollment",
          percents: [50]
        }
      }
    }
  },
  /**
   * Aggregation for getting the total number of each 'person role' that is
   * contained in the search
   *
   * Person roles include things like "Primary Investigator", "Contact", "etc"
   */
  group_by_person_role: {
    terms: {
      field: "person.roleHistory.role.keyword"
    }
  },
  /**
   * Aggregation for getting the count of trials by country
   * contained in the search
   */
  group_by_country: {
    terms: {
      field: "facility.country.keyword",
      // top 10 is fine for now, we can make variable later if needed
      size: 10
    }
  },
  /**
   * Aggregation for grouping trials by phase an status
   * contained in the search
   *
   * The outer grouping is by phase and each phase group will contain
   * a group by status
   *
   * ex:
   *   [
   *     {
   *       key: 'Phase 1',
   *       count: 3,
   *       group_by_status: [{ key: 'Status 1', count: 0 }, { key: 'Status 2', count: 3 }]
   *     },
   *     {
   *       key: 'Phase 2',
   *       count: 5,
   *       group_by_status: [{ key: 'Status 1', count: 2 }, { key: 'Status 2', count: 3 }]
   *     }
   *   ]
   */
  group_by_phase_and_status: {
    terms: {
      field: "study.phase.keyword"
    },
    aggs: {
      group_by_status: {
        terms: {
          field: "study.overall_status.keyword"
        }
      }
    }
  },
  median_facility_count: {
    nested: {
      path: "facility"
    },
    aggs: {
      median_facility_count: {
        scripted_metric: {
          init_script: "state.trialToFacilityCount = new HashMap()",
          map_script:
            "state.trialToFacilityCount.put(doc._id.value, 1 + state.trialToFacilityCount.getOrDefault(doc._id.value, 0))",
          combine_script: "return state.trialToFacilityCount",
          reduce_script: `
              Map trialToFacilityCount = new HashMap(); \
              for (state in states) { \
                  state.forEach( \
                      (trialId, facilityCount) -> { \
                          trialToFacilityCount.put(trialId, facilityCount +  trialToFacilityCount.getOrDefault(trialId, 0)) \
                      } \
                  ) \
              } \
              def counts = new ArrayList(trialToFacilityCount.values()); \
              Collections.sort(counts); \
              int middle = counts.size() / 2; \
              return counts.size() == 0 ? 0 : counts[middle];
              `
        }
      }
    }
  },
  patients_per_site_per_month: {
    filter: {
      bool: {
        must: {
          range: {
            "study.patients_per_site_per_month": {
              gt: 0
            }
          }
        },
        must_not: [
          {
            term: {
              "study.overall_status.keyword": "Not yet recruiting"
            }
          },
          {
            term: {
              "study.overall_status.keyword": "Recruiting"
            }
          }
        ]
      }
    },
    aggs: {
      avg_patients_per_site_per_month: {
        avg: {
          field: "study.patients_per_site_per_month"
        }
      },
      median_patients_per_site_per_month: {
        percentiles: {
          field: "study.patients_per_site_per_month",
          percents: [50]
        }
      }
    }
  },
  enrollment_duration_v2: {
    filter: {
      bool: {
        must: {
          range: {
            "study.enrollment_duration_in_months": {
              gt: 0
            }
          }
        },
        must_not: [
          {
            term: {
              "study.overall_status.keyword": "Not yet recruiting"
            }
          },
          {
            term: {
              "study.overall_status.keyword": "Recruiting"
            }
          }
        ]
      }
    },
    aggs: {
      avg_duration: {
        avg: {
          field: "study.enrollment_duration_in_months"
        }
      },
      median_duration: {
        percentiles: {
          field: "study.enrollment_duration_in_months",
          percents: [50]
        }
      }
    }
  },
  enrollment_graphs: {
    filter: {
      bool: {
        must_not: [
          {
            term: {
              "study.overall_status.keyword": "Not yet recruiting"
            }
          },
          {
            term: {
              "study.overall_status.keyword": "Recruiting"
            }
          }
        ]
      }
    },
    aggs: {
      group_by_enrollment_duration: {
        range: {
          field: "study.enrollment_duration_in_months",
          ranges: EnrollmentDurationRange
        }
      },
      group_by_patients_per_site_per_month: {
        range: {
          field: "study.patients_per_site_per_month",
          ranges: PatientsPerSitePerMonthRange
        }
      }
    }
  },
  group_by_top_countries: {
    filter: { match_all: {} },
    aggs: {
      group_by_sites: {
        nested: {
          path: "facility"
        },
        aggs: {
          order_by_sites: {
            terms: {
              field: "facility.country.keyword",
              size: 20
            }
          }
        }
      },
      group_by_trials: {
        nested: {
          path: "facility"
        },
        aggs: {
          order_by_trials: {
            aggs: {
              trials: {
                reverse_nested: {}
              }
            },
            terms: {
              field: "facility.country.keyword",
              size: 20,
              order: {
                trials: "desc"
              }
            }
          }
        }
      },
      group_by_avg_enrollment_rate: {
        filter: {
          bool: {
            must: {
              range: {
                "study.patients_per_site_per_month": {
                  gt: 0
                }
              }
            },
            must_not: [
              {
                term: {
                  "study.overall_status.keyword": "Not yet recruiting"
                }
              },
              {
                term: {
                  "study.overall_status.keyword": "Recruiting"
                }
              }
            ]
          }
        },
        aggs: {
          filtered_trials: {
            nested: {
              path: "facility"
            },
            aggs: {
              order_by_avg_enrollment_rate: {
                terms: {
                  field: "facility.country.keyword",
                  size: 200
                },
                aggs: {
                  trials: {
                    reverse_nested: {},
                    aggs: {
                      sum_patients_per_site_per_month: {
                        sum: {
                          field: "study.patients_per_site_per_month"
                        }
                      }
                    }
                  },
                  avg_patients_per_site_per_month: {
                    bucket_script: {
                      buckets_path: {
                        totalPatients: "trials>sum_patients_per_site_per_month",
                        totalTrials: "trials>_count"
                      },
                      script: "params.totalPatients / params.totalTrials"
                    }
                  },
                  avg_sort: {
                    bucket_sort: {
                      sort: [
                        {
                          avg_patients_per_site_per_month: {
                            order: "desc"
                          }
                        }
                      ]
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  }
};

export const trialsAggregationsV3: Record<
  keyof RootAggregationV3,
  Record<string, unknown>
> = {
  ...trialsAggregations,
  total_enrollment: {
    filter: { range: { "study.enrollment": { lte: 100000, gt: 0 } } },
    aggs: {
      enrollment_sum: { sum: { field: "study.enrollment" } },
      median: {
        percentiles: {
          field: "study.enrollment",
          percents: [50]
        }
      }
    }
  },
  group_by_person_role: {
    nested: {
      path: "person.roleHistory"
    },
    aggs: {
      group_by_role: {
        terms: {
          field: "person.roleHistory.role.keyword"
        }
      }
    }
  },
  group_by_country: {
    nested: {
      path: "facility"
    },
    aggs: {
      country_groups: {
        terms: {
          field: "facility.country.keyword",
          size: 20
        }
      }
    }
  },
  facility_count: {
    nested: {
      path: "facility"
    },
    aggs: {
      filtered_facility_count: {
        filter: {
          exists: {
            field: "facility.statusHistory"
          }
        }
      }
    }
  },
  filtered_facility_count: {
    filter: {
      range: {
        number_of_facilities: {
          gt: 0
        }
      }
    },
    aggs: {
      median: {
        percentiles: {
          field: "number_of_facilities",
          percents: [50]
        }
      }
    }
  }
};

/**
 * Creates the trial country breakdown aggregation
 * Provides comprehensive country-level metrics including trial counts, enrollment metrics,
 * duration metrics, and H1-specific metrics
 */
export const createTrialCountryBreakdownAggregation = (): Record<
  string,
  unknown
> => ({
  nested: {
    path: "facility"
  },
  aggs: {
    countries: {
      terms: {
        field: "facility.country.keyword",
        order: {
          "trials>total_trials": "desc"
        }
      },
      aggs: {
        // Get back to trial level for calculations
        trials: {
          reverse_nested: {},
          aggs: {
            // Total trials count
            total_trials: {
              value_count: {
                field: "_id"
              }
            },
            // Active trials (excluding "Not yet recruiting" and "Recruiting")
            active_trials: {
              filter: {
                terms: {
                  "study.overall_status.keyword": [
                    "Not yet recruiting",
                    "Recruiting",
                    "Enrolling by invitation",
                    "Active, not recruiting",
                    "Active"
                  ]
                }
              }
            },
            // Recruiting trials
            recruiting_trials: {
              filter: {
                term: {
                  "study.overall_status.keyword": "Recruiting"
                }
              }
            },
            // Enrollment duration median (in months)
            enrollment_duration_median: {
              filter: {
                bool: {
                  must: {
                    range: {
                      "study.enrollment_duration_in_months": {
                        gt: 0
                      }
                    }
                  },
                  must_not: [
                    {
                      term: {
                        "study.overall_status.keyword": "Not yet recruiting"
                      }
                    },
                    {
                      term: {
                        "study.overall_status.keyword": "Recruiting"
                      }
                    }
                  ]
                }
              },
              aggs: {
                median: {
                  percentiles: {
                    field: "study.enrollment_duration_in_months",
                    percents: [50]
                  }
                }
              }
            },
            // Enrollment rate median (patients per site per month)
            enrollment_rate_median: {
              filter: {
                bool: {
                  must: {
                    range: {
                      "study.patients_per_site_per_month": {
                        gt: 0
                      }
                    }
                  },
                  must_not: [
                    {
                      term: {
                        "study.overall_status.keyword": "Not yet recruiting"
                      }
                    },
                    {
                      term: {
                        "study.overall_status.keyword": "Recruiting"
                      }
                    }
                  ]
                }
              },
              aggs: {
                median: {
                  percentiles: {
                    field: "study.patients_per_site_per_month",
                    percents: [50]
                  }
                }
              }
            },
            // Patient enrollment median
            patient_enrollment_median: {
              filter: {
                range: {
                  "study.enrollment": {
                    gt: 0,
                    lte: 100000
                  }
                }
              },
              aggs: {
                median: {
                  percentiles: {
                    field: "study.enrollment",
                    percents: [50]
                  }
                }
              }
            },
            // Trial duration median in months (completion_date - start_date)
            trial_duration_median: {
              filter: {
                bool: {
                  must: [
                    {
                      exists: {
                        field: "study.start_date"
                      }
                    },
                    {
                      exists: {
                        field: "study.completion_date"
                      }
                    }
                  ]
                }
              },
              aggs: {
                median: {
                  percentiles: {
                    script:
                      "ZonedDateTime d1 = doc['study.completion_date'].value; ZonedDateTime d2 = doc['study.start_date'].value; long differenceInMillis = ChronoUnit.MILLIS.between(d1, d2); return Math.abs(differenceInMillis) / (1000 * 60 * 60 * 24 * 30.44);",
                    percents: [50]
                  }
                }
              }
            }
          }
        },
        // H1 metrics - moved outside trials to avoid nested context issues
        h1_sites_count: {
          filter: {
            exists: {
              field: "facility.h1_organization_id"
            }
          },
          aggs: {
            count: {
              cardinality: {
                field: "facility.h1_organization_id"
              }
            }
          }
        },
        h1_investigators_count: {
          nested: {
            path: "facility.personHistory"
          },
          aggs: {
            investigators_filter: {
              filter: {
                bool: {
                  must: [
                    {
                      exists: {
                        field: "facility.personHistory.h1_person_id"
                      }
                    },
                    {
                      terms: {
                        "facility.personHistory.role.keyword": [
                          "Principal Investigator",
                          "Sub-Investigator"
                        ]
                      }
                    }
                  ]
                }
              },
              aggs: {
                count: {
                  cardinality: {
                    field: "facility.personHistory.h1_person_id"
                  }
                }
              }
            }
          }
        }
      }
    }
  }
});

/**
 * Creates the trial region breakdown aggregation
 * Provides comprehensive region-level metrics within a specific country including trial counts, enrollment metrics,
 * duration metrics, and H1-specific metrics
 */
export const createTrialRegionBreakdownAggregation = (
  country: string
): Record<string, unknown> => ({
  nested: {
    path: "facility"
  },
  aggs: {
    // Filter facilities by the specified country
    country_filter: {
      filter: {
        term: {
          "facility.country.keyword": country
        }
      },
      aggs: {
        // Bucket by state/region within the country
        regions: {
          terms: {
            field: "facility.state.keyword",
            order: {
              "trials>total_trials": "desc"
            }
          },
          aggs: {
            // Get back to trial level for calculations
            trials: {
              reverse_nested: {},
              aggs: {
                // Total trials count
                total_trials: {
                  value_count: {
                    field: "_id"
                  }
                },
                // Active trials (excluding "Not yet recruiting" and "Recruiting")
                active_trials: {
                  filter: {
                    terms: {
                      "study.overall_status.keyword": [
                        "Not yet recruiting",
                        "Recruiting",
                        "Enrolling by invitation",
                        "Active, not recruiting",
                        "Active"
                      ]
                    }
                  }
                },
                // Recruiting trials
                recruiting_trials: {
                  filter: {
                    term: {
                      "study.overall_status.keyword": "Recruiting"
                    }
                  }
                },
                // Patient enrollment median
                patient_enrollment_median: {
                  filter: {
                    range: {
                      "study.enrollment": {
                        gt: 0,
                        lte: 100000
                      }
                    }
                  },
                  aggs: {
                    median: {
                      percentiles: {
                        field: "study.enrollment",
                        percents: [50]
                      }
                    }
                  }
                },
                // Enrollment duration median (in months)
                enrollment_duration_median: {
                  filter: {
                    bool: {
                      must: {
                        range: {
                          "study.enrollment_duration_in_months": {
                            gt: 0
                          }
                        }
                      },
                      must_not: [
                        {
                          term: {
                            "study.overall_status.keyword": "Not yet recruiting"
                          }
                        },
                        {
                          term: {
                            "study.overall_status.keyword": "Recruiting"
                          }
                        }
                      ]
                    }
                  },
                  aggs: {
                    median: {
                      percentiles: {
                        field: "study.enrollment_duration_in_months",
                        percents: [50]
                      }
                    }
                  }
                },
                // Enrollment rate median (patients per site per month)
                enrollment_rate_median: {
                  filter: {
                    bool: {
                      must: {
                        range: {
                          "study.patients_per_site_per_month": {
                            gt: 0
                          }
                        }
                      },
                      must_not: [
                        {
                          term: {
                            "study.overall_status.keyword": "Not yet recruiting"
                          }
                        },
                        {
                          term: {
                            "study.overall_status.keyword": "Recruiting"
                          }
                        }
                      ]
                    }
                  },
                  aggs: {
                    median: {
                      percentiles: {
                        field: "study.patients_per_site_per_month",
                        percents: [50]
                      }
                    }
                  }
                },
                // Trial duration median in months (completion_date - start_date)
                trial_duration_median: {
                  filter: {
                    bool: {
                      must: [
                        {
                          exists: {
                            field: "study.start_date"
                          }
                        },
                        {
                          exists: {
                            field: "study.completion_date"
                          }
                        }
                      ]
                    }
                  },
                  aggs: {
                    median: {
                      percentiles: {
                        script:
                          "ZonedDateTime d1 = doc['study.completion_date'].value; ZonedDateTime d2 = doc['study.start_date'].value; long differenceInMillis = ChronoUnit.MILLIS.between(d1, d2); return Math.abs(differenceInMillis) / (1000 * 60 * 60 * 24 * 30.44);",
                        percents: [50]
                      }
                    }
                  }
                }
              }
            },
            // H1 metrics - moved outside trials to avoid nested context issues
            h1_sites_count: {
              filter: {
                exists: {
                  field: "facility.h1_organization_id"
                }
              },
              aggs: {
                count: {
                  cardinality: {
                    field: "facility.h1_organization_id"
                  }
                }
              }
            },
            h1_investigators_count: {
              nested: {
                path: "facility.personHistory"
              },
              aggs: {
                investigators_filter: {
                  filter: {
                    bool: {
                      must: [
                        {
                          exists: {
                            field: "facility.personHistory.h1_person_id"
                          }
                        },
                        {
                          terms: {
                            "facility.personHistory.role.keyword": [
                              "Principal Investigator",
                              "Sub-Investigator"
                            ]
                          }
                        }
                      ]
                    }
                  },
                  aggs: {
                    count: {
                      cardinality: {
                        field: "facility.personHistory.h1_person_id"
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  }
});
