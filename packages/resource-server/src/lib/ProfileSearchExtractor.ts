import {
  ProfileCongressMetrics,
  ProfilePublicationMetrics,
  ProfileTrialMetrics,
  ProfileDiagnosisMetrics,
  ProfileProcedureMetrics,
  ProfilePaymentMetrics
} from "@h1nyc/search-sdk";
import {
  Bucket,
  CongressAggregationsBucket,
  DiagnosisAggregationsBucket,
  PaymentAggregationsBucket,
  ProcedureAggregationsBucket,
  PublicationAggregationsBucket,
  TrialAggregationsBucket
} from "./ProfileSearchBuilder";

export const extractProfileTrialMetrics = (
  bucket: Bucket
): ProfileTrialMetrics | null => {
  // agg values are in different positions in the response object depending on if there are search terms/filters
  const metrics: TrialAggregationsBucket | undefined =
    bucket.trials && "trialFilters" in bucket.trials
      ? bucket.trials.trialFilters?.buckets[0]
      : bucket.trials;

  if (!metrics) {
    return null;
  }

  return {
    personId: bucket.key,
    total: metrics.doc_count,
    inProgressCount: metrics.inProgressCount.doc_count,
    completedCount: metrics.completedCount.doc_count,
    terminatedCount: metrics.terminatedCount.doc_count,
    topConditions: metrics.topConditions.buckets.map((bucket) => bucket.key),
    topInterventions: metrics.topInterventions.buckets.map(
      (bucket) => bucket.key
    ),
    totalByYear: metrics.yearTotals.buckets.map((bucket) => ({
      year: new Date(bucket.key_as_string).getUTCFullYear(),
      total: bucket.doc_count
    })),
    trialIds: metrics.ids.buckets.map((id) => id.key)
  };
};

export const extractProfilePublicationMetrics = (
  bucket: Bucket
): ProfilePublicationMetrics | null => {
  // agg values are in different positions in the response object depending on if there are search terms/filters
  const metrics: PublicationAggregationsBucket | undefined =
    bucket.publications && "publicationFilters" in bucket.publications
      ? bucket.publications.publicationFilters?.buckets[0]
      : bucket.publications;

  if (!metrics) {
    return null;
  }

  return {
    personId: bucket.key,
    total: metrics.doc_count,
    citationSum: metrics.citationCount.value,
    microBloggingSum: metrics.microBloggingCount.value,
    totalByYear: metrics.yearTotals.buckets.map((bucket) => ({
      year: new Date(bucket.key_as_string).getUTCFullYear(),
      total: bucket.doc_count
    }))
  };
};

export const extractProfileCongressMetrics = (
  bucket: Bucket
): ProfileCongressMetrics | null => {
  // agg values are in different positions in the response object depending on if there are search terms/filters
  const metrics: CongressAggregationsBucket | undefined =
    bucket.congresses && "congressFilters" in bucket.congresses
      ? bucket.congresses.congressFilters?.buckets[0]
      : bucket.congresses;

  if (!metrics) {
    return null;
  }

  return {
    personId: bucket.key,
    total: metrics.doc_count,
    sessionsTotal: metrics.sessionsTotal.doc_count,
    postersTotal: metrics.postersTotal.doc_count,
    topCongresses: metrics.topCongresses.buckets.map((bucket) => ({
      name: bucket.key,
      presentations: bucket.doc_count
    }))
  };
};

export const extractProfileDiagnosisMetrics = (
  bucket: Bucket
): ProfileDiagnosisMetrics | null => {
  // agg values are in different positions in the response object depending on if there are search terms/filters
  const metrics: DiagnosisAggregationsBucket | undefined =
    bucket.diagnoses && "diagnosisFilters" in bucket.diagnoses
      ? bucket.diagnoses.diagnosisFilters?.buckets[0]
      : bucket.diagnoses;

  if (!metrics) {
    return null;
  }

  return {
    personId: bucket.key,
    total: metrics.doc_count,
    internalCountSum: metrics.internalCount.value
    // additional metrics for diagnoses will be added here
  };
};

export const extractProfileProcedureMetrics = (
  bucket: Bucket
): ProfileProcedureMetrics | null => {
  // agg values are in different positions in the response object depending on if there are search terms/filters
  const metrics: ProcedureAggregationsBucket | undefined =
    bucket.procedures && "procedureFilters" in bucket.procedures
      ? bucket.procedures.procedureFilters?.buckets[0]
      : bucket.procedures;

  if (!metrics) {
    return null;
  }

  return {
    personId: bucket.key,
    total: metrics.doc_count,
    internalCountSum: metrics.internalCount.value
    // additional metrics for procedures will be added here
  };
};

export const extractProfilePaymentMetrics = (
  bucket: Bucket
): ProfilePaymentMetrics | null => {
  // agg values are in different positions in the response object depending on if there are search terms/filters
  const metrics: PaymentAggregationsBucket | undefined =
    bucket.payments && "paymentFilters" in bucket.payments
      ? bucket.payments.paymentFilters?.buckets[0]
      : bucket.payments;

  if (!metrics) {
    return null;
  }

  return {
    personId: bucket.key,
    total: metrics.doc_count,
    totalPaymentsAmount: metrics.totalPaymentsAmount.value
    // additional metrics for payments will be added here
  };
};
