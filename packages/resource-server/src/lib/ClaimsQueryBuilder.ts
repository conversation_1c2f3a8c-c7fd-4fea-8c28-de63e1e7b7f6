import {
  SearchRequest,
  SearchTotalHits
} from "@elastic/elasticsearch/lib/api/types";
import { DiagnosesCareClustersFields } from "@h1nyc/search-sdk";
import { DiagnosesCcsrSortFields } from "@h1nyc/search-sdk";
import {
  ClaimsFilterRange,
  DiagnosesFields,
  DiagnosesSortFields,
  PrescriptionsFields,
  PrescriptionsSortFields,
  ProceduresFields,
  ProcedureSortFields,
  SortDirection
} from "@h1nyc/search-sdk";
import { buildTermsQuery } from "../util/QueryBuildingUtils";
import { ProceduresCcsrSortFields } from "@h1nyc/search-sdk";
import { ProceduresCareClustersFields } from "@h1nyc/search-sdk";

const DEFAULT_RESULT_SIZE = 100;
const DEFAULT_RESULT_OFFSET = 0;
interface ElasticInnerHitsResponse<S, IH> {
  hits: {
    total: number;
    max_score: number;
    hits: {
      _source: S;
      inner_hits: IH;
    }[];
  };
}

export type DiagnosesResponse = ElasticInnerHitsResponse<
  {
    DRG_diagnosesCount: number;
    DRG_diagnosesCount_5_year: number;
    DRG_diagnosesCount_2_year: number;
    DRG_diagnosesCount_1_year: number;
    DRG_diagnosesUniqueCount: number;
    DRG_diagnosesUniqueCount_5_year: number;
    DRG_diagnosesUniqueCount_2_year: number;
    DRG_diagnosesUniqueCount_1_year: number;
  },
  {
    DRG_diagnoses: {
      hits: {
        total: SearchTotalHits;
        hits: DiagnosisHit[];
      };
    };
  }
>;

export type DiagnosesCcsrResponse = ElasticInnerHitsResponse<
  {
    DRG_diagnosesUniqueCount: number;
    DRG_diagnosesUniqueCount_5_year: number;
    DRG_diagnosesUniqueCount_2_year: number;
    DRG_diagnosesUniqueCount_1_year: number;
  },
  {
    ccsr: {
      hits: {
        total: SearchTotalHits;
        hits: DiagnosisCcsrHit[];
      };
    };
  }
>;

export type ProceduresCcsrResponse = ElasticInnerHitsResponse<
  {
    DRG_proceduresUniqueCount: number;
    DRG_proceduresUniqueCount_5_year: number;
    DRG_proceduresUniqueCount_2_year: number;
    DRG_proceduresUniqueCount_1_year: number;
  },
  {
    ccsr_px: {
      hits: {
        total: SearchTotalHits;
        hits: ProcedureCcsrHit[];
      };
    };
  }
>;

export type ProceduresResponse = ElasticInnerHitsResponse<
  {
    DRG_proceduresCount: number;
    DRG_proceduresCount_5_year: number;
    DRG_proceduresCount_2_year: number;
    DRG_proceduresCount_1_year: number;
    DRG_proceduresUniqueCount: number;
    DRG_proceduresUniqueCount_5_year: number;
    DRG_proceduresUniqueCount_2_year: number;
    DRG_proceduresUniqueCount_1_year: number;
  },
  {
    DRG_procedures: {
      hits: {
        total: SearchTotalHits;
        hits: ProcedureHit[];
      };
    };
  }
>;

export type PrescriptionsResponse = ElasticInnerHitsResponse<
  {
    num_prescriptions: number;
    num_prescriptions_5_year: number;
    num_prescriptions_2_year: number;
    num_prescriptions_1_year: number;
    prescriptions_patient_count: number;
    prescriptions_patient_count_1_year: number;
    prescriptions_patient_count_2_year: number;
    prescriptions_patient_count_5_year: number;
    rank_max_prescriptions: number;
    rank_1_year_prescriptions: number;
    rank_2_year_prescriptions: number;
    rank_5_year_prescriptions: number;
    hcp_quartile: number;
    hcp_quartile_1_year: number;
    hcp_quartile_2_year: number;
    hcp_quartile_5_year: number;
    pctOfUniquePatients: number;
    pctOfUniquePatients_1_year: number;
    pctOfUniquePatients_2_year: number;
    pctOfUniquePatients_5_year: number;
  },
  {
    prescriptions: {
      hits: {
        total: SearchTotalHits;
        hits: PrescriptionsHit[];
      };
    };
  }
>;

interface ClaimsTotalsHit {
  _source: {
    DRG_proceduresCount: number;
    DRG_proceduresCount_5_year: number;
    DRG_proceduresCount_2_year: number;
    DRG_proceduresCount_1_year: number;
    DRG_proceduresUniqueCount: number;
    DRG_proceduresUniqueCount_5_year: number;
    DRG_proceduresUniqueCount_2_year: number;
    DRG_proceduresUniqueCount_1_year: number;
    DRG_diagnosesCount: number;
    DRG_diagnosesCount_5_year: number;
    DRG_diagnosesCount_2_year: number;
    DRG_diagnosesCount_1_year: number;
    DRG_diagnosesUniqueCount: number;
    DRG_diagnosesUniqueCount_5_year: number;
    DRG_diagnosesUniqueCount_2_year: number;
    DRG_diagnosesUniqueCount_1_year: number;
    num_prescriptions: number;
    num_prescriptions_5_year: number;
    num_prescriptions_2_year: number;
    num_prescriptions_1_year: number;
    prescriptions_patient_count: number;
    prescriptions_patient_count_5_year: number;
    prescriptions_patient_count_2_year: number;
    prescriptions_patient_count_1_year: number;
    rank_max_prescriptions: number;
    rank_1_year_prescriptions: number;
    rank_2_year_prescriptions: number;
    rank_5_year_prescriptions: number;
    hcp_quartile: number;
    hcp_quartile_1_year: number;
    hcp_quartile_2_year: number;
    hcp_quartile_5_year: number;
    pctOfUniquePatients: number;
    pctOfUniquePatients_1_year: number;
    pctOfUniquePatients_2_year: number;
    pctOfUniquePatients_5_year: number;
  };
}

interface DiagnosesRangeFields {
  diagnosesCount:
    | "DRG_diagnosesCount"
    | "DRG_diagnosesCount_5_year"
    | "DRG_diagnosesCount_2_year"
    | "DRG_diagnosesCount_1_year"
    | "DRG_diagnosesUniqueCount"
    | "DRG_diagnosesUniqueCount_5_year"
    | "DRG_diagnosesUniqueCount_2_year"
    | "DRG_diagnosesUniqueCount_1_year";
  percent:
    | "DRG_diagnoses.pctOfClaims"
    | "DRG_diagnoses.pctOfClaims_5_year"
    | "DRG_diagnoses.pctOfClaims_2_year"
    | "DRG_diagnoses.pctOfClaims_1_year"
    | "DRG_diagnoses.pctOfUniqueClaims"
    | "DRG_diagnoses.pctOfUniqueClaims_5_year"
    | "DRG_diagnoses.pctOfUniqueClaims_2_year"
    | "DRG_diagnoses.pctOfUniqueClaims_1_year";
  internalCount:
    | "DRG_diagnoses.internalCount"
    | "DRG_diagnoses.internalCount_5_year"
    | "DRG_diagnoses.internalCount_2_year"
    | "DRG_diagnoses.internalCount_1_year"
    | "DRG_diagnoses.internalUniqueCount"
    | "DRG_diagnoses.internalUniqueCount_5_year"
    | "DRG_diagnoses.internalUniqueCount_2_year"
    | "DRG_diagnoses.internalUniqueCount_1_year";
}

interface DiagnosesCcsrRangeFields {
  diagnosesPatientCount:
    | "DRG_diagnosesUniqueCount"
    | "DRG_diagnosesUniqueCount_5_year"
    | "DRG_diagnosesUniqueCount_2_year"
    | "DRG_diagnosesUniqueCount_1_year";
  percent:
    | "ccsr.pctOfCcsrPatients"
    | "ccsr.pctOfCcsrPatients_5_year"
    | "ccsr.pctOfCcsrPatients_2_year"
    | "ccsr.pctOfCcsrPatients_1_year";
  internalCount:
    | "ccsr.internalCount"
    | "ccsr.internalCount_5_year"
    | "ccsr.internalCount_2_year"
    | "ccsr.internalCount_1_year";
  internalUniqueCount:
    | "ccsr.internalUniqueCount"
    | "ccsr.internalUniqueCount_5_year"
    | "ccsr.internalUniqueCount_2_year"
    | "ccsr.internalUniqueCount_1_year";
  hcpQuartile:
    | "ccsr.hcp_quartile"
    | "ccsr.hcp_quartile_5_year"
    | "ccsr.hcp_quartile_2_year"
    | "ccsr.hcp_quartile_1_year";
}

interface ProceduresCcsrRangeFields {
  proceduresPatientCount:
    | "DRG_proceduresUniqueCount"
    | "DRG_proceduresUniqueCount_5_year"
    | "DRG_proceduresUniqueCount_2_year"
    | "DRG_proceduresUniqueCount_1_year";
  percent:
    | "ccsr_px.pctOfCcsrPatients"
    | "ccsr_px.pctOfCcsrPatients_5_year"
    | "ccsr_px.pctOfCcsrPatients_2_year"
    | "ccsr_px.pctOfCcsrPatients_1_year";
  internalCount:
    | "ccsr_px.internalCount"
    | "ccsr_px.internalCount_5_year"
    | "ccsr_px.internalCount_2_year"
    | "ccsr_px.internalCount_1_year";
  internalUniqueCount:
    | "ccsr_px.internalUniqueCount"
    | "ccsr_px.internalUniqueCount_5_year"
    | "ccsr_px.internalUniqueCount_2_year"
    | "ccsr_px.internalUniqueCount_1_year";
  hcpQuartile:
    | "ccsr_px.hcp_quartile"
    | "ccsr_px.hcp_quartile_5_year"
    | "ccsr_px.hcp_quartile_2_year"
    | "ccsr_px.hcp_quartile_1_year";
}

interface ProceduresRangeFields {
  proceduresCount:
    | "DRG_proceduresCount"
    | "DRG_proceduresCount_5_year"
    | "DRG_proceduresCount_2_year"
    | "DRG_proceduresCount_1_year"
    | "DRG_proceduresUniqueCount"
    | "DRG_proceduresUniqueCount_5_year"
    | "DRG_proceduresUniqueCount_2_year"
    | "DRG_proceduresUniqueCount_1_year";
  percent:
    | "DRG_procedures.percentage"
    | "DRG_procedures.percentage_5_year"
    | "DRG_procedures.percentage_2_year"
    | "DRG_procedures.percentage_1_year"
    | "DRG_procedures.uniquePercentage"
    | "DRG_procedures.uniquePercentage_5_year"
    | "DRG_procedures.uniquePercentage_2_year"
    | "DRG_procedures.uniquePercentage_1_year";
  internalCount:
    | "DRG_procedures.internalCount"
    | "DRG_procedures.internalCount_5_year"
    | "DRG_procedures.internalCount_2_year"
    | "DRG_procedures.internalCount_1_year"
    | "DRG_procedures.internalUniqueCount"
    | "DRG_procedures.internalUniqueCount_5_year"
    | "DRG_procedures.internalUniqueCount_2_year"
    | "DRG_procedures.internalUniqueCount_1_year";
}

interface PrescriptionsRangeFields {
  numPrescriptions:
    | "prescriptions.num_prescriptions"
    | "prescriptions.num_prescriptions_5_year"
    | "prescriptions.num_prescriptions_2_year"
    | "prescriptions.num_prescriptions_1_year";
  totalNumPrescriptions:
    | "num_prescriptions"
    | "num_prescriptions_5_year"
    | "num_prescriptions_2_year"
    | "num_prescriptions_1_year";
  patientCount:
    | "prescriptions.patient_count"
    | "prescriptions.patient_count_1_year"
    | "prescriptions.patient_count_2_year"
    | "prescriptions.patient_count_5_year";
  totalPatientCount:
    | "prescriptions_patient_count"
    | "prescriptions_patient_count_1_year"
    | "prescriptions_patient_count_2_year"
    | "prescriptions_patient_count_5_year";
  rank:
    | "prescriptions.rank_max_prescriptions"
    | "prescriptions.rank_1_year_prescriptions"
    | "prescriptions.rank_2_year_prescriptions"
    | "prescriptions.rank_5_year_prescriptions";
  quartile:
    | "prescriptions.hcp_quartile"
    | "prescriptions.hcp_quartile_1_year"
    | "prescriptions.hcp_quartile_2_year"
    | "prescriptions.hcp_quartile_5_year";
  percentage:
    | "prescriptions.pctOfUniquePatients"
    | "prescriptions.pctOfUniquePatients_1_year"
    | "prescriptions.pctOfUniquePatients_2_year"
    | "prescriptions.pctOfUniquePatients_5_year";
}

export interface ClaimsTotalsResponse {
  hits: {
    hits: ClaimsTotalsHit[];
  };
}

interface DiagnosisHit {
  fields: {
    "DRG_diagnoses.diagnosisCode_eng": string[];
    "DRG_diagnoses.internalCount": number[];
    "DRG_diagnoses.internalCount_5_year": number[];
    "DRG_diagnoses.internalCount_2_year": number[];
    "DRG_diagnoses.internalCount_1_year": number[];
    "DRG_diagnoses.internalUniqueCount": number[];
    "DRG_diagnoses.internalUniqueCount_5_year": number[];
    "DRG_diagnoses.internalUniqueCount_2_year": number[];
    "DRG_diagnoses.internalUniqueCount_1_year": number[];
    "DRG_diagnoses.pctOfClaims": string[];
    "DRG_diagnoses.pctOfClaims_5_year": string[];
    "DRG_diagnoses.pctOfClaims_2_year": string[];
    "DRG_diagnoses.pctOfClaims_1_year": string[];
    "DRG_diagnoses.pctOfUniqueClaims": string[];
    "DRG_diagnoses.pctOfUniqueClaims_5_year": string[];
    "DRG_diagnoses.pctOfUniqueClaims_2_year": string[];
    "DRG_diagnoses.pctOfUniqueClaims_1_year": string[];
    "DRG_diagnoses.description_eng.keyword": string[];
    "DRG_diagnoses.codeScheme.keyword": string[];
  };
}

interface DiagnosisCcsrHit {
  fields: {
    "ccsr.description_eng": string[];
    "ccsr.hcp_quartile": number[];
    "ccsr.hcp_quartile_5_year": number[];
    "ccsr.hcp_quartile_2_year": number[];
    "ccsr.hcp_quartile_1_year": number[];
    "ccsr.pctOfCcsrPatients": string[];
    "ccsr.pctOfCcsrPatients_5_year": string[];
    "ccsr.pctOfCcsrPatients_2_year": string[];
    "ccsr.pctOfCcsrPatients_1_year": string[];
    "ccsr.internalCount": number[];
    "ccsr.internalCount_5_year": number[];
    "ccsr.internalCount_2_year": number[];
    "ccsr.internalCount_1_year": number[];
    "ccsr.internalUniqueCount": number[];
    "ccsr.internalUniqueCount_5_year": number[];
    "ccsr.internalUniqueCount_2_year": number[];
    "ccsr.internalUniqueCount_1_year": number[];
  };
}

interface ProcedureCcsrHit {
  fields: {
    "ccsr_px.description_eng": string[];
    "ccsr_px.hcp_quartile": number[];
    "ccsr_px.hcp_quartile_5_year": number[];
    "ccsr_px.hcp_quartile_2_year": number[];
    "ccsr_px.hcp_quartile_1_year": number[];
    "ccsr_px.pctOfCcsrPatients": string[];
    "ccsr_px.pctOfCcsrPatients_5_year": string[];
    "ccsr_px.pctOfCcsrPatients_2_year": string[];
    "ccsr_px.pctOfCcsrPatients_1_year": string[];
    "ccsr_px.internalCount": number[];
    "ccsr_px.internalCount_5_year": number[];
    "ccsr_px.internalCount_2_year": number[];
    "ccsr_px.internalCount_1_year": number[];
    "ccsr_px.internalUniqueCount": number[];
    "ccsr_px.internalUniqueCount_5_year": number[];
    "ccsr_px.internalUniqueCount_2_year": number[];
    "ccsr_px.internalUniqueCount_1_year": number[];
  };
}

interface ProcedureHit {
  fields: {
    "DRG_procedures.procedureCode_eng.keyword": string[];
    "DRG_procedures.internalCount": number[];
    "DRG_procedures.internalCount_5_year": number[];
    "DRG_procedures.internalCount_2_year": number[];
    "DRG_procedures.internalCount_1_year": number[];
    "DRG_procedures.internalUniqueCount": number[];
    "DRG_procedures.internalUniqueCount_5_year": number[];
    "DRG_procedures.internalUniqueCount_2_year": number[];
    "DRG_procedures.internalUniqueCount_1_year": number[];
    "DRG_procedures.percentage": string[];
    "DRG_procedures.percentage_5_year": string[];
    "DRG_procedures.percentage_2_year": string[];
    "DRG_procedures.percentage_1_year": string[];
    "DRG_procedures.uniquePercentage": string[];
    "DRG_procedures.uniquePercentage_5_year": string[];
    "DRG_procedures.uniquePercentage_2_year": string[];
    "DRG_procedures.uniquePercentage_1_year": string[];
    "DRG_procedures.description_eng.keyword": string[];
    "DRG_procedures.codeScheme": string[];
  };
}

interface PrescriptionsHit {
  fields: {
    "prescriptions.generic_name": string[];
    "prescriptions.brand_name": string[];
    "prescriptions.drug_class": string[];
    "prescriptions.num_prescriptions": number[];
    "prescriptions.num_prescriptions_5_year": number[];
    "prescriptions.num_prescriptions_2_year": number[];
    "prescriptions.num_prescriptions_1_year": number[];
    "prescriptions.patient_count": number[];
    "prescriptions.patient_count_1_year": number[];
    "prescriptions.patient_count_2_year": number[];
    "prescriptions.patient_count_5_year": number[];
    "prescriptions.rank_max_prescriptions": number[];
    "prescriptions.rank_1_year_prescriptions": number[];
    "prescriptions.rank_2_year_prescriptions": number[];
    "prescriptions.rank_5_year_prescriptions": number[];
    "prescriptions.hcp_quartile": number[];
    "prescriptions.hcp_quartile_1_year": number[];
    "prescriptions.hcp_quartile_2_year": number[];
    "prescriptions.hcp_quartile_5_year": number[];
    "prescriptions.pctOfUniquePatients": number[];
    "prescriptions.pctOfUniquePatients_1_year": number[];
    "prescriptions.pctOfUniquePatients_2_year": number[];
    "prescriptions.pctOfUniquePatients_5_year": number[];
  };
}

function buildTermClaimFilter(path: string, terms?: string[]) {
  const filter = [] as any[];
  if (terms && terms.length) {
    terms.forEach((e) => {
      filter.push({
        match_phrase: {
          [path]: e
        }
      });
    });
  }
  return filter;
}

const getProceduresSortBy = (
  sortBy: ProcedureSortFields,
  rangeFields: ProceduresRangeFields
) => {
  switch (sortBy) {
    case ProcedureSortFields.CodeScheme:
      return ProceduresFields.CodeScheme;
    case ProcedureSortFields.Description:
      return ProceduresFields.Description;
    case ProcedureSortFields.ProcedureCode:
      return ProceduresFields.ProcedureCode;
    case ProcedureSortFields.Count:
      return rangeFields.internalCount;
    case ProcedureSortFields.PercentOfClaims:
      return rangeFields.percent;
    default:
      return rangeFields.internalCount;
  }
};

const getPrescriptionsSortBy = (
  sortBy: PrescriptionsSortFields,
  rangeFields: PrescriptionsRangeFields
) => {
  switch (sortBy) {
    case PrescriptionsSortFields.PrescriptionsCount:
      return rangeFields.numPrescriptions;
    case PrescriptionsSortFields.PatientCount:
      return rangeFields.patientCount;
    case PrescriptionsSortFields.GenericName:
      return PrescriptionsFields.GenericName;
    case PrescriptionsSortFields.BrandName:
      return PrescriptionsFields.BrandName;
    case PrescriptionsSortFields.Rank:
      return rangeFields.rank;
    case PrescriptionsSortFields.Quartile:
      return rangeFields.quartile;
    case PrescriptionsSortFields.Percentage:
      return rangeFields.percentage;
    default:
      return rangeFields.numPrescriptions;
  }
};

const getDiagnosesSortBy = (
  sortBy: DiagnosesSortFields,
  rangeFields: DiagnosesRangeFields
) => {
  switch (sortBy) {
    case DiagnosesSortFields.CodeScheme:
      return DiagnosesFields.CodeScheme;
    case DiagnosesSortFields.Description:
      return DiagnosesFields.Description;
    case DiagnosesSortFields.DiagnosisCode:
      return DiagnosesFields.DiagnosisCode;
    case DiagnosesSortFields.Count:
      return rangeFields.internalCount;
    case DiagnosesSortFields.PercentOfClaims:
      return rangeFields.percent;
    default:
      return rangeFields.internalCount;
  }
};
const getDiagnosesCcsrSortBy = (
  sortBy: DiagnosesCcsrSortFields,
  rangeFields: DiagnosesCcsrRangeFields
) => {
  switch (sortBy) {
    case DiagnosesCcsrSortFields.Description:
      return DiagnosesCareClustersFields.Description;
    case DiagnosesCcsrSortFields.HcpQuartile:
      return rangeFields.hcpQuartile;
    case DiagnosesCcsrSortFields.PercentOfClaims:
      return rangeFields.internalUniqueCount;
    default:
      return rangeFields.internalUniqueCount;
  }
};

const getProceduresCcsrSortBy = (
  sortBy: ProceduresCcsrSortFields,
  rangeFields: ProceduresCcsrRangeFields
) => {
  switch (sortBy) {
    case ProceduresCcsrSortFields.Description:
      return ProceduresCareClustersFields.Description;
    case ProceduresCcsrSortFields.HcpQuartile:
      return rangeFields.hcpQuartile;
    case ProceduresCcsrSortFields.PercentOfClaims:
      return rangeFields.internalUniqueCount;
    default:
      return rangeFields.internalUniqueCount;
  }
};

export const getDiagnosesRangeAdjustedFields = (
  range: ClaimsFilterRange
): DiagnosesRangeFields => {
  switch (range) {
    case ClaimsFilterRange.max:
      return {
        diagnosesCount: "DRG_diagnosesCount",
        internalCount: DiagnosesFields.InternalCount,
        percent: DiagnosesFields.PercentOfClaims
      };
    case ClaimsFilterRange.fiveYear:
      return {
        diagnosesCount: "DRG_diagnosesCount_5_year",
        internalCount: DiagnosesFields.InternalCount5Year,
        percent: DiagnosesFields.PercentOfClaims5Year
      };
    case ClaimsFilterRange.twoYear:
      return {
        diagnosesCount: "DRG_diagnosesCount_2_year",
        internalCount: DiagnosesFields.InternalCount2Year,
        percent: DiagnosesFields.PercentOfClaims2Year
      };
    case ClaimsFilterRange.oneYear:
      return {
        diagnosesCount: "DRG_diagnosesCount_1_year",
        internalCount: DiagnosesFields.InternalCount1Year,
        percent: DiagnosesFields.PercentOfClaims1Year
      };
    default:
      return {
        diagnosesCount: "DRG_diagnosesCount",
        internalCount: DiagnosesFields.InternalCount,
        percent: DiagnosesFields.PercentOfClaims
      };
  }
};
export const getDiagnosesRangeAdjustedFieldsForUniquePatientCount = (
  range: ClaimsFilterRange
): DiagnosesRangeFields => {
  switch (range) {
    case ClaimsFilterRange.max:
      return {
        diagnosesCount: "DRG_diagnosesUniqueCount",
        internalCount: DiagnosesFields.InternalUniqueCount,
        percent: DiagnosesFields.PercentOfUniqueClaims
      };
    case ClaimsFilterRange.fiveYear:
      return {
        diagnosesCount: "DRG_diagnosesUniqueCount_5_year",
        internalCount: DiagnosesFields.InternalUniqueCount5Year,
        percent: DiagnosesFields.PercentOfUniqueClaims5Year
      };
    case ClaimsFilterRange.twoYear:
      return {
        diagnosesCount: "DRG_diagnosesUniqueCount_2_year",
        internalCount: DiagnosesFields.InternalUniqueCount2Year,
        percent: DiagnosesFields.PercentOfUniqueClaims2Year
      };
    case ClaimsFilterRange.oneYear:
      return {
        diagnosesCount: "DRG_diagnosesUniqueCount_1_year",
        internalCount: DiagnosesFields.InternalUniqueCount1Year,
        percent: DiagnosesFields.PercentOfUniqueClaims1Year
      };
    default:
      return {
        diagnosesCount: "DRG_diagnosesUniqueCount",
        internalCount: DiagnosesFields.InternalUniqueCount,
        percent: DiagnosesFields.PercentOfUniqueClaims
      };
  }
};

export const getDiagnosesCcsrRangeAdjustedFieldsForUniquePatientCount = (
  range: ClaimsFilterRange
): DiagnosesCcsrRangeFields => {
  switch (range) {
    case ClaimsFilterRange.max:
      return {
        diagnosesPatientCount: "DRG_diagnosesUniqueCount",
        percent: DiagnosesCareClustersFields.PercentOfUniqueClaims,
        internalUniqueCount: DiagnosesCareClustersFields.InternalUniqueCount,
        internalCount: DiagnosesCareClustersFields.InternalCount,
        hcpQuartile: DiagnosesCareClustersFields.HcpQuartile
      };
    case ClaimsFilterRange.fiveYear:
      return {
        diagnosesPatientCount: "DRG_diagnosesUniqueCount_5_year",
        percent: DiagnosesCareClustersFields.PercentOfUniqueClaims5Year,
        internalUniqueCount:
          DiagnosesCareClustersFields.InternalUniqueCount5Year,
        internalCount: DiagnosesCareClustersFields.InternalCount5Year,
        hcpQuartile: DiagnosesCareClustersFields.HcpQuartile5Year
      };
    case ClaimsFilterRange.twoYear:
      return {
        diagnosesPatientCount: "DRG_diagnosesUniqueCount_2_year",
        percent: DiagnosesCareClustersFields.PercentOfUniqueClaims2Year,
        internalUniqueCount:
          DiagnosesCareClustersFields.InternalUniqueCount2Year,
        internalCount: DiagnosesCareClustersFields.InternalCount2Year,
        hcpQuartile: DiagnosesCareClustersFields.HcpQuartile2Year
      };
    case ClaimsFilterRange.oneYear:
      return {
        diagnosesPatientCount: "DRG_diagnosesUniqueCount_1_year",
        percent: DiagnosesCareClustersFields.PercentOfUniqueClaims1Year,
        internalUniqueCount:
          DiagnosesCareClustersFields.InternalUniqueCount1Year,
        internalCount: DiagnosesCareClustersFields.InternalCount1Year,
        hcpQuartile: DiagnosesCareClustersFields.HcpQuartile1Year
      };
    default:
      return {
        diagnosesPatientCount: "DRG_diagnosesUniqueCount",
        percent: DiagnosesCareClustersFields.PercentOfUniqueClaims,
        internalUniqueCount: DiagnosesCareClustersFields.InternalUniqueCount,
        internalCount: DiagnosesCareClustersFields.InternalCount,
        hcpQuartile: DiagnosesCareClustersFields.HcpQuartile
      };
  }
};

export const getProceduresCcsrRangeAdjustedFieldsForUniquePatientCount = (
  range: ClaimsFilterRange
): ProceduresCcsrRangeFields => {
  switch (range) {
    case ClaimsFilterRange.max:
      return {
        proceduresPatientCount: "DRG_proceduresUniqueCount",
        percent: ProceduresCareClustersFields.PercentOfUniqueClaims,
        internalUniqueCount: ProceduresCareClustersFields.InternalUniqueCount,
        internalCount: ProceduresCareClustersFields.InternalCount,
        hcpQuartile: ProceduresCareClustersFields.HcpQuartile
      };
    case ClaimsFilterRange.fiveYear:
      return {
        proceduresPatientCount: "DRG_proceduresUniqueCount_5_year",
        percent: ProceduresCareClustersFields.PercentOfUniqueClaims5Year,
        internalUniqueCount:
          ProceduresCareClustersFields.InternalUniqueCount5Year,
        internalCount: ProceduresCareClustersFields.InternalCount5Year,
        hcpQuartile: ProceduresCareClustersFields.HcpQuartile5Year
      };
    case ClaimsFilterRange.twoYear:
      return {
        proceduresPatientCount: "DRG_proceduresUniqueCount_2_year",
        percent: ProceduresCareClustersFields.PercentOfUniqueClaims2Year,
        internalUniqueCount:
          ProceduresCareClustersFields.InternalUniqueCount2Year,
        internalCount: ProceduresCareClustersFields.InternalCount2Year,
        hcpQuartile: ProceduresCareClustersFields.HcpQuartile2Year
      };
    case ClaimsFilterRange.oneYear:
      return {
        proceduresPatientCount: "DRG_proceduresUniqueCount_1_year",
        percent: ProceduresCareClustersFields.PercentOfUniqueClaims1Year,
        internalUniqueCount:
          ProceduresCareClustersFields.InternalUniqueCount1Year,
        internalCount: ProceduresCareClustersFields.InternalCount1Year,
        hcpQuartile: ProceduresCareClustersFields.HcpQuartile1Year
      };
    default:
      return {
        proceduresPatientCount: "DRG_proceduresUniqueCount",
        percent: ProceduresCareClustersFields.PercentOfUniqueClaims,
        internalUniqueCount: ProceduresCareClustersFields.InternalUniqueCount,
        internalCount: ProceduresCareClustersFields.InternalCount,
        hcpQuartile: ProceduresCareClustersFields.HcpQuartile
      };
  }
};

export const getProceduresRangeAdjustedFields = (
  range: ClaimsFilterRange
): ProceduresRangeFields => {
  switch (range) {
    case ClaimsFilterRange.max:
      return {
        proceduresCount: "DRG_proceduresCount",
        internalCount: ProceduresFields.InternalCount,
        percent: ProceduresFields.PercentOfClaims
      };
    case ClaimsFilterRange.fiveYear:
      return {
        proceduresCount: "DRG_proceduresCount_5_year",
        internalCount: ProceduresFields.InternalCount5Year,
        percent: ProceduresFields.PercentOfClaims5Year
      };
    case ClaimsFilterRange.twoYear:
      return {
        proceduresCount: "DRG_proceduresCount_2_year",
        internalCount: ProceduresFields.InternalCount2Year,
        percent: ProceduresFields.PercentOfClaims2Year
      };
    case ClaimsFilterRange.oneYear:
      return {
        proceduresCount: "DRG_proceduresCount_1_year",
        internalCount: ProceduresFields.InternalCount1Year,
        percent: ProceduresFields.PercentOfClaims1Year
      };
    default:
      return {
        proceduresCount: "DRG_proceduresCount",
        internalCount: ProceduresFields.InternalCount,
        percent: ProceduresFields.PercentOfClaims
      };
  }
};

export const getPrescriptionsRangeAdjustedFields = (
  range: ClaimsFilterRange
): PrescriptionsRangeFields => {
  switch (range) {
    case ClaimsFilterRange.max:
      return {
        numPrescriptions: PrescriptionsFields.NumPrescriptions,
        totalNumPrescriptions: PrescriptionsFields.TotalNumPrescriptions,
        patientCount: PrescriptionsFields.PatientCount,
        totalPatientCount: PrescriptionsFields.TotalPatientCount,
        rank: PrescriptionsFields.Rank,
        quartile: PrescriptionsFields.Quartile,
        percentage: PrescriptionsFields.Percentage
      };
    case ClaimsFilterRange.fiveYear:
      return {
        numPrescriptions: PrescriptionsFields.NumPrescriptions5Year,
        totalNumPrescriptions: PrescriptionsFields.TotalNumPrescriptions5Year,
        patientCount: PrescriptionsFields.PatientCount5Year,
        totalPatientCount: PrescriptionsFields.TotalPatientCount5Year,
        rank: PrescriptionsFields.Rank5Year,
        quartile: PrescriptionsFields.Quartile5Year,
        percentage: PrescriptionsFields.Percentage5Year
      };
    case ClaimsFilterRange.twoYear:
      return {
        numPrescriptions: PrescriptionsFields.NumPrescriptions2Year,
        totalNumPrescriptions: PrescriptionsFields.TotalNumPrescriptions2Year,
        patientCount: PrescriptionsFields.PatientCount2Year,
        totalPatientCount: PrescriptionsFields.TotalPatientCount2Year,
        rank: PrescriptionsFields.Rank2Year,
        quartile: PrescriptionsFields.Quartile2Year,
        percentage: PrescriptionsFields.Percentage2Year
      };
    case ClaimsFilterRange.oneYear:
      return {
        numPrescriptions: PrescriptionsFields.NumPrescriptions1Year,
        totalNumPrescriptions: PrescriptionsFields.TotalNumPrescriptions1Year,
        patientCount: PrescriptionsFields.PatientCount1Year,
        totalPatientCount: PrescriptionsFields.TotalPatientCount1Year,
        rank: PrescriptionsFields.Rank1Year,
        quartile: PrescriptionsFields.Quartile1Year,
        percentage: PrescriptionsFields.Percentage1Year
      };
    default:
      return {
        numPrescriptions: PrescriptionsFields.NumPrescriptions,
        totalNumPrescriptions: PrescriptionsFields.TotalNumPrescriptions,
        patientCount: PrescriptionsFields.PatientCount,
        totalPatientCount: PrescriptionsFields.TotalPatientCount,
        rank: PrescriptionsFields.Rank,
        quartile: PrescriptionsFields.Quartile,
        percentage: PrescriptionsFields.Percentage
      };
  }
};

export const getProceduresRangeAdjustedFieldsForUniquePatientCount = (
  range: ClaimsFilterRange
): ProceduresRangeFields => {
  switch (range) {
    case ClaimsFilterRange.max:
      return {
        proceduresCount: "DRG_proceduresUniqueCount",
        internalCount: ProceduresFields.InternalUniqueCount,
        percent: ProceduresFields.PercentOfUniqueClaims
      };
    case ClaimsFilterRange.fiveYear:
      return {
        proceduresCount: "DRG_proceduresUniqueCount_5_year",
        internalCount: ProceduresFields.InternalUniqueCount5Year,
        percent: ProceduresFields.PercentOfUniqueClaims5Year
      };
    case ClaimsFilterRange.twoYear:
      return {
        proceduresCount: "DRG_proceduresUniqueCount_2_year",
        internalCount: ProceduresFields.InternalUniqueCount2Year,
        percent: ProceduresFields.PercentOfUniqueClaims2Year
      };
    case ClaimsFilterRange.oneYear:
      return {
        proceduresCount: "DRG_proceduresUniqueCount_1_year",
        internalCount: ProceduresFields.InternalUniqueCount1Year,
        percent: ProceduresFields.PercentOfUniqueClaims1Year
      };
    default:
      return {
        proceduresCount: "DRG_proceduresUniqueCount",
        internalCount: ProceduresFields.InternalUniqueCount,
        percent: ProceduresFields.PercentOfUniqueClaims
      };
  }
};

const buildDiagnosesDocValueFields = (rangeFields: DiagnosesRangeFields) => {
  return [
    {
      field: DiagnosesFields.Description
    },
    {
      field: DiagnosesFields.DiagnosisCode
    },
    {
      field: rangeFields.percent
    },
    {
      field: rangeFields.internalCount
    },
    {
      field: DiagnosesFields.CodeScheme
    }
  ];
};

const buildDiagnosesCcsrDocValueFields = (
  rangeFields: DiagnosesCcsrRangeFields
) => {
  return [
    {
      field: DiagnosesCareClustersFields.Description
    },
    {
      field: rangeFields.percent
    },
    {
      field: rangeFields.hcpQuartile
    },
    {
      field: rangeFields.internalUniqueCount
    }
  ];
};

const buildProceduresCcsrDocValueFields = (
  rangeFields: ProceduresCcsrRangeFields
) => {
  return [
    {
      field: ProceduresCareClustersFields.Description
    },
    {
      field: rangeFields.percent
    },
    {
      field: rangeFields.hcpQuartile
    },
    {
      field: rangeFields.internalUniqueCount
    }
  ];
};

const buildProceduresDocValueFields = (rangeFields: ProceduresRangeFields) => {
  return [
    {
      field: ProceduresFields.Description
    },
    {
      field: ProceduresFields.ProcedureCode
    },
    {
      field: rangeFields.percent
    },
    {
      field: rangeFields.internalCount
    },
    {
      field: ProceduresFields.CodeScheme
    }
  ];
};

export function buildDiagnosesQuery(
  personId: string,
  rangeFields: DiagnosesRangeFields,
  terms: string[] = [],
  size = DEFAULT_RESULT_SIZE,
  from = DEFAULT_RESULT_OFFSET,
  sortBy: DiagnosesSortFields = DiagnosesSortFields.Count,
  sortDirection = SortDirection.Desc,
  ccsrClaimCodes: string[] = []
): SearchRequest {
  const safeSortBy = getDiagnosesSortBy(sortBy, rangeFields);

  return {
    _source: rangeFields.diagnosesCount,
    size: 1,
    query: {
      bool: {
        must: [
          {
            nested: {
              path: "DRG_diagnoses",
              query: {
                bool: {
                  must: [
                    {
                      match_all: {}
                    }
                  ],
                  filter: [
                    {
                      bool: {
                        must: [],
                        should: [
                          ...buildTermClaimFilter(
                            "DRG_diagnoses.description_eng",
                            terms
                          ),
                          ...buildTermClaimFilter(
                            "DRG_diagnoses.diagnosisCode_eng.autocomplete_search",
                            terms
                          ),
                          ...(ccsrClaimCodes.length
                            ? [
                                buildTermsQuery(
                                  "DRG_diagnoses.diagnosisCode_eng",
                                  ccsrClaimCodes
                                )
                              ]
                            : [])
                        ]
                      }
                    },
                    {
                      range: {
                        [rangeFields.internalCount]: {
                          gte: 1
                        }
                      }
                    }
                  ]
                }
              },
              inner_hits: {
                size,
                from,
                _source: false,
                sort: {
                  [safeSortBy]: {
                    order: sortDirection
                  }
                },
                docvalue_fields: buildDiagnosesDocValueFields(rangeFields)
              }
            }
          }
        ],
        filter: [
          {
            term: {
              id: personId
            }
          }
        ]
      }
    }
  };
}

export function buildProceduresQuery(
  personId: string,
  rangeFields: ProceduresRangeFields,
  terms: string[] = [],
  size = DEFAULT_RESULT_SIZE,
  from = DEFAULT_RESULT_OFFSET,
  sortBy: ProcedureSortFields = ProcedureSortFields.Count,
  sortDirection = SortDirection.Desc
) {
  const safeSortBy = getProceduresSortBy(sortBy, rangeFields);

  return {
    _source: rangeFields.proceduresCount,
    size: 1,
    query: {
      bool: {
        must: [
          {
            nested: {
              path: "DRG_procedures",
              query: {
                bool: {
                  must: [
                    {
                      match_all: {}
                    }
                  ],
                  filter: [
                    {
                      bool: {
                        must: [],
                        should: [
                          ...buildTermClaimFilter(
                            "DRG_procedures.description_eng",
                            terms
                          ),
                          ...buildTermClaimFilter(
                            "DRG_procedures.procedureCode_eng.autocomplete_search",
                            terms
                          )
                        ]
                      }
                    },
                    {
                      range: {
                        [rangeFields.internalCount]: {
                          gte: 1
                        }
                      }
                    }
                  ]
                }
              },
              inner_hits: {
                size,
                from,
                _source: false,
                sort: {
                  [safeSortBy]: {
                    order: sortDirection
                  }
                },
                docvalue_fields: buildProceduresDocValueFields(rangeFields)
              }
            }
          }
        ],
        filter: [
          {
            term: {
              id: personId
            }
          }
        ]
      }
    }
  };
}

export function buildPrescriptionsQuery(
  personId: string,
  rangeFields: PrescriptionsRangeFields,
  terms: string[] = [],
  size = DEFAULT_RESULT_SIZE,
  from = DEFAULT_RESULT_OFFSET,
  sortBy: PrescriptionsSortFields = PrescriptionsSortFields.PrescriptionsCount,
  sortDirection = SortDirection.Desc
) {
  const safeSortBy = getPrescriptionsSortBy(sortBy, rangeFields);

  return {
    _source: rangeFields.totalPatientCount,
    size: 1,
    query: {
      bool: {
        must: [
          {
            nested: {
              path: "prescriptions",
              query: {
                bool: {
                  must: [
                    {
                      match_all: {}
                    }
                  ],
                  filter: [
                    {
                      bool: {
                        must: [],
                        should: [
                          ...buildTermClaimFilter(
                            "prescriptions.generic_name.text",
                            terms
                          ),
                          ...buildTermClaimFilter(
                            "prescriptions.generic_name.autocomplete_search",
                            terms
                          ),
                          ...buildTermClaimFilter(
                            "prescriptions.brand_name.text",
                            terms
                          ),
                          ...buildTermClaimFilter(
                            "prescriptions.brand_name.autocomplete_search",
                            terms
                          ),
                          ...buildTermClaimFilter(
                            "prescriptions.drug_class.text",
                            terms
                          ),
                          ...buildTermClaimFilter(
                            "prescriptions.drug_class.autocomplete_search",
                            terms
                          )
                        ]
                      }
                    },
                    {
                      range: {
                        [rangeFields.numPrescriptions]: {
                          gte: 1
                        }
                      }
                    }
                  ]
                }
              },
              inner_hits: {
                size,
                from,
                _source: false,
                sort: {
                  [safeSortBy]: {
                    order: sortDirection
                  }
                },
                docvalue_fields: [
                  {
                    field: PrescriptionsFields.GenericName
                  },
                  {
                    field: PrescriptionsFields.BrandName
                  },
                  {
                    field: PrescriptionsFields.DrugClass
                  },
                  {
                    field: rangeFields.numPrescriptions
                  },
                  {
                    field: rangeFields.patientCount
                  },
                  {
                    field: rangeFields.rank
                  },
                  {
                    field: rangeFields.quartile
                  },
                  {
                    field: rangeFields.percentage
                  }
                ]
              }
            }
          }
        ],
        filter: [
          {
            term: {
              id: personId
            }
          }
        ]
      }
    }
  };
}

export function buildClaimsTotalsQuery(
  personId: string,
  procedureEncounterRangeFields: ProceduresRangeFields,
  diagnosesEncounterRangeFields: DiagnosesRangeFields,
  prescriptionsRangeFields: PrescriptionsRangeFields,
  procedurePatientRangeFields: ProceduresRangeFields,
  diagnosesPatientRangeFields: DiagnosesRangeFields
) {
  return {
    _source: [
      diagnosesEncounterRangeFields.diagnosesCount,
      procedureEncounterRangeFields.proceduresCount,
      prescriptionsRangeFields.totalNumPrescriptions,
      prescriptionsRangeFields.totalPatientCount,
      diagnosesPatientRangeFields.diagnosesCount,
      procedurePatientRangeFields.proceduresCount
    ],
    size: 1,
    query: {
      bool: {
        filter: [
          {
            term: {
              id: personId
            }
          }
        ]
      }
    }
  };
}

export function buildDiagnosesCcsrQuery(
  personId: string,
  rangeFields: DiagnosesCcsrRangeFields,
  terms: string[] = [],
  size = DEFAULT_RESULT_SIZE,
  from = DEFAULT_RESULT_OFFSET,
  sortBy: DiagnosesCcsrSortFields = DiagnosesCcsrSortFields.PercentOfClaims,
  sortDirection = SortDirection.Desc
): SearchRequest {
  const safeSortBy = getDiagnosesCcsrSortBy(sortBy, rangeFields);
  const termsQuery = terms.length
    ? [buildTermsQuery("ccsr.description_eng", terms)]
    : [];
  return {
    _source: rangeFields.diagnosesPatientCount,
    size: 1,
    query: {
      bool: {
        must: [
          {
            nested: {
              path: "ccsr",
              query: {
                bool: {
                  must: [
                    {
                      match_all: {}
                    }
                  ],
                  filter: [
                    ...termsQuery,
                    {
                      range: {
                        [rangeFields.internalUniqueCount]: {
                          gt: 0
                        }
                      }
                    }
                  ]
                }
              },
              inner_hits: {
                size,
                from,
                _source: false,
                sort: [
                  {
                    [safeSortBy]: {
                      order: sortDirection
                    }
                  },
                  {
                    [rangeFields.internalUniqueCount]: {
                      order: "desc"
                    }
                  },
                  {
                    [DiagnosesCareClustersFields.Description]: {
                      order: "asc"
                    }
                  }
                ],
                docvalue_fields: buildDiagnosesCcsrDocValueFields(rangeFields)
              }
            }
          }
        ],
        filter: [
          {
            term: {
              id: personId
            }
          }
        ]
      }
    }
  };
}

export function buildProceduresCcsrQuery(
  personId: string,
  rangeFields: ProceduresCcsrRangeFields,
  terms: string[] = [],
  size = DEFAULT_RESULT_SIZE,
  from = DEFAULT_RESULT_OFFSET,
  sortBy: ProceduresCcsrSortFields = ProceduresCcsrSortFields.PercentOfClaims,
  sortDirection = SortDirection.Desc
): SearchRequest {
  const safeSortBy = getProceduresCcsrSortBy(sortBy, rangeFields);
  const termsQuery = terms.length
    ? [buildTermsQuery("ccsr_px.description_eng", terms)]
    : [];
  return {
    _source: rangeFields.proceduresPatientCount,
    size: 1,
    query: {
      bool: {
        must: [
          {
            nested: {
              path: "ccsr_px",
              query: {
                bool: {
                  must: [
                    {
                      match_all: {}
                    }
                  ],
                  filter: [
                    ...termsQuery,
                    {
                      range: {
                        [rangeFields.internalUniqueCount]: {
                          gt: 0
                        }
                      }
                    }
                  ]
                }
              },
              inner_hits: {
                size,
                from,
                _source: false,
                sort: [
                  {
                    [safeSortBy]: {
                      order: sortDirection
                    }
                  },
                  {
                    [rangeFields.internalUniqueCount]: {
                      order: "desc"
                    }
                  },
                  {
                    [ProceduresCareClustersFields.Description]: {
                      order: "asc"
                    }
                  }
                ],
                docvalue_fields: buildProceduresCcsrDocValueFields(rangeFields)
              }
            }
          }
        ],
        filter: [
          {
            term: {
              id: personId
            }
          }
        ]
      }
    }
  };
}
