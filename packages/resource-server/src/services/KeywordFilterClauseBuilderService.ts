import {
  QueryDslBoolQuery,
  QueryDslFunctionScoreQuery,
  QueryDslNestedQuery,
  QueryDslQueryContainer,
  QueryDslRangeQuery
} from "@elastic/elasticsearch/lib/api/types";
import {
  FilterInterface,
  KeywordFilterAutocompleteFilterField,
  KeywordFilterAutocompleteInput,
  KeywordSearchInput,
  DigitalLeaderTypes,
  PastDateRangeEnumFilter,
  CareerLengthEnumFilter,
  SearchSliceOptionsEnum,
  Apps
} from "@h1nyc/search-sdk";
import { NameSearchInput } from "@h1nyc/search-sdk/dist/interfaces/NameSearchInput";
import { SavedTerritory } from "@h1nyc/account-user-entities";
import { EntityType } from "@h1nyc/account-user-entities";
import { Service } from "typedi";
import _ from "lodash";
import { subYears } from "date-fns";

import { TagsHelperService } from "./TagsHelperService";
import {
  LanguageDetector,
  ParsedQueryTree,
  KeywordSearchFeatureFlags,
  ClaimType,
  publicationsQueryFields,
  getLeaderScoreField
} from "../services/KeywordSearchResourceServiceRewrite";
import { ParsedQueryTreeToElasticsearchQueriesService } from "./ParsedQueryTreeToElasticsearchQueries";
import { AutocompleteFeatureFlags } from "./KeywordAutocompleteResourceService";
import { CHINESE, ENGLISH, JAPANESE } from "./LanguageDetectService";
import { Trace } from "../Tracer";
import { createLogger } from "../lib/Logger";
import { NameSearchFeatureFlags } from "./NameSearchResourceServiceRewrite";
import { Language } from "./LanguageDetectService";
import { buildClaimsRegionFilterForPeopleSearch } from "../util/ClaimsRegionFilterUtils";
import { RulesParserService } from "./RulesParserService";
import { GeometryCollection, Polygon } from "geojson";
import { NestedPath } from "../util/QueryParsingUtils";
import {
  buildFieldExistsQuery,
  buildFieldMustNotExistsQuery,
  buildTermQuery,
  buildTermsQuery,
  buildBoolShouldQuery,
  buildMatchPhrasePrefixQuery
} from "../util/QueryBuildingUtils";
import { REGION_CODES_BY_COUNTRY_CODE_AND_REGION_NAME } from "../lib/data/regions";
import { COUNTRY_NAME_TO_CODE } from "../lib/data/countries";
import { BothQueryParseResult } from "../lib/ParserTypes/types";
import { QueryParserService } from "./QueryParserService";

type Context = "search" | "autocomplete";

type AssetPath = "congress" | "trials" | "publications";

export type FeatureFlags =
  | KeywordSearchFeatureFlags
  | AutocompleteFeatureFlags
  | NameSearchFeatureFlags;

export const raceFilterValueToPatientsDiversityRatioFieldMap: ReadonlyMap<
  string,
  string
> = new Map<string, string>([
  ["American Indian Or Alaska Native", "americanIndianOrAlaskaNative"],
  ["Asian Pacific Islander", "asianPacificIslander"],
  ["Black Non-Hispanic", "blackNonHispanic"],
  ["Hispanic", "hispanic"],
  ["White Non-Hispanic", "whiteNonHispanic"],
  ["Not Identified", "notIdentified"],
  ["Indigenous (Brazil Only)", "indigenousBrazil"],
  ["Mixed (Brazil Only)", "mixedBrazil"]
]);

export const MINIMUM_RACE_THRESHOLD = 0;
export const MAXIMUM_RACE_THRESHOLD = 1;

type ValueRange = {
  min: number | null;
  max: number | null;
};

type RaceThreshold = {
  key: string;
  min?: number;
  max?: number;
};

type DiagnosesInternalCountField =
  | "DRG_diagnoses.internalUniqueCount"
  | "DRG_diagnoses.internalUniqueCount_1_year"
  | "DRG_diagnoses.internalUniqueCount_2_year"
  | "DRG_diagnoses.internalUniqueCount_5_year"
  | "DRG_diagnoses.internalCount"
  | "DRG_diagnoses.internalCount_1_year"
  | "DRG_diagnoses.internalCount_2_year"
  | "DRG_diagnoses.internalCount_5_year";
type ProceduresInternalCountField =
  | "DRG_procedures.internalUniqueCount"
  | "DRG_procedures.internalUniqueCount_1_year"
  | "DRG_procedures.internalUniqueCount_2_year"
  | "DRG_procedures.internalUniqueCount_5_year"
  | "DRG_procedures.internalCount"
  | "DRG_procedures.internalCount_1_year"
  | "DRG_procedures.internalCount_2_year"
  | "DRG_procedures.internalCount_5_year";
type PrescriptionsInternalCountField =
  | "prescriptions.num_prescriptions"
  | "prescriptions.num_prescriptions_1_year"
  | "prescriptions.num_prescriptions_2_year"
  | "prescriptions.num_prescriptions_5_year";

type CcsrInternalCountField =
  | "ccsr.internalUniqueCount"
  | "ccsr.internalUniqueCount_1_year"
  | "ccsr.internalUniqueCount_2_year"
  | "ccsr.internalUniqueCount_5_year"
  | "ccsr.internalCount"
  | "ccsr.internalCount_1_year"
  | "ccsr.internalCount_2_year"
  | "ccsr.internalCount_5_year";

type FilterClauseBuilderOptions = {
  skipProjectIdFilter?: boolean;
};

export const AT_LEAST_ONE = 1;
export const DEFAULT_PROJECT_ID = "1";

const CLAIMS_CODE_AND_DESCRIPTION_SEPARATOR = " - ";

// TODO: We should have a separate filter for people that have CTMS data in the project slice
// const HAS_CTMS = toTermFilter("hasCtms", true);
const IN_CTMS_NETWORK = toTermFilter("inCtmsNetwork", true);

export const DEFAULT_GEO_BOUNDING_BOX = {
  bottom_right: {
    lat: -90,
    lon: 180
  },
  top_left: {
    lat: 90,
    lon: -180
  }
};

const HCPU_ADDRESSES = "addressesForHCPU";
const TL_ADDRESSES = "addressesForTL";

const AFFILIATIONS = "affiliations";
const AFFILIATIONS_INSTITUTION = `${AFFILIATIONS}.institution`;
const AFFILIATIONS_INSTITUTION_LOCATION = `${AFFILIATIONS_INSTITUTION}.location`;
const AFFILIATIONS_IS_CURRENT = toTermFilter(`${AFFILIATIONS}.isCurrent`, true);
const US_COUNTRY_FILTER = toTermFilter(
  "affiliations.institution.filters.country",
  "us"
);
const AFFILIATIONS_IS_WORK_TYPE = buildTermsQuery(`${AFFILIATIONS}.type`, [
  "Work Affiliation"
]);
const AFFILIATIONS_INSTITUTION_NAME = `${AFFILIATIONS_INSTITUTION}.name.keyword`;
const CUSTOM_TERRITORY_INNER_HITS_NAME = "custom_territory_affiliations";

@Service()
export class KeywordFilterClauseBuilderService {
  private readonly logger = createLogger(this);

  constructor(
    private queryParserService: QueryParserService,
    private tagsHelperService: TagsHelperService,
    private parsedQueryTreeToElasticsearchQueriesService: ParsedQueryTreeToElasticsearchQueriesService,
    private rulesParserService: RulesParserService
  ) {}

  /**
   * Until autocomplete and search treat filters for pubs/claims/payments/trials/congresses
   * the same, the cleanest way to support both is with separate entry points.  Here's why
   * these 5 filter types must be separate code paths:
   *
   * autocomplete: none of the 5 filter types are used in a nested context.
   *
   * search: all 5 filter types are used in a nested context
   *
   * Once autocomplete has been updated to support nested filters, then this distinction can
   * be removed.  There are JIRA tickets outlining the content of the work to achieve this so
   * this should pretty temporary.
   */

  async buildForAutocompleteRequest(
    input: KeywordFilterAutocompleteInput,
    languageDetector: LanguageDetector,
    featureFlags: AutocompleteFeatureFlags,
    parsedQueryTree?: ParsedQueryTree,
    territories?: SavedTerritory[]
  ): Promise<Array<QueryDslQueryContainer>> {
    return this.build(
      input,
      languageDetector,
      "autocomplete",
      featureFlags,
      parsedQueryTree,
      undefined,
      territories
    );
  }

  async buildFiltersForQueryIntentSearchRequest(
    input: KeywordSearchInput,
    languageDetector: LanguageDetector,
    featureFlags: KeywordSearchFeatureFlags,
    parsedQueryTree?: ParsedQueryTree,
    territories?: SavedTerritory[],
    bothParserResponse?: BothQueryParseResult,
    isLLMGenerated?: boolean
  ): Promise<{
    filtersWithParsedQuery: Array<QueryDslQueryContainer>;
    filtersWithoutParsedQuery: Array<QueryDslQueryContainer>;
  }> {
    const asyncFilters = await this.buildAsyncFilters(input, featureFlags);

    const filtersWithParsedQuery: QueryDslQueryContainer[] = [
      ...asyncFilters,
      ...this.buildFilters(
        input,
        languageDetector,
        "search",
        featureFlags,
        parsedQueryTree,
        undefined,
        territories,
        bothParserResponse,
        isLLMGenerated
      )
    ];
    const filtersWithoutParsedQuery = [
      ...asyncFilters,
      ...this.buildFilters(
        input,
        languageDetector,
        "search",
        featureFlags,
        undefined,
        undefined,
        territories,
        bothParserResponse,
        isLLMGenerated
      )
    ];

    return {
      filtersWithParsedQuery,
      filtersWithoutParsedQuery
    };
  }

  buildPatientClaimsFilter({
    suppliedFilters
  }: KeywordFilterAutocompleteInput) {
    return this.rulesParserService.parseRulesToEsQueries(suppliedFilters);
  }

  buildPatientPopulationFilter(
    input: KeywordSearchInput,
    featureFlags: KeywordSearchFeatureFlags
  ): QueryDslQueryContainer[] {
    const { suppliedFilters } = input;
    const filter =
      this.rulesParserService.parseRulesToEsQueries(suppliedFilters);
    const genericDrugNames =
      this.rulesParserService.extractGenericDrugNamesFromRules(suppliedFilters);
    const hasPrescriptionEligibilityCriteria = !_.isEmpty(genericDrugNames);
    const shouldIncludeRxPatientClaims =
      hasPrescriptionEligibilityCriteria ||
      hasPrescriptionFilters(suppliedFilters);
    const mustNotClauses = [];
    const diagnosesOrCcsrFilters = [];
    if (filter.length === 0) {
      if (suppliedFilters.claims?.diagnosesICD?.values?.length > 0) {
        diagnosesOrCcsrFilters.push({
          terms: {
            "patientClaims.diagnosisIcdCode":
              suppliedFilters.claims.diagnosesICD.values.map(
                (codeAndDescription: string) =>
                  codeAndDescription.split(/\s+/)[0]
              )
          }
        });
      }
      if (
        !!suppliedFilters.exclusionClaims?.diagnosesICD?.values.length &&
        featureFlags.enableCcsrExclusionForMatchedCounts
      ) {
        mustNotClauses.push({
          terms: {
            "patientClaims.diagnosisIcdCode":
              suppliedFilters.exclusionClaims.diagnosesICD.values.map(
                (codeAndDescription: string) =>
                  codeAndDescription.split(/\s+/)[0]
              )
          }
        });
      }
      if (suppliedFilters.claims?.ccsr?.values?.length) {
        diagnosesOrCcsrFilters.push({
          terms: {
            "patientClaims.ccsrDescriptions":
              suppliedFilters.claims.ccsr!.values
          }
        });
      }
      if (
        !!suppliedFilters.exclusionClaims?.ccsr?.values?.length &&
        featureFlags.enableCcsrExclusionForMatchedCounts
      ) {
        mustNotClauses.push({
          terms: {
            "patientClaims.ccsrDescriptions":
              suppliedFilters.exclusionClaims.ccsr!.values
          }
        });
      }
      if (diagnosesOrCcsrFilters.length == 2) {
        filter.push({
          bool: {
            should: diagnosesOrCcsrFilters,
            minimum_should_match: 1
          }
        });
      } else {
        filter.push(...diagnosesOrCcsrFilters);
      }

      if (
        filter.length === 0 &&
        (suppliedFilters?.indications?.values ?? []).length > 0
      ) {
        filter.push({
          terms: {
            "patientClaims.diagnosisIndications":
              suppliedFilters.indications!.values!
          }
        });
      }
      // if claims or indication filter is not applied then only apply the searched keyword as a filter in diagnosis indications
      if (filter.length === 0 && input.query) {
        filter.push({
          term: {
            "patientClaims.diagnosisIndications": input.query
              .trim()
              .toLowerCase()
          }
        });
      }

      const diversityFilters =
        this.buildPatientDiversityFiltersForPatientPopulation(input);
      filter.push(...diversityFilters);
    }

    // let countryFilter: QueryDslQueryContainer | undefined = undefined;
    // if (suppliedFilters.country.values.length) {
    //   countryFilter = {
    //     terms: {
    //       "patientClaims.country": suppliedFilters.country.values
    //     }
    //   };
    // }

    // if (countryFilter && filter.length) {
    //   filter.push(countryFilter);
    // }

    if (shouldIncludeRxPatientClaims && !(mustNotClauses.length > 0)) {
      return filter;
    } else if (mustNotClauses.length && shouldIncludeRxPatientClaims) {
      return [
        {
          bool: {
            must: filter.length ? filter : undefined,
            must_not: mustNotClauses.length ? mustNotClauses : undefined
          }
        }
      ];
    }

    if (filter.length || mustNotClauses.length) {
      // Only include medical claims
      return [
        {
          bool: {
            must: filter.length ? filter : undefined,
            must_not: mustNotClauses.length ? mustNotClauses : undefined,
            should: [
              buildFieldExistsQuery("patientClaims.diagnosisIcdCode"),
              buildFieldExistsQuery("patientClaims.procedureCode")
            ],
            minimum_should_match: 1
          }
        }
      ];
    }

    return [];
  }

  async buildForSearchRequest(
    input: KeywordSearchInput | NameSearchInput,
    languageDetector: LanguageDetector,
    feaureFlags: KeywordSearchFeatureFlags | NameSearchFeatureFlags,
    parsedQueryTree?: ParsedQueryTree,
    options?: FilterClauseBuilderOptions,
    territories?: SavedTerritory[],
    bothParserResponse?: BothQueryParseResult,
    isLLMgenerated?: boolean
  ): Promise<Array<QueryDslQueryContainer>> {
    return this.build(
      input,
      languageDetector,
      "search",
      feaureFlags,
      parsedQueryTree,
      options,
      territories,
      bothParserResponse,
      isLLMgenerated
    );
  }

  private async buildAsyncFilters(
    input: KeywordFilterAutocompleteInput,
    featureFlags: FeatureFlags
  ) {
    const filters: Array<QueryDslQueryContainer> = [];

    filters.push(
      ...(await this.buildPublicPrivateTagsFilterEntities(input, featureFlags))
    );
    filters.push(
      ...(await this.buildPublicPrivateTagsExclusionFilter(input, featureFlags))
    );
    filters.push(...(await this.buildCTMSFilter(input, featureFlags)));

    return filters;
  }

  private buildFilters(
    input: KeywordFilterAutocompleteInput,
    languageDetector: LanguageDetector,
    context: Context,
    featureFlags: FeatureFlags,
    parsedQueryTree: ParsedQueryTree,
    options?: FilterClauseBuilderOptions,
    territories?: SavedTerritory[],
    bothParserResponse?: BothQueryParseResult,
    isLLMgenerated?: boolean
  ) {
    let filters: Array<QueryDslQueryContainer> = [];
    filters.push(...this.buildProjectSliceFilter(input, options));
    filters.push(...this.buildPeopleIdAndH1dnIdFilter(input));
    filters.push(...this.buildProgrammaticTagsFilter(input));
    filters.push(...this.buildProgrammaticTagsExclusionFilter(input));
    filters.push(...this.buildDesignationsFilter(input));
    filters.push(...this.buildDesignationsExclusionFilter(input));
    filters.push(
      ...this.buildSpecialtiesIndicationFilter(
        input,
        featureFlags,
        languageDetector
      )
    );
    filters.push(...this.buildNPIFilter(input));

    if (featureFlags.enableLocationFilterRegionRollup) {
      filters.push(...this.buildGeoRollupLocationFilters(input));
    } else {
      filters.push(
        ...this.buildNonContextualLocationFilters(input, languageDetector)
      );
    }

    if (
      input.suppliedFilters.trialEnrollmentRate &&
      (input.suppliedFilters.trialEnrollmentRate?.min ||
        input.suppliedFilters.trialEnrollmentRate?.max)
    ) {
      filters.push({
        range: {
          trialEnrollmentRate: {
            gte: input.suppliedFilters.trialEnrollmentRate?.min,
            lte: input.suppliedFilters.trialEnrollmentRate?.max
          }
        }
      });
    }

    filters.push(
      ...this.buildInstitutionFilters(input, languageDetector, isLLMgenerated)
    );
    filters.push(...this.buildInstitutionExclusionFilters(input));
    filters.push(...this.buildPatientDiversityFilters(input));
    filters.push(...this.buildPatientDiversityExclusionFilters(input));
    filters.push(...this.buildPatientDiversityRaceThresholdFilter(input));
    filters.push(...this.buildProviderDiversityFilters(input, featureFlags));
    filters.push(...this.buildProviderDiversityExclusionFilters(input));
    filters.push(...this.buildReferralsFilters(input));
    filters.push(...this.buildReferralsExclusionFilters(input));
    filters.push(...this.buildSocialMediaFilters(input));
    filters.push(...this.buildFOMemberFilter(input));
    filters.push(...this.buildGeoBoundingBoxFilter(input, featureFlags));
    filters.push(
      ...this.buildClaimsFilters(input, featureFlags, parsedQueryTree)
    );
    filters.push(...this.buildGeoDistanceFilter(input));
    filters.push(...this.buildGeoShapeFilter(input));
    filters.push(...this.buildDigitalLeaderFilterForSearch(input));
    filters.push(...this.buildGeoHashGridsFilter(input));
    filters.push(...this.buildIsInactiveAndIsIndustryFilter(input));
    filters.push(...this.addTerritoryFilters(territories ?? []));
    filters.push(...this.buildIndicationLeaderFilters(input));
    if (
      "enableCountrySpecificNonIndicationLeaderFilters" in featureFlags &&
      featureFlags.enableCountrySpecificNonIndicationLeaderFilters
    ) {
      filters.push(
        ...this.buildCountrySpecificNonIndicationLeaderFilters(input)
      );
    } else {
      filters.push(...this.buildNonIndicationLeaderFilters(input));
    }
    filters.push(...this.buildGlobalLeaderFilter(input, featureFlags));
    filters.push(...this.buildCareerLengthFilter(input));

    if (bothParserResponse) {
      filters.push(
        ...this.buildBothOperatorFilters(bothParserResponse, featureFlags)
      );
    }

    if (context === "autocomplete") {
      filters.push(
        ...this.buildPaymentsFiltersForSearch(
          input,
          featureFlags,
          parsedQueryTree
        )
      );
      filters.push(
        ...this.buildCongressFiltersForSearch(input, parsedQueryTree)
      );
      filters.push(
        ...this.buildPublicationsFiltersForSearch(
          input,
          languageDetector,
          parsedQueryTree
        )
      );
      filters.push(...this.buildTrialsFiltersForSearch(input, parsedQueryTree));
    } else {
      filters.push(
        ...this.buildClaimsDiagnosesExclusionFiltersForSearch(
          input,
          featureFlags
        )
      );
      filters.push(
        ...this.buildClaimsProceduresExclusionFiltersForSearch(input)
      );
      filters.push(...this.buildPrescriptionsExclusionFiltersForSearch(input));

      filters.push(
        ...this.buildPaymentsFiltersForSearch(
          input,
          featureFlags,
          parsedQueryTree
        )
      );
      filters.push(...this.buildPaymentsExclusionFiltersForSearch(input));
      filters.push(
        ...this.buildCongressFiltersForSearch(input, parsedQueryTree)
      );
      filters.push(...this.buildCongressExclusionFiltersForSearch(input));
      filters.push(
        ...this.buildPublicationsFiltersForSearch(
          input,
          languageDetector,
          parsedQueryTree
        )
      );
      filters.push(...this.buildPublicationsExclusionFiltersForSearch(input));
      filters.push(...this.buildTrialsFiltersForSearch(input, parsedQueryTree));
      filters.push(...this.buildTrialsExclusionFiltersForSearch(input));
      filters.push(
        ...this.buildClaimsCcsrExclusionFiltersForSearch(input, featureFlags)
      );
      filters.push(...this.buildClaimsCcsrPxExclusionFiltersForSearch(input));
    }

    filters = this.buildDateRangeFilter(
      input,
      filters,
      languageDetector,
      parsedQueryTree
    );

    const claimsFilter = this.buildPatientClaimsFilter(input);

    /*
      We add has_child query to filter clause only
      if matched patient count doesn't influence ranking.
      Else we add it to must clause
    */
    if (
      claimsFilter.length &&
      !input.sortBy?.patientsDiversityRank &&
      (input.sortBy?.diagnoses ?? 0) === 0
    ) {
      // const countryValues = input.suppliedFilters.country.values;
      // if (countryValues.length) {
      //   claimsFilter.push({
      //     terms: {
      //       "patientClaims.country": countryValues
      //     }
      //   });
      // }
      const childQuery = {
        has_child: {
          type: "claim",
          query: {
            bool: {
              filter: claimsFilter
            }
          }
        }
      };
      filters.push(childQuery);
    }

    filters.push(...this.buildRisingStarsFilterWithoutIndication(input));
    return filters;
  }

  private async build(
    input: KeywordFilterAutocompleteInput,
    languageDetector: LanguageDetector,
    context: Context,
    featureFlags: FeatureFlags,
    parsedQueryTree?: ParsedQueryTree,
    options?: FilterClauseBuilderOptions,
    territories?: SavedTerritory[],
    bothParserResponse?: BothQueryParseResult,
    isLLMgenerated?: boolean
  ): Promise<Array<QueryDslQueryContainer>> {
    const filters: Array<QueryDslQueryContainer> = [];

    filters.push(
      ...this.buildFilters(
        input,
        languageDetector,
        context,
        featureFlags,
        parsedQueryTree,
        options,
        territories,
        bothParserResponse,
        isLLMgenerated
      )
    );
    filters.push(...(await this.buildAsyncFilters(input, featureFlags)));

    return filters;
  }

  private buildProjectSliceFilter(
    input: KeywordFilterAutocompleteInput,
    options?: FilterClauseBuilderOptions
  ): Array<QueryDslQueryContainer> {
    let filter: QueryDslQueryContainer;

    if (options?.skipProjectIdFilter) {
      return [];
    }

    if (input.sliceOption) {
      switch (input.sliceOption) {
        case SearchSliceOptionsEnum.FullCorpus: {
          filter = {
            bool: {
              must: this.buildProjectIdsFilter([
                DEFAULT_PROJECT_ID,
                input.projectId
              ]),
              must_not: {
                term: {
                  otherProjectsPersonIn: input.projectId
                }
              }
            }
          };
          return [filter];
        }
        case SearchSliceOptionsEnum.InsideSlice: {
          filter = this.buildProjectIdFilter(input.projectId);
          return [filter];
        }
        case SearchSliceOptionsEnum.OutsideSlice: {
          filter = {
            bool: {
              must_not: [
                this.buildProjectIdFilter(input.projectId),
                {
                  term: {
                    otherProjectsPersonIn: input.projectId
                  }
                }
              ],
              must: this.buildProjectIdFilter(DEFAULT_PROJECT_ID)
            }
          };
          return [filter];
        }
      }
    }
    return [this.buildProjectIdFilter(input.projectId)];
  }

  private buildProjectIdsFilter(
    projectIds: Array<string>
  ): QueryDslQueryContainer {
    const projectIdFilter = {
      terms: {
        projectIds
      }
    };

    return projectIdFilter;
  }

  private buildProjectIdFilter(projectId: string): QueryDslQueryContainer {
    const projectIdFilter = {
      term: {
        projectIds: projectId
      }
    };

    return projectIdFilter;
  }

  private buildPeopleIdAndH1dnIdFilter({
    suppliedFilters
  }: KeywordFilterAutocompleteInput): Array<QueryDslQueryContainer> {
    const { peopleIds, h1dnIds } = suppliedFilters;
    const shoulds = [];

    if (peopleIds?.length) {
      const idFilter = this.toTermsFilter("id", peopleIds);
      shoulds.push(idFilter);
    }

    if (h1dnIds?.length) {
      const h1dnIdFilter = this.toTermsFilter("h1dn_id", h1dnIds);
      const groupH1dnIdFilter = this.toTermsFilter(
        "groupH1dnPersonId",
        h1dnIds
      );
      shoulds.push(h1dnIdFilter, groupH1dnIdFilter);
    }

    if (shoulds.length) {
      return [
        {
          bool: {
            should: shoulds
          }
        }
      ];
    }

    return [];
  }

  private buildProgrammaticTagsExclusionFilter({
    suppliedFilters,
    projectId
  }: KeywordFilterAutocompleteInput): Array<QueryDslQueryContainer> {
    if (!suppliedFilters.exclusionTags?.programmaticTagIds?.length) {
      return [];
    }

    const filter: QueryDslQueryContainer = {
      bool: {
        must_not: {
          nested: {
            path: "tags",
            query: {
              bool: {
                must: [
                  {
                    terms: {
                      "tags.id":
                        suppliedFilters.exclusionTags.programmaticTagIds || []
                    }
                  },
                  {
                    term: {
                      "tags.active": true
                    }
                  },
                  {
                    term: {
                      "tags.project_id": projectId
                    }
                  }
                ]
              }
            }
          }
        }
      }
    };

    return [filter];
  }

  private buildProgrammaticTagsFilter({
    suppliedFilters,
    projectId
  }: KeywordFilterAutocompleteInput): Array<QueryDslQueryContainer> {
    if (!suppliedFilters.tags.programmaticTagIds?.length) {
      return [];
    }

    const filter: QueryDslQueryContainer = {
      nested: {
        path: "tags",
        query: {
          bool: {
            must: [
              {
                terms: {
                  "tags.id": suppliedFilters.tags.programmaticTagIds!
                }
              },
              {
                term: {
                  "tags.active": true
                }
              },
              {
                term: {
                  "tags.project_id": projectId
                }
              }
            ]
          }
        }
      }
    };

    return [filter];
  }

  private buildGlobalLeaderFilter(
    { suppliedFilters }: KeywordFilterAutocompleteInput,
    featureFlags: FeatureFlags
  ): Array<QueryDslQueryContainer> {
    const isGlobalLeader = suppliedFilters.isGlobalLeader?.value ?? false;

    if (!featureFlags.enableNewGlobalLeaderTier || !isGlobalLeader) {
      return [];
    }

    const filters: Array<QueryDslQueryContainer> = [];

    if (
      "enableExUSGlobalLeader" in featureFlags &&
      featureFlags.enableExUSGlobalLeader
    ) {
      filters.push(toTermFilter("isGlobalLeader", isGlobalLeader));
    } else {
      filters.push({
        nested: {
          path: "tags",
          query: {
            bool: {
              must: [
                {
                  terms: {
                    "tags.id": ["f44bc58d-a91d-425f-b479-5c60bde1ef80"]
                  }
                },
                {
                  term: {
                    "tags.active": true
                  }
                }
              ]
            }
          }
        }
      });
    }

    return filters;
  }

  private unwrapHcpAndH1dnIdsFromEntities(
    entities: { entityId: string; entityType: EntityType }[]
  ): { hcpIds: string[]; h1dnIds: string[] } {
    const segregatedEntities = entities.reduce<{
      hcpIds: string[];
      h1dnIds: string[];
    }>(
      (prev, curr) => {
        if (curr.entityType === "person") {
          prev.hcpIds.push(curr.entityId);
        } else if (curr.entityType === "h1dn") {
          prev.h1dnIds.push(curr.entityId);
        }
        return prev;
      },
      { hcpIds: [], h1dnIds: [] }
    );
    return {
      hcpIds: _.uniq(segregatedEntities.hcpIds),
      h1dnIds: _.uniq(segregatedEntities.h1dnIds)
    };
  }

  private collectTagIds({ tags }: FilterInterface): Array<string> {
    const publicTagIds = tags.publicTagIds || [];
    const privateTagIds = tags.privateTagIds || [];

    return [...publicTagIds, ...privateTagIds];
  }

  private async buildPublicPrivateTagsExclusionFilter(
    { suppliedFilters, projectId }: KeywordFilterAutocompleteInput,
    featureFlags: FeatureFlags
  ): Promise<Array<QueryDslQueryContainer>> {
    const publicTagIds = suppliedFilters.exclusionTags?.publicTagIds || [];
    const privateTagIds = suppliedFilters.exclusionTags?.privateTagIds || [];

    const tagIds = [...publicTagIds, ...privateTagIds];

    if (!tagIds.length) {
      return [];
    }

    if (featureFlags?.enableTagsInElasticsearch) {
      return [
        {
          bool: {
            must_not: [
              {
                terms: {
                  tagIds: tagIds
                }
              }
            ]
          }
        }
      ];
    }

    const entities = await this.tagsHelperService.getEntitiesInProjectsByTags(
      projectId,
      tagIds
    );

    if (!entities?.length) {
      return [];
    }

    const entityIds = this.unwrapHcpAndH1dnIdsFromEntities(entities);

    const must_not: QueryDslQueryContainer[] = [];

    if (entityIds.hcpIds.length > 0) {
      must_not.push(this.toTermsFilter("id", entityIds.hcpIds));
    }

    if (entityIds.h1dnIds.length > 0) {
      must_not.push(this.toTermsFilter("h1dn_id", entityIds.h1dnIds));
    }

    return must_not.length === 0
      ? []
      : [
          {
            bool: {
              must_not
            }
          }
        ];
  }

  @Trace("h1-search.filters.buildPublicPrivateTagsFilterEntities")
  private async buildPublicPrivateTagsFilterEntities(
    { suppliedFilters, projectId }: KeywordFilterAutocompleteInput,
    featureFlags: FeatureFlags
  ): Promise<Array<QueryDslQueryContainer>> {
    const tagIds = this.collectTagIds(suppliedFilters);
    if (!tagIds.length) {
      return [];
    }

    if (featureFlags?.enableTagsInElasticsearch) {
      return [
        {
          terms: {
            tagIds
          }
        }
      ];
    }

    let entities = await this.tagsHelperService.getEntitiesInProjectsByTags(
      projectId,
      tagIds
    );

    if (!entities?.length) {
      return [];
    }

    // intersection of HCPs within tags is performed here.
    if (suppliedFilters.intersectTags?.value === true && tagIds.length > 1) {
      const entityIdToTagIdMap = new Map<string, string[]>();
      entities.forEach((entity) => {
        entityIdToTagIdMap.has(entity.entityId)
          ? entityIdToTagIdMap.get(entity.entityId)!.push(entity.tagId)
          : entityIdToTagIdMap.set(entity.entityId, [entity.tagId]);
      });
      const intersectEntities: typeof entities = [];
      entities.forEach((entity) => {
        if (entityIdToTagIdMap.get(entity.entityId)?.length === tagIds.length) {
          intersectEntities.push(entity);
        }
      });
      this.logger.info(
        {
          tagIds,
          totalAssignments: entities.length,
          intersectedAssignments: intersectEntities.length / tagIds.length
        },
        "performing hcp tags intersection"
      );
      entities = intersectEntities;
    }

    if (
      suppliedFilters.intersectTags?.value === true &&
      entities.length === 0
    ) {
      return [this.toTermsFilter("id", ["no-match-hcp-id"])];
    }

    const entityIds = this.unwrapHcpAndH1dnIdsFromEntities(entities);
    this.logger.info(
      { hcpIds: entityIds.hcpIds.length, h1dnIds: entityIds.h1dnIds.length },
      "total HCPs in tags filter"
    );

    const should: QueryDslQueryContainer[] = [];

    if (entityIds.hcpIds.length > 0) {
      should.push(this.toTermsFilter("id", entityIds.hcpIds));
    }

    if (entityIds.h1dnIds.length > 0) {
      should.push(this.toTermsFilter("h1dn_id", entityIds.h1dnIds));
    }

    return should.length === 0
      ? []
      : [
          {
            bool: {
              should
            }
          }
        ];
  }

  private buildDesignationsFilter({
    filterField,
    filterValue,
    suppliedFilters
  }: KeywordFilterAutocompleteInput) {
    const filters = [];

    if (filterField === KeywordFilterAutocompleteFilterField.DESIGNATION) {
      if (filterValue) {
        const filter = this.toMatchPhraseQuery(
          "designations.autocomplete_search",
          filterValue
        );
        filters.push(filter);
      }
    } else if (!_.isEmpty(suppliedFilters.designations?.values)) {
      const filter = this.toTermsFilter(
        "designations",
        suppliedFilters.designations!.values
      );
      filters.push(filter);
    }

    return filters;
  }

  private buildDesignationsExclusionFilter({
    suppliedFilters
  }: KeywordFilterAutocompleteInput): Array<QueryDslQueryContainer> {
    if (!_.isEmpty(suppliedFilters.designationsExclusion?.values)) {
      const filter = this.toTermsFilter(
        "designations",
        suppliedFilters.designationsExclusion!.values
      );
      return this.buildExclusionQuery(filter);
    }

    return [];
  }

  private buildSpecialtiesIndicationFilter(
    input: KeywordFilterAutocompleteInput,
    featureFlags: FeatureFlags,
    languageDetector: LanguageDetector
  ): Array<QueryDslQueryContainer> {
    const specialtyInclusionFilter = this.buildSpecialtiesInclusionFilter(
      input,
      languageDetector
    );
    const specialtyExclusionFilter =
      this.buildSpecialtiesExclusionFilter(input);
    const indicationFilter = this.buildIndicationFilters(input, featureFlags);

    if (input.suppliedFilters.indicationsSpecialtyORed?.value) {
      return [
        {
          bool: {
            must: [...specialtyExclusionFilter],
            should: [...specialtyInclusionFilter, ...indicationFilter],
            minimum_should_match: 1
          }
        }
      ];
    } else {
      return [
        ...specialtyInclusionFilter,
        ...specialtyExclusionFilter,
        ...indicationFilter
      ];
    }
  }

  private buildSpecialtiesInclusionFilter(
    {
      filterField,
      filterValue,
      suppliedFilters
    }: KeywordFilterAutocompleteInput,
    languageDetector: LanguageDetector
  ) {
    const filters = [];

    if (filterField === KeywordFilterAutocompleteFilterField.SPECIALTY) {
      if (filterValue) {
        const filterValueLanguage = languageDetector(filterValue);
        const filter = this.toMatchPhraseQuery(
          `specialty_${filterValueLanguage}.autocomplete_search`,
          filterValue
        );
        filters.push(filter);
      }
    } else if (suppliedFilters.specialty?.values.length) {
      const filter = this.toTermsFilter(
        "specialty_multi",
        suppliedFilters.specialty.values
      );
      filters.push(filter);
    }

    return filters;
  }

  private buildSpecialtiesExclusionFilter({
    suppliedFilters
  }: KeywordFilterAutocompleteInput): Array<QueryDslQueryContainer> {
    if (!_.isEmpty(suppliedFilters.specialtyExclusion?.values)) {
      const filter = this.toTermsFilter(
        "specialty_multi",
        suppliedFilters.specialtyExclusion!.values
      );
      return this.buildExclusionQuery(filter);
    }
    return [];
  }

  private buildNPIFilter({
    suppliedFilters
  }: KeywordFilterAutocompleteInput): Array<QueryDslQueryContainer> {
    if (!suppliedFilters.npi.values.length) {
      return [];
    }

    const npiFilter = this.toTermsFilter(
      "npiNumber",
      suppliedFilters.npi.values
    );
    const drCodeFilter = this.toTermsFilter(
      "drCode",
      suppliedFilters.npi.values
    );

    return [this.toBoolShould([npiFilter, drCodeFilter])];
  }

  private anyLocationFiltersWereSupplied(
    filterField: KeywordFilterAutocompleteFilterField,
    suppliedFilters: FilterInterface
  ) {
    return (
      filterField === KeywordFilterAutocompleteFilterField.COUNTRY ||
      filterField === KeywordFilterAutocompleteFilterField.REGION ||
      filterField === KeywordFilterAutocompleteFilterField.CITY ||
      filterField === KeywordFilterAutocompleteFilterField.POSTAL_CODE ||
      suppliedFilters.country.values.length > 0 ||
      !_.isEmpty(suppliedFilters.excludeCountry?.values) ||
      suppliedFilters.state.values.length > 0 ||
      !_.isEmpty(suppliedFilters.excludeState?.values) ||
      suppliedFilters.city.values.length > 0 ||
      !_.isEmpty(suppliedFilters.excludeCity?.values) ||
      suppliedFilters.zipCode.values.length > 0 ||
      !_.isEmpty(suppliedFilters.excludeZipCode?.values)
    );
  }

  private buildNonContextualLocationFilters(
    {
      filterField,
      filterValue,
      suppliedFilters
    }: KeywordFilterAutocompleteInput,
    languageDetector: LanguageDetector
  ): Array<QueryDslQueryContainer> {
    if (!this.anyLocationFiltersWereSupplied(filterField!, suppliedFilters)) {
      return [];
    }

    const filters: Array<QueryDslQueryContainer> = [];

    if (filterField === KeywordFilterAutocompleteFilterField.COUNTRY) {
      if (filterValue) {
        const languageCode = languageDetector(filterValue);
        filters.push(
          this.toMatchPhraseQuery(
            `country_${languageCode}.autocomplete_search`,
            filterValue
          )
        );
      }
    } else {
      if (suppliedFilters.country.values.length > 0) {
        filters.push(
          this.toTermsFilter("country_multi", suppliedFilters.country.values)
        );
      }
      if (!_.isEmpty(suppliedFilters.excludeCountry?.values)) {
        filters.push({
          bool: {
            must_not: this.toTermsFilter(
              "country_multi",
              suppliedFilters.excludeCountry!.values
            )
          }
        });
      }
    }

    if (filterField === KeywordFilterAutocompleteFilterField.REGION) {
      if (filterValue) {
        const languageCode = languageDetector(filterValue);
        filters.push(
          this.toMatchPhraseQuery(
            `state_${languageCode}.autocomplete_search`,
            filterValue
          )
        );
      }
    } else if (suppliedFilters.state.values.length > 0) {
      filters.push(
        this.toTermsFilter("state_multi", suppliedFilters.state.values)
      );
    }

    if (filterField === KeywordFilterAutocompleteFilterField.CITY) {
      if (filterValue) {
        const languageCode = languageDetector(filterValue);
        if (languageCode === ENGLISH) {
          filters.push(
            this.toMatchPhraseQuery(
              "locationToken.autocomplete_search",
              filterValue
            )
          );
        } else {
          filters.push(
            this.toMatchPhraseQuery(
              `city_${languageCode}.autocomplete_search`,
              filterValue
            )
          );
        }
      }
    } else if (suppliedFilters.city.values.length > 0) {
      filters.push(
        this.buildCityFilter(suppliedFilters.city.values, languageDetector)
      );
    }

    if (filterField === KeywordFilterAutocompleteFilterField.POSTAL_CODE) {
      if (filterValue) {
        filters.push(
          this.toMatchPhraseQuery("zipCode5.autocomplete_search", filterValue)
        );
      }
    } else if (suppliedFilters.zipCode.values.length > 0) {
      filters.push(
        this.toTermsFilter("zipCode5", suppliedFilters.zipCode.values)
      );
    }

    return filters;
  }

  buildGeoRollupLocationFilters({
    filterField,
    filterValue,
    suppliedFilters,
    app
  }: KeywordFilterAutocompleteInput): Array<QueryDslQueryContainer> {
    if (!this.anyLocationFiltersWereSupplied(filterField!, suppliedFilters)) {
      return [];
    }

    const filters: Array<QueryDslQueryContainer> = [];
    const excludedFilters: Array<QueryDslQueryContainer> = [];

    const nestedPath =
      app === Apps.TRIAL_LANDSCAPE ? TL_ADDRESSES : HCPU_ADDRESSES;

    if (filterField === KeywordFilterAutocompleteFilterField.COUNTRY) {
      if (filterValue) {
        filters.push({
          bool: {
            should: [
              buildMatchPhrasePrefixQuery(`${nestedPath}.country`, filterValue),
              buildMatchPhrasePrefixQuery(
                `${nestedPath}.country_level_regions`,
                filterValue
              )
            ]
          }
        });
      }
    } else {
      if (suppliedFilters.country.values.length) {
        const countryFilterValues = this.collectCountryFilterValues(
          suppliedFilters.country.values
        );

        if (countryFilterValues.length) {
          filters.push({
            bool: {
              should: [
                this.toTermsFilter(
                  `${nestedPath}.filters.country`,
                  countryFilterValues
                ),
                this.toTermsFilter(
                  `${nestedPath}.filters.country_level_regions`,
                  suppliedFilters.country.values
                )
              ]
            }
          });
        }
      }

      if (suppliedFilters.excludeCountry?.values.length) {
        const excludedCountryFilterValues = this.collectCountryFilterValues(
          suppliedFilters.excludeCountry.values
        );

        if (excludedCountryFilterValues.length) {
          excludedFilters.push(
            buildBoolShouldQuery([
              this.toTermsFilter(
                `${nestedPath}.filters.country`,
                excludedCountryFilterValues
              ),
              this.toTermsFilter(
                `${nestedPath}.filters.country_level_regions`,
                suppliedFilters.excludeCountry.values
              )
            ])
          );
        }
      }
    }

    if (filterField === KeywordFilterAutocompleteFilterField.REGION) {
      if (filterValue) {
        filters.push({
          bool: {
            should: [
              buildMatchPhrasePrefixQuery(`${nestedPath}.region`, filterValue),
              buildMatchPhrasePrefixQuery(
                `${nestedPath}.state_level_regions`,
                filterValue
              )
            ]
          }
        });
      }
    } else {
      if (suppliedFilters.state.values.length) {
        const regionFilterValues = this.collectRegionFilterValues(
          suppliedFilters.state.values
        );

        if (regionFilterValues.length) {
          filters.push({
            bool: {
              should: [
                this.toTermsFilter(
                  `${nestedPath}.filters.region`,
                  regionFilterValues
                ),
                this.toTermsFilter(
                  `${nestedPath}.filters.state_level_regions`,
                  suppliedFilters.state.values
                )
              ]
            }
          });
        }
      }

      if (suppliedFilters.excludeState?.values.length) {
        const excludedRegionFilterValues = this.collectRegionFilterValues(
          suppliedFilters.excludeState.values
        );

        if (excludedRegionFilterValues.length) {
          excludedFilters.push(
            buildBoolShouldQuery([
              this.toTermsFilter(
                `${nestedPath}.filters.region`,
                excludedRegionFilterValues
              ),
              this.toTermsFilter(
                `${nestedPath}.filters.state_level_regions`,
                suppliedFilters.excludeState.values
              )
            ])
          );
        }
      }
    }

    if (filterField === KeywordFilterAutocompleteFilterField.CITY) {
      if (filterValue) {
        filters.push({
          bool: {
            should: [
              buildMatchPhrasePrefixQuery(`${nestedPath}.city`, filterValue),
              buildMatchPhrasePrefixQuery(
                `${nestedPath}.city_level_regions`,
                filterValue
              )
            ]
          }
        });
      }
    } else {
      if (suppliedFilters.city.values.length) {
        const cityFilterValues = this.collectCityFilterValues(
          suppliedFilters.city.values
        );

        if (cityFilterValues.length) {
          filters.push({
            bool: {
              should: [
                this.toTermsFilter(
                  `${nestedPath}.filters.city`,
                  cityFilterValues
                ),
                this.toTermsFilter(
                  `${nestedPath}.filters.city_level_regions`,
                  suppliedFilters.city.values
                )
              ]
            }
          });
        }
      }

      if (suppliedFilters.excludeCity?.values.length) {
        const excludedCityFilterValues = this.collectCityFilterValues(
          suppliedFilters.excludeCity.values
        );

        if (excludedCityFilterValues.length) {
          excludedFilters.push(
            buildBoolShouldQuery([
              this.toTermsFilter(
                `${nestedPath}.filters.city`,
                excludedCityFilterValues
              ),
              this.toTermsFilter(
                `${nestedPath}.filters.city_level_regions`,
                suppliedFilters.excludeCity.values
              )
            ])
          );
        }
      }
    }

    if (filterField === KeywordFilterAutocompleteFilterField.POSTAL_CODE) {
      if (filterValue) {
        filters.push({
          regexp: {
            [`${nestedPath}.filters.postal_code`]: `us\\|.+\\|${filterValue}[^|]*`
          }
        });
      }
    } else {
      if (suppliedFilters.zipCode.values.length) {
        const postalCodeFilter = this.collectPostalCodeFilterValues(
          nestedPath,
          suppliedFilters.zipCode.values
        );

        filters.push(postalCodeFilter);
      }

      if (suppliedFilters.excludeZipCode?.values.length) {
        const excludedPostalCodeFilters = this.collectPostalCodeFilterValues(
          nestedPath,
          suppliedFilters.excludeZipCode.values
        );

        excludedFilters.push(excludedPostalCodeFilters);
      }
    }

    if (filters.length > 0 || excludedFilters.length > 0) {
      const geoLocationFilters = [];

      if (excludedFilters.length > 0) {
        const nestedQuery: QueryDslQueryContainer = {
          bool: {
            must_not: {
              nested: {
                path: nestedPath,
                query: {
                  bool: {
                    filter: excludedFilters
                  }
                }
              }
            }
          }
        };
        geoLocationFilters.push(nestedQuery);
      }

      if (filters.length > 0) {
        const nestedQuery: QueryDslQueryContainer = {
          nested: {
            path: nestedPath,
            query: {
              bool: {
                filter: filters
              }
            }
          }
        };
        geoLocationFilters.push(nestedQuery);
      }

      return geoLocationFilters;
    }

    return [];
  }

  private buildCityFilter(
    cityValues: ReadonlyArray<string>,
    languageDetector: LanguageDetector
  ) {
    const englishCityValues = this.getFilterValuesForLanguageCode(
      cityValues,
      languageDetector,
      ENGLISH
    );
    const japaneseCityValues = this.getFilterValuesForLanguageCode(
      cityValues,
      languageDetector,
      JAPANESE
    );
    const chineseCityValues = this.getFilterValuesForLanguageCode(
      cityValues,
      languageDetector,
      CHINESE
    );

    const cityShoulds: QueryDslQueryContainer[] = [];

    if (englishCityValues.length > 0) {
      cityShoulds.push(this.toTermsFilter("locationToken", englishCityValues));
    }
    if (japaneseCityValues.length > 0) {
      cityShoulds.push(this.toTermsFilter("city_jpn", japaneseCityValues));
    }
    if (chineseCityValues.length > 0) {
      cityShoulds.push(this.toTermsFilter("city_cmn", chineseCityValues));
    }

    if (cityShoulds.length > 1) {
      return this.toBoolShould(cityShoulds);
    }

    return cityShoulds[0];
  }

  private toBoolShould(
    should: Array<QueryDslQueryContainer>
  ): QueryDslQueryContainer {
    return {
      bool: {
        should,
        minimum_should_match: 1
      }
    };
  }

  private getFilterValuesForLanguageCode(
    filterValues: ReadonlyArray<string>,
    languageDetector: LanguageDetector,
    languageCode: string
  ): string[] {
    return _.compact(
      filterValues.map((value) => {
        const detectedLanguage = languageDetector(value);
        if (detectedLanguage === languageCode) {
          return value;
        }

        return null;
      })
    );
  }

  private buildInstitutionFilters(
    {
      filterField,
      filterValue,
      suppliedFilters
    }: KeywordFilterAutocompleteInput,
    languageDetector: LanguageDetector,
    isLLMgenerated = false
  ): Array<QueryDslQueryContainer> {
    const filters: Array<QueryDslQueryContainer> = [];

    if (filterField === KeywordFilterAutocompleteFilterField.AFFILIATION) {
      if (filterValue) {
        const fieldNames = [
          "presentWorkInstitutionNames_eng.autocomplete_search",
          "presentWorkInstitutionNames_cmn.autocomplete_search",
          "presentWorkInstitutionNames_jpn.autocomplete_search"
        ];
        filters.push(this.toMultiMatchPhraseQuery(fieldNames, filterValue));
      }
    } else if (suppliedFilters.presentWorkInstitutions.values.length > 0) {
      filters.push(
        this.toTermsFilter(
          "presentWorkInstitutionNames_multi",
          suppliedFilters.presentWorkInstitutions.values
        )
      );
    }

    if (filterField === KeywordFilterAutocompleteFilterField.MEDICAL_SCHOOL) {
      if (filterValue) {
        const filterValueLanguage = languageDetector(filterValue);
        const fieldName = `studentInstitutionNames_${filterValueLanguage}.autocomplete_search`;
        filters.push(this.toMatchPhraseQuery(fieldName, filterValue));
      }
    } else if (suppliedFilters.studentInstitutions.values.length > 0) {
      // Every individual student institution filter can be of any language (e.g '東京大学' and 'Stanford University'). We need to search for each applied filter in its appropriate language field.
      const languageToFilterValueMap = new Map<Language, string[]>();
      for (const value of suppliedFilters.studentInstitutions.values) {
        const filterValueLanguage = languageDetector(value);
        if (!languageToFilterValueMap.has(filterValueLanguage)) {
          languageToFilterValueMap.set(filterValueLanguage, [value]);
        } else {
          languageToFilterValueMap.get(filterValueLanguage)?.push(value);
        }
      }
      const shoulds: Array<QueryDslQueryContainer> = [];
      languageToFilterValueMap.forEach(
        (filterValues: string[], language: Language) => {
          shoulds.push(
            this.toTermsFilter(
              `studentInstitutionNames_${language}`,
              filterValues
            )
          );
        }
      );
      if (shoulds.length > 0) {
        filters.push({
          bool: {
            should: shoulds
          }
        });
      }
    }
    const societyAffiliationsAppliedFilter =
      suppliedFilters.societyAffiliations?.values;
    if (societyAffiliationsAppliedFilter?.length) {
      filters.push({
        nested: {
          path: "affiliations",
          query: {
            terms: {
              ["affiliations.institution.name.keyword"]:
                societyAffiliationsAppliedFilter!
            }
          }
        }
      });
    }
    if (suppliedFilters.hasSocietyAffiliation?.value) {
      filters.push(toTermFilter("hasSocietyAffiliation", true));
    }

    // This is currently only required for FONameSearch endpoint where filter input can contain either student or current work affiliation.
    if (!filterField && suppliedFilters.institution.values.length > 0) {
      if (isLLMgenerated) {
        filters.push({
          bool: {
            should: [
              {
                wildcard: {
                  presentWorkInstitutionNames_multi: {
                    value: `*${suppliedFilters.institution.values[0]}*`,
                    case_insensitive: true
                  }
                }
              },
              {
                wildcard: {
                  studentInstitutionNames_eng: {
                    value: `*${suppliedFilters.institution.values[0]}*`,
                    case_insensitive: true
                  }
                }
              }
            ]
          }
        });
      } else {
        filters.push({
          bool: {
            should: [
              this.toTermsFilter(
                "studentInstitutionNames_eng",
                suppliedFilters.institution.values
              ),
              this.toTermsFilter(
                "presentWorkInstitutionNames_multi",
                suppliedFilters.institution.values
              )
            ]
          }
        });
      }
    }

    if (
      suppliedFilters.graduationYearRange.min ||
      suppliedFilters.graduationYearRange.max
    ) {
      const graduationYearRange: QueryDslQueryContainer = {
        range: {
          medSchoolGraduationYear: {
            format: "yyyy"
          }
        }
      };

      if (graduationYearRange.range?.medSchoolGraduationYear) {
        if (suppliedFilters.graduationYearRange.min) {
          graduationYearRange.range.medSchoolGraduationYear.gte =
            suppliedFilters.graduationYearRange.min;
        }
        if (suppliedFilters.graduationYearRange.max) {
          graduationYearRange.range.medSchoolGraduationYear.lte =
            suppliedFilters.graduationYearRange.max;
        }
      }

      filters.push(graduationYearRange);
    }

    return filters;
  }

  private buildInstitutionExclusionFilters({
    suppliedFilters
  }: KeywordFilterAutocompleteInput): Array<QueryDslQueryContainer> {
    const filters = [];
    if (!_.isEmpty(suppliedFilters.presentWorkInstitutionsExclusion?.values)) {
      filters.push(
        this.toTermsFilter(
          "presentWorkInstitutionNames_multi",
          suppliedFilters.presentWorkInstitutionsExclusion!.values
        )
      );
    }

    if (!_.isEmpty(suppliedFilters.studentInstitutionsExclusion?.values)) {
      filters.push(
        this.toTermsFilter(
          // there is no studentInstitutionNames_multi so replicate prod behavior
          "studentInstitutionNames_eng",
          suppliedFilters.studentInstitutionsExclusion!.values
        )
      );
    }
    if (filters.length > 0) {
      return this.buildExclusionQuery(filters);
    }

    return [];
  }

  private buildPatientDiversityFilters({
    filterField,
    filterValue,
    suppliedFilters,
    sortBy
  }: KeywordFilterAutocompleteInput): Array<QueryDslQueryContainer> {
    const filters: Array<QueryDslQueryContainer> = [];

    if (
      filterField ===
        KeywordFilterAutocompleteFilterField.DIVERSITY_PATIENT_DIVERSITY &&
      filterValue
    ) {
      filters.push(
        this.toMatchPhraseQuery(
          "patientsDiversityRaces_eng.autocomplete_search",
          filterValue
        )
      );
    }

    const raceFilterValues = suppliedFilters.patientsDiversity.race.values;

    if (
      raceFilterValues.length === 0 &&
      sortBy?.patientsDiversityRank === 1.0
    ) {
      // If there is no specific race filter then the only filter that we want to apply is that patientsDiversityPercent > 0.
      // As the HCPs without any diverse patient demography are not part of the results for "H1 Diversity Sort"
      filters.push(
        this.toFieldRangeGreaterThanEqualToValueFilter(
          "patientsDiversityPercent",
          0
        )
      );
    } else if (raceFilterValues.length > 0) {
      // Note: It does not make much sense if users apply race filter with the default H1 Ranking. Nevertheless the filter keeps only those HCPs in the result
      // that have treated the race selected by the user in the filter
      // If the user has selected specific races in the frontend then the "H1 Diversity Sort" result set should contain only those HCPs who have treated
      // patients of atleast one selected race. For example if the filter selected is "Hispanic" then "patientsDiversityRatio.hispanic" should be > 0 for every result returned.

      const shoulds: Array<QueryDslQueryContainer> = [];
      for (const raceFilterValue of raceFilterValues) {
        try {
          const patientsDiversityRatioField =
            "patientsDiversityRatio." +
            this.mapRaceFilterValueToPatientsDiversityRatioField(
              raceFilterValue
            );
          shoulds.push(
            this.toFieldRangeGreaterThanZeroFilter(patientsDiversityRatioField)
          );
        } catch (error) {
          this.logger.error(error);
        }
      }
      if (shoulds.length > 0) {
        filters.push({
          bool: {
            should: shoulds,
            minimum_should_match: 1
          }
        });
      }
    }

    if (
      filterField === KeywordFilterAutocompleteFilterField.DIVERSITY_PATIENT_AGE
    ) {
      if (filterValue) {
        filters.push(
          this.toMatchPhraseQuery(
            "patientsDiversityAgeRanges_eng.autocomplete_search",
            filterValue
          )
        );
      }
    }

    if (
      filterField === KeywordFilterAutocompleteFilterField.DIVERSITY_PATIENT_SEX
    ) {
      if (filterValue) {
        filters.push(
          this.toMatchPhraseQuery(
            "patientsDiversitySex_eng.autocomplete_search",
            filterValue
          )
        );
      }
    } else if (suppliedFilters.patientsDiversity.sex.values.length > 0) {
      filters.push(
        this.toTermsFilter(
          "patientsDiversitySex_eng",
          suppliedFilters.patientsDiversity.sex.values
        )
      );
    }

    return filters;
  }

  private buildPatientDiversityFiltersForPatientPopulation(
    input: KeywordSearchInput
  ) {
    const filters = [];
    const raceFilterValues =
      input.suppliedFilters?.patientsDiversity?.race?.values ?? [];
    if (raceFilterValues.length > 0) {
      filters.push(
        this.toTermsFilter("patientClaims.diversity", raceFilterValues)
      );
    }
    const ageFiltervalues =
      input.suppliedFilters?.patientsDiversity?.ageRange?.values ?? [];
    if (ageFiltervalues.length > 0) {
      filters.push(this.toTermsFilter("patientClaims.age", ageFiltervalues));
    }
    const sexFilterValues =
      input.suppliedFilters?.patientsDiversity?.sex?.values ?? [];
    if (sexFilterValues.length > 0) {
      filters.push(
        this.toTermsFilter(
          "patientClaims.gender",
          sexFilterValues.map((sex) => {
            return sex === "male" ? "M" : "F";
          })
        )
      );
    }
    return filters;
  }

  private buildPatientDiversityExclusionFilters({
    suppliedFilters
  }: KeywordFilterAutocompleteInput): Array<QueryDslQueryContainer> {
    const filters = [];

    if (!_.isEmpty(suppliedFilters.patientsDiversityExclusion?.race.values)) {
      const raceFilterValues =
        suppliedFilters.patientsDiversityExclusion!.race.values;
      for (const raceFilterValue of raceFilterValues) {
        try {
          const patientsDiversityRatioField =
            "patientsDiversityRatio." +
            this.mapRaceFilterValueToPatientsDiversityRatioField(
              raceFilterValue
            );
          filters.push(this.toFieldValueExists(patientsDiversityRatioField));
        } catch (error) {
          this.logger.error(error);
        }
      }
    }

    if (
      !_.isEmpty(suppliedFilters.patientsDiversityExclusion?.ageRange.values)
    ) {
      filters.push(
        this.toTermsFilter(
          "patientsDiversityAgeRanges_eng",
          suppliedFilters.patientsDiversityExclusion!.ageRange.values
        )
      );
    }

    if (!_.isEmpty(suppliedFilters.patientsDiversityExclusion?.sex.values)) {
      filters.push(
        this.toTermsFilter(
          "patientsDiversitySex_eng",
          suppliedFilters.patientsDiversityExclusion!.sex.values
        )
      );
    }

    if (filters.length > 0) {
      return this.buildExclusionQuery(filters);
    }

    return [];
  }

  private buildPatientDiversityRaceThresholdFilter({
    suppliedFilters
  }: KeywordFilterAutocompleteInput): Array<QueryDslQueryContainer> {
    const raceFilterMinMaxPercentages =
      suppliedFilters.patientsDiversity?.raceMix?.thresholdValue;

    if (!raceFilterMinMaxPercentages?.length) return [];

    const shoulds: Array<QueryDslQueryContainer> = raceFilterMinMaxPercentages
      .map((raceThresholdValue: RaceThreshold) => {
        try {
          return this.toFieldRangeMinMaxFilter(
            "patientsDiversityRatio." +
              this.mapRaceFilterValueToPatientsDiversityRatioField(
                raceThresholdValue.key
              ),
            {
              min: raceThresholdValue.min
                ? raceThresholdValue.min / 100
                : MINIMUM_RACE_THRESHOLD,
              max: raceThresholdValue.max
                ? raceThresholdValue.max / 100
                : MAXIMUM_RACE_THRESHOLD
            }
          );
        } catch (error) {
          this.logger.error(error);
          return undefined;
        }
      })
      .filter(
        (
          filter: QueryDslQueryContainer | undefined
        ): filter is QueryDslQueryContainer => !!filter
      );

    const filters: Array<QueryDslQueryContainer> = [];
    if (shoulds.length > 0) {
      filters.push({
        bool: {
          should: shoulds,
          minimum_should_match: 1
        }
      });
    }

    return filters;
  }

  private buildProviderDiversityFilters(
    {
      filterField,
      filterValue,
      suppliedFilters
    }: KeywordFilterAutocompleteInput,
    featureFlags: FeatureFlags
  ): Array<QueryDslQueryContainer> {
    const filters: Array<QueryDslQueryContainer> = [];

    const providerDiversityIsNotAIQFilter = {
      nested: {
        path: "providerDiversity",
        query: {
          term: {
            "providerDiversity.isAIQ": false
          }
        }
      }
    };

    if (
      filterField ===
      KeywordFilterAutocompleteFilterField.DIVERSITY_PROVIDER_LANGUAGES
    ) {
      if (filterValue) {
        filters.push(
          this.toMatchPhraseQuery(
            "providerDiversityLanguagesSpoken_eng.autocomplete_search",
            filterValue
          )
        );
      }
    } else if (
      suppliedFilters.providerDiversity.languagesSpoken.values.length > 0
    ) {
      filters.push(
        this.toTermsFilter(
          "providerDiversityLanguagesSpoken_eng",
          suppliedFilters.providerDiversity.languagesSpoken.values
        )
      );
    }

    if (
      filterField ===
      KeywordFilterAutocompleteFilterField.DIVERSITY_PROVIDER_RACE
    ) {
      if (
        featureFlags &&
        "enableAiqData" in featureFlags &&
        !featureFlags?.enableAiqData
      ) {
        filters.push(providerDiversityIsNotAIQFilter);
      }

      if (filterValue) {
        filters.push(
          this.toMatchPhraseQuery(
            "providerDiversityRaces_eng.autocomplete_search",
            filterValue
          )
        );
      }
    } else if (suppliedFilters.providerDiversity.race.values.length > 0) {
      if (
        featureFlags &&
        "enableAiqData" in featureFlags &&
        !featureFlags?.enableAiqData
      ) {
        filters.push(providerDiversityIsNotAIQFilter);
      }

      filters.push(
        this.toTermsFilter(
          "providerDiversityRaces_eng",
          suppliedFilters.providerDiversity.race.values
        )
      );
    }

    if (
      filterField ===
      KeywordFilterAutocompleteFilterField.DIVERSITY_PROVIDER_SEX
    ) {
      if (filterValue) {
        filters.push(
          this.toMatchPhraseQuery(
            "providerDiversitySex_eng.autocomplete_search",
            filterValue
          )
        );
      }
    } else if (suppliedFilters.providerDiversity.sex.values.length > 0) {
      filters.push(
        this.toTermsFilter(
          "providerDiversitySex_eng",
          suppliedFilters.providerDiversity.sex.values
        )
      );
    }

    return filters;
  }

  private buildProviderDiversityExclusionFilters({
    suppliedFilters
  }: KeywordFilterAutocompleteInput): Array<QueryDslQueryContainer> {
    const filters = [];

    if (!_.isEmpty(suppliedFilters.providerDiversityExclusion?.race.values)) {
      filters.push(
        this.toTermsFilter(
          "providerDiversityRaces_eng",
          suppliedFilters.providerDiversityExclusion!.race.values
        )
      );
    }

    if (
      !_.isEmpty(
        suppliedFilters.providerDiversityExclusion?.languagesSpoken.values
      )
    ) {
      filters.push(
        this.toTermsFilter(
          "providerDiversityLanguagesSpoken_eng",
          suppliedFilters.providerDiversityExclusion!.languagesSpoken.values
        )
      );
    }

    if (!_.isEmpty(suppliedFilters.providerDiversityExclusion?.sex.values)) {
      filters.push(
        this.toTermsFilter(
          "providerDiversitySex_eng",
          suppliedFilters.providerDiversityExclusion!.sex.values
        )
      );
    }

    if (filters.length > 0) {
      return this.buildExclusionQuery(filters);
    }

    return [];
  }

  private buildReferralsFilters({
    filterField,
    filterValue,
    suppliedFilters
  }: KeywordFilterAutocompleteInput): Array<QueryDslQueryContainer> {
    const filters: Array<QueryDslQueryContainer> = [];

    const hasSentMinCount = !!suppliedFilters.referrals.minReferralsSent.value;
    const hasReceivedMinCount =
      !!suppliedFilters.referrals.minReferralsReceived.value;
    const hasSentMaxCount = !!suppliedFilters.referrals.maxReferralsSent?.value;
    const hasReceivedMaxCount =
      !!suppliedFilters.referrals.maxReferralsReceived?.value;
    const hasAnyReferralCount =
      hasSentMinCount ||
      hasSentMaxCount ||
      hasReceivedMinCount ||
      hasReceivedMaxCount;

    if (
      filterField ===
      KeywordFilterAutocompleteFilterField.REFERRALS_SERVICE_LINE
    ) {
      if (filterValue) {
        filters.push(
          this.toMatchPhraseQuery(
            "referralsServiceLine_eng.autocomplete_search",
            filterValue
          )
        );
      }
    } else if (
      !hasAnyReferralCount &&
      suppliedFilters.referrals.serviceLine.values.length > 0
    ) {
      filters.push(
        this.toTermsFilter(
          "referralsServiceLine_eng",
          suppliedFilters.referrals.serviceLine.values
        )
      );
    }

    if (hasSentMinCount || hasSentMaxCount) {
      if (suppliedFilters.referrals.serviceLine.values.length > 0) {
        const filter = this.buildNestedReferralsSentCountQuery(suppliedFilters);
        filters.push(filter);
      } else {
        filters.push(
          this.toFieldRangeMinMaxFilter("referralsSentCount", {
            min: suppliedFilters.referrals.minReferralsSent.value,
            max: suppliedFilters.referrals.maxReferralsSent?.value ?? null
          })
        );
      }
    }

    if (hasReceivedMinCount || hasReceivedMaxCount) {
      if (suppliedFilters.referrals.serviceLine.values.length > 0) {
        const filter =
          this.buildNestedReferralsReceivedCountQuery(suppliedFilters);
        filters.push(filter);
      } else {
        filters.push(
          this.toFieldRangeMinMaxFilter("referralsReceivedCount", {
            min: suppliedFilters.referrals.minReferralsReceived.value,
            max: suppliedFilters.referrals.maxReferralsReceived?.value ?? null
          })
        );
      }
    }

    return filters;
  }

  private buildReferralsExclusionFilters({
    suppliedFilters
  }: KeywordFilterAutocompleteInput): Array<QueryDslQueryContainer> {
    const filters = [];
    if (!_.isEmpty(suppliedFilters.referralsExclusion?.serviceLine.values)) {
      filters.push(
        this.toTermsFilter(
          "referralsServiceLine_eng",
          suppliedFilters.referralsExclusion!.serviceLine.values
        )
      );
    }

    if (filters.length > 0) {
      return this.buildExclusionQuery(filters);
    }

    return [];
  }

  private buildNestedReferralsSentCountQuery(
    suppliedFilters: FilterInterface
  ): QueryDslQueryContainer {
    const nestedReferralsSentQuery: QueryDslQueryContainer = {
      function_score: {
        query: {
          nested: {
            path: "referralsSent",
            score_mode: "sum",
            query: {
              bool: {
                must: [
                  {
                    function_score: {
                      functions: [
                        {
                          field_value_factor: {
                            field: "referralsSent.count",
                            missing: 0
                          }
                        }
                      ]
                    }
                  }
                ],
                filter: [
                  this.toTermsFilter(
                    "referralsSent.serviceDescription_eng",
                    suppliedFilters.referrals.serviceLine.values
                  )
                ]
              }
            }
          }
        },
        functions: [
          {
            script_score: buildMinMaxFilterScriptForAsset(
              suppliedFilters.referrals.minReferralsSent.value,
              suppliedFilters.referrals.maxReferralsSent?.value
            )
          }
        ],
        min_score: 1
      }
    };

    return nestedReferralsSentQuery;
  }

  private buildNestedReferralsReceivedCountQuery(
    suppliedFilters: FilterInterface
  ): QueryDslQueryContainer {
    const nestedReferralsReceivedQuery: QueryDslQueryContainer = {
      function_score: {
        query: {
          nested: {
            path: "referralsReceived",
            score_mode: "sum",
            query: {
              bool: {
                must: [
                  {
                    function_score: {
                      functions: [
                        {
                          field_value_factor: {
                            field: "referralsReceived.count",
                            missing: 0
                          }
                        }
                      ]
                    }
                  }
                ],
                filter: [
                  this.toTermsFilter(
                    "referralsReceived.serviceDescription_eng",
                    suppliedFilters.referrals.serviceLine.values
                  )
                ]
              }
            }
          }
        },
        functions: [
          {
            script_score: buildMinMaxFilterScriptForAsset(
              suppliedFilters.referrals.minReferralsReceived.value,
              suppliedFilters.referrals.maxReferralsReceived?.value
            )
          }
        ],
        min_score: 1
      }
    };

    return nestedReferralsReceivedQuery;
  }

  private toTermsFilter(
    fieldName: string,
    values: string[]
  ): QueryDslQueryContainer {
    return {
      terms: {
        [fieldName]: values
      }
    };
  }

  private toWilcardFilter(
    fieldName: string,
    value: string
  ): QueryDslQueryContainer {
    return {
      wildcard: {
        [fieldName]: value
      }
    };
  }

  private toFieldValueExists(fieldName: string): QueryDslQueryContainer {
    return {
      bool: {
        must: [toExists(fieldName)]
      }
    };
  }

  private toFieldRangeGreaterThanZeroFilter(
    fieldName: string
  ): QueryDslQueryContainer {
    return {
      range: {
        [fieldName]: {
          gt: 0
        }
      }
    };
  }

  private toFieldRangeGreaterThanEqualToValueFilter(
    fieldName: string,
    value: number
  ): QueryDslQueryContainer {
    return {
      range: {
        [fieldName]: {
          gte: value
        }
      }
    };
  }

  private toFieldRangeMinMaxFilter(
    fieldName: string,
    valueRange: ValueRange
  ): QueryDslQueryContainer {
    return {
      range: {
        [fieldName]: {
          gte: valueRange.min ?? undefined,
          lte: valueRange.max ?? undefined
        }
      }
    };
  }

  private toMultiMatchPhraseQuery(
    fieldNames: string[],
    value: string
  ): QueryDslQueryContainer {
    return {
      multi_match: {
        type: "phrase",
        query: value,
        fields: fieldNames,
        slop: 5
      }
    };
  }

  private toMatchPhraseQuery(
    fieldName: string,
    value: string
  ): QueryDslQueryContainer {
    return {
      match_phrase: {
        [fieldName]: {
          query: value,
          slop: 5
        }
      }
    };
  }

  private toDateRangeFilter(
    path: string,
    dateRange: QueryDslRangeQuery
  ): QueryDslQueryContainer {
    return {
      bool: {
        should: [
          {
            range: {
              [path]: {
                ...dateRange,
                format: "epoch_millis"
              }
            }
          },
          {
            bool: {
              must_not: toExists(path)
            }
          }
        ],
        minimum_should_match: 1
      }
    };
  }

  private buildCongressFiltersForSearch(
    {
      filterField,
      filterValue,
      suppliedFilters
    }: KeywordFilterAutocompleteInput,
    parsedQueryTree?: ParsedQueryTree
  ) {
    const filters: Array<QueryDslQueryContainer> = [];

    if (filterField === KeywordFilterAutocompleteFilterField.CONGRESS_NAME) {
      if (filterValue) {
        filters.push(
          this.toMatchPhraseQuery(
            "congress.name_eng.autocomplete_search",
            filterValue
          )
        );
      }
    } else if (suppliedFilters.congresses.name.values.length > 0) {
      filters.push(
        this.toTermsFilter(
          "congress.name_eng",
          suppliedFilters.congresses.name.values
        )
      );
    }

    if (
      filterField === KeywordFilterAutocompleteFilterField.CONGRESS_ORGANIZER
    ) {
      if (filterValue) {
        filters.push(
          this.toMatchPhraseQuery(
            "congress.organizer_eng.autocomplete_search",
            filterValue
          )
        );
      }
    } else if (suppliedFilters.congresses.organizerName.values.length > 0) {
      filters.push(
        this.toTermsFilter(
          "congress.organizer_eng",
          suppliedFilters.congresses.organizerName.values
        )
      );
    }

    if (
      filterField ===
      KeywordFilterAutocompleteFilterField.CONGRESS_CONTRIBUTOR_ROLE
    ) {
      if (filterValue) {
        filters.push(
          this.toMatchPhraseQuery(
            "congress.role.autocomplete_search",
            filterValue
          )
        );
      }
    } else if (suppliedFilters.congresses.contributorRole?.values?.length) {
      filters.push(
        this.toTermsFilter(
          "congress.role",
          suppliedFilters.congresses.contributorRole!.values
        )
      );
    }

    const dateRange = this.parsePastDateRangeEnum(
      suppliedFilters.congresses.timeFrame?.value
    );

    if (dateRange) {
      filters.push({
        range: {
          "congress.masterInitialDate": dateRange
        }
      });
    }

    const sessionTimeFrame = suppliedFilters.congresses.sessionTimeFrame?.value;
    if (sessionTimeFrame?.min || sessionTimeFrame?.max) {
      filters.push(
        this.toDateRangeFilter("congress.presentedDate", {
          gte: sessionTimeFrame?.min,
          lte: sessionTimeFrame?.max ?? undefined
        })
      );
    }

    if (
      parsedQueryTree &&
      (suppliedFilters.congresses.minCount.value || filters.length)
    ) {
      filters.push(this.parseQueryTreeForCongress(parsedQueryTree));
    }

    if (
      suppliedFilters.congresses.minCount.value ||
      suppliedFilters.congresses.maxCount?.value
    ) {
      const functionScoreQuery: QueryDslFunctionScoreQuery = {
        query: {
          nested: {
            path: "congress",
            score_mode: "sum",
            query: {
              bool: {
                must: {
                  match_all: {}
                },
                filter: filters
              }
            }
          }
        },
        functions: [
          {
            script_score: buildMinMaxFilterScriptForAsset(
              suppliedFilters.congresses.minCount.value,
              suppliedFilters.congresses.maxCount?.value
            )
          }
        ],
        min_score: 1
      };

      return [{ function_score: functionScoreQuery }];
    } else if (filters.length) {
      const nestedQuery = {
        path: "congress",
        query: {
          bool: {
            filter: filters
          }
        }
      };

      return [{ nested: nestedQuery }];
    }

    return [];
  }

  private parseQueryTreeForCongress(
    parsedQueryTree: ParsedQueryTree
  ): QueryDslQueryContainer {
    return this.parsedQueryTreeToElasticsearchQueriesService.parse(
      parsedQueryTree!,
      [
        "congress.keywords_eng",
        "congress.title_eng",
        "congress.organizer_eng.search",
        "congress.name_eng.search",
        "congress.role.search"
      ]
    );
  }

  private buildCongressExclusionFiltersForSearch({
    suppliedFilters
  }: KeywordFilterAutocompleteInput): Array<QueryDslQueryContainer> {
    const filters = [];
    if (!_.isEmpty(suppliedFilters.congressesExclusion?.name.values)) {
      filters.push(
        this.toTermsFilter(
          "congress.name_eng",
          suppliedFilters.congressesExclusion!.name.values
        )
      );
    }

    if (!_.isEmpty(suppliedFilters.congressesExclusion?.organizerName.values)) {
      filters.push(
        this.toTermsFilter(
          "congress.organizer_eng",
          suppliedFilters.congressesExclusion!.organizerName.values
        )
      );
    }

    if (
      !_.isEmpty(suppliedFilters.congressesExclusion?.contributorRole?.values)
    ) {
      filters.push(
        this.toTermsFilter(
          "congress.role",
          suppliedFilters.congressesExclusion!.contributorRole!.values
        )
      );
    }

    if (filters.length > 0) {
      return this.buildNestedExclusionQueryForAsset("congress", filters);
    }

    return [];
  }

  private buildTrialsFiltersForSearch(
    {
      query,
      suppliedFilters,
      filterValue,
      filterField
    }: KeywordFilterAutocompleteInput,
    parsedQueryTree?: ParsedQueryTree
  ): Array<QueryDslQueryContainer> {
    const filters: Array<QueryDslQueryContainer> = [];

    if (filterField === KeywordFilterAutocompleteFilterField.TRIAL_STATUS) {
      if (filterValue) {
        filters.push(
          this.toMatchPhraseQuery(
            "trials.status_eng.autocomplete_search",
            filterValue
          )
        );
      }
    } else if (suppliedFilters.trials.status.values.length > 0) {
      filters.push(
        this.toTermsFilter(
          "trials.status_eng",
          suppliedFilters.trials.status.values
        )
      );
    }

    if (filterField === KeywordFilterAutocompleteFilterField.TRIAL_PHASE) {
      if (filterValue) {
        filters.push(
          this.toMatchPhraseQuery(
            "trials.phase_eng.autocomplete_search",
            filterValue
          )
        );
      }
    } else if (suppliedFilters.trials.phase.values.length > 0) {
      filters.push(
        this.toTermsFilter(
          "trials.phase_eng",
          suppliedFilters.trials.phase.values
        )
      );
    }

    if (filterField === KeywordFilterAutocompleteFilterField.TRIAL_STUDY_TYPE) {
      if (filterValue) {
        filters.push(
          this.toMatchPhraseQuery(
            "trials.type_eng.autocomplete_search",
            filterValue
          )
        );
      }
    } else if (suppliedFilters.trials.studyType.values.length > 0) {
      filters.push(
        this.toTermsFilter(
          "trials.type_eng",
          suppliedFilters.trials.studyType.values
        )
      );
    }

    if (filterField === KeywordFilterAutocompleteFilterField.TRIAL_SPONSOR) {
      if (filterValue) {
        filters.push(
          this.toMatchPhraseQuery(
            "trials.sponsor_eng.autocomplete_search",
            filterValue
          )
        );
      }
    } else if (suppliedFilters.trials.sponsor.values.length > 0) {
      filters.push(
        this.toTermsFilter(
          "trials.sponsor_eng",
          suppliedFilters.trials.sponsor.values
        )
      );
    }

    if (
      filterField === KeywordFilterAutocompleteFilterField.TRIAL_SPONSOR_TYPE
    ) {
      if (filterValue) {
        filters.push(
          this.toMatchPhraseQuery(
            "trials.sponsorType_eng.autocomplete_search",
            filterValue
          )
        );
      }
    } else if (suppliedFilters.trials.sponsorType.values.length > 0) {
      filters.push(
        this.toTermsFilter(
          "trials.sponsorType_eng",
          suppliedFilters.trials.sponsorType.values
        )
      );
    }

    if (filterField === KeywordFilterAutocompleteFilterField.BIOMARKERS) {
      if (filterValue) {
        filters.push(
          this.toMatchPhraseQuery(
            "trials.biomarker.autocomplete_search",
            filterValue
          )
        );
      }
    } else if (
      suppliedFilters.trials.biomarkers !== undefined &&
      suppliedFilters.trials.biomarkers.values.length > 0
    ) {
      filters.push(
        this.toTermsFilter(
          "trials.biomarker",
          suppliedFilters.trials.biomarkers.values
        )
      );
    }

    if (filterField === KeywordFilterAutocompleteFilterField.TRIAL_ID) {
      if (filterValue) {
        // filtervalue is uppercased because trials.id field is defined as keyword and all ids are upppercased starting with NCT
        filters.push(
          this.toWilcardFilter("trials.id", filterValue.toUpperCase() + "*")
        );
      }
    } else if (
      suppliedFilters.trials.id !== undefined &&
      suppliedFilters.trials.id.values.length > 0
    ) {
      filters.push(
        this.toTermsFilter("trials.id", suppliedFilters.trials.id.values)
      );
    }

    if (filterField === KeywordFilterAutocompleteFilterField.TRIAL_ROLES) {
      if (filterValue) {
        filters.push(
          this.toMatchPhraseQuery(
            "trials.roles.autocomplete_search",
            filterValue
          )
        );
      }
    } else if (
      suppliedFilters.trials.roles !== undefined &&
      suppliedFilters.trials.roles.values.length > 0
    ) {
      // replace "Responsible Party" with "Sponsor"
      //as we renamed this role and we want to return results for saved searches we need to support this
      // keep both values which doesn't harm anything else
      if (
        suppliedFilters.trials.roles.values.includes("Responsible Party") &&
        !suppliedFilters.trials.roles.values.includes("Sponsor")
      ) {
        suppliedFilters.trials.roles.values.push("Sponsor");
      }

      filters.push(
        this.toTermsFilter("trials.roles", suppliedFilters.trials.roles.values)
      );
    }

    if (
      suppliedFilters.trials.timeFrame &&
      suppliedFilters.trials.timeFrame.min &&
      suppliedFilters.trials.timeFrame.max
    ) {
      filters.push({
        range: {
          "trials.startDate": {
            gte: suppliedFilters.trials.timeFrame.min,
            lte: suppliedFilters.trials.timeFrame.max
          }
        }
      });
    }

    const minMaxConditions: string[] = [];

    if (suppliedFilters.trials.minCount.value) {
      minMaxConditions.push("(_score >= params.minValue)");
    }

    if (
      !(
        _.isUndefined(suppliedFilters.trials.maxCount?.value) ||
        _.isNull(suppliedFilters.trials.maxCount?.value)
      )
    ) {
      minMaxConditions.push("(_score <= params.maxValue)");
    }

    if (filters.length || minMaxConditions.length) {
      // filter the nested trials based on the indication filters applied by the user or implictily applied by the query. This uses KG indication-trials tags.
      const indicationValues = suppliedFilters.indications?.values;
      if (indicationValues?.length) {
        filters.push(
          this.toTermsFilter("trials.kgIndications", indicationValues)
        );
      }

      // if the user query does not have any matching indications and is not an advanced operator query then we need to add it as a filter.
      const queryIsIndication =
        parsedQueryTree &&
        typeof query === "string" &&
        typeof parsedQueryTree === "string" &&
        indicationValues &&
        indicationValues.includes(query.trim().toLowerCase());
      if (parsedQueryTree && !queryIsIndication) {
        filters.push(this.parseQueryTreeForTrials(parsedQueryTree));
      }
    }

    if (minMaxConditions.length) {
      const isMaxCountZero = suppliedFilters.trials.maxCount?.value === 0;
      if (isMaxCountZero) {
        const zeroTrialsQuery: QueryDslQueryContainer = {
          bool: {
            must_not: {
              nested: {
                path: "trials",
                query: {
                  exists: {
                    field: "trials"
                  }
                }
              }
            }
          }
        };
        return [zeroTrialsQuery];
      }
      const functionScoreQuery: QueryDslFunctionScoreQuery = {
        query: {
          nested: {
            path: "trials",
            score_mode: "sum",
            query: {
              bool: {
                must: {
                  match_all: {}
                },
                filter: filters
              }
            }
          }
        },
        functions: [
          {
            script_score: {
              script: {
                params: {
                  minValue: suppliedFilters.trials.minCount.value,
                  maxValue: suppliedFilters.trials.maxCount?.value
                },
                source: `${minMaxConditions.join("&&")}? 1:0`
              }
            }
          }
        ],
        min_score: 1
      };

      return [{ function_score: functionScoreQuery }];
    } else if (filters.length) {
      const nestedQuery = {
        path: "trials",
        query: {
          bool: {
            filter: filters
          }
        }
      };

      return [{ nested: nestedQuery }];
    }

    return [];
  }

  private parseQueryTreeForTrials(
    parsedQueryTree: ParsedQueryTree
  ): QueryDslQueryContainer {
    return this.parsedQueryTreeToElasticsearchQueriesService.parse(
      parsedQueryTree!,
      [
        "trials.officialTitle",
        "trials.briefTitle",
        "trials.conditions",
        "trials.interventions",
        "trials.keywords",
        "trials.summary"
      ],
      ENGLISH
    );
  }

  private buildTrialsExclusionFiltersForSearch({
    suppliedFilters
  }: KeywordFilterAutocompleteInput): Array<QueryDslQueryContainer> {
    const filters = [];

    if (!_.isEmpty(suppliedFilters.trialsExclusion?.status.values)) {
      filters.push(
        this.toTermsFilter(
          "trials.status_eng",
          suppliedFilters.trialsExclusion!.status.values
        )
      );
    }

    if (!_.isEmpty(suppliedFilters.trialsExclusion?.phase.values)) {
      filters.push(
        this.toTermsFilter(
          "trials.phase_eng",
          suppliedFilters.trialsExclusion!.phase.values
        )
      );
    }

    if (!_.isEmpty(suppliedFilters.trialsExclusion?.sponsor.values)) {
      filters.push(
        this.toTermsFilter(
          "trials.sponsor_eng",
          suppliedFilters.trialsExclusion!.sponsor.values
        )
      );
    }

    if (!_.isEmpty(suppliedFilters.trialsExclusion?.sponsorType.values)) {
      filters.push(
        this.toTermsFilter(
          "trials.sponsorType_eng",
          suppliedFilters.trialsExclusion!.sponsorType.values
        )
      );
    }

    if (!_.isEmpty(suppliedFilters.trialsExclusion?.studyType.values)) {
      filters.push(
        this.toTermsFilter(
          "trials.type_eng",
          suppliedFilters.trialsExclusion!.studyType.values
        )
      );
    }

    if (filters.length > 0) {
      return this.buildNestedExclusionQueryForAsset("trials", filters);
    }

    return [];
  }

  public eitherMinOrMaxDateRangeValuesWereSupplied({
    dateRangePicker
  }: FilterInterface) {
    return dateRangePicker.min || dateRangePicker.max;
  }

  private buildPublicationsFiltersForSearch(
    {
      query,
      filterField,
      filterValue,
      suppliedFilters
    }: KeywordFilterAutocompleteInput,
    languageDetector: LanguageDetector,
    parsedQueryTree?: ParsedQueryTree
  ): Array<QueryDslQueryContainer> {
    const publicationSidePanelFilter: Array<QueryDslQueryContainer> = [];

    if (filterField === KeywordFilterAutocompleteFilterField.JOURNAL_NAME) {
      if (filterValue) {
        const language = languageDetector(filterValue);
        publicationSidePanelFilter.push(
          this.toMatchPhraseQuery(
            `publications.journalName_${language}.autocomplete_search`,
            filterValue
          )
        );
      }
    } else if (suppliedFilters.publications.journal.values.length > 0) {
      publicationSidePanelFilter.push(
        this.toTermsFilter(
          "publications.journal_multi",
          suppliedFilters.publications.journal.values
        )
      );
    }

    if (filterField === KeywordFilterAutocompleteFilterField.PUBLICATION_TYPE) {
      if (filterValue) {
        publicationSidePanelFilter.push(
          this.toMatchPhraseQuery(
            "publications.type_eng.autocomplete_search",
            filterValue
          )
        );
      }
    } else if (suppliedFilters.publications.type.values.length > 0) {
      publicationSidePanelFilter.push(
        this.toTermsFilter(
          "publications.type_eng",
          suppliedFilters.publications.type.values
        )
      );
    }

    publicationSidePanelFilter.push(
      ...this.buildAuthorOrderFilters(suppliedFilters)
    );

    const dateRange = this.parsePastDateRangeEnum(
      suppliedFilters.publications.publicationDate?.value
    );

    if (dateRange) {
      publicationSidePanelFilter.push({
        range: {
          "publications.datePublished": dateRange
        }
      });
    }

    const publicationUserQueryFilters = [];

    // filter the nested publications based on the indication filters applied by the user or implictily applied by the query. This uses KG indication-publication tags.
    const indicationValues = suppliedFilters.indications?.values;
    if (publicationSidePanelFilter.length === 0 && indicationValues?.length) {
      publicationUserQueryFilters.push(
        this.toTermsFilter("publications.kgIndications", indicationValues)
      );
    }

    // if the user query does not have any matching indications and is not an advanced operator query then we need to add it as a filter.
    const queryIsIndication =
      parsedQueryTree &&
      typeof query === "string" &&
      typeof parsedQueryTree === "string" &&
      indicationValues &&
      indicationValues.includes(query.trim().toLowerCase());
    if (parsedQueryTree && !queryIsIndication) {
      publicationUserQueryFilters.push(
        this.parseQueryTreeForPublications(parsedQueryTree)
      );
    }

    const finalFilters = [];
    if (
      publicationSidePanelFilter.length &&
      !suppliedFilters.publications.minCount.value &&
      !suppliedFilters.publications.socialMediaMinCount.value &&
      !suppliedFilters.publications.maxCount?.value &&
      !suppliedFilters.publications.socialMediaMaxCount?.value
    ) {
      const nestedQuery = {
        path: "publications",
        query: {
          bool: {
            filter: [
              ...publicationSidePanelFilter,
              ...publicationUserQueryFilters
            ] // qualify all the publications of an HCP who have the user query AND the side panel filters
          }
        }
      };

      finalFilters.push({ nested: nestedQuery });
    } else {
      if (
        suppliedFilters.publications.minCount.value ||
        suppliedFilters.publications.maxCount?.value
      ) {
        const functionScoreQuery: QueryDslFunctionScoreQuery = {
          query: {
            nested: {
              path: "publications",
              score_mode: "sum",
              query: {
                bool: {
                  must: {
                    match_all: {}
                  },
                  filter: [
                    ...publicationSidePanelFilter,
                    ...publicationUserQueryFilters
                  ]
                }
              }
            }
          },
          functions: [
            {
              script_score: buildMinMaxFilterScriptForAsset(
                suppliedFilters.publications.minCount.value,
                suppliedFilters.publications.maxCount?.value
              )
            }
          ],
          min_score: 1
        };

        finalFilters.push({ function_score: functionScoreQuery });
      }
      if (
        suppliedFilters.publications.socialMediaMinCount.value ||
        suppliedFilters.publications.socialMediaMaxCount?.value
      ) {
        const functionScoreQuery: QueryDslFunctionScoreQuery = {
          query: {
            nested: {
              path: "publications",
              score_mode: "sum",
              query: {
                bool: {
                  must: [
                    {
                      function_score: {
                        functions: [
                          {
                            field_value_factor: {
                              field: "publications.microBloggingCount",
                              missing: 0
                            }
                          }
                        ]
                      }
                    }
                  ],
                  filter: [
                    ...publicationSidePanelFilter,
                    ...publicationUserQueryFilters
                  ]
                }
              }
            }
          },
          functions: [
            {
              script_score: buildMinMaxFilterScriptForAsset(
                suppliedFilters.publications.socialMediaMinCount.value,
                suppliedFilters.publications.socialMediaMaxCount?.value
              )
            }
          ],
          min_score: 1
        };

        finalFilters.push({ function_score: functionScoreQuery });
      }
    }
    return finalFilters;
  }

  private buildAuthorOrderFilters(suppliedFilters: FilterInterface) {
    const filters = [];

    if (suppliedFilters.publications.isFirstOrder?.value) {
      filters.push(toTermFilter("publications.isFirstOrder", true));
    }

    if (suppliedFilters.publications.isLastOrder?.value) {
      filters.push(toTermFilter("publications.isLastOrder", true));
    }

    if (!filters.length) {
      return [];
    }

    return [this.toBoolShould(filters)];
  }

  private parsePastDateRangeEnum(
    dateRangeEnum: PastDateRangeEnumFilter | null | undefined
  ): QueryDslRangeQuery | undefined {
    if (!dateRangeEnum) {
      return;
    }

    const currentDate = new Date();

    switch (dateRangeEnum) {
      case PastDateRangeEnumFilter.OneYear:
        return {
          gte: subYears(currentDate, 1).getTime(),
          lte: currentDate.getTime()
        };
      case PastDateRangeEnumFilter.TwoYear:
        return {
          gte: subYears(currentDate, 2).getTime(),
          lte: currentDate.getTime()
        };
      case PastDateRangeEnumFilter.ThreeYear:
        return {
          gte: subYears(currentDate, 3).getTime(),
          lte: currentDate.getTime()
        };
      case PastDateRangeEnumFilter.FourYear:
        return {
          gte: subYears(currentDate, 4).getTime(),
          lte: currentDate.getTime()
        };
      case PastDateRangeEnumFilter.FiveYear:
        return {
          gte: subYears(currentDate, 5).getTime(),
          lte: currentDate.getTime()
        };
      case PastDateRangeEnumFilter.TenYear:
        return {
          gte: subYears(currentDate, 10).getTime(),
          lte: currentDate.getTime()
        };
      default:
        return;
    }
  }

  private parseCareerLengthFilterEnum(
    careerLengthFilterEnum: CareerLengthEnumFilter | null | undefined
  ): QueryDslRangeQuery | undefined {
    if (!careerLengthFilterEnum) {
      return;
    }

    switch (careerLengthFilterEnum) {
      case CareerLengthEnumFilter.UnderTen:
        return {
          gte: 0,
          lte: 10
        };
      case CareerLengthEnumFilter.TenToThirty:
        return {
          gte: 10,
          lte: 30
        };
      case CareerLengthEnumFilter.OverThirty:
        return {
          gte: 30
        };
      default:
        return;
    }
  }

  // change this query to use the new parsed query tree that includes indication term filter
  private parseQueryTreeForPublications(parsedQueryTree: ParsedQueryTree) {
    const parsedQuery = this.parsedQueryTreeToElasticsearchQueriesService.parse(
      parsedQueryTree!,
      publicationsQueryFields
    );
    return parsedQuery;
  }

  private findNestedAssetFilters(
    path: AssetPath,
    filter: QueryDslQueryContainer
  ) {
    return (
      filter.nested?.path === path ||
      filter.function_score?.query?.nested?.path === path
    );
  }

  private findNestedFilterArray(
    query: QueryDslQueryContainer
  ): Array<QueryDslQueryContainer> {
    return (query.nested?.query?.bool?.filter ||
      query.function_score?.query?.nested?.query?.bool
        ?.filter) as Array<QueryDslQueryContainer>;
  }

  private pushFilterToQueryContainer(
    query: QueryDslQueryContainer,
    filterToPush: QueryDslQueryContainer
  ) {
    const filtersArray = this.findNestedFilterArray(query);
    filtersArray?.push(filterToPush);
  }

  private getNestedDateRangeFilter(
    path: AssetPath,
    parsedQueryTree: ParsedQueryTree,
    dateRangeFilter: QueryDslQueryContainer
  ) {
    const filters: QueryDslQueryContainer[] = [];
    filters.push(dateRangeFilter);

    if (parsedQueryTree) {
      if (path === "congress") {
        const congressParsedQueryFilter =
          this.parseQueryTreeForCongress(parsedQueryTree);
        filters.push(congressParsedQueryFilter);
      } else if (path === "publications") {
        const publicationsParsedQueryFilter =
          this.parseQueryTreeForPublications(parsedQueryTree);
        filters.push(publicationsParsedQueryFilter);
      } else if (path === "trials") {
        const trialsParsedQueryFilter =
          this.parseQueryTreeForTrials(parsedQueryTree);
        filters.push(trialsParsedQueryFilter);
      }
    }

    return {
      nested: {
        path,
        query: {
          bool: {
            filter: filters
          }
        }
      }
    };
  }

  private addDateRangeFilterToExisting(
    path: AssetPath,
    filters: QueryDslQueryContainer[],
    input: KeywordFilterAutocompleteInput,
    parsedQueryTree: ParsedQueryTree,
    dateRangeFilter: QueryDslQueryContainer,
    autocompleteFilterFields: KeywordFilterAutocompleteFilterField[]
  ) {
    const existingFilter = filters.find((filter) =>
      this.findNestedAssetFilters(path, filter)
    );

    if (existingFilter) {
      this.pushFilterToQueryContainer(existingFilter, dateRangeFilter);
    } else if (
      input.filterField &&
      autocompleteFilterFields.includes(input.filterField)
    ) {
      filters.push(
        this.getNestedDateRangeFilter(path, parsedQueryTree, dateRangeFilter)
      );
    }

    return filters;
  }

  public addDateRangeFilterInNestedClause(
    suppliedFilters: FilterInterface
  ): [QueryDslQueryContainer, QueryDslQueryContainer, QueryDslQueryContainer] {
    const dateRange: QueryDslRangeQuery = {};

    if (suppliedFilters.dateRangePicker.min) {
      dateRange.gte = suppliedFilters.dateRangePicker.min;
    }
    if (suppliedFilters.dateRangePicker.max) {
      dateRange.lte = suppliedFilters.dateRangePicker.max;
    }

    const congressDateRangeFilter = this.toDateRangeFilter(
      "congress.masterInitialDate",
      dateRange
    );
    const trialsDateRangeFilter = this.toDateRangeFilter(
      "trials.startDate",
      dateRange
    );
    const publicationsDateRangeFilter = this.toDateRangeFilter(
      "publications.datePublished",
      dateRange
    );

    return [
      congressDateRangeFilter,
      trialsDateRangeFilter,
      publicationsDateRangeFilter
    ];
  }

  private buildDateRangeFilter(
    input: KeywordFilterAutocompleteInput,
    filters: Array<QueryDslQueryContainer>,
    languageDetector: LanguageDetector,
    parsedQueryTree: ParsedQueryTree
  ) {
    const suppliedFilters = input.suppliedFilters;
    const congressFilterFields = [
      KeywordFilterAutocompleteFilterField.CONGRESS_NAME,
      KeywordFilterAutocompleteFilterField.CONGRESS_ORGANIZER
    ];
    const publicationsFilterFields = [
      KeywordFilterAutocompleteFilterField.JOURNAL_NAME,
      KeywordFilterAutocompleteFilterField.PUBLICATION_TYPE
    ];
    const trialsFilterFields = [
      KeywordFilterAutocompleteFilterField.TRIAL_STATUS,
      KeywordFilterAutocompleteFilterField.TRIAL_PHASE,
      KeywordFilterAutocompleteFilterField.TRIAL_SPONSOR,
      KeywordFilterAutocompleteFilterField.TRIAL_SPONSOR_TYPE,
      KeywordFilterAutocompleteFilterField.TRIAL_STUDY_TYPE,
      KeywordFilterAutocompleteFilterField.TRIAL_ID
    ];

    if (this.eitherMinOrMaxDateRangeValuesWereSupplied(suppliedFilters)) {
      const [
        congressDateRangeFilter,
        trialsDateRangeFilter,
        publicationsDateRangeFilter
      ] = this.addDateRangeFilterInNestedClause(suppliedFilters);
      filters = this.addDateRangeFilterToExisting(
        "congress",
        filters,
        input,
        parsedQueryTree,
        congressDateRangeFilter,
        congressFilterFields
      );

      filters = this.addDateRangeFilterToExisting(
        "trials",
        filters,
        input,
        parsedQueryTree,
        trialsDateRangeFilter,
        trialsFilterFields
      );

      filters = this.addDateRangeFilterToExisting(
        "publications",
        filters,
        input,
        parsedQueryTree,
        publicationsDateRangeFilter,
        publicationsFilterFields
      );

      const addCongressToShould = !filters.some((filter) =>
        this.findNestedAssetFilters("congress", filter)
      );

      const addTrialsToShould = !filters.some((filter) =>
        this.findNestedAssetFilters("trials", filter)
      );

      const addPublicationsToShould = !filters.some((filter) =>
        this.findNestedAssetFilters("publications", filter)
      );

      const shoulds: QueryDslQueryContainer[] = [];

      if (addCongressToShould) {
        shoulds.push(
          this.getNestedDateRangeFilter(
            "congress",
            parsedQueryTree,
            congressDateRangeFilter
          )
        );
      }

      if (addTrialsToShould) {
        shoulds.push(
          this.getNestedDateRangeFilter(
            "trials",
            parsedQueryTree,
            trialsDateRangeFilter
          )
        );
      }

      if (addPublicationsToShould) {
        shoulds.push(
          this.getNestedDateRangeFilter(
            "publications",
            parsedQueryTree,
            publicationsDateRangeFilter
          )
        );
      }

      if (shoulds.length) {
        // NOTE: This is needed because minimum_should_match: 0 will not work
        if (shoulds.length !== 3) {
          shoulds.push({
            match_all: {}
          });
        }
        const shouldQuery: QueryDslQueryContainer = {
          bool: {
            should: shoulds,
            minimum_should_match: 1
          }
        };

        filters.push(shouldQuery);
      }
    }

    return filters;
  }

  private buildPublicationsExclusionFiltersForSearch({
    suppliedFilters
  }: KeywordFilterAutocompleteInput): Array<QueryDslQueryContainer> {
    const filters: Array<QueryDslQueryContainer> = [];

    if (!_.isEmpty(suppliedFilters.publicationsExclusion?.journal.values)) {
      filters.push(
        this.toTermsFilter(
          "publications.journal_multi",
          suppliedFilters.publicationsExclusion!.journal.values
        )
      );
    }
    if (!_.isEmpty(suppliedFilters.publicationsExclusion?.type.values)) {
      filters.push(
        this.toTermsFilter(
          "publications.type_eng",
          suppliedFilters.publicationsExclusion!.type.values
        )
      );
    }

    if (filters.length > 0) {
      return this.buildNestedExclusionQueryForAsset("publications", filters);
    }

    return [];
  }

  private parseQueryTreeForPrescription(
    parsedQueryTree: ParsedQueryTree,
    featureFlags: FeatureFlags
  ): QueryDslQueryContainer {
    const prescriptionsQueryFields = ["prescriptions.generic_name.text"];

    if (
      featureFlags &&
      "enableIndicationSynonymPrescriptionSearch" in featureFlags &&
      featureFlags.enableIndicationSynonymPrescriptionSearch
    ) {
      prescriptionsQueryFields.push("prescriptions.indications.text");
    }
    return this.parsedQueryTreeToElasticsearchQueriesService.parse(
      parsedQueryTree!,
      prescriptionsQueryFields
    );
  }
  private parseQueryTreeForDRGProcedures(
    parsedQueryTree: ParsedQueryTree
  ): QueryDslQueryContainer {
    return this.parsedQueryTreeToElasticsearchQueriesService.parse(
      parsedQueryTree!,
      ["DRG_procedures.codeAndDescription_eng"]
    );
  }

  private parseQueryTreeForPayment(
    parsedQueryTree: ParsedQueryTree
  ): QueryDslQueryContainer {
    return this.parsedQueryTreeToElasticsearchQueriesService.parse(
      parsedQueryTree!,
      ["payments.associatedDrugOrDevice", "payments.payerCompany"]
    );
  }

  private buildPaymentsFiltersForSearch(
    {
      filterField,
      filterValue,
      suppliedFilters
    }: KeywordFilterAutocompleteInput,
    featureFlags?: FeatureFlags,
    parsedQueryTree?: ParsedQueryTree
  ): Array<QueryDslQueryContainer> {
    const filters: Array<QueryDslQueryContainer> = [];

    if (filterField === KeywordFilterAutocompleteFilterField.PAYMENTS_COMPANY) {
      if (filterValue) {
        filters.push(
          this.toMatchPhraseQuery(
            "payments.payerCompany.autocomplete_search",
            filterValue
          )
        );
      }
    } else if (suppliedFilters.payments.company.values.length > 0) {
      filters.push(
        this.toTermsFilter(
          "payments.payerCompany",
          suppliedFilters.payments.company.values
        )
      );
    }

    if (
      filterField ===
      KeywordFilterAutocompleteFilterField.PAYMENTS_DRUG_OR_DEVICE
    ) {
      if (filterValue) {
        filters.push(
          this.toMatchPhraseQuery(
            "payments.associatedDrugOrDevice.autocomplete_search",
            filterValue
          )
        );
      }
    } else if (suppliedFilters.payments.drugOrDevice.values.length > 0) {
      filters.push(
        this.toTermsFilter(
          "payments.associatedDrugOrDevice",
          suppliedFilters.payments.drugOrDevice.values
        )
      );
    }

    if (
      filterField ===
      KeywordFilterAutocompleteFilterField.PAYMENTS_TYPE_OF_FUNDING
    ) {
      if (filterValue) {
        filters.push(
          this.toMatchPhraseQuery(
            "payments.natureOfPayment.autocomplete_search",
            filterValue
          )
        );
      }
    } else if (suppliedFilters.payments.fundingType.values.length > 0) {
      filters.push(
        this.toTermsFilter(
          "payments.natureOfPayment",
          suppliedFilters.payments.fundingType.values
        )
      );
    }

    if (
      filterField === KeywordFilterAutocompleteFilterField.PAYMENTS_CATEGORY
    ) {
      if (filterValue) {
        filters.push(
          this.toMatchPhraseQuery(
            "payments.category.autocomplete_search",
            filterValue
          )
        );
      }
    } else if (suppliedFilters.payments.category?.values.length) {
      filters.push(
        this.toTermsFilter(
          "payments.category",
          suppliedFilters.payments.category!.values
        )
      );
    }

    if (
      featureFlags &&
      "enableQueryContextualPaymentsFiltering" in featureFlags &&
      featureFlags?.enableQueryContextualPaymentsFiltering &&
      parsedQueryTree &&
      (suppliedFilters.payments.minAmount.value || filters.length)
    ) {
      filters.push(
        this.parsedQueryTreeToElasticsearchQueriesService.parse(
          parsedQueryTree,
          ["payments.associatedDrugOrDevice", "payments.payerCompany"]
        )
      );
    }

    if (
      suppliedFilters.payments.minAmount.value ||
      suppliedFilters.payments.maxAmount?.value
    ) {
      const functionScoreQuery: QueryDslFunctionScoreQuery = {
        query: {
          nested: {
            path: "payments",
            score_mode: "sum",
            query: {
              bool: {
                must: [
                  {
                    function_score: {
                      functions: [
                        {
                          field_value_factor: {
                            field: "payments.amount",
                            missing: 0
                          }
                        }
                      ]
                    }
                  }
                ],
                filter: filters
              }
            }
          }
        },
        functions: [
          {
            script_score: buildMinMaxFilterScriptForAsset(
              suppliedFilters.payments.minAmount.value,
              suppliedFilters.payments.maxAmount?.value
            )
          }
        ],
        min_score: 1
      };

      return [{ function_score: functionScoreQuery }];
    } else if (filters.length) {
      const nestedQuery = {
        path: "payments",
        query: {
          bool: {
            filter: filters
          }
        }
      };

      return [{ nested: nestedQuery }];
    }

    return [];
  }

  private buildPaymentsExclusionFiltersForSearch({
    suppliedFilters
  }: KeywordFilterAutocompleteInput): Array<QueryDslQueryContainer> {
    const filters = [];

    if (!_.isEmpty(suppliedFilters.paymentsExclusion?.company.values)) {
      filters.push(
        this.toTermsFilter(
          "payments.payerCompany",
          suppliedFilters.paymentsExclusion!.company.values
        )
      );
    }

    if (!_.isEmpty(suppliedFilters.paymentsExclusion?.drugOrDevice.values)) {
      filters.push(
        this.toTermsFilter(
          "payments.associatedDrugOrDevice",
          suppliedFilters.paymentsExclusion!.drugOrDevice.values
        )
      );
    }

    if (!_.isEmpty(suppliedFilters.paymentsExclusion?.fundingType.values)) {
      filters.push(
        this.toTermsFilter(
          "payments.natureOfPayment",
          suppliedFilters.paymentsExclusion!.fundingType.values
        )
      );
    }

    if (!_.isEmpty(suppliedFilters.paymentsExclusion?.category?.values)) {
      filters.push(
        this.toTermsFilter(
          "payments.category",
          suppliedFilters.paymentsExclusion!.category!.values
        )
      );
    }

    if (filters.length > 0) {
      return this.buildNestedExclusionQueryForAsset("payments", filters);
    }

    return [];
  }

  private buildClaimsDiagnosesExclusionFiltersForSearch(
    { suppliedFilters }: KeywordFilterAutocompleteInput,
    featureFlags?: FeatureFlags
  ): Array<QueryDslQueryContainer> {
    if (
      !suppliedFilters.exclusionClaims?.diagnosesICD.values.length ||
      (featureFlags?.enableCcsrExclusionForMatchedCounts &&
        (suppliedFilters.claims?.showUniquePatients?.value ?? false))
    ) {
      return [];
    }

    const filter = this.toTermsFilter(
      "DRG_diagnoses.diagnosisCode_eng",
      suppliedFilters.exclusionClaims.diagnosesICD.values.map(extractClaimsCode)
    );

    const nestedExclusionQuery = {
      bool: {
        must_not: {
          nested: {
            path: "DRG_diagnoses",
            query: filter
          }
        }
      }
    };

    return [nestedExclusionQuery];
  }

  private buildClaimsCcsrExclusionFiltersForSearch(
    { suppliedFilters }: KeywordFilterAutocompleteInput,
    featureFlags?: FeatureFlags
  ): Array<QueryDslQueryContainer> {
    if (
      !suppliedFilters.exclusionClaims?.ccsr?.values.length ||
      (featureFlags?.enableCcsrExclusionForMatchedCounts &&
        (suppliedFilters.claims.showUniquePatients?.value ?? false))
    ) {
      return [];
    }

    const filter = this.toTermsFilter(
      "ccsr.description_eng",
      suppliedFilters.exclusionClaims.ccsr.values
    );

    const nestedExclusionQuery = {
      bool: {
        must_not: {
          nested: {
            path: "ccsr",
            query: filter
          }
        }
      }
    };

    return [nestedExclusionQuery];
  }

  private buildClaimsCcsrPxExclusionFiltersForSearch({
    suppliedFilters
  }: KeywordFilterAutocompleteInput): Array<QueryDslQueryContainer> {
    if (!suppliedFilters.exclusionClaims?.ccsrPx?.values.length) {
      return [];
    }

    const filter = this.toTermsFilter(
      "ccsr.description_eng",
      suppliedFilters.exclusionClaims.ccsrPx.values
    );

    const nestedExclusionQuery = {
      bool: {
        must_not: {
          nested: {
            path: "ccsr",
            query: filter
          }
        }
      }
    };

    return [nestedExclusionQuery];
  }

  private buildClaimsFilters(
    input: KeywordFilterAutocompleteInput,
    featureFlags: FeatureFlags,
    parsedQueryTree?: ParsedQueryTree
  ): Array<QueryDslQueryContainer> {
    const suppliedClaimsFilters = input.suppliedFilters.claims;
    const diagnosesFilters = this.buildClaimsDiagnosesFiltersForSearch(
      input,
      featureFlags,
      parsedQueryTree
    );
    const proceduresFilters = this.buildClaimsProceduresFiltersForSearch(
      input,
      featureFlags,
      parsedQueryTree
    );
    const prescriptionsFilters = this.buildPrescriptionsFiltersForSearch(
      input,
      parsedQueryTree
    );

    const ccsrFilters = this.buildClaimsCcsrFiltersForSearch(
      input,
      featureFlags
    );

    const ccsrPxFilters = this.buildClaimsCcsrPxFiltersForSearch(
      input,
      featureFlags
    );

    const filters = [];
    const icdCodeRequestedOrSupplied =
      !!suppliedClaimsFilters.diagnosesICD.values.length ||
      !!this.isDiagnosisFilterField(input.filterField);
    const procedureCodeRequestedOrSupplied =
      !!suppliedClaimsFilters.proceduresCPT.values.length ||
      !!this.isProceduresFilterField(input.filterField);
    if (suppliedClaimsFilters.timeFrame?.value) {
      const must: QueryDslQueryContainer[] = [];
      const should: QueryDslQueryContainer[] = [];

      if (
        this.diagnosesFilterSuppliedOrRequested(input, suppliedClaimsFilters)
      ) {
        if (
          this.ccsrFilterSuppliedOrRequested(input, suppliedClaimsFilters) &&
          icdCodeRequestedOrSupplied
        ) {
          const queryClauseForIcdAndCcsr =
            diagnosesFilters.length + ccsrFilters.length > 0
              ? [
                  {
                    bool: {
                      should: [...diagnosesFilters, ...ccsrFilters]
                    }
                  }
                ]
              : [];
          must.push(...queryClauseForIcdAndCcsr);
        } else {
          must.push(...diagnosesFilters);
        }
      } else {
        should.push(...diagnosesFilters);
      }

      if (
        this.proceduresFilterSuppliedOrRequested(input, suppliedClaimsFilters)
      ) {
        if (
          this.ccsrPxFilterSuppliedOrRequested(input, suppliedClaimsFilters) &&
          procedureCodeRequestedOrSupplied
        ) {
          const queryClauseForProcedureAndCcsrPx =
            ccsrPxFilters.length + proceduresFilters.length > 0
              ? [
                  {
                    bool: {
                      should: [...proceduresFilters, ...ccsrFilters]
                    }
                  }
                ]
              : [];
          must.push(...queryClauseForProcedureAndCcsrPx);
        } else {
          must.push(...proceduresFilters);
        }
      } else {
        should.push(...proceduresFilters);
      }

      if (
        this.prescriptionsFilterSuppliedOrRequested(
          input,
          suppliedClaimsFilters
        )
      ) {
        must.push(...prescriptionsFilters);
      } else {
        should.push(...prescriptionsFilters);
      }
      if (
        this.ccsrFilterSuppliedOrRequested(input, suppliedClaimsFilters) &&
        !icdCodeRequestedOrSupplied
      ) {
        must.push(...ccsrFilters);
      }
      if (
        this.ccsrPxFilterSuppliedOrRequested(input, suppliedClaimsFilters) &&
        !procedureCodeRequestedOrSupplied
      ) {
        must.push(...ccsrPxFilters);
      }

      if (must.length) {
        if (parsedQueryTree) {
          filters.push(
            this.toFunctionScoreQuery({
              must
            })
          );
        } else {
          filters.push(...must);
        }
      } else {
        if (parsedQueryTree) {
          filters.push(
            this.toFunctionScoreQuery({
              should
            })
          );
        } else {
          filters.push({
            bool: {
              should
            }
          });
        }
      }
    } else {
      const queryClauseForIcdAndCcsr = this.getQueryForCareClusterAndClaimCode(
        this.ccsrFilterSuppliedOrRequested(input, suppliedClaimsFilters),
        icdCodeRequestedOrSupplied,
        diagnosesFilters,
        ccsrFilters
      );
      const queryClauseForProcedureCodeAndCcsrPx =
        this.getQueryForCareClusterAndClaimCode(
          this.ccsrPxFilterSuppliedOrRequested(input, suppliedClaimsFilters),
          procedureCodeRequestedOrSupplied,
          proceduresFilters,
          ccsrPxFilters
        );

      filters.push(
        ...queryClauseForIcdAndCcsr,
        ...prescriptionsFilters,
        ...queryClauseForProcedureCodeAndCcsrPx
      );
    }

    const claimsRegionFilter =
      buildClaimsRegionFilterForPeopleSearch(featureFlags);

    if (filters.length && claimsRegionFilter) {
      return [
        {
          bool: {
            must_not: [claimsRegionFilter],
            filter: filters
          }
        }
      ];
    }

    return filters;
  }

  private toFunctionScoreQuery(
    bool: QueryDslBoolQuery
  ): QueryDslQueryContainer {
    return {
      function_score: {
        query: {
          bool
        },
        min_score: 1
      }
    };
  }

  private diagnosesFilterSuppliedOrRequested(
    { filterField }: KeywordFilterAutocompleteInput,
    suppliedClaimsFilters: FilterInterface["claims"]
  ) {
    return !!(
      suppliedClaimsFilters.diagnosesICD.values.length ||
      suppliedClaimsFilters.diagnosesICDMinCount.value ||
      suppliedClaimsFilters.diagnosesICDMaxCount?.value ||
      this.isDiagnosisFilterField(filterField)
    );
  }

  private proceduresFilterSuppliedOrRequested(
    { filterField }: KeywordFilterAutocompleteInput,
    suppliedClaimsFilters: FilterInterface["claims"]
  ) {
    return !!(
      suppliedClaimsFilters.proceduresCPT.values.length ||
      suppliedClaimsFilters.proceduresCPTMinCount.value ||
      suppliedClaimsFilters.proceduresCPTMaxCount?.value ||
      this.isProceduresFilterField(filterField)
    );
  }

  private prescriptionsFilterSuppliedOrRequested(
    { filterField }: KeywordFilterAutocompleteInput,
    suppliedClaimsFilters: FilterInterface["claims"]
  ) {
    return !!(
      suppliedClaimsFilters.genericNames?.values?.length ||
      suppliedClaimsFilters.drugClasses?.values?.length ||
      suppliedClaimsFilters.brandNames?.values?.length ||
      suppliedClaimsFilters.prescriptionsMinCount?.value ||
      this.isPrescriptionsFilterField(filterField)
    );
  }

  private ccsrFilterSuppliedOrRequested(
    { filterField }: KeywordFilterAutocompleteInput,
    suppliedClaimsFilters: FilterInterface["claims"]
  ) {
    return !!(
      suppliedClaimsFilters.ccsr?.values.length ||
      this.isCcsrFilterField(filterField)
    );
  }

  private ccsrPxFilterSuppliedOrRequested(
    { filterField }: KeywordFilterAutocompleteInput,
    suppliedClaimsFilters: FilterInterface["claims"]
  ) {
    return !!(
      suppliedClaimsFilters.ccsrPx?.values.length ||
      this.isCcsrPxFilterField(filterField)
    );
  }

  private isDiagnosisFilterField(
    filterField: KeywordFilterAutocompleteFilterField | undefined
  ) {
    return (
      filterField === KeywordFilterAutocompleteFilterField.DIAGNOSES ||
      filterField === KeywordFilterAutocompleteFilterField.DRG_DIAGNOSES
    );
  }

  private isProceduresFilterField(
    filterField: KeywordFilterAutocompleteFilterField | undefined
  ) {
    return (
      filterField === KeywordFilterAutocompleteFilterField.PROCEDURES ||
      filterField === KeywordFilterAutocompleteFilterField.DRG_PROCEDURES
    );
  }

  private isPrescriptionsFilterField(
    filterField: KeywordFilterAutocompleteFilterField | undefined
  ) {
    return filterField === KeywordFilterAutocompleteFilterField.GENERIC_NAME;
  }

  private isCcsrFilterField(
    filterField: KeywordFilterAutocompleteFilterField | undefined
  ) {
    return filterField === KeywordFilterAutocompleteFilterField.CCSR;
  }

  private isCcsrPxFilterField(
    filterField: KeywordFilterAutocompleteFilterField | undefined
  ) {
    return filterField === KeywordFilterAutocompleteFilterField.CCSR_PX;
  }

  private buildClaimsDiagnosesFiltersForSearch(
    {
      filterField,
      filterValue,
      suppliedFilters
    }: KeywordFilterAutocompleteInput,
    featureFlags:
      | KeywordSearchFeatureFlags
      | AutocompleteFeatureFlags
      | NameSearchFeatureFlags,
    parsedQueryTree?: ParsedQueryTree
  ): Array<QueryDslQueryContainer> {
    const filters: Array<QueryDslQueryContainer> = [];
    const suppliedDiagnosisCodes = suppliedFilters.claims.diagnosesICD.values;
    let suppliedDiagnosisCodesTermsQuery: QueryDslQueryContainer | undefined;

    if (suppliedDiagnosisCodes.length) {
      suppliedDiagnosisCodesTermsQuery = this.toTermsFilter(
        "DRG_diagnoses.diagnosisCode_eng",
        suppliedDiagnosisCodes.map(extractClaimsCode)
      );
    }

    if (this.isDiagnosisFilterField(filterField)) {
      if (filterValue) {
        filters.push(
          this.toMatchPhraseQuery(
            "DRG_diagnoses.codeAndDescription_eng.autocomplete_search",
            filterValue
          )
        );
      }
    } //We do not want to add diagnoses filter when fetching ccsr autocomplete as it is an OR with diagnoses
    else if (
      suppliedDiagnosisCodesTermsQuery &&
      !this.isCcsrFilterField(filterField)
    ) {
      filters.push(suppliedDiagnosisCodesTermsQuery);
    }

    const countOrTimeFrameFilterApplied = !!(
      suppliedFilters.claims.diagnosesICDMinCount.value ||
      suppliedFilters.claims.diagnosesICDMaxCount?.value ||
      suppliedFilters.claims.timeFrame?.value
    );
    const suppliedCcsrDescriptions = suppliedFilters.claims.ccsr?.values ?? [];
    if (
      parsedQueryTree &&
      !suppliedDiagnosisCodes.length &&
      !suppliedCcsrDescriptions.length &&
      countOrTimeFrameFilterApplied
    ) {
      filters.push(
        this.parsedQueryTreeToElasticsearchQueriesService.parse(
          parsedQueryTree,
          ["DRG_diagnoses.codeAndDescription"],
          ENGLISH
        )
      );
    }
    const { enableUniquePatientCountForClaims } = featureFlags;
    let internalCountField = `DRG_diagnoses.${getInternalCountFieldForClaims(
      "diagnoses",
      featureFlags,
      suppliedFilters
    )}`;
    const uniquePatientToggleFromInput =
      suppliedFilters.claims.showUniquePatients?.value ?? false;
    let rootCountField =
      enableUniquePatientCountForClaims && uniquePatientToggleFromInput
        ? "DRG_diagnosesUniqueCount"
        : "DRG_diagnosesCount";

    if (suppliedFilters.claims.timeFrame?.value) {
      internalCountField =
        internalCountField + `_${suppliedFilters.claims.timeFrame.value}_year`;
      rootCountField =
        rootCountField + `_${suppliedFilters.claims.timeFrame.value}_year`;
    }

    const minCount = this.getMinClaimsCount(
      suppliedFilters,
      suppliedFilters.claims.diagnosesICDMinCount.value
    );
    const maxCount = suppliedFilters.claims.diagnosesICDMaxCount?.value ?? null;

    // Using a root level filter when no query term or filter value is supplied.
    if (
      !parsedQueryTree &&
      !filterValue &&
      !suppliedDiagnosisCodes.length &&
      countOrTimeFrameFilterApplied
    ) {
      if (suppliedCcsrDescriptions.length) {
        return [];
      }
      return [
        this.toFieldRangeMinMaxFilter(rootCountField, {
          min: minCount,
          max: maxCount
        })
      ];
    }

    if (countOrTimeFrameFilterApplied) {
      if (
        (parsedQueryTree && !suppliedCcsrDescriptions.length) ||
        (suppliedDiagnosisCodes.length &&
          !this.isDiagnosisFilterField(filterField) &&
          !this.isCcsrFilterField(filterField))
      ) {
        return [
          this.toDiagnosesNestedFunctionScoreQuery(
            filters,
            internalCountField as DiagnosesInternalCountField,
            minCount,
            maxCount
          )
        ];
      }
      if (!suppliedCcsrDescriptions.length) {
        filters.push(
          this.toFieldRangeMinMaxFilter(internalCountField, {
            min: minCount,
            max: maxCount
          })
        );
      }
    }

    if (filters.length) {
      const nestedQuery = {
        path: "DRG_diagnoses",
        query: {
          bool: {
            filter: filters
          }
        }
      };
      return [{ nested: nestedQuery }];
    }

    return [];
  }

  private buildClaimsCcsrFiltersForSearch(
    {
      filterField,
      filterValue,
      suppliedFilters
    }: KeywordFilterAutocompleteInput,
    featureFlags:
      | KeywordSearchFeatureFlags
      | AutocompleteFeatureFlags
      | NameSearchFeatureFlags
  ): Array<QueryDslQueryContainer> {
    const filters: Array<QueryDslQueryContainer> = [];
    const suppliedCcsrDescriptions = suppliedFilters.claims.ccsr?.values;
    let suppliedCcsrTermsQuery: QueryDslQueryContainer | undefined;

    if (suppliedCcsrDescriptions?.length) {
      suppliedCcsrTermsQuery = this.toTermsFilter(
        "ccsr.description_eng",
        suppliedCcsrDescriptions
      );
    }

    if (this.isCcsrFilterField(filterField)) {
      if (filterValue) {
        filters.push(
          this.toMatchPhraseQuery(
            "ccsr.description_eng.autocomplete_search",
            filterValue
          )
        );
      }
      //We do not want to add ccsr filter when diagoses autocomplete is fetched since they are ORed
    } else if (
      suppliedCcsrTermsQuery &&
      !this.isDiagnosisFilterField(filterField)
    ) {
      filters.push(suppliedCcsrTermsQuery);
    }

    const countOrTimeFrameFilterApplied = !!(
      suppliedFilters.claims.diagnosesICDMinCount.value ||
      suppliedFilters.claims.diagnosesICDMaxCount?.value ||
      suppliedFilters.claims.timeFrame?.value
    );

    let internalCountField = `ccsr.${getInternalCountFieldForClaims(
      "diagnoses",
      featureFlags,
      suppliedFilters
    )}`;

    if (suppliedFilters.claims.timeFrame?.value) {
      internalCountField =
        internalCountField + `_${suppliedFilters.claims.timeFrame.value}_year`;
    }

    const minCount = this.getMinClaimsCount(
      suppliedFilters,
      suppliedFilters.claims.diagnosesICDMinCount.value
    );
    const maxCount = suppliedFilters.claims.diagnosesICDMaxCount?.value ?? null;

    if (countOrTimeFrameFilterApplied) {
      if (
        suppliedCcsrDescriptions?.length &&
        !this.isCcsrFilterField(filterField) &&
        !this.isDiagnosisFilterField(filterField)
      ) {
        return [
          this.toCcsrNestedFunctionScoreQuery(
            filters,
            internalCountField as CcsrInternalCountField,
            minCount,
            maxCount
          )
        ];
      } else if (this.isCcsrFilterField(filterField)) {
        filters.push(
          this.toFieldRangeMinMaxFilter(internalCountField, {
            min: minCount,
            max: maxCount
          })
        );
      }
    }

    if (filters.length) {
      const nestedQuery = {
        path: "ccsr",
        query: {
          bool: {
            filter: filters
          }
        }
      };
      return [{ nested: nestedQuery }];
    }

    return [];
  }

  private buildClaimsCcsrPxFiltersForSearch(
    {
      filterField,
      filterValue,
      suppliedFilters
    }: KeywordFilterAutocompleteInput,
    featureFlags:
      | KeywordSearchFeatureFlags
      | AutocompleteFeatureFlags
      | NameSearchFeatureFlags
  ): Array<QueryDslQueryContainer> {
    const filters: Array<QueryDslQueryContainer> = [];
    const suppliedCcsrPxDescriptions = suppliedFilters.claims.ccsrPx?.values;
    let suppliedCcsrPxTermsQuery: QueryDslQueryContainer | undefined;

    if (suppliedCcsrPxDescriptions?.length) {
      suppliedCcsrPxTermsQuery = this.toTermsFilter(
        "ccsr_px.description_eng",
        suppliedCcsrPxDescriptions
      );
    }

    if (this.isCcsrPxFilterField(filterField)) {
      if (filterValue) {
        filters.push(
          this.toMatchPhraseQuery(
            "ccsr_px.description_eng.autocomplete_search",
            filterValue
          )
        );
      }
      //We do not want to add ccsr_px filter when procedure autocomplete is fetched since they are ORed
    } else if (
      suppliedCcsrPxTermsQuery &&
      !this.isProceduresFilterField(filterField)
    ) {
      filters.push(suppliedCcsrPxTermsQuery);
    }

    const countOrTimeFrameFilterApplied = !!(
      suppliedFilters.claims.proceduresCPTMinCount.value ||
      suppliedFilters.claims.proceduresCPTMaxCount?.value ||
      suppliedFilters.claims.timeFrame?.value
    );

    let internalCountField = `ccsr_px.${getInternalCountFieldForClaims(
      "diagnoses", //since ccsr_px uses similar counts as diagnoses
      featureFlags,
      suppliedFilters
    )}`;

    if (suppliedFilters.claims.timeFrame?.value) {
      internalCountField =
        internalCountField + `_${suppliedFilters.claims.timeFrame.value}_year`;
    }

    const minCount = this.getMinClaimsCount(
      suppliedFilters,
      suppliedFilters.claims.proceduresCPTMinCount.value
    );
    const maxCount =
      suppliedFilters.claims.proceduresCPTMaxCount?.value ?? null;

    if (countOrTimeFrameFilterApplied) {
      if (
        suppliedCcsrPxDescriptions?.length &&
        !this.isCcsrPxFilterField(filterField) &&
        !this.isProceduresFilterField(filterField)
      ) {
        return [
          this.toCcsrPxNestedFunctionScoreQuery(
            filters,
            internalCountField as CcsrInternalCountField,
            minCount,
            maxCount
          )
        ];
      } else if (this.isCcsrPxFilterField(filterField)) {
        filters.push(
          this.toFieldRangeMinMaxFilter(internalCountField, {
            min: minCount,
            max: maxCount
          })
        );
      }
    }

    if (filters.length) {
      const nestedQuery = {
        path: "ccsr_px",
        query: {
          bool: {
            filter: filters
          }
        }
      };
      return [{ nested: nestedQuery }];
    }

    return [];
  }

  private buildPrescriptionsFiltersForSearch(
    {
      filterField,
      filterValue,
      suppliedFilters
    }: KeywordFilterAutocompleteInput,
    parsedQueryTree?: ParsedQueryTree
  ): Array<QueryDslQueryContainer> {
    const filters: Array<QueryDslQueryContainer> = [];
    const suppliedGenericNames = suppliedFilters?.claims?.genericNames?.values;
    const suppliedDrugClasses = suppliedFilters?.claims?.drugClasses?.values;
    const suppliedBrandNames = suppliedFilters?.claims?.brandNames?.values;
    let suppliedGenericNamesTermsQuery: QueryDslQueryContainer | undefined;
    let suppliedDrugClassesTermsQuery: QueryDslQueryContainer | undefined;
    let suppliedBrandNamesTermsQuery: QueryDslQueryContainer | undefined;
    const suppliedBrandNameOrGeneric =
      suppliedFilters?.claims?.brandNameOrGeneric?.values;

    if (suppliedGenericNames?.length) {
      suppliedGenericNamesTermsQuery = this.toTermsFilter(
        "prescriptions.generic_name",
        suppliedGenericNames
      );
    }

    if (suppliedDrugClasses?.length) {
      suppliedDrugClassesTermsQuery = this.toTermsFilter(
        "prescriptions.drug_class",
        suppliedDrugClasses
      );
    }

    if (suppliedBrandNames?.length) {
      suppliedBrandNamesTermsQuery = this.toTermsFilter(
        "prescriptions.brand_name",
        suppliedBrandNames
      );
    }

    if (suppliedBrandNameOrGeneric?.length) {
      const genericNames: string[] = [];
      const brandNames: string[] = [];

      for (const item of suppliedBrandNameOrGeneric) {
        if (item.brandName) {
          brandNames.push(item.name);
        } else {
          genericNames.push(item.name);
        }
      }

      if (brandNames.length) {
        suppliedBrandNamesTermsQuery = this.toTermsFilter(
          "prescriptions.brand_name",
          brandNames
        );
      }

      if (genericNames.length) {
        suppliedGenericNamesTermsQuery = this.toTermsFilter(
          "prescriptions.generic_name",
          genericNames
        );
      }
    }

    if (filterField === KeywordFilterAutocompleteFilterField.GENERIC_NAME) {
      if (filterValue) {
        filters.push(
          this.toMatchPhraseQuery(
            "prescriptions.generic_name.autocomplete_search",
            filterValue
          )
        );
      }
    } else if (suppliedGenericNamesTermsQuery) {
      filters.push(suppliedGenericNamesTermsQuery);
    }

    if (filterField === KeywordFilterAutocompleteFilterField.DRUG_CLASS) {
      if (filterValue) {
        filters.push(
          this.toMatchPhraseQuery(
            "prescriptions.drug_class.autocomplete_search",
            filterValue
          )
        );
      }
    } else if (suppliedDrugClassesTermsQuery) {
      filters.push(suppliedDrugClassesTermsQuery);
    }

    if (filterField === KeywordFilterAutocompleteFilterField.BRAND_NAME) {
      if (filterValue) {
        filters.push(
          this.toMatchPhraseQuery(
            "prescriptions.brand_name.autocomplete_search",
            filterValue
          )
        );
      }
    } else if (suppliedBrandNamesTermsQuery) {
      filters.push(suppliedBrandNamesTermsQuery);
    }

    const countOrTimeFrameFilterApplied = !!(
      suppliedFilters.claims.prescriptionsMinCount?.value ||
      suppliedFilters.claims.prescriptionsMaxCount?.value ||
      suppliedFilters.claims.prescriptionsTimeFrame?.value
    );

    if (
      parsedQueryTree &&
      !suppliedGenericNames?.length &&
      countOrTimeFrameFilterApplied
    ) {
      filters.push(
        this.parsedQueryTreeToElasticsearchQueriesService.parse(
          parsedQueryTree,
          ["prescriptions.generic_name.text"]
        )
      );
    }

    let internalCountField = "prescriptions.num_prescriptions";
    let rootCountField = "num_prescriptions";

    if (suppliedFilters.claims.prescriptionsTimeFrame?.value) {
      internalCountField =
        internalCountField +
        `_${suppliedFilters.claims.prescriptionsTimeFrame.value}_year`;
      rootCountField =
        rootCountField +
        `_${suppliedFilters.claims.prescriptionsTimeFrame.value}_year`;
    }

    const minCount = this.getMinPrescriptionsCount(
      suppliedFilters,
      suppliedFilters.claims.prescriptionsMinCount?.value ?? null
    );
    const maxCount =
      suppliedFilters.claims.prescriptionsMaxCount?.value ?? null;

    // Using a root level filter when no query term or filter value is supplied.
    if (
      !parsedQueryTree &&
      !filterValue &&
      !suppliedGenericNames?.length &&
      !suppliedBrandNames?.length &&
      !suppliedDrugClasses?.length &&
      countOrTimeFrameFilterApplied
    ) {
      return [
        this.toFieldRangeMinMaxFilter(rootCountField, {
          min: minCount,
          max: maxCount
        })
      ];
    }

    if (countOrTimeFrameFilterApplied) {
      if (
        parsedQueryTree ||
        (suppliedGenericNames?.length &&
          filterField !== KeywordFilterAutocompleteFilterField.GENERIC_NAME) ||
        (suppliedBrandNames?.length &&
          filterField !== KeywordFilterAutocompleteFilterField.BRAND_NAME) ||
        (suppliedDrugClasses?.length &&
          filterField !== KeywordFilterAutocompleteFilterField.DRUG_CLASS)
      ) {
        return [
          this.toPrescriptionsNestedFunctionScoreQuery(
            filters,
            internalCountField as PrescriptionsInternalCountField,
            minCount,
            maxCount
          )
        ];
      }

      filters.push(
        this.toFieldRangeMinMaxFilter(internalCountField, {
          min: minCount,
          max: maxCount
        })
      );
    }

    if (filters.length) {
      const nestedQuery = {
        path: "prescriptions",
        query: {
          bool: {
            filter: filters
          }
        }
      };
      return [{ nested: nestedQuery }];
    }

    return [];
  }

  private buildPrescriptionsExclusionFiltersForSearch({
    suppliedFilters
  }: KeywordFilterAutocompleteInput): Array<QueryDslQueryContainer> {
    const filters: QueryDslQueryContainer[] = [];

    if (suppliedFilters.exclusionClaims?.genericNames?.values.length) {
      filters.push(
        this.toTermsFilter(
          "prescriptions.generic_name",
          suppliedFilters.exclusionClaims.genericNames.values
        )
      );
    }

    if (suppliedFilters.exclusionClaims?.drugClasses?.values.length) {
      filters.push(
        this.toTermsFilter(
          "prescriptions.drug_class",
          suppliedFilters.exclusionClaims.drugClasses?.values
        )
      );
    }

    if (suppliedFilters.exclusionClaims?.brandNames?.values.length) {
      filters.push(
        this.toTermsFilter(
          "prescriptions.brand_name",
          suppliedFilters.exclusionClaims.brandNames?.values
        )
      );
    }

    if (!filters.length) {
      return [];
    }

    const nestedExclusionQuery: QueryDslQueryContainer = {
      bool: {
        must_not: {
          nested: {
            path: "prescriptions",
            query: {
              bool: {
                filter: filters
              }
            }
          }
        }
      }
    };

    return [nestedExclusionQuery];
  }

  private buildClaimsProceduresExclusionFiltersForSearch({
    suppliedFilters
  }: KeywordFilterAutocompleteInput): Array<QueryDslQueryContainer> {
    if (!suppliedFilters.exclusionClaims?.proceduresCPT.values.length) {
      return [];
    }
    const filter = this.toTermsFilter(
      "DRG_procedures.procedureCode_eng",
      suppliedFilters.exclusionClaims.proceduresCPT.values.map(
        extractClaimsCode
      )
    );

    const nestedExclusionQuery = {
      bool: {
        must_not: {
          nested: {
            path: "DRG_procedures",
            query: filter
          }
        }
      }
    };

    return [nestedExclusionQuery];
  }

  private buildClaimsProceduresFiltersForSearch(
    {
      filterField,
      filterValue,
      suppliedFilters
    }: KeywordFilterAutocompleteInput,
    featureFlags:
      | KeywordSearchFeatureFlags
      | AutocompleteFeatureFlags
      | NameSearchFeatureFlags,
    parsedQueryTree?: ParsedQueryTree
  ): Array<QueryDslQueryContainer> {
    const filters: Array<QueryDslQueryContainer> = [];
    const suppliedProcedureCodes = suppliedFilters.claims.proceduresCPT.values;
    let suppliedProcedureCodeTermsQuery: QueryDslQueryContainer | undefined;
    const suppliedCcsrPxDescriptions =
      suppliedFilters.claims.ccsrPx?.values ?? [];

    if (suppliedProcedureCodes.length) {
      suppliedProcedureCodeTermsQuery = this.toTermsFilter(
        "DRG_procedures.procedureCode_eng",
        suppliedProcedureCodes.map(extractClaimsCode)
      );
    }

    if (this.isProceduresFilterField(filterField)) {
      if (filterValue) {
        filters.push(
          this.toMatchPhraseQuery(
            "DRG_procedures.codeAndDescription_eng.autocomplete_search",
            filterValue
          )
        );
      }
    } //We do not want to add procedures filter when fetching ccsrPx autocomplete as it is an OR with procedures
    else if (
      suppliedProcedureCodeTermsQuery &&
      !this.isCcsrPxFilterField(filterField)
    ) {
      filters.push(suppliedProcedureCodeTermsQuery);
    }

    const countOrTimeFrameFilterApplied = !!(
      suppliedFilters.claims.proceduresCPTMinCount.value ||
      suppliedFilters.claims.proceduresCPTMaxCount?.value ||
      suppliedFilters.claims.timeFrame?.value
    );

    if (
      parsedQueryTree &&
      !suppliedProcedureCodes.length &&
      !suppliedCcsrPxDescriptions.length &&
      countOrTimeFrameFilterApplied
    ) {
      filters.push(
        this.parsedQueryTreeToElasticsearchQueriesService.parse(
          parsedQueryTree,
          ["DRG_procedures.codeAndDescription"],
          ENGLISH
        )
      );
    }
    const {
      enableUniquePatientCountForClaims,
      disableUniquePatientCountForOnlyProcedures
    } = featureFlags;
    let internalCountField =
      !disableUniquePatientCountForOnlyProcedures &&
      enableUniquePatientCountForClaims
        ? "DRG_procedures.internalUniqueCount"
        : "DRG_procedures.internalCount";
    let rootCountField =
      !disableUniquePatientCountForOnlyProcedures &&
      enableUniquePatientCountForClaims
        ? "DRG_proceduresUniqueCount"
        : "DRG_proceduresCount";

    if (suppliedFilters.claims.timeFrame?.value) {
      internalCountField =
        internalCountField + `_${suppliedFilters.claims.timeFrame.value}_year`;
      rootCountField =
        rootCountField + `_${suppliedFilters.claims.timeFrame.value}_year`;
    }

    const minCount = this.getMinClaimsCount(
      suppliedFilters,
      suppliedFilters.claims.proceduresCPTMinCount.value
    );
    const maxCount =
      suppliedFilters.claims.proceduresCPTMaxCount?.value ?? null;

    // Using a root level filter when no query term or filter value is supplied.
    if (
      !parsedQueryTree &&
      !filterValue &&
      !suppliedProcedureCodes.length &&
      countOrTimeFrameFilterApplied
    ) {
      if (suppliedCcsrPxDescriptions.length) {
        return [];
      }
      return [
        this.toFieldRangeMinMaxFilter(rootCountField, {
          min: minCount,
          max: maxCount
        })
      ];
    }

    if (countOrTimeFrameFilterApplied) {
      if (
        (parsedQueryTree && !suppliedCcsrPxDescriptions.length) ||
        (suppliedProcedureCodes.length &&
          !this.isProceduresFilterField(filterField) &&
          !this.isCcsrPxFilterField(filterField))
      ) {
        return [
          this.toProceduresNestedFunctionScoreQuery(
            filters,
            internalCountField as ProceduresInternalCountField,
            minCount,
            maxCount
          )
        ];
      }
      if (!suppliedCcsrPxDescriptions.length) {
        filters.push(
          this.toFieldRangeMinMaxFilter(internalCountField, {
            min: minCount,
            max: maxCount
          })
        );
      }
    }

    if (filters.length) {
      const nestedQuery = {
        path: "DRG_procedures",
        query: {
          bool: {
            filter: filters
          }
        }
      };
      return [{ nested: nestedQuery }];
    }

    return [];
  }

  private toDiagnosesNestedFunctionScoreQuery =
    this.toNestedFunctionScoreQuery.bind(this, "DRG_diagnoses");
  private toProceduresNestedFunctionScoreQuery =
    this.toNestedFunctionScoreQuery.bind(this, "DRG_procedures");
  private toPrescriptionsNestedFunctionScoreQuery =
    this.toNestedFunctionScoreQuery.bind(this, "prescriptions");
  private toCcsrNestedFunctionScoreQuery = this.toNestedFunctionScoreQuery.bind(
    this,
    "ccsr"
  );
  private toCcsrPxNestedFunctionScoreQuery =
    this.toNestedFunctionScoreQuery.bind(this, "ccsr_px");
  private toNestedFunctionScoreQuery(
    path: NestedPath,
    filters: QueryDslQueryContainer[],
    internalCountField:
      | ProceduresInternalCountField
      | DiagnosesInternalCountField
      | PrescriptionsInternalCountField
      | CcsrInternalCountField,
    minCount: number | null,
    maxCount?: number | null
  ): QueryDslQueryContainer {
    const nestedQuery: QueryDslNestedQuery = {
      path,
      query: {
        function_score: {
          score_mode: "sum",
          boost_mode: "replace",
          query: {
            bool: {
              must: filters
            }
          },
          functions: [
            {
              field_value_factor: {
                field: internalCountField,
                missing: 0
              }
            }
          ]
        }
      },
      score_mode: "sum"
    };

    return {
      function_score: {
        query: {
          nested: nestedQuery
        },
        functions: [
          {
            script_score: buildMinMaxFilterScriptForAsset(minCount, maxCount)
          }
        ],
        min_score: 1
      }
    };
  }

  private buildSocialMediaFilters({
    suppliedFilters
  }: KeywordFilterAutocompleteInput): Array<QueryDslQueryContainer> {
    const filters = [];

    if (suppliedFilters.hasLinkedin.value) {
      filters.push(toTermFilter("hasLinkedin", true));
    }

    if (suppliedFilters.hasTwitter.value) {
      filters.push(toTermFilter("hasTwitter", true));
    }

    return filters;
  }

  private buildFOMemberFilter({
    suppliedFilters
  }: KeywordFilterAutocompleteInput): Array<QueryDslQueryContainer> {
    const filters = [];

    if (suppliedFilters.isFacultyOpinionsMember?.value) {
      filters.push({
        term: {
          isFacultyOpinionsMember: true
        }
      });
    }

    return filters;
  }

  /**
   * Maps the race filter value coming from the frontend to the respective patientsDiversityRatio field in the elasticsearch people index.
   * e.g. "Hispanic" is represented by "patientsDiversityRatio.hispanic"
   * @param raceFilter
   * @returns elasticsearch field within patientsDiversityRatio object
   */
  public mapRaceFilterValueToPatientsDiversityRatioField(
    raceFilter: string
  ): string {
    if (!raceFilterValueToPatientsDiversityRatioFieldMap.has(raceFilter)) {
      throw new Error("Invalid value for race filter: " + raceFilter);
    }
    return raceFilterValueToPatientsDiversityRatioFieldMap.get(raceFilter)!;
  }

  private async buildCTMSFilter(
    { suppliedFilters }: KeywordFilterAutocompleteInput,
    { enableCTMSV2 }: FeatureFlags
  ): Promise<Array<QueryDslQueryContainer>> {
    const filters: Array<QueryDslQueryContainer> = [];

    // TODO: hasCTMSData is currently a misnomer
    // This now filters on people are in the CTMS network but may or may not have CTMS data in the project slice
    // We should a have separate filter for people with CTMS data
    if (suppliedFilters.hasCTMSData?.value) {
      //TODO: Remove once we delete old CTMS indices
      if (enableCTMSV2) {
        filters.push(IN_CTMS_NETWORK);
      } else {
        const personIds =
          await this.tagsHelperService.getCTMSPersonIdsForSearch();
        if (!personIds?.length) {
          return [];
        }
        filters.push({
          terms: {
            id: personIds
          }
        });
      }
    }

    return filters;
  }

  private buildGeoBoundingBoxFilter(
    { suppliedFilters }: KeywordFilterAutocompleteInput,
    featureFlags: FeatureFlags
  ): Array<QueryDslQueryContainer> {
    const boundingBox = suppliedFilters.geoBoundingBox?.value;
    if (boundingBox) {
      const nestedBoundingBoxFilter = {
        nested: {
          path: AFFILIATIONS,
          query: {
            bool: {
              filter: [
                {
                  geo_bounding_box: {
                    /*
                     * TODO: Remove ts-ignore once we update the elasticsearch js client
                     * Types for geo_bounding box are wrong in our version of the client (7.13)
                     * 7.14 contains correct types for geo_bounding_box
                     * This is the commit with the updated types:
                     * https://github.com/elastic/elasticsearch-js/commit/8c0cd05ecb3eda20c48f1ef54c23cf9a107936b4#diff-9c72cd5b60f97485f79f0a568b27a60a117ac59fb8f3bf70b3dd612413e96155L4460
                     */
                    // @ts-ignore
                    [AFFILIATIONS_INSTITUTION_LOCATION]: {
                      ...boundingBox
                    }
                  }
                },
                AFFILIATIONS_IS_CURRENT
              ]
            }
          }
        }
      };

      // TODO: hasCTMSData is currently a misnomer
      // This now filters on people are in the CTMS network but may or may not have CTMS data in the project slice
      // We should a have separate filter for people with CTMS data
      const filters = [];
      if (
        featureFlags.enableCTMSV2 &&
        suppliedFilters.hasCTMSData?.value &&
        _.isEqual(boundingBox, DEFAULT_GEO_BOUNDING_BOX)
      ) {
        filters.push({
          bool: {
            should: [IN_CTMS_NETWORK, nestedBoundingBoxFilter],
            minimum_should_match: 1
          }
        });
      } else {
        filters.push(nestedBoundingBoxFilter);
      }

      return filters;
    }

    return [];
  }

  private buildGeoDistanceFilter({
    suppliedFilters
  }: KeywordFilterAutocompleteInput): Array<QueryDslQueryContainer> {
    const geoDistanceInput = suppliedFilters.geoDistance?.value;
    if (!geoDistanceInput) return [];

    return [
      {
        nested: {
          path: AFFILIATIONS,
          query: {
            bool: {
              filter: [
                {
                  geo_distance: {
                    distance: `${geoDistanceInput.distance}${geoDistanceInput.distanceUnit}`,
                    // @ts-ignore
                    "affiliations.institution.location":
                      geoDistanceInput.geoPosition
                  }
                },
                AFFILIATIONS_IS_CURRENT
              ]
            }
          }
        }
      }
    ];
  }

  private buildGeoShapeFilter({
    suppliedFilters
  }: KeywordFilterAutocompleteInput): Array<QueryDslQueryContainer> {
    const geoShapeInput = suppliedFilters.geoShape?.value;
    if (!geoShapeInput || _.isEmpty(geoShapeInput.coordinates)) return [];
    return [
      {
        nested: {
          path: AFFILIATIONS,
          query: {
            bool: {
              filter: [
                {
                  geo_shape: {
                    [AFFILIATIONS_INSTITUTION_LOCATION]: {
                      shape: {
                        type: geoShapeInput.type,
                        // TODO: Remove ts-ignore once we update the elasticsearch js client
                        // @ts-ignore
                        coordinates: [
                          geoShapeInput.coordinates?.map((point) => [
                            point.lon,
                            point.lat
                          ])
                        ]
                      }
                    }
                  }
                },
                AFFILIATIONS_IS_CURRENT
              ]
            }
          }
        }
      }
    ];
  }

  private buildGeoHashGridsFilter({
    suppliedFilters
  }: KeywordFilterAutocompleteInput): Array<QueryDslQueryContainer> {
    const geoHashGrids = suppliedFilters.geoHashGrids?.value;

    if (geoHashGrids && geoHashGrids.length > 0) {
      return [
        {
          nested: {
            path: AFFILIATIONS,
            query: {
              bool: {
                filter: [
                  AFFILIATIONS_IS_CURRENT,
                  {
                    bool: {
                      /*
                       * TODO: Remove ts-ignore once we update the elasticsearch js client
                       * Types for geo_bounding box are wrong in our version of the client (7.13)
                       */
                      // @ts-ignore
                      should:
                        geoHashGrids.map((geoHash) => ({
                          geo_bounding_box: {
                            [AFFILIATIONS_INSTITUTION_LOCATION]: {
                              // In order to specify a bounding box that would match entire area of a geohash
                              // the geohash can be specified in both top_left and bottom_right parameters
                              // https://www.elastic.co/guide/en/elasticsearch/reference/current/query-dsl-geo-bounding-box-query.html#_geohash_2
                              top_left: geoHash,
                              bottom_right: geoHash
                            }
                          }
                        })) || []
                    }
                  }
                ]
              }
            }
          }
        }
      ];
    }

    return [];
  }

  private buildDigitalLeaderFilterForSearch({
    suppliedFilters
  }: KeywordFilterAutocompleteInput): Array<QueryDslQueryContainer> {
    const filters = [];
    const digitalLeaderValue = suppliedFilters.digitalLeader?.value;

    switch (digitalLeaderValue) {
      case DigitalLeaderTypes.All: {
        filters.push(this.toFieldValueExists("digitalRank"));
        break;
      }
      case DigitalLeaderTypes.TopOne: {
        filters.push(this.toTermsFilter("top1PercentileDigitalRank", ["true"]));
        break;
      }
      case DigitalLeaderTypes.TopTen: {
        filters.push(
          this.toTermsFilter("top10PercentileDigitalRank", ["true"])
        );
        break;
      }
    }
    return filters;
  }

  private getMinClaimsCount(
    suppliedFilters: FilterInterface,
    minCount: number | null
  ): number | null {
    if (suppliedFilters.claims.timeFrame?.value) {
      return minCount || AT_LEAST_ONE;
    }

    return minCount;
  }

  private getMinPrescriptionsCount(
    suppliedFilters: FilterInterface,
    minCount: number | null
  ): number | null {
    if (suppliedFilters.claims.prescriptionsTimeFrame?.value) {
      return minCount || AT_LEAST_ONE;
    }

    return minCount;
  }
  private buildNestedExclusionQueryForAsset(
    path: string,
    filters: QueryDslQueryContainer[]
  ): Array<QueryDslQueryContainer> {
    const nestedExclusionQuery = {
      bool: {
        must_not: {
          nested: {
            path,
            query: {
              bool: {
                should: filters
              }
            }
          }
        }
      }
    };
    return [nestedExclusionQuery];
  }

  private buildExclusionQuery(
    filters: QueryDslQueryContainer | QueryDslQueryContainer[]
  ): Array<QueryDslQueryContainer> {
    const exclusionQuery = {
      bool: {
        must_not: filters
      }
    };

    return [exclusionQuery];
  }
  private buildIsInactiveAndIsIndustryFilter({
    suppliedFilters
  }: KeywordFilterAutocompleteInput): Array<QueryDslQueryContainer> {
    const { isInactive, isIndustry } = suppliedFilters;

    const filters: QueryDslQueryContainer[] = [];

    // The frontend sends the filter value as true or false, but we only want to add the filter term when it's false.
    if (isInactive?.value === false) {
      filters.push(toFieldIsFalseOrDoesNotExists("isInactive"));
    }
    if (isIndustry?.value === false) {
      filters.push(toFieldIsFalseOrDoesNotExists("isIndustry"));
    }

    // When isInactive and isIndustry are both true we still want to return results (but not filter with the true value).
    if (isInactive?.value && isIndustry?.value) {
      return [this.toBoolShould([])];
    }

    return filters;
  }

  private buildCareerLengthFilter({
    suppliedFilters
  }: KeywordFilterAutocompleteInput): Array<QueryDslQueryContainer> {
    const { careerLength } = suppliedFilters;

    const valueRange = this.parseCareerLengthFilterEnum(careerLength?.value);

    return valueRange ? [{ range: { careerLength: valueRange } }] : [];
  }

  private buildRisingStarsFilterWithoutIndication({
    suppliedFilters
  }: KeywordFilterAutocompleteInput): Array<QueryDslQueryContainer> {
    const isRisingStar = suppliedFilters.isRisingStar?.value;
    const isIndicationApplied = !!suppliedFilters.indications?.values.length;
    const filters = [];
    if (isRisingStar && !isIndicationApplied) {
      filters.push(
        buildFieldExistsQuery(getLeaderScoreField(suppliedFilters, false))
      );
    }
    return filters;
  }

  private addTerritoryFilters(
    territories: SavedTerritory[]
  ): QueryDslQueryContainer[] {
    const filters: QueryDslQueryContainer[] = [];

    if (!territories?.length) return filters;
    const shoulds: QueryDslQueryContainer[] = [];
    const customTerritoryShoulds: QueryDslQueryContainer[] = [];
    territories.forEach((territory) => {
      const data = territory.territoryData;

      // Check if territoryData has countries and add a filter
      if (data.countries && Array.isArray(data.countries)) {
        shoulds.push(this.toTermsFilter("country_multi", data.countries));
      }

      // Check if territoryData has postalCodes and add a filter
      if (data.postalCodes && Array.isArray(data.postalCodes)) {
        shoulds.push(this.toTermsFilter("zipCode5", data.postalCodes));
      }

      // Check if territoryData has regions and add a filter
      if (data.regions && Array.isArray(data.regions)) {
        shoulds.push(this.toTermsFilter("state_multi", data.regions));
      }

      // Check if territoryData has a geoJson and add a geo-shape filter
      if (data.geoJson && typeof data.geoJson === "object") {
        const geoShapeQueries = this.createGeoShapeQueryForTerritory(
          data.geoJson as GeometryCollection
        );
        if (geoShapeQueries.length) {
          customTerritoryShoulds.push(...geoShapeQueries);
        }
      }
    });

    if (customTerritoryShoulds.length) {
      const geoShapeQuery: QueryDslQueryContainer = {
        nested: {
          path: "affiliations",
          query: {
            bool: {
              should: customTerritoryShoulds,
              filter: [
                AFFILIATIONS_IS_WORK_TYPE,
                AFFILIATIONS_IS_CURRENT,
                US_COUNTRY_FILTER
              ],
              minimum_should_match: 1
            }
          },
          inner_hits: {
            name: CUSTOM_TERRITORY_INNER_HITS_NAME,
            size: 3,
            _source: false,
            docvalue_fields: [AFFILIATIONS_INSTITUTION_NAME]
          }
        }
      };
      shoulds.push(geoShapeQuery);
    }

    if (shoulds.length) {
      filters.push({
        bool: {
          should: shoulds,
          minimum_should_match: 1
        }
      });
    }
    return filters;
  }

  private createGeoShapeQueryForTerritory(territory: GeometryCollection) {
    const filteredPolygons: Polygon[] = territory.geometries.filter(
      (geometry) => geometry.type === "Polygon"
    ) as Polygon[];
    const shouldClauses: QueryDslQueryContainer[] = filteredPolygons.map(
      (geoPolygon) => ({
        geo_shape: {
          ["affiliations.institution.location"]: {
            shape: {
              type: "polygon",
              coordinates: geoPolygon.coordinates
            },
            relation: "intersects"
          }
        }
      })
    );
    return shouldClauses;
  }

  private buildNonIndicationLeaderFilters(
    input: KeywordFilterAutocompleteInput
  ) {
    const filters = [];
    const isNationalLeader =
      input.suppliedFilters.isNationalLeader?.value ?? false;
    const isRegionalLeader =
      input.suppliedFilters.isRegionalLeader?.value ?? false;
    const isLocalLeader = input.suppliedFilters.isLocalLeader?.value ?? false;
    if (isNationalLeader) {
      filters.push(toTermFilter("isNationalLeader", isNationalLeader));
    }
    if (isRegionalLeader) {
      filters.push(toTermFilter("isRegionalLeader", isRegionalLeader));
    }
    if (isLocalLeader) {
      filters.push(toTermFilter("isLocalLeader", isLocalLeader));
    }
    return filters;
  }

  private buildCountrySpecificNonIndicationLeaderFilters(
    input: KeywordFilterAutocompleteInput
  ) {
    const filters: QueryDslQueryContainer[] = [];
    const isNationalLeader =
      input.suppliedFilters.isNationalLeader?.value ?? false;
    const isRegionalLeader =
      input.suppliedFilters.isRegionalLeader?.value ?? false;
    const isLocalLeader = input.suppliedFilters.isLocalLeader?.value ?? false;
    const countryFilters = input.suppliedFilters.country.values;
    const countryFilterCodes = this.collectCountryFilterValues(countryFilters);

    const filterBuilder = (leaderType: string, value: boolean) => {
      const query: QueryDslBoolQuery = {};

      const valuePath = `${leaderType}.value`;
      const countryPath = `${leaderType}.country_codes`;
      query.filter = [toTermFilter(valuePath, value)];
      if (countryFilterCodes.length) {
        query.filter.push(this.toTermsFilter(countryPath, countryFilterCodes));
      }

      const finalQuery: QueryDslQueryContainer = {
        bool: query
      };
      return finalQuery;
    };

    if (isNationalLeader) {
      filters.push(filterBuilder("nationalLeader", isNationalLeader));
    }
    if (isRegionalLeader) {
      filters.push(filterBuilder("regionalLeader", isRegionalLeader));
    }
    if (isLocalLeader) {
      filters.push(filterBuilder("localLeader", isLocalLeader));
    }

    return filters;
  }

  private buildIndicationFilters(
    input: KeywordFilterAutocompleteInput,
    featureFlags: FeatureFlags
  ) {
    const filters = [];
    let indicationValues = input.suppliedFilters.indications?.values;
    // suggested L2 indications are always ORed with the main (L3) indication applied
    const suggestedL2IndicationValues =
      input.suppliedFilters.suggestedL2Indications?.values;

    if (indicationValues && suggestedL2IndicationValues) {
      indicationValues = [...indicationValues, ...suggestedL2IndicationValues];
    }
    const { enableNestedIndicationFilter } = featureFlags;
    if (indicationValues?.length && enableNestedIndicationFilter) {
      const filtersToAdd = [];
      const indicationsTermsQuery = this.toTermsFilter(
        "indications.indication.keyword",
        indicationValues
      );
      filtersToAdd.push(indicationsTermsQuery);
      if (!isLeaderboardSortOrDiversitySortApplied(input)) {
        let scoreField;
        if (input.suppliedFilters.isRisingStar?.value) {
          scoreField = "indications.indicationRisingScore";
        } else {
          scoreField = "indications.indicationScore";
        }
        const indicationScoreExistsFilter = buildFieldExistsQuery(scoreField);
        filtersToAdd.push(indicationScoreExistsFilter);
      }
      const nestedQuery = {
        path: "indications",
        query: {
          bool: {
            filter: filtersToAdd
          }
        }
      };
      const nestedFilterQuery: QueryDslQueryContainer = { nested: nestedQuery };
      filters.push(nestedFilterQuery);
    }
    return filters;
  }

  private buildIndicationLeaderFilters(input: KeywordFilterAutocompleteInput) {
    const filters = [];
    let scoreField;
    if (input.suppliedFilters.isEducatorLeader?.value) {
      scoreField = "indications.indicationScoreForEducator";
    }
    if (input.suppliedFilters.isTrialLeader?.value) {
      if (input.suppliedFilters.isRisingStar?.value) {
        scoreField = "indications.indicationScoreForRisingTrial";
      } else {
        scoreField = "indications.indicationScoreForTrial";
      }
    }
    if (input.suppliedFilters.isScholarLeader?.value) {
      scoreField = "indications.indicationScoreForScholar";
    }
    if (input.suppliedFilters.isPublicationLeader?.value) {
      if (input.suppliedFilters.isRisingStar?.value) {
        scoreField = "indications.indicationScoreForRisingPub";
      } else {
        scoreField = "indications.indicationScoreForPub";
      }
    }
    if (input.suppliedFilters.isDOLeader?.value) {
      scoreField = "indications.indicationScoreForDigital";
    }
    if (scoreField) {
      const filtersToAdd = [];
      const indicationScoreExistsFilter = buildFieldExistsQuery(scoreField);
      filtersToAdd.push(indicationScoreExistsFilter);
      const indicationValues = input.suppliedFilters.indications?.values;
      if (indicationValues?.length) {
        const indicationTermsFilter = buildTermsQuery(
          "indications.indication.keyword",
          indicationValues
        );
        filtersToAdd.push(indicationTermsFilter);
      } else if (scoreField === "indications.indicationScoreForEducator") {
        const oldEducatorCriteria = {
          range: {
            congressCount: {
              gte: 10
            }
          }
        };
        // This logic corresponds to (Affiliation in [school,college,university) AND is [society leader]
        const affiliationsBasedCriteria: QueryDslQueryContainer = {
          bool: {
            filter: [
              {
                nested: {
                  path: "affiliations",
                  query: {
                    bool: {
                      filter: [
                        {
                          term: {
                            ["affiliations.isCurrent"]: true
                          }
                        },
                        {
                          term: {
                            ["affiliations.type"]: "Work Affiliation"
                          }
                        },
                        {
                          terms: {
                            ["affiliations.institution.orgTypes"]: [
                              "School",
                              "University",
                              "College"
                            ]
                          }
                        }
                      ]
                    }
                  }
                }
              },
              {
                nested: {
                  path: "affiliations",
                  query: {
                    bool: {
                      filter: [
                        {
                          term: {
                            ["affiliations.isCurrent"]: true
                          }
                        },
                        {
                          term: {
                            ["affiliations.type"]: "Society Member"
                          }
                        }
                      ]
                    }
                  }
                }
              }
            ]
          }
        };

        const nestedFilterQuery: QueryDslQueryContainer = {
          nested: {
            path: "indications",
            query: {
              bool: {
                filter: filtersToAdd
              }
            }
          }
        };
        const finalEducatorQuery: QueryDslQueryContainer = {
          bool: {
            should: [
              nestedFilterQuery,
              oldEducatorCriteria,
              affiliationsBasedCriteria
            ],
            minimum_should_match: 1
          }
        };
        return [finalEducatorQuery];
      }

      const nestedFilterQuery: QueryDslQueryContainer = {
        nested: {
          path: "indications",
          query: {
            bool: {
              filter: filtersToAdd
            }
          }
        }
      };
      filters.push(nestedFilterQuery);
    }
    return filters;
  }

  private buildShouldClausesForQuery(
    query: string,
    featureFlags: FeatureFlags
  ): QueryDslQueryContainer {
    const parsedQueryTree = this.queryParserService.parseQuery(query);

    const shoulds: QueryDslQueryContainer[] = [];

    //trials
    shoulds.push({
      nested: {
        path: "trials",
        query: this.parseQueryTreeForTrials(parsedQueryTree)
      }
    });

    //publications- kgIndications
    shoulds.push({
      nested: {
        path: "trials",
        query: this.toTermsFilter("trials.kgIndications", [query])
      }
    });

    //publications
    shoulds.push({
      nested: {
        path: "publications",
        query: this.parseQueryTreeForPublications(parsedQueryTree)
      }
    });

    //publications- kgIndications
    shoulds.push({
      nested: {
        path: "publications",
        query: this.toTermsFilter("publications.kgIndications", [query])
      }
    });

    //congresses
    shoulds.push({
      nested: {
        path: "congress",
        query: this.parseQueryTreeForCongress(parsedQueryTree)
      }
    });

    //payments
    shoulds.push({
      nested: {
        path: "payments",
        query: this.parseQueryTreeForPayment(parsedQueryTree)
      }
    });

    //procedures
    shoulds.push({
      nested: {
        path: "DRG_procedures",
        query: this.parseQueryTreeForDRGProcedures(parsedQueryTree)
      }
    });

    //prescriptions
    shoulds.push({
      nested: {
        path: "prescriptions",
        query: this.parseQueryTreeForPrescription(parsedQueryTree, featureFlags)
      }
    });

    const finalQuery: QueryDslQueryContainer = {
      bool: {
        should: shoulds,
        minimum_should_match: 1
      }
    };

    return finalQuery;
  }

  private buildBothOperatorFilters(
    bothParserResponse: BothQueryParseResult,
    featureFlags: FeatureFlags
  ): QueryDslQueryContainer[] {
    const queryList = bothParserResponse.terms;
    const queryWiseClauses: QueryDslQueryContainer[] = queryList.map((query) =>
      this.buildShouldClausesForQuery(query, featureFlags)
    );

    return queryWiseClauses;
  }

  private collectCountryFilterValues(
    countryValues: Array<string> = []
  ): Array<string> {
    if (countryValues.every(is2CharacterISOCountryCode)) {
      return countryValues;
    }

    // REMOVE WHEN FIXED
    const countryCodes: Array<string> = [];
    for (const countryName of countryValues) {
      if (countryName in COUNTRY_NAME_TO_CODE) {
        countryCodes.push(COUNTRY_NAME_TO_CODE[countryName]);
      } else {
        countryCodes.push(countryName);
      }
    }

    return countryCodes;
  }

  private collectRegionFilterValues(
    regionValues: Array<string> = []
  ): Array<string> {
    if (regionValues.every(isFilterValueAlreadyPipeDelimited)) {
      return regionValues;
    }

    // REMOVE WHEN FIXED
    const regionFilterValues: Array<string> = [];
    for (const regionName of regionValues) {
      if (regionName in REGIONS_KEYED_BY_NAME) {
        const region = REGIONS_KEYED_BY_NAME[regionName];
        regionFilterValues.push(`${region.country}|${region.code}`);
      } else {
        regionFilterValues.push(regionName);
      }
    }

    return regionFilterValues;
  }

  private collectCityFilterValues(
    cityValues: Array<string> = []
  ): Array<string> {
    if (cityValues.every(isFilterValueAlreadyPipeDelimited)) {
      return cityValues;
    }

    // REMOVE WHEN FIXED
    const cityFilterValues: Array<string> = [];
    for (const city of cityValues) {
      const delimitedCityFilterValues = toDelimitedCityFilterValues(city);
      if (delimitedCityFilterValues) {
        cityFilterValues.push(...delimitedCityFilterValues);
      }
    }

    return cityFilterValues;
  }

  private collectPostalCodeFilterValues(
    nestedPath: string,
    postalCodeValues: Array<string> = []
  ): QueryDslQueryContainer {
    if (postalCodeValues.every(isFilterValueAlreadyPipeDelimited)) {
      return {
        terms: {
          [`${nestedPath}.filters.postal_code`]: postalCodeValues
        }
      };
    }

    // REMOVE WHEN FIXED
    const pipeDelimitedPostalCodes = postalCodeValues
      .filter((value) => !!value)
      .join("|");
    const wildcardPrefixMatchForAmbiguousPostalCodes = `us\\|.+\\|(${pipeDelimitedPostalCodes})`;
    return {
      regexp: {
        [`${nestedPath}.filters.postal_code`]: {
          value: wildcardPrefixMatchForAmbiguousPostalCodes
        }
      }
    };
  }

  private getQueryForCareClusterAndClaimCode(
    careClusterSuppliedOrRequested: boolean,
    claimCodeSuppliedOrReuqested: boolean,
    claimCodeFilters: QueryDslQueryContainer[],
    careClusterFilters: QueryDslQueryContainer[]
  ) {
    if (!careClusterSuppliedOrRequested || !claimCodeSuppliedOrReuqested) {
      return [...claimCodeFilters, ...careClusterFilters];
    }
    const queryClauseForClaimCodeAndCcsr =
      claimCodeFilters.length > 0 && careClusterFilters.length > 0
        ? [
            {
              bool: {
                should: [...claimCodeFilters, ...careClusterFilters]
              }
            }
          ]
        : [...claimCodeFilters, ...careClusterFilters];
    return queryClauseForClaimCodeAndCcsr;
  }
}

function isLeaderboardSortOrDiversitySortApplied(
  input: KeywordFilterAutocompleteInput
): boolean {
  const filters = input.suppliedFilters;
  return (
    !_.isEmpty(filters.indications?.values) &&
    (((filters.isEducatorLeader?.value ||
      filters.isTrialLeader?.value ||
      filters.isScholarLeader?.value ||
      filters.isPublicationLeader?.value ||
      filters.isDOLeader?.value) ??
      false) ||
      (input.sortBy?.patientsDiversityRank ?? 0) > 0)
  );
}
function toFieldIsFalseOrDoesNotExists(field: string): QueryDslQueryContainer {
  return buildBoolShouldQuery([
    buildTermQuery(field, false),
    buildFieldMustNotExistsQuery(field)
  ]);
}

function toExists(field: string): QueryDslQueryContainer {
  return {
    exists: {
      field
    }
  };
}

function toTermFilter(
  fieldName: string,
  value: string | number | boolean
): QueryDslQueryContainer {
  return {
    term: {
      [fieldName]: value
    }
  };
}

export function buildMinMaxFilterScriptForAsset(
  minValue: number | null,
  maxValue?: number | null
) {
  const minMaxConditions: string[] = [];

  if (minValue) {
    minMaxConditions.push("(_score >= params.minValue)");
  }

  if (maxValue) {
    minMaxConditions.push("(_score <= params.maxValue)");
  }

  return {
    script: {
      params: {
        minValue: minValue ?? undefined,
        maxValue: maxValue ?? undefined
      },
      source: `${minMaxConditions.join("&&")}? 1:0`
    }
  };
}

export function extractClaimsCode(codeAndDescription: string): string {
  return codeAndDescription.split(CLAIMS_CODE_AND_DESCRIPTION_SEPARATOR)[0];
}
function getInternalCountFieldForClaims(
  claimType: ClaimType,
  featureFlags:
    | KeywordSearchFeatureFlags
    | AutocompleteFeatureFlags
    | NameSearchFeatureFlags,
  suppliedFilters: FilterInterface
) {
  const {
    enableUniquePatientCountForClaims,
    disableUniquePatientCountForOnlyProcedures
  } = featureFlags;
  const uniquePatientToggleFromInput: boolean =
    suppliedFilters.claims?.showUniquePatients?.value ?? false;
  const isProcedureWithUniqueCount =
    claimType === "procedures" && !disableUniquePatientCountForOnlyProcedures;
  const isDiagnosis = claimType === "diagnoses";

  const shouldUseUniquePatientCount =
    uniquePatientToggleFromInput &&
    (isProcedureWithUniqueCount || isDiagnosis) &&
    enableUniquePatientCountForClaims;

  const claimsCountField = shouldUseUniquePatientCount
    ? "internalUniqueCount"
    : "internalCount";
  return claimsCountField;
}

function hasPrescriptionFilters(suppliedFilters: FilterInterface) {
  return (
    !_.isEmpty(suppliedFilters.claims.genericNames?.values) ||
    // TODO: Enabled drug_class and brand_name once patient docs have it
    // !_.isEmpty(suppliedFilters.claims.drugClasses?.values) ||
    // !_.isEmpty(suppliedFilters.claims.brandNames?.values) ||
    suppliedFilters.claims.prescriptionsMinCount?.value ||
    suppliedFilters.claims.prescriptionsMaxCount?.value ||
    suppliedFilters.claims.prescriptionsMaxCount?.value === 0
  );
}

// The following functions can be removed once the location filters are converted
// to use the pipe-delimited format and no longer need to be converted.

// this is required to assist the translation of "Pennsylvania" => "us|PA"
const REGIONS_KEYED_BY_NAME = Object.entries(
  REGION_CODES_BY_COUNTRY_CODE_AND_REGION_NAME
).reduce((acc, [country, regions]) => {
  Object.entries(regions).forEach(([regionName, code]) => {
    acc[regionName] = { code, country };
  });
  return acc;
}, {} as Record<string, Region>);

const IS_CITY_STATE_COUNTRY_FORMAT = 3;
const IS_CITY_COUNTRY_FORMAT = 2;
const IS_CITY_LEVEL_REGION = 1;

interface Region {
  code: string;
  country: string;
}

const indicesFor4FieldCityFilterFormat = {
  city: 0,
  county: 1,
  region: 2,
  country: 3
};

const COUNTRY_CODES = Object.values(COUNTRY_NAME_TO_CODE);

function is2CharacterISOCountryCode(countryName: string) {
  return COUNTRY_CODES.includes(countryName);
}

function lookupCountryCode(countryName: string) {
  return COUNTRY_NAME_TO_CODE[countryName as keyof typeof COUNTRY_NAME_TO_CODE];
}

function isFilterValueAlreadyPipeDelimited(filterValue: string) {
  return filterValue.includes("|");
}

/*
inputs can be "Lancaster, PA, United States" and "Lancaster, United States" where the region is missing
REMOVE WHEN FIXED
*/
function getRegionValueFromCityFilter(locationFields: string[]) {
  let region;
  if (locationFields[indicesFor4FieldCityFilterFormat.country]) {
    region = locationFields[indicesFor4FieldCityFilterFormat.region];
  } else if (locationFields[indicesFor4FieldCityFilterFormat.region]) {
    region = locationFields[indicesFor4FieldCityFilterFormat.county];
  }

  if (region) {
    return hasRegionCode(region) ? REGIONS_KEYED_BY_NAME[region].code : region;
  }

  return "";
}

function getCountryValueFromCityFilter(locationFields: string[]) {
  if (locationFields[indicesFor4FieldCityFilterFormat.country]) {
    return locationFields[indicesFor4FieldCityFilterFormat.country];
  } else if (locationFields[indicesFor4FieldCityFilterFormat.region]) {
    return locationFields[indicesFor4FieldCityFilterFormat.region];
  }

  return locationFields[indicesFor4FieldCityFilterFormat.county];
}

function hasRegionCode(region: string) {
  return region in REGIONS_KEYED_BY_NAME;
}

function toDelimitedCityFilterValues(userCityFilter: string) {
  if (isFilterValueAlreadyPipeDelimited(userCityFilter)) {
    return [userCityFilter];
  }

  const locationFields = userCityFilter.split(/,/).map(_.trim);

  if (locationFields.length === IS_CITY_LEVEL_REGION) {
    return [userCityFilter];
  }

  if (
    locationFields.length !== IS_CITY_STATE_COUNTRY_FORMAT &&
    locationFields.length !== IS_CITY_COUNTRY_FORMAT
  ) {
    return null;
  }

  const city = locationFields[indicesFor4FieldCityFilterFormat.city];
  const region = getRegionValueFromCityFilter(locationFields);
  const countryName = getCountryValueFromCityFilter(locationFields);
  const countryCode = lookupCountryCode(countryName);

  if (!countryCode) {
    return null;
  }

  return [[countryCode, region, city].join("|")];
}
