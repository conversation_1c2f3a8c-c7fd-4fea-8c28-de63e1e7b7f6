import { faker } from "@faker-js/faker";
import {
  createMockInstance,
  getEmptyKeywordSearchFilters
} from "../util/TestUtils";
import { ConfigService } from "./ConfigService";
import {
  AggregationsAggregate,
  QueryDslQueryContainer,
  SearchRequest,
  SearchResponse,
  AggregationsTermsAggregateBase
} from "@elastic/elasticsearch/lib/api/types";
import { QueryParserService } from "./QueryParserService";
import { ITree } from "../lib/ParserTypes/types";
import {
  AutocompleteFeatureFlags,
  KeywordAutocompleteResourceService,
  autocompleteFeatureFlagTypes,
  featureFlagDefaults
} from "./KeywordAutocompleteResourceService";
import { ElasticSearchService } from "./ElasticSearchService";
import {
  FilterInterface,
  KeywordFilterAutocompleteFilterField
} from "@h1nyc/search-sdk";
import { QueryUnderstandingServiceClient } from "./QueryUnderstandingServiceClient";
import { QueryUnderstandingServiceResponse } from "../proto/query_understanding_service_pb";
import { UserResourceClient } from "@h1nyc/account-sdk";
import { KeywordFilterClauseBuilderService } from "./KeywordFilterClauseBuilderService";
import { FeatureFlagsService } from "@h1nyc/systems-feature-flags";
import { ParsedQueryTreeToElasticsearchQueriesService } from "./ParsedQueryTreeToElasticsearchQueries";
import {
  generateMockElasticsearchTermQuery,
  mockParseTreeToElasticsearchQueries
} from "./HCPDocumentTestingUtils";
import { NestedPath } from "../util/QueryParsingUtils";
import {
  DocCountBucket,
  KeywordAutocompleteResponseAdapterService
} from "./KeywordAutocompleteResponseAdapterService";
import { CHINESE, ENGLISH, JAPANESE, Language } from "./LanguageDetectService";
import { LDFlagsState } from "launchdarkly-node-server-sdk";
import { AggregationContainerBuilderService } from "./AggregationContainerBuilderService";
import CalculateMinimumNameVariations from "./queryBuilders/CalculateMinimumNameVariations";
import NameSearchBuilderFactory from "./queryBuilders/NameSearchBuilderFactory";
import DefaultNameSearchBuilder from "./queryBuilders/DefaultNameSearchBuilder";
import ChineseJapaneseNameSearchBuilder from "./queryBuilders/ChineseJapaneseNameSearchBuilder";
import { when } from "jest-when";
import { publicationsQueryFields } from "./KeywordSearchResourceServiceRewrite";
import {
  CcsrIcdMappingRepository,
  CcsrPxMappingRepository
} from "@h1nyc/pipeline-repositories";
import { IndicationsTreeSearchService } from "./IndicationsTreeSearchService";

const SURGERY_JPN = "サージャリー";
const SURGERY_CMN = "手术";
const COMPUTER_THAI = "คอมพิวเตอร์";
const SURGERY_THAI = "การผ่าตัด";

// TODO: this could probably be extracted to a testing utility
function generateMockElasticsearchResponse(
  aggregations: Record<string, AggregationsAggregate> = {
    matching: generateMockAggregates()
  }
): SearchResponse<never> {
  return {
    took: faker.datatype.number(),
    timed_out: false,
    _shards: {
      total: faker.datatype.number(),
      successful: faker.datatype.number(),
      skipped: faker.datatype.number(),
      failed: faker.datatype.number()
    },
    hits: {
      total: {
        value: faker.datatype.number(),
        relation: "eq"
      },
      max_score: faker.datatype.number(),
      hits: []
    },
    aggregations
  };
}

function generateFilters(
  overrides: Partial<FilterInterface> = {}
): FilterInterface {
  const baseFilters: FilterInterface = getEmptyKeywordSearchFilters();

  return { ...baseFilters, ...overrides };
}

function generateMockAggregates(
  buckets: Array<DocCountBucket> = []
): AggregationsTermsAggregateBase<DocCountBucket> {
  return {
    doc_count_error_upper_bound: faker.datatype.number(),
    sum_other_doc_count: faker.datatype.number(),
    buckets
  };
}

function generateMockLanguage(language: string): any {
  return {
    language,
    title: faker.datatype.string(),
    flagCountryCode: faker.address.countryCode(),
    userLanguageSettings: [],
    projectLanguageSettings: []
  };
}

function mockAugmentedQueryInQueryUnderstandingServiceResponse(
  queryUnderstandingService: jest.Mocked<QueryUnderstandingServiceClient>,
  query: string
) {
  const queryUnderstandingServiceResponse: QueryUnderstandingServiceResponse =
    new QueryUnderstandingServiceResponse();
  queryUnderstandingServiceResponse.setAugmentedQuery(query);
  queryUnderstandingService.analyze.mockResolvedValue(
    queryUnderstandingServiceResponse
  );
}

const FEATURE_FLAG_DEFAULTS: AutocompleteFeatureFlags = {
  enableQueryContextualPaymentsFiltering: false,
  enableCTMSV2: false,
  enableTagsInElasticsearch: false,
  enableUniquePatientCountForClaims: false,
  enableBrazilianClaims: true,
  disableUniquePatientCountForOnlyProcedures: false,
  enableNestedIndicationFilter: false,
  enableCcsrExclusionForMatchedCounts: false,
  enableLocationFilterRegionRollup: false,
  enableAiqData: false,
  enableBothOperatorForSearch: false,
  enableNewGlobalLeaderTier: false
};

function generateFeatureFlagsState(
  featureFlagValues: AutocompleteFeatureFlags
) {
  const getFlagValue = jest.fn();

  for (const flag of autocompleteFeatureFlagTypes) {
    const flagKey = featureFlagDefaults[flag].key;
    when(getFlagValue)
      .calledWith(flagKey)
      .mockReturnValue(featureFlagValues[flag]);
  }

  return {
    getFlagValue
  } as unknown as LDFlagsState;
}

function mockFeatureFlagsService(
  overrides: Partial<AutocompleteFeatureFlags> = {}
) {
  const featureFlagsService = createMockInstance(
    FeatureFlagsService as any
  ) as jest.Mocked<FeatureFlagsService>;

  featureFlagsService.getAllFlags = jest
    .fn()
    .mockResolvedValue(
      generateFeatureFlagsState({ ...FEATURE_FLAG_DEFAULTS, ...overrides })
    );

  return featureFlagsService;
}

describe("KeywordAutocompleteResourceService", () => {
  describe("Keyword autocomplete", () => {
    describe("negative tests", () => {
      it("should rethrow an error thrown by queryUnderstandingServiceClient.analyze", async () => {
        const configService = createMockInstance(ConfigService);
        configService.elasticPeopleIndex = faker.datatype.string();

        const queryParserService = createMockInstance(QueryParserService);

        const elasticSearchService = createMockInstance(ElasticSearchService);
        elasticSearchService.query.mockResolvedValue(
          generateMockElasticsearchResponse()
        );
        const queryUnderstandingService = createMockInstance(
          QueryUnderstandingServiceClient
        );
        const err = new Error(
          "queryUnderstandingService.analyze threw an error for some reason"
        );
        queryUnderstandingService.analyze.mockRejectedValue(err);

        const userClient = createMockInstance(UserResourceClient);
        const keywordFilterClauseBuilderService = createMockInstance(
          KeywordFilterClauseBuilderService
        );
        keywordFilterClauseBuilderService.buildForAutocompleteRequest.mockResolvedValue(
          []
        );

        const parsedQueryTreeToElasticsearchQueriesService = createMockInstance(
          ParsedQueryTreeToElasticsearchQueriesService
        );

        const keywordAutocompleteResponseAdapterService = createMockInstance(
          KeywordAutocompleteResponseAdapterService
        );

        const nameSearchBuilderFactory = createMockInstance(
          NameSearchBuilderFactory
        );
        const calculateMinimumNameVariations = createMockInstance(
          CalculateMinimumNameVariations
        );

        const featureFlagsService = mockFeatureFlagsService();
        const aggregationContainerBuilderService =
          new AggregationContainerBuilderService(
            parsedQueryTreeToElasticsearchQueriesService,
            keywordFilterClauseBuilderService
          );
        const ccsrIcdMappingRepository = createMockInstance(
          CcsrIcdMappingRepository
        );

        const ccsrPxMappingRepository = createMockInstance(
          CcsrPxMappingRepository
        );

        const indicationsTreeSearchService = createMockInstance(
          IndicationsTreeSearchService
        );

        const keywordAutocompleteResourceService =
          new KeywordAutocompleteResourceService(
            configService,
            elasticSearchService,
            queryParserService,
            queryUnderstandingService,
            userClient,
            keywordFilterClauseBuilderService,
            parsedQueryTreeToElasticsearchQueriesService,
            keywordAutocompleteResponseAdapterService,
            featureFlagsService,
            aggregationContainerBuilderService,
            nameSearchBuilderFactory,
            calculateMinimumNameVariations,
            ccsrIcdMappingRepository,
            ccsrPxMappingRepository,
            indicationsTreeSearchService
          );

        const projectId = faker.datatype.string();
        const projectFeatures = {
          engagementsV2: faker.datatype.boolean()
        };
        const userId = faker.datatype.string();
        const query = undefined;
        const filterField = KeywordFilterAutocompleteFilterField.DESIGNATION;
        const filterValue = faker.random.word();

        const specialties = [faker.datatype.string(), faker.datatype.string()];
        const suppliedFilters = generateFilters();
        suppliedFilters.specialty!.values = specialties;

        try {
          await keywordAutocompleteResourceService.keywordAutocomplete({
            projectId,
            projectFeatures,
            userId,
            query,
            filterField,
            filterValue,
            suppliedFilters
          });
        } catch (thrownErr) {
          expect(thrownErr).toEqual(err);
        }
      });

      it("should rethrow an error thrown by elasticService.query", async () => {
        const configService = createMockInstance(ConfigService);
        configService.elasticPeopleIndex = faker.datatype.string();

        const queryParserService = createMockInstance(QueryParserService);

        const elasticSearchService = createMockInstance(ElasticSearchService);
        const err = new Error(
          "elasticSearchService.query threw an error for some reason"
        );
        elasticSearchService.query.mockRejectedValue(err);

        const queryUnderstandingService = createMockInstance(
          QueryUnderstandingServiceClient
        );

        const userClient = createMockInstance(UserResourceClient);
        const keywordFilterClauseBuilderService = createMockInstance(
          KeywordFilterClauseBuilderService
        );
        keywordFilterClauseBuilderService.buildForAutocompleteRequest.mockResolvedValue(
          []
        );

        const parsedQueryTreeToElasticsearchQueriesService = createMockInstance(
          ParsedQueryTreeToElasticsearchQueriesService
        );

        const nameSearchBuilderFactory = createMockInstance(
          NameSearchBuilderFactory
        );
        const calculateMinimumNameVariations = createMockInstance(
          CalculateMinimumNameVariations
        );

        const keywordAutocompleteResponseAdapterService = createMockInstance(
          KeywordAutocompleteResponseAdapterService
        );

        const featureFlagsService = mockFeatureFlagsService();

        const aggregationContainerBuilderService =
          new AggregationContainerBuilderService(
            parsedQueryTreeToElasticsearchQueriesService,
            keywordFilterClauseBuilderService
          );
        const ccsrIcdMappingRepository = createMockInstance(
          CcsrIcdMappingRepository
        );

        const ccsrPxMappingRepository = createMockInstance(
          CcsrPxMappingRepository
        );
        const indicationsTreeSearchService = createMockInstance(
          IndicationsTreeSearchService
        );
        const keywordAutocompleteResourceService =
          new KeywordAutocompleteResourceService(
            configService,
            elasticSearchService,
            queryParserService,
            queryUnderstandingService,
            userClient,
            keywordFilterClauseBuilderService,
            parsedQueryTreeToElasticsearchQueriesService,
            keywordAutocompleteResponseAdapterService,
            featureFlagsService,
            aggregationContainerBuilderService,
            nameSearchBuilderFactory,
            calculateMinimumNameVariations,
            ccsrIcdMappingRepository,
            ccsrPxMappingRepository,
            indicationsTreeSearchService
          );

        const projectId = faker.datatype.string();
        const projectFeatures = {
          engagementsV2: faker.datatype.boolean()
        };
        const userId = faker.datatype.string();
        const query = undefined;
        const filterField = KeywordFilterAutocompleteFilterField.DESIGNATION;
        const filterValue = faker.random.word();

        const specialties = [faker.datatype.string(), faker.datatype.string()];
        const suppliedFilters = generateFilters();
        suppliedFilters.specialty!.values = specialties;

        try {
          await keywordAutocompleteResourceService.keywordAutocomplete({
            projectId,
            projectFeatures,
            userId,
            query,
            filterField,
            filterValue,
            suppliedFilters
          });
        } catch (thrownErr) {
          expect(thrownErr).toEqual(err);
        }
      });
    });

    describe("filters that depend on query term context", () => {
      describe("requests with query", () => {
        describe("no query-contextual filters", () => {
          it("should include all 6 top-level elements as shoulds with minimum_should_match=1", async () => {
            const configService = createMockInstance(ConfigService);
            configService.elasticPeopleIndex = faker.datatype.string();

            const queryParserService = createMockInstance(QueryParserService);
            const textValue1 = faker.datatype.string();
            const textValue2 = faker.datatype.string();
            const parsedTree: ITree = [
              "And",
              [
                ["Including", ["Text", textValue1]],
                ["Including", ["Text", textValue2]]
              ]
            ];
            queryParserService.parseQuery.mockReturnValue(parsedTree);

            const elasticSearchService =
              createMockInstance(ElasticSearchService);

            elasticSearchService.query.mockResolvedValue(
              generateMockElasticsearchResponse()
            );
            const queryUnderstandingService = createMockInstance(
              QueryUnderstandingServiceClient
            );
            const userClient = createMockInstance(UserResourceClient);
            const keywordFilterClauseBuilderService = createMockInstance(
              KeywordFilterClauseBuilderService
            );
            keywordFilterClauseBuilderService.buildForAutocompleteRequest.mockResolvedValue(
              []
            );

            const parsedQueryTreeToElasticsearchQueriesService =
              createMockInstance(ParsedQueryTreeToElasticsearchQueriesService);

            const pathSpecificQueries: Record<
              NestedPath,
              QueryDslQueryContainer
            > = {
              trials: generateMockElasticsearchTermQuery(),
              publications: generateMockElasticsearchTermQuery(),
              congress: generateMockElasticsearchTermQuery(),
              payments: generateMockElasticsearchTermQuery(),
              DRG_diagnoses: generateMockElasticsearchTermQuery(),
              DRG_procedures: generateMockElasticsearchTermQuery(),
              prescriptions: generateMockElasticsearchTermQuery(),
              ccsr: generateMockElasticsearchTermQuery(),
              ccsr_px: generateMockElasticsearchTermQuery()
            };

            const mockParseTreeToQueries =
              mockParseTreeToElasticsearchQueries(pathSpecificQueries);

            parsedQueryTreeToElasticsearchQueriesService.parse.mockImplementation(
              mockParseTreeToQueries
            );

            const nameSearchBuilderFactory = createMockInstance(
              NameSearchBuilderFactory
            );
            const calculateMinimumNameVariations = createMockInstance(
              CalculateMinimumNameVariations
            );

            const keywordAutocompleteResponseAdapterService =
              createMockInstance(KeywordAutocompleteResponseAdapterService);

            const featureFlagsService = mockFeatureFlagsService();

            const aggregationContainerBuilderService = createMockInstance(
              AggregationContainerBuilderService
            );
            const ccsrIcdMappingRepository = createMockInstance(
              CcsrIcdMappingRepository
            );

            const ccsrPxMappingRepository = createMockInstance(
              CcsrPxMappingRepository
            );

            const indicationsTreeSearchService = createMockInstance(
              IndicationsTreeSearchService
            );

            const keywordAutocompleteResourceService =
              new KeywordAutocompleteResourceService(
                configService,
                elasticSearchService,
                queryParserService,
                queryUnderstandingService,
                userClient,
                keywordFilterClauseBuilderService,
                parsedQueryTreeToElasticsearchQueriesService,
                keywordAutocompleteResponseAdapterService,
                featureFlagsService,
                aggregationContainerBuilderService,
                nameSearchBuilderFactory,
                calculateMinimumNameVariations,
                ccsrIcdMappingRepository,
                ccsrPxMappingRepository,
                indicationsTreeSearchService
              );

            const projectId = faker.datatype.string();
            const projectFeatures = {
              engagementsV2: faker.datatype.boolean()
            };
            const userId = faker.datatype.string();
            const query = textValue1 + " AND " + textValue2;
            const filterField = KeywordFilterAutocompleteFilterField.SPECIALTY;
            const filterValue = faker.random.word();
            const suppliedFilters = generateFilters();

            mockAugmentedQueryInQueryUnderstandingServiceResponse(
              queryUnderstandingService,
              query
            );

            await keywordAutocompleteResourceService.keywordAutocomplete({
              projectId,
              projectFeatures,
              userId,
              query,
              filterField,
              filterValue,
              suppliedFilters
            });

            expect(elasticSearchService.query).toHaveBeenCalledWith(
              expect.objectContaining({
                query: {
                  bool: {
                    filter: [
                      {
                        bool: {
                          should: expect.arrayContaining([
                            {
                              nested: {
                                path: "congress",
                                score_mode: "sum",
                                query: {
                                  bool: {
                                    must: { match_all: {} },
                                    filter: [pathSpecificQueries.congress]
                                  }
                                }
                              }
                            },
                            {
                              nested: {
                                path: "trials",
                                score_mode: "sum",
                                query: {
                                  bool: {
                                    must: { match_all: {} },
                                    filter: [pathSpecificQueries.trials]
                                  }
                                }
                              }
                            },
                            {
                              nested: {
                                path: "publications",
                                query: {
                                  bool: {
                                    filter: [pathSpecificQueries.publications]
                                  }
                                }
                              }
                            },
                            {
                              nested: {
                                path: "payments",
                                score_mode: "sum",
                                query: {
                                  bool: {
                                    must: { match_all: {} },
                                    filter: [pathSpecificQueries.payments]
                                  }
                                }
                              }
                            },
                            {
                              nested: {
                                path: "DRG_diagnoses",
                                query: {
                                  bool: {
                                    filter: [pathSpecificQueries.DRG_diagnoses]
                                  }
                                }
                              }
                            },
                            {
                              nested: {
                                path: "DRG_procedures",
                                query: {
                                  bool: {
                                    filter: [pathSpecificQueries.DRG_procedures]
                                  }
                                }
                              }
                            },
                            {
                              nested: {
                                path: "prescriptions",
                                query: {
                                  bool: {
                                    filter: [pathSpecificQueries.prescriptions]
                                  }
                                }
                              }
                            }
                          ]),
                          minimum_should_match: 1
                        }
                      }
                    ]
                  }
                }
              })
            );
          });

          describe("Brazilian claims disabled", () => {
            it("should include all 6 top-level elements as shoulds, filtering out HCPs in Brazil for claims", async () => {
              const configService = createMockInstance(ConfigService);
              configService.elasticPeopleIndex = faker.datatype.string();

              const queryParserService = createMockInstance(QueryParserService);
              const textValue1 = faker.datatype.string();
              const textValue2 = faker.datatype.string();
              const parsedTree: ITree = [
                "And",
                [
                  ["Including", ["Text", textValue1]],
                  ["Including", ["Text", textValue2]]
                ]
              ];
              queryParserService.parseQuery.mockReturnValue(parsedTree);

              const elasticSearchService =
                createMockInstance(ElasticSearchService);

              elasticSearchService.query.mockResolvedValue(
                generateMockElasticsearchResponse()
              );
              const queryUnderstandingService = createMockInstance(
                QueryUnderstandingServiceClient
              );
              const userClient = createMockInstance(UserResourceClient);
              const keywordFilterClauseBuilderService = createMockInstance(
                KeywordFilterClauseBuilderService
              );
              keywordFilterClauseBuilderService.buildForAutocompleteRequest.mockResolvedValue(
                []
              );

              const parsedQueryTreeToElasticsearchQueriesService =
                createMockInstance(
                  ParsedQueryTreeToElasticsearchQueriesService
                );

              const pathSpecificQueries: Record<
                NestedPath,
                QueryDslQueryContainer
              > = {
                trials: generateMockElasticsearchTermQuery(),
                publications: generateMockElasticsearchTermQuery(),
                congress: generateMockElasticsearchTermQuery(),
                payments: generateMockElasticsearchTermQuery(),
                DRG_diagnoses: generateMockElasticsearchTermQuery(),
                DRG_procedures: generateMockElasticsearchTermQuery(),
                prescriptions: generateMockElasticsearchTermQuery(),
                ccsr: generateMockElasticsearchTermQuery(),
                ccsr_px: generateMockElasticsearchTermQuery()
              };

              const mockParseTreeToQueries =
                mockParseTreeToElasticsearchQueries(pathSpecificQueries);

              parsedQueryTreeToElasticsearchQueriesService.parse.mockImplementation(
                mockParseTreeToQueries
              );

              const nameSearchBuilderFactory = createMockInstance(
                NameSearchBuilderFactory
              );
              const calculateMinimumNameVariations = createMockInstance(
                CalculateMinimumNameVariations
              );

              const keywordAutocompleteResponseAdapterService =
                createMockInstance(KeywordAutocompleteResponseAdapterService);

              const featureFlagsService = mockFeatureFlagsService({
                enableBrazilianClaims: false
              });

              const aggregationContainerBuilderService = createMockInstance(
                AggregationContainerBuilderService
              );
              const ccsrIcdMappingRepository = createMockInstance(
                CcsrIcdMappingRepository
              );

              const ccsrPxMappingRepository = createMockInstance(
                CcsrPxMappingRepository
              );

              const indicationsTreeSearchService = createMockInstance(
                IndicationsTreeSearchService
              );

              const keywordAutocompleteResourceService =
                new KeywordAutocompleteResourceService(
                  configService,
                  elasticSearchService,
                  queryParserService,
                  queryUnderstandingService,
                  userClient,
                  keywordFilterClauseBuilderService,
                  parsedQueryTreeToElasticsearchQueriesService,
                  keywordAutocompleteResponseAdapterService,
                  featureFlagsService,
                  aggregationContainerBuilderService,
                  nameSearchBuilderFactory,
                  calculateMinimumNameVariations,
                  ccsrIcdMappingRepository,
                  ccsrPxMappingRepository,
                  indicationsTreeSearchService
                );

              const projectId = faker.datatype.string();
              const projectFeatures = {
                engagementsV2: faker.datatype.boolean()
              };
              const userId = faker.datatype.string();
              const query = textValue1 + " AND " + textValue2;
              const filterField =
                KeywordFilterAutocompleteFilterField.SPECIALTY;
              const filterValue = faker.random.word();
              const suppliedFilters = generateFilters();

              mockAugmentedQueryInQueryUnderstandingServiceResponse(
                queryUnderstandingService,
                query
              );

              await keywordAutocompleteResourceService.keywordAutocomplete({
                projectId,
                projectFeatures,
                userId,
                query,
                filterField,
                filterValue,
                suppliedFilters
              });

              expect(elasticSearchService.query).toHaveBeenCalledWith(
                expect.objectContaining({
                  query: {
                    bool: {
                      filter: [
                        {
                          bool: {
                            should: expect.arrayContaining([
                              {
                                nested: {
                                  path: "congress",
                                  score_mode: "sum",
                                  query: {
                                    bool: {
                                      must: { match_all: {} },
                                      filter: [pathSpecificQueries.congress]
                                    }
                                  }
                                }
                              },
                              {
                                nested: {
                                  path: "trials",
                                  score_mode: "sum",
                                  query: {
                                    bool: {
                                      must: { match_all: {} },
                                      filter: [pathSpecificQueries.trials]
                                    }
                                  }
                                }
                              },
                              {
                                nested: {
                                  path: "publications",
                                  query: {
                                    bool: {
                                      filter: [pathSpecificQueries.publications]
                                    }
                                  }
                                }
                              },
                              {
                                nested: {
                                  path: "payments",
                                  score_mode: "sum",
                                  query: {
                                    bool: {
                                      must: { match_all: {} },
                                      filter: [pathSpecificQueries.payments]
                                    }
                                  }
                                }
                              },
                              {
                                bool: {
                                  must_not: [
                                    expect.termsQuery("country_multi", [
                                      "Brazil"
                                    ])
                                  ],
                                  must: [
                                    {
                                      nested: {
                                        path: "DRG_diagnoses",
                                        query: {
                                          bool: {
                                            filter: [
                                              pathSpecificQueries.DRG_diagnoses
                                            ]
                                          }
                                        }
                                      }
                                    }
                                  ]
                                }
                              },
                              {
                                bool: {
                                  must_not: [
                                    expect.termsQuery("country_multi", [
                                      "Brazil"
                                    ])
                                  ],
                                  must: [
                                    {
                                      nested: {
                                        path: "DRG_procedures",
                                        query: {
                                          bool: {
                                            filter: [
                                              pathSpecificQueries.DRG_procedures
                                            ]
                                          }
                                        }
                                      }
                                    }
                                  ]
                                }
                              },
                              {
                                nested: {
                                  path: "prescriptions",
                                  query: {
                                    bool: {
                                      filter: [
                                        pathSpecificQueries.prescriptions
                                      ]
                                    }
                                  }
                                }
                              }
                            ]),
                            minimum_should_match: 1
                          }
                        }
                      ]
                    }
                  }
                })
              );
            });

            it("should include a must_not clause to filter out Brazilian HCPs for diagnoses/procedures autocomplete requests", async () => {
              const configService = createMockInstance(ConfigService);
              configService.elasticPeopleIndex = faker.datatype.string();

              const queryParserService = createMockInstance(QueryParserService);
              const textValue1 = faker.datatype.string();
              const textValue2 = faker.datatype.string();
              const parsedTree: ITree = [
                "And",
                [
                  ["Including", ["Text", textValue1]],
                  ["Including", ["Text", textValue2]]
                ]
              ];
              queryParserService.parseQuery.mockReturnValue(parsedTree);

              const elasticSearchService =
                createMockInstance(ElasticSearchService);

              elasticSearchService.query.mockResolvedValue(
                generateMockElasticsearchResponse()
              );
              const queryUnderstandingService = createMockInstance(
                QueryUnderstandingServiceClient
              );
              const userClient = createMockInstance(UserResourceClient);
              const keywordFilterClauseBuilderService = createMockInstance(
                KeywordFilterClauseBuilderService
              );
              keywordFilterClauseBuilderService.buildForAutocompleteRequest.mockResolvedValue(
                []
              );

              const parsedQueryTreeToElasticsearchQueriesService =
                createMockInstance(
                  ParsedQueryTreeToElasticsearchQueriesService
                );

              const pathSpecificQueries: Record<
                NestedPath,
                QueryDslQueryContainer
              > = {
                trials: generateMockElasticsearchTermQuery(),
                publications: generateMockElasticsearchTermQuery(),
                congress: generateMockElasticsearchTermQuery(),
                payments: generateMockElasticsearchTermQuery(),
                DRG_diagnoses: generateMockElasticsearchTermQuery(),
                DRG_procedures: generateMockElasticsearchTermQuery(),
                prescriptions: generateMockElasticsearchTermQuery(),
                ccsr: generateMockElasticsearchTermQuery(),
                ccsr_px: generateMockElasticsearchTermQuery()
              };

              const mockParseTreeToQueries =
                mockParseTreeToElasticsearchQueries(pathSpecificQueries);

              parsedQueryTreeToElasticsearchQueriesService.parse.mockImplementation(
                mockParseTreeToQueries
              );

              const nameSearchBuilderFactory = createMockInstance(
                NameSearchBuilderFactory
              );
              const calculateMinimumNameVariations = createMockInstance(
                CalculateMinimumNameVariations
              );

              const keywordAutocompleteResponseAdapterService =
                createMockInstance(KeywordAutocompleteResponseAdapterService);

              const featureFlagsService = mockFeatureFlagsService({
                enableBrazilianClaims: false
              });

              const aggregationContainerBuilderService = createMockInstance(
                AggregationContainerBuilderService
              );
              const ccsrIcdMappingRepository = createMockInstance(
                CcsrIcdMappingRepository
              );

              const ccsrPxMappingRepository = createMockInstance(
                CcsrPxMappingRepository
              );

              const indicationsTreeSearchService = createMockInstance(
                IndicationsTreeSearchService
              );

              const keywordAutocompleteResourceService =
                new KeywordAutocompleteResourceService(
                  configService,
                  elasticSearchService,
                  queryParserService,
                  queryUnderstandingService,
                  userClient,
                  keywordFilterClauseBuilderService,
                  parsedQueryTreeToElasticsearchQueriesService,
                  keywordAutocompleteResponseAdapterService,
                  featureFlagsService,
                  aggregationContainerBuilderService,
                  nameSearchBuilderFactory,
                  calculateMinimumNameVariations,
                  ccsrIcdMappingRepository,
                  ccsrPxMappingRepository,
                  indicationsTreeSearchService
                );

              const projectId = faker.datatype.string();
              const projectFeatures = {
                engagementsV2: faker.datatype.boolean()
              };
              const userId = faker.datatype.string();
              const query = textValue1 + " AND " + textValue2;
              const suppliedFilters = generateFilters();

              for (const filterField of [
                KeywordFilterAutocompleteFilterField.DRG_DIAGNOSES,
                KeywordFilterAutocompleteFilterField.DIAGNOSES,
                KeywordFilterAutocompleteFilterField.PROCEDURES,
                KeywordFilterAutocompleteFilterField.DRG_PROCEDURES
              ]) {
                const filterValue = faker.random.word();

                mockAugmentedQueryInQueryUnderstandingServiceResponse(
                  queryUnderstandingService,
                  query
                );

                await keywordAutocompleteResourceService.keywordAutocomplete({
                  projectId,
                  projectFeatures,
                  userId,
                  query,
                  filterField,
                  filterValue,
                  suppliedFilters
                });

                expect(elasticSearchService.query).toHaveBeenCalledWith(
                  expect.objectContaining({
                    query: {
                      bool: {
                        filter: [],
                        must_not: [
                          expect.termsQuery("country_multi", ["Brazil"])
                        ]
                      }
                    }
                  })
                );
              }
            });
          });

          describe("language detection", () => {
            it("should include all language fields for publications filter", async () => {
              const configService = createMockInstance(ConfigService);
              configService.elasticPeopleIndex = faker.datatype.string();

              const queryParserService = createMockInstance(QueryParserService);
              const textValue1 = COMPUTER_THAI;
              const textValue2 = SURGERY_THAI;
              const parsedTree: ITree = [
                "And",
                [
                  ["Including", ["Text", textValue1]],
                  ["Including", ["Text", textValue2]]
                ]
              ];
              queryParserService.parseQuery.mockReturnValue(parsedTree);

              const elasticSearchService =
                createMockInstance(ElasticSearchService);

              elasticSearchService.query.mockResolvedValue(
                generateMockElasticsearchResponse()
              );
              const queryUnderstandingService = createMockInstance(
                QueryUnderstandingServiceClient
              );
              const userClient = createMockInstance(UserResourceClient);
              userClient.getUsersPreferredLanguage.mockResolvedValue(
                generateMockLanguage("english")
              );
              const keywordFilterClauseBuilderService = createMockInstance(
                KeywordFilterClauseBuilderService
              );
              keywordFilterClauseBuilderService.buildForAutocompleteRequest.mockResolvedValue(
                []
              );

              const parsedQueryTreeToElasticsearchQueriesService =
                createMockInstance(
                  ParsedQueryTreeToElasticsearchQueriesService
                );

              const pathSpecificQueries: Record<
                NestedPath,
                QueryDslQueryContainer
              > = {
                trials: generateMockElasticsearchTermQuery(),
                publications: generateMockElasticsearchTermQuery(),
                congress: generateMockElasticsearchTermQuery(),
                payments: generateMockElasticsearchTermQuery(),
                DRG_diagnoses: generateMockElasticsearchTermQuery(),
                DRG_procedures: generateMockElasticsearchTermQuery(),
                prescriptions: generateMockElasticsearchTermQuery(),
                ccsr: generateMockElasticsearchTermQuery(),
                ccsr_px: generateMockElasticsearchTermQuery()
              };

              const mockParseTreeToQueries =
                mockParseTreeToElasticsearchQueries(pathSpecificQueries);

              parsedQueryTreeToElasticsearchQueriesService.parse.mockImplementation(
                mockParseTreeToQueries
              );

              const nameSearchBuilderFactory = createMockInstance(
                NameSearchBuilderFactory
              );
              const calculateMinimumNameVariations = createMockInstance(
                CalculateMinimumNameVariations
              );

              const keywordAutocompleteResponseAdapterService =
                createMockInstance(KeywordAutocompleteResponseAdapterService);

              const featureFlagsService = mockFeatureFlagsService();

              const aggregationContainerBuilderService = createMockInstance(
                AggregationContainerBuilderService
              );
              const ccsrIcdMappingRepository = createMockInstance(
                CcsrIcdMappingRepository
              );

              const ccsrPxMappingRepository = createMockInstance(
                CcsrPxMappingRepository
              );

              const indicationsTreeSearchService = createMockInstance(
                IndicationsTreeSearchService
              );

              const keywordAutocompleteResourceService =
                new KeywordAutocompleteResourceService(
                  configService,
                  elasticSearchService,
                  queryParserService,
                  queryUnderstandingService,
                  userClient,
                  keywordFilterClauseBuilderService,
                  parsedQueryTreeToElasticsearchQueriesService,
                  keywordAutocompleteResponseAdapterService,
                  featureFlagsService,
                  aggregationContainerBuilderService,
                  nameSearchBuilderFactory,
                  calculateMinimumNameVariations,
                  ccsrIcdMappingRepository,
                  ccsrPxMappingRepository,
                  indicationsTreeSearchService
                );

              const projectId = faker.datatype.string();
              const projectFeatures = {
                engagementsV2: faker.datatype.boolean()
              };
              const userId = faker.datatype.string();
              const filterField =
                KeywordFilterAutocompleteFilterField.SPECIALTY;
              const filterValue = faker.random.word();
              const query = COMPUTER_THAI + " AND " + SURGERY_THAI;
              const suppliedFilters = generateFilters();

              mockAugmentedQueryInQueryUnderstandingServiceResponse(
                queryUnderstandingService,
                query
              );

              await keywordAutocompleteResourceService.keywordAutocomplete({
                projectId,
                projectFeatures,
                userId,
                query,
                filterField,
                filterValue,
                suppliedFilters
              });

              expect(elasticSearchService.query).toHaveBeenCalledWith(
                expect.objectContaining({
                  query: {
                    bool: {
                      filter: [
                        {
                          bool: {
                            should: expect.arrayContaining([
                              {
                                nested: {
                                  path: "publications",
                                  query: {
                                    bool: {
                                      filter: [pathSpecificQueries.publications]
                                    }
                                  }
                                }
                              }
                            ]),
                            minimum_should_match: 1
                          }
                        }
                      ]
                    }
                  }
                })
              );

              expect(
                parsedQueryTreeToElasticsearchQueriesService.parse
              ).toHaveBeenCalledWith(parsedTree, publicationsQueryFields);
            });
          });

          it("should query with augmented query when QueryUnderstanding service is called", async () => {
            const configService = createMockInstance(ConfigService);
            configService.elasticPeopleIndex = faker.datatype.string();

            const queryParserService = createMockInstance(QueryParserService);

            const elasticSearchService =
              createMockInstance(ElasticSearchService);

            const queryUnderstandingServiceClient = createMockInstance(
              QueryUnderstandingServiceClient
            );
            const queryUnderstandingServiceResponse: QueryUnderstandingServiceResponse =
              new QueryUnderstandingServiceResponse();

            const queryWord1 = faker.datatype.string();
            const queryWord2 = faker.datatype.string();
            const queryWord3 = faker.datatype.string();

            const augmentedQuery = `${queryWord1} OR ${queryWord2} OR ${queryWord3}`;
            const expectedAugmentedQuery = `${queryWord1} | ${queryWord2} | ${queryWord3}`;
            queryUnderstandingServiceResponse.setAugmentedQuery(augmentedQuery);
            queryUnderstandingServiceClient.analyze.mockResolvedValue(
              queryUnderstandingServiceResponse
            );

            elasticSearchService.query.mockResolvedValue(
              generateMockElasticsearchResponse()
            );
            const userClient = createMockInstance(UserResourceClient);
            const keywordFilterClauseBuilderService = createMockInstance(
              KeywordFilterClauseBuilderService
            );
            keywordFilterClauseBuilderService.buildForAutocompleteRequest.mockResolvedValue(
              []
            );

            const parsedQueryTreeToElasticsearchQueriesService =
              createMockInstance(ParsedQueryTreeToElasticsearchQueriesService);

            const pathSpecificQueries: Record<
              NestedPath,
              QueryDslQueryContainer
            > = {
              trials: generateMockElasticsearchTermQuery(),
              publications: generateMockElasticsearchTermQuery(),
              congress: generateMockElasticsearchTermQuery(),
              payments: generateMockElasticsearchTermQuery(),
              DRG_diagnoses: generateMockElasticsearchTermQuery(),
              DRG_procedures: generateMockElasticsearchTermQuery(),
              prescriptions: generateMockElasticsearchTermQuery(),
              ccsr: generateMockElasticsearchTermQuery(),
              ccsr_px: generateMockElasticsearchTermQuery()
            };

            const mockParseTreeToQueries =
              mockParseTreeToElasticsearchQueries(pathSpecificQueries);

            parsedQueryTreeToElasticsearchQueriesService.parse.mockImplementation(
              mockParseTreeToQueries
            );

            const nameSearchBuilderFactory = createMockInstance(
              NameSearchBuilderFactory
            );
            const calculateMinimumNameVariations = createMockInstance(
              CalculateMinimumNameVariations
            );

            const keywordAutocompleteResponseAdapterService =
              createMockInstance(KeywordAutocompleteResponseAdapterService);

            const featureFlagsService = mockFeatureFlagsService();

            const aggregationContainerBuilderService = createMockInstance(
              AggregationContainerBuilderService
            );
            const ccsrIcdMappingRepository = createMockInstance(
              CcsrIcdMappingRepository
            );

            const ccsrPxMappingRepository = createMockInstance(
              CcsrPxMappingRepository
            );

            const indicationsTreeSearchService = createMockInstance(
              IndicationsTreeSearchService
            );

            const keywordAutocompleteResourceService =
              new KeywordAutocompleteResourceService(
                configService,
                elasticSearchService,
                queryParserService,
                queryUnderstandingServiceClient,
                userClient,
                keywordFilterClauseBuilderService,
                parsedQueryTreeToElasticsearchQueriesService,
                keywordAutocompleteResponseAdapterService,
                featureFlagsService,
                aggregationContainerBuilderService,
                nameSearchBuilderFactory,
                calculateMinimumNameVariations,
                ccsrIcdMappingRepository,
                ccsrPxMappingRepository,
                indicationsTreeSearchService
              );

            const projectId = faker.datatype.string();
            const projectFeatures = {
              engagementsV2: faker.datatype.boolean()
            };
            const userId = faker.datatype.string();
            const query = faker.datatype.string();
            const filterField = KeywordFilterAutocompleteFilterField.SPECIALTY;
            const filterValue = faker.random.word();

            const suppliedFilters = generateFilters();

            await keywordAutocompleteResourceService.keywordAutocomplete({
              projectId,
              projectFeatures,
              userId,
              query,
              filterField,
              filterValue,
              suppliedFilters
            });

            expect(elasticSearchService.query).toHaveBeenCalledWith({
              index: configService.elasticPeopleIndex,
              _source_includes: [],
              size: 0,
              query: {
                bool: {
                  filter: [
                    {
                      bool: {
                        should: expect.arrayContaining([
                          {
                            nested: {
                              path: "congress",
                              score_mode: "sum",
                              query: {
                                bool: {
                                  must: { match_all: {} },
                                  filter: [pathSpecificQueries.congress]
                                }
                              }
                            }
                          },
                          {
                            nested: {
                              path: "trials",
                              score_mode: "sum",
                              query: {
                                bool: {
                                  must: { match_all: {} },
                                  filter: [pathSpecificQueries.trials]
                                }
                              }
                            }
                          },
                          {
                            nested: {
                              path: "publications",
                              query: {
                                bool: {
                                  filter: [pathSpecificQueries.publications]
                                }
                              }
                            }
                          },
                          {
                            nested: {
                              path: "payments",
                              score_mode: "sum",
                              query: {
                                bool: {
                                  must: { match_all: {} },
                                  filter: [pathSpecificQueries.payments]
                                }
                              }
                            }
                          },
                          {
                            nested: {
                              path: "DRG_diagnoses",
                              query: {
                                bool: {
                                  filter: [pathSpecificQueries.DRG_diagnoses]
                                }
                              }
                            }
                          },
                          {
                            nested: {
                              path: "DRG_procedures",
                              query: {
                                bool: {
                                  filter: [pathSpecificQueries.DRG_procedures]
                                }
                              }
                            }
                          },
                          {
                            nested: {
                              path: "prescriptions",
                              query: {
                                bool: {
                                  filter: [pathSpecificQueries.prescriptions]
                                }
                              }
                            }
                          }
                        ]),
                        minimum_should_match: 1
                      }
                    }
                  ]
                }
              }
            } as SearchRequest);

            expect(
              parsedQueryTreeToElasticsearchQueriesService.parse
            ).toHaveBeenCalledWith(expectedAugmentedQuery, [
              "congress.keywords_eng",
              "congress.title_eng",
              "congress.organizer_eng.search",
              "congress.name_eng.search",
              "congress.role.search"
            ]);

            expect(
              parsedQueryTreeToElasticsearchQueriesService.parse
            ).toHaveBeenCalledWith(
              expectedAugmentedQuery,
              [
                "trials.officialTitle",
                "trials.briefTitle",
                "trials.conditions",
                "trials.interventions",
                "trials.keywords",
                "trials.summary"
              ],
              ENGLISH
            );

            expect(
              parsedQueryTreeToElasticsearchQueriesService.parse
            ).toHaveBeenCalledWith(
              expectedAugmentedQuery,
              publicationsQueryFields
            );

            expect(
              parsedQueryTreeToElasticsearchQueriesService.parse
            ).toHaveBeenCalledWith(expectedAugmentedQuery, [
              "payments.associatedDrugOrDevice",
              "payments.payerCompany"
            ]);

            expect(
              parsedQueryTreeToElasticsearchQueriesService.parse
            ).toHaveBeenCalledWith(
              expectedAugmentedQuery,
              ["DRG_procedures.codeAndDescription"],
              ENGLISH
            );

            expect(
              parsedQueryTreeToElasticsearchQueriesService.parse
            ).toHaveBeenCalledWith(
              expectedAugmentedQuery,
              ["DRG_diagnoses.codeAndDescription"],
              ENGLISH
            );

            expect(
              parsedQueryTreeToElasticsearchQueriesService.parse
            ).toHaveBeenCalledWith(expectedAugmentedQuery, [
              "prescriptions.generic_name.text"
            ]);
          });
        });

        describe("claims query-contexual filters", () => {
          it("should not contain asset should clauses when diagnosis filter is present", async () => {
            const configService = createMockInstance(ConfigService);
            configService.elasticPeopleIndex = faker.datatype.string();

            const queryParserService = createMockInstance(QueryParserService);

            const elasticSearchService =
              createMockInstance(ElasticSearchService);

            const queryUnderstandingServiceClient = createMockInstance(
              QueryUnderstandingServiceClient
            );
            const queryUnderstandingServiceResponse: QueryUnderstandingServiceResponse =
              new QueryUnderstandingServiceResponse();
            queryUnderstandingServiceClient.analyze.mockResolvedValue(
              queryUnderstandingServiceResponse
            );

            elasticSearchService.query.mockResolvedValue(
              generateMockElasticsearchResponse()
            );
            const userClient = createMockInstance(UserResourceClient);
            const keywordFilterClauseBuilderService = createMockInstance(
              KeywordFilterClauseBuilderService
            );
            keywordFilterClauseBuilderService.buildForAutocompleteRequest.mockResolvedValue(
              []
            );

            const parsedQueryTreeToElasticsearchQueriesService =
              new ParsedQueryTreeToElasticsearchQueriesService();

            const nameSearchBuilderFactory = createMockInstance(
              NameSearchBuilderFactory
            );
            const calculateMinimumNameVariations = createMockInstance(
              CalculateMinimumNameVariations
            );

            const keywordAutocompleteResponseAdapterService =
              createMockInstance(KeywordAutocompleteResponseAdapterService);

            const featureFlagsService = mockFeatureFlagsService();

            const aggregationContainerBuilderService = createMockInstance(
              AggregationContainerBuilderService
            );
            const ccsrIcdMappingRepository = createMockInstance(
              CcsrIcdMappingRepository
            );

            const ccsrPxMappingRepository = createMockInstance(
              CcsrPxMappingRepository
            );

            const indicationsTreeSearchService = createMockInstance(
              IndicationsTreeSearchService
            );

            const keywordAutocompleteResourceService =
              new KeywordAutocompleteResourceService(
                configService,
                elasticSearchService,
                queryParserService,
                queryUnderstandingServiceClient,
                userClient,
                keywordFilterClauseBuilderService,
                parsedQueryTreeToElasticsearchQueriesService,
                keywordAutocompleteResponseAdapterService,
                featureFlagsService,
                aggregationContainerBuilderService,
                nameSearchBuilderFactory,
                calculateMinimumNameVariations,
                ccsrIcdMappingRepository,
                ccsrPxMappingRepository,
                indicationsTreeSearchService
              );

            const projectId = faker.datatype.string();
            const projectFeatures = {
              engagementsV2: faker.datatype.boolean()
            };
            const userId = faker.datatype.string();
            const query = faker.datatype.string();
            queryUnderstandingServiceResponse.setAugmentedQuery(query);
            const filterField = KeywordFilterAutocompleteFilterField.SPECIALTY;
            const filterValue = faker.random.word();

            const suppliedFilters = generateFilters();
            suppliedFilters.claims.diagnosesICD.values.push(
              faker.datatype.string()
            );

            await keywordAutocompleteResourceService.keywordAutocomplete({
              projectId,
              projectFeatures,
              userId,
              query,
              filterField,
              filterValue,
              suppliedFilters
            });

            expect(elasticSearchService.query).toHaveBeenCalledWith({
              index: configService.elasticPeopleIndex,
              _source_includes: [],
              size: 0,
              query: {
                bool: {
                  filter: expect.not.arrayContaining([
                    {
                      bool: {
                        should: expect.anything(),
                        minimum_should_match: 1
                      }
                    }
                  ])
                }
              }
            });
          });
          it("should not contain asset should clauses when procedure filter is present", async () => {
            const configService = createMockInstance(ConfigService);
            configService.elasticPeopleIndex = faker.datatype.string();

            const queryParserService = createMockInstance(QueryParserService);

            const elasticSearchService =
              createMockInstance(ElasticSearchService);

            const queryUnderstandingServiceClient = createMockInstance(
              QueryUnderstandingServiceClient
            );
            const queryUnderstandingServiceResponse: QueryUnderstandingServiceResponse =
              new QueryUnderstandingServiceResponse();
            queryUnderstandingServiceClient.analyze.mockResolvedValue(
              queryUnderstandingServiceResponse
            );

            elasticSearchService.query.mockResolvedValue(
              generateMockElasticsearchResponse()
            );
            const userClient = createMockInstance(UserResourceClient);
            const keywordFilterClauseBuilderService = createMockInstance(
              KeywordFilterClauseBuilderService
            );
            keywordFilterClauseBuilderService.buildForAutocompleteRequest.mockResolvedValue(
              []
            );

            const parsedQueryTreeToElasticsearchQueriesService =
              new ParsedQueryTreeToElasticsearchQueriesService();

            const nameSearchBuilderFactory = createMockInstance(
              NameSearchBuilderFactory
            );
            const calculateMinimumNameVariations = createMockInstance(
              CalculateMinimumNameVariations
            );

            const keywordAutocompleteResponseAdapterService =
              createMockInstance(KeywordAutocompleteResponseAdapterService);

            const featureFlagsService = mockFeatureFlagsService();

            const aggregationContainerBuilderService = createMockInstance(
              AggregationContainerBuilderService
            );
            const ccsrIcdMappingRepository = createMockInstance(
              CcsrIcdMappingRepository
            );

            const ccsrPxMappingRepository = createMockInstance(
              CcsrPxMappingRepository
            );

            const indicationsTreeSearchService = createMockInstance(
              IndicationsTreeSearchService
            );

            const keywordAutocompleteResourceService =
              new KeywordAutocompleteResourceService(
                configService,
                elasticSearchService,
                queryParserService,
                queryUnderstandingServiceClient,
                userClient,
                keywordFilterClauseBuilderService,
                parsedQueryTreeToElasticsearchQueriesService,
                keywordAutocompleteResponseAdapterService,
                featureFlagsService,
                aggregationContainerBuilderService,
                nameSearchBuilderFactory,
                calculateMinimumNameVariations,
                ccsrIcdMappingRepository,
                ccsrPxMappingRepository,
                indicationsTreeSearchService
              );

            const projectId = faker.datatype.string();
            const projectFeatures = {
              engagementsV2: faker.datatype.boolean()
            };
            const userId = faker.datatype.string();
            const query = faker.datatype.string();
            queryUnderstandingServiceResponse.setAugmentedQuery(query);
            const filterField = KeywordFilterAutocompleteFilterField.SPECIALTY;
            const filterValue = faker.random.word();

            const suppliedFilters = generateFilters();
            suppliedFilters.claims.proceduresCPT.values.push(
              faker.datatype.string()
            );

            await keywordAutocompleteResourceService.keywordAutocomplete({
              projectId,
              projectFeatures,
              userId,
              query,
              filterField,
              filterValue,
              suppliedFilters
            });

            expect(elasticSearchService.query).toHaveBeenCalledWith({
              index: configService.elasticPeopleIndex,
              _source_includes: [],
              size: 0,
              query: {
                bool: {
                  filter: expect.not.arrayContaining([
                    {
                      bool: {
                        should: expect.anything(),
                        minimum_should_match: 1
                      }
                    }
                  ])
                }
              }
            });
          });

          it("should not contain asset should clauses when generic name filter is present", async () => {
            const configService = createMockInstance(ConfigService);
            configService.elasticPeopleIndex = faker.datatype.string();

            const queryParserService = createMockInstance(QueryParserService);

            const elasticSearchService =
              createMockInstance(ElasticSearchService);

            const queryUnderstandingServiceClient = createMockInstance(
              QueryUnderstandingServiceClient
            );
            const queryUnderstandingServiceResponse: QueryUnderstandingServiceResponse =
              new QueryUnderstandingServiceResponse();
            queryUnderstandingServiceClient.analyze.mockResolvedValue(
              queryUnderstandingServiceResponse
            );

            elasticSearchService.query.mockResolvedValue(
              generateMockElasticsearchResponse()
            );
            const userClient = createMockInstance(UserResourceClient);
            const keywordFilterClauseBuilderService = createMockInstance(
              KeywordFilterClauseBuilderService
            );
            keywordFilterClauseBuilderService.buildForAutocompleteRequest.mockResolvedValue(
              []
            );

            const parsedQueryTreeToElasticsearchQueriesService =
              new ParsedQueryTreeToElasticsearchQueriesService();

            const nameSearchBuilderFactory = createMockInstance(
              NameSearchBuilderFactory
            );
            const calculateMinimumNameVariations = createMockInstance(
              CalculateMinimumNameVariations
            );

            const keywordAutocompleteResponseAdapterService =
              createMockInstance(KeywordAutocompleteResponseAdapterService);

            const featureFlagsService = mockFeatureFlagsService();

            const aggregationContainerBuilderService = createMockInstance(
              AggregationContainerBuilderService
            );
            const ccsrIcdMappingRepository = createMockInstance(
              CcsrIcdMappingRepository
            );

            const ccsrPxMappingRepository = createMockInstance(
              CcsrPxMappingRepository
            );

            const indicationsTreeSearchService = createMockInstance(
              IndicationsTreeSearchService
            );

            const keywordAutocompleteResourceService =
              new KeywordAutocompleteResourceService(
                configService,
                elasticSearchService,
                queryParserService,
                queryUnderstandingServiceClient,
                userClient,
                keywordFilterClauseBuilderService,
                parsedQueryTreeToElasticsearchQueriesService,
                keywordAutocompleteResponseAdapterService,
                featureFlagsService,
                aggregationContainerBuilderService,
                nameSearchBuilderFactory,
                calculateMinimumNameVariations,
                ccsrIcdMappingRepository,
                ccsrPxMappingRepository,
                indicationsTreeSearchService
              );

            const projectId = faker.datatype.string();
            const projectFeatures = {
              engagementsV2: faker.datatype.boolean()
            };
            const userId = faker.datatype.string();
            const query = faker.datatype.string();
            queryUnderstandingServiceResponse.setAugmentedQuery(query);
            const filterField = KeywordFilterAutocompleteFilterField.SPECIALTY;
            const filterValue = faker.random.word();

            const suppliedFilters = generateFilters();
            suppliedFilters.claims.genericNames.values.push(
              faker.datatype.string()
            );

            await keywordAutocompleteResourceService.keywordAutocomplete({
              projectId,
              projectFeatures,
              userId,
              query,
              filterField,
              filterValue,
              suppliedFilters
            });

            expect(elasticSearchService.query).toHaveBeenCalledWith({
              index: configService.elasticPeopleIndex,
              _source_includes: [],
              size: 0,
              query: {
                bool: {
                  filter: expect.not.arrayContaining([
                    {
                      bool: {
                        should: expect.anything(),
                        minimum_should_match: 1
                      }
                    }
                  ])
                }
              }
            });
          });

          it("should contain diagnosis claims synonyms in should clause instead of augmented query", async () => {
            const configService = createMockInstance(ConfigService);
            configService.elasticPeopleIndex = faker.datatype.string();

            const queryParserService = createMockInstance(QueryParserService);

            const elasticSearchService =
              createMockInstance(ElasticSearchService);

            const queryUnderstandingServiceClient = createMockInstance(
              QueryUnderstandingServiceClient
            );
            const queryUnderstandingServiceResponse: QueryUnderstandingServiceResponse =
              new QueryUnderstandingServiceResponse();
            queryUnderstandingServiceClient.analyze.mockResolvedValue(
              queryUnderstandingServiceResponse
            );

            elasticSearchService.query.mockResolvedValue(
              generateMockElasticsearchResponse()
            );
            const userClient = createMockInstance(UserResourceClient);
            const keywordFilterClauseBuilderService = createMockInstance(
              KeywordFilterClauseBuilderService
            );
            keywordFilterClauseBuilderService.buildForAutocompleteRequest.mockResolvedValue(
              []
            );

            const parsedQueryTreeToElasticsearchQueriesService =
              new ParsedQueryTreeToElasticsearchQueriesService();

            const nameSearchBuilderFactory = createMockInstance(
              NameSearchBuilderFactory
            );
            const calculateMinimumNameVariations = createMockInstance(
              CalculateMinimumNameVariations
            );

            const keywordAutocompleteResponseAdapterService =
              createMockInstance(KeywordAutocompleteResponseAdapterService);

            const featureFlagsService = mockFeatureFlagsService();

            const aggregationContainerBuilderService = createMockInstance(
              AggregationContainerBuilderService
            );
            const ccsrIcdMappingRepository = createMockInstance(
              CcsrIcdMappingRepository
            );

            const ccsrPxMappingRepository = createMockInstance(
              CcsrPxMappingRepository
            );

            const indicationsTreeSearchService = createMockInstance(
              IndicationsTreeSearchService
            );

            const keywordAutocompleteResourceService =
              new KeywordAutocompleteResourceService(
                configService,
                elasticSearchService,
                queryParserService,
                queryUnderstandingServiceClient,
                userClient,
                keywordFilterClauseBuilderService,
                parsedQueryTreeToElasticsearchQueriesService,
                keywordAutocompleteResponseAdapterService,
                featureFlagsService,
                aggregationContainerBuilderService,
                nameSearchBuilderFactory,
                calculateMinimumNameVariations,
                ccsrIcdMappingRepository,
                ccsrPxMappingRepository,
                indicationsTreeSearchService
              );

            const projectId = faker.datatype.string();
            const projectFeatures = {
              engagementsV2: faker.datatype.boolean()
            };
            const userId = faker.datatype.string();
            const query = faker.datatype.string();
            queryUnderstandingServiceResponse.setAugmentedQuery(query);
            queryUnderstandingServiceResponse.addDiagnosisCodes(
              faker.datatype.string()
            );
            const filterField = KeywordFilterAutocompleteFilterField.SPECIALTY;
            const filterValue = faker.random.word();

            const suppliedFilters = generateFilters();
            await keywordAutocompleteResourceService.keywordAutocomplete({
              projectId,
              projectFeatures,
              userId,
              query,
              filterField,
              filterValue,
              suppliedFilters
            });

            expect(elasticSearchService.query).toHaveBeenCalledWith({
              index: configService.elasticPeopleIndex,
              _source_includes: [],
              size: 0,
              query: {
                bool: {
                  filter: expect.arrayContaining([
                    {
                      bool: {
                        should: expect.arrayContaining([
                          {
                            nested: {
                              path: "DRG_diagnoses",
                              query: {
                                bool: {
                                  filter: [
                                    {
                                      simple_query_string:
                                        expect.objectContaining({
                                          query:
                                            "(" +
                                            queryUnderstandingServiceResponse.getDiagnosisCodesList()[0] +
                                            "*)"
                                        })
                                    }
                                  ]
                                }
                              }
                            }
                          }
                        ]),
                        minimum_should_match: 1
                      }
                    }
                  ])
                }
              }
            });
          });
        });
      });
    });
  });

  describe("Name autocomplete", () => {
    describe("negative tests", () => {
      it("should rethrow an error thrown by elasticService.query", async () => {
        const configService = createMockInstance(ConfigService);
        configService.elasticPeopleIndex = faker.datatype.string();

        const queryParserService = createMockInstance(QueryParserService);

        const elasticSearchService = createMockInstance(ElasticSearchService);
        const err = new Error(
          "elasticSearchService.query threw an error for some reason"
        );
        elasticSearchService.query.mockRejectedValue(err);

        const queryUnderstandingService = createMockInstance(
          QueryUnderstandingServiceClient
        );

        const userClient = createMockInstance(UserResourceClient);
        const keywordFilterClauseBuilderService = createMockInstance(
          KeywordFilterClauseBuilderService
        );
        keywordFilterClauseBuilderService.buildForAutocompleteRequest.mockResolvedValue(
          []
        );

        const parsedQueryTreeToElasticsearchQueriesService = createMockInstance(
          ParsedQueryTreeToElasticsearchQueriesService
        );

        const nameSearchBuilderFactory = createMockInstance(
          NameSearchBuilderFactory
        );
        const calculateMinimumNameVariations = createMockInstance(
          CalculateMinimumNameVariations
        );

        const keywordAutocompleteResponseAdapterService = createMockInstance(
          KeywordAutocompleteResponseAdapterService
        );

        const featureFlagsService = mockFeatureFlagsService();

        const aggregationContainerBuilderService =
          new AggregationContainerBuilderService(
            parsedQueryTreeToElasticsearchQueriesService,
            keywordFilterClauseBuilderService
          );
        const ccsrIcdMappingRepository = createMockInstance(
          CcsrIcdMappingRepository
        );

        const ccsrPxMappingRepository = createMockInstance(
          CcsrPxMappingRepository
        );

        const indicationsTreeSearchService = createMockInstance(
          IndicationsTreeSearchService
        );

        const keywordAutocompleteResourceService =
          new KeywordAutocompleteResourceService(
            configService,
            elasticSearchService,
            queryParserService,
            queryUnderstandingService,
            userClient,
            keywordFilterClauseBuilderService,
            parsedQueryTreeToElasticsearchQueriesService,
            keywordAutocompleteResponseAdapterService,
            featureFlagsService,
            aggregationContainerBuilderService,
            nameSearchBuilderFactory,
            calculateMinimumNameVariations,
            ccsrIcdMappingRepository,
            ccsrPxMappingRepository,
            indicationsTreeSearchService
          );

        const projectId = faker.datatype.string();
        const projectFeatures = {
          engagementsV2: faker.datatype.boolean()
        };
        const userId = faker.datatype.string();
        const query = undefined;
        const filterField = KeywordFilterAutocompleteFilterField.DESIGNATION;
        const filterValue = faker.random.word();

        const specialties = [faker.datatype.string(), faker.datatype.string()];
        const suppliedFilters = generateFilters();
        suppliedFilters.specialty!.values = specialties;

        try {
          await keywordAutocompleteResourceService.nameAutocomplete({
            projectId,
            projectFeatures,
            userId,
            query,
            filterField,
            filterValue,
            suppliedFilters
          });
        } catch (thrownErr) {
          expect(thrownErr).toEqual(err);
        }
      });
    });

    describe("Query building", () => {
      describe("Multi lang Diabled", () => {
        describe("language detection", () => {
          it("should call getNameQueryShouldClauses with 'eng' when user preferred and detected lang is 'cmn' but multi lang is disabled", async () => {
            const configService = createMockInstance(ConfigService);
            configService.elasticPeopleIndex = faker.datatype.string();

            const queryParserService = createMockInstance(QueryParserService);

            const elasticSearchService =
              createMockInstance(ElasticSearchService);

            elasticSearchService.query.mockResolvedValue(
              generateMockElasticsearchResponse()
            );
            const queryUnderstandingService = createMockInstance(
              QueryUnderstandingServiceClient
            );
            const userClient = createMockInstance(UserResourceClient);
            userClient.getUsersPreferredLanguage.mockResolvedValue(
              generateMockLanguage("chinese")
            );
            const keywordFilterClauseBuilderService = createMockInstance(
              KeywordFilterClauseBuilderService
            );
            keywordFilterClauseBuilderService.buildForAutocompleteRequest.mockResolvedValue(
              []
            );

            const parsedQueryTreeToElasticsearchQueriesService =
              createMockInstance(ParsedQueryTreeToElasticsearchQueriesService);

            const nameSearchBuilder =
              ChineseJapaneseNameSearchBuilder.getInstance();

            const nameSearchBuilderFactoryMock = createMockInstance(
              NameSearchBuilderFactory
            );

            nameSearchBuilderFactoryMock.getNameSearchBuilder.mockReturnValue(
              nameSearchBuilder
            );

            const calculateMinimumNameVariations = createMockInstance(
              CalculateMinimumNameVariations
            );

            const keywordAutocompleteResponseAdapterService =
              createMockInstance(KeywordAutocompleteResponseAdapterService);

            const featureFlagsService = mockFeatureFlagsService();

            const aggregationContainerBuilderService = createMockInstance(
              AggregationContainerBuilderService
            );

            const ccsrIcdMappingRepository = createMockInstance(
              CcsrIcdMappingRepository
            );

            const ccsrPxMappingRepository = createMockInstance(
              CcsrPxMappingRepository
            );

            const indicationsTreeSearchService = createMockInstance(
              IndicationsTreeSearchService
            );

            const keywordAutocompleteResourceService =
              new KeywordAutocompleteResourceService(
                configService,
                elasticSearchService,
                queryParserService,
                queryUnderstandingService,
                userClient,
                keywordFilterClauseBuilderService,
                parsedQueryTreeToElasticsearchQueriesService,
                keywordAutocompleteResponseAdapterService,
                featureFlagsService,
                aggregationContainerBuilderService,
                nameSearchBuilderFactoryMock,
                calculateMinimumNameVariations,
                ccsrIcdMappingRepository,
                ccsrPxMappingRepository,
                indicationsTreeSearchService
              );

            const projectId = faker.datatype.string();
            const projectFeatures = {
              engagementsV2: faker.datatype.boolean(),
              searchMultiLanguage: false
            };
            const userId = faker.datatype.string();
            const query = SURGERY_CMN;
            const filterField = KeywordFilterAutocompleteFilterField.SPECIALTY;
            const filterValue = faker.random.word();

            const suppliedFilters = generateFilters();

            mockAugmentedQueryInQueryUnderstandingServiceResponse(
              queryUnderstandingService,
              query
            );

            await keywordAutocompleteResourceService.nameAutocomplete({
              projectId,
              projectFeatures,
              userId,
              query,
              filterField,
              filterValue,
              suppliedFilters
            });

            const lang: Language = ENGLISH as Language;

            expect(
              nameSearchBuilderFactoryMock.getNameSearchBuilder
            ).toHaveBeenCalledWith(lang);
            expect(
              calculateMinimumNameVariations.calculateMinimumFieldsToMatch
            ).toHaveBeenCalledWith(
              elasticSearchService,
              configService.elasticPeopleIndex,
              query
            );
          });

          it("should call getNameQueryShouldClauses with 'eng' when user preferred and detected lang is 'jpn' but multi lang is disabled", async () => {
            const configService = createMockInstance(ConfigService);
            configService.elasticPeopleIndex = faker.datatype.string();

            const queryParserService = createMockInstance(QueryParserService);

            const elasticSearchService =
              createMockInstance(ElasticSearchService);

            elasticSearchService.query.mockResolvedValue(
              generateMockElasticsearchResponse()
            );
            const queryUnderstandingService = createMockInstance(
              QueryUnderstandingServiceClient
            );
            const userClient = createMockInstance(UserResourceClient);
            userClient.getUsersPreferredLanguage.mockResolvedValue(
              generateMockLanguage("japanese")
            );
            const keywordFilterClauseBuilderService = createMockInstance(
              KeywordFilterClauseBuilderService
            );
            keywordFilterClauseBuilderService.buildForAutocompleteRequest.mockResolvedValue(
              []
            );

            const parsedQueryTreeToElasticsearchQueriesService =
              createMockInstance(ParsedQueryTreeToElasticsearchQueriesService);

            const nameSearchBuilder =
              ChineseJapaneseNameSearchBuilder.getInstance();

            const nameSearchBuilderFactoryMock = createMockInstance(
              NameSearchBuilderFactory
            );

            nameSearchBuilderFactoryMock.getNameSearchBuilder.mockReturnValue(
              nameSearchBuilder
            );

            const calculateMinimumNameVariations = createMockInstance(
              CalculateMinimumNameVariations
            );

            const keywordAutocompleteResponseAdapterService =
              createMockInstance(KeywordAutocompleteResponseAdapterService);

            const featureFlagsService = mockFeatureFlagsService();

            const aggregationContainerBuilderService = createMockInstance(
              AggregationContainerBuilderService
            );
            const ccsrIcdMappingRepository = createMockInstance(
              CcsrIcdMappingRepository
            );

            const ccsrPxMappingRepository = createMockInstance(
              CcsrPxMappingRepository
            );

            const indicationsTreeSearchService = createMockInstance(
              IndicationsTreeSearchService
            );

            const keywordAutocompleteResourceService =
              new KeywordAutocompleteResourceService(
                configService,
                elasticSearchService,
                queryParserService,
                queryUnderstandingService,
                userClient,
                keywordFilterClauseBuilderService,
                parsedQueryTreeToElasticsearchQueriesService,
                keywordAutocompleteResponseAdapterService,
                featureFlagsService,
                aggregationContainerBuilderService,
                nameSearchBuilderFactoryMock,
                calculateMinimumNameVariations,
                ccsrIcdMappingRepository,
                ccsrPxMappingRepository,
                indicationsTreeSearchService
              );

            const projectId = faker.datatype.string();
            const projectFeatures = {
              engagementsV2: faker.datatype.boolean(),
              searchMultiLanguage: false
            };
            const userId = faker.datatype.string();
            const query = SURGERY_JPN;
            const filterField = KeywordFilterAutocompleteFilterField.SPECIALTY;
            const filterValue = faker.random.word();

            const suppliedFilters = generateFilters();

            mockAugmentedQueryInQueryUnderstandingServiceResponse(
              queryUnderstandingService,
              query
            );

            await keywordAutocompleteResourceService.nameAutocomplete({
              projectId,
              projectFeatures,
              userId,
              query,
              filterField,
              filterValue,
              suppliedFilters,
              language: JAPANESE
            });

            const lang: Language = ENGLISH as Language;

            expect(
              nameSearchBuilderFactoryMock.getNameSearchBuilder
            ).toHaveBeenCalledWith(lang);
            expect(
              calculateMinimumNameVariations.calculateMinimumFieldsToMatch
            ).toHaveBeenCalledWith(
              elasticSearchService,
              configService.elasticPeopleIndex,
              query
            );
          });
        });
      });
      describe("Multi lang enabled", () => {
        describe("language detection", () => {
          it("should call getNameQueryShouldClauses with 'jpn' when detected language is 'cmn' but user preferred language is 'jpn'", async () => {
            const configService = createMockInstance(ConfigService);
            configService.elasticPeopleIndex = faker.datatype.string();

            const queryParserService = createMockInstance(QueryParserService);

            const elasticSearchService =
              createMockInstance(ElasticSearchService);

            elasticSearchService.query.mockResolvedValue(
              generateMockElasticsearchResponse()
            );
            const queryUnderstandingService = createMockInstance(
              QueryUnderstandingServiceClient
            );
            const userClient = createMockInstance(UserResourceClient);
            userClient.getUsersPreferredLanguage.mockResolvedValue(
              generateMockLanguage("japanese")
            );
            const keywordFilterClauseBuilderService = createMockInstance(
              KeywordFilterClauseBuilderService
            );
            keywordFilterClauseBuilderService.buildForAutocompleteRequest.mockResolvedValue(
              []
            );

            const parsedQueryTreeToElasticsearchQueriesService =
              createMockInstance(ParsedQueryTreeToElasticsearchQueriesService);

            const nameSearchBuilder =
              ChineseJapaneseNameSearchBuilder.getInstance();

            const nameSearchBuilderFactoryMock = createMockInstance(
              NameSearchBuilderFactory
            );

            nameSearchBuilderFactoryMock.getNameSearchBuilder.mockReturnValue(
              nameSearchBuilder
            );

            const calculateMinimumNameVariations = createMockInstance(
              CalculateMinimumNameVariations
            );

            const keywordAutocompleteResponseAdapterService =
              createMockInstance(KeywordAutocompleteResponseAdapterService);

            const featureFlagsService = mockFeatureFlagsService();

            const aggregationContainerBuilderService = createMockInstance(
              AggregationContainerBuilderService
            );
            const ccsrIcdMappingRepository = createMockInstance(
              CcsrIcdMappingRepository
            );

            const ccsrPxMappingRepository = createMockInstance(
              CcsrPxMappingRepository
            );

            const indicationsTreeSearchService = createMockInstance(
              IndicationsTreeSearchService
            );

            const keywordAutocompleteResourceService =
              new KeywordAutocompleteResourceService(
                configService,
                elasticSearchService,
                queryParserService,
                queryUnderstandingService,
                userClient,
                keywordFilterClauseBuilderService,
                parsedQueryTreeToElasticsearchQueriesService,
                keywordAutocompleteResponseAdapterService,
                featureFlagsService,
                aggregationContainerBuilderService,
                nameSearchBuilderFactoryMock,
                calculateMinimumNameVariations,
                ccsrIcdMappingRepository,
                ccsrPxMappingRepository,
                indicationsTreeSearchService
              );

            const projectId = faker.datatype.string();
            const projectFeatures = {
              engagementsV2: faker.datatype.boolean(),
              searchMultiLanguage: true
            };
            const userId = faker.datatype.string();
            const query = SURGERY_CMN;
            const filterField = KeywordFilterAutocompleteFilterField.SPECIALTY;
            const filterValue = faker.random.word();

            const suppliedFilters = generateFilters();

            mockAugmentedQueryInQueryUnderstandingServiceResponse(
              queryUnderstandingService,
              query
            );

            await keywordAutocompleteResourceService.nameAutocomplete({
              projectId,
              projectFeatures,
              userId,
              query,
              filterField,
              filterValue,
              suppliedFilters
            });

            const lang: Language = JAPANESE as Language;

            expect(
              nameSearchBuilderFactoryMock.getNameSearchBuilder
            ).toHaveBeenCalledWith(lang);
            expect(
              calculateMinimumNameVariations.calculateMinimumFieldsToMatch
            ).not.toHaveBeenCalled();
          });

          it("should call getNameQueryShouldClauses with 'cmn' when detected language is 'cmn'", async () => {
            const configService = createMockInstance(ConfigService);
            configService.elasticPeopleIndex = faker.datatype.string();

            const queryParserService = createMockInstance(QueryParserService);

            const elasticSearchService =
              createMockInstance(ElasticSearchService);

            elasticSearchService.query.mockResolvedValue(
              generateMockElasticsearchResponse()
            );
            const queryUnderstandingService = createMockInstance(
              QueryUnderstandingServiceClient
            );
            const userClient = createMockInstance(UserResourceClient);
            userClient.getUsersPreferredLanguage.mockResolvedValue(
              generateMockLanguage("chinese")
            );
            const keywordFilterClauseBuilderService = createMockInstance(
              KeywordFilterClauseBuilderService
            );
            keywordFilterClauseBuilderService.buildForAutocompleteRequest.mockResolvedValue(
              []
            );

            const parsedQueryTreeToElasticsearchQueriesService =
              createMockInstance(ParsedQueryTreeToElasticsearchQueriesService);

            const nameSearchBuilder =
              ChineseJapaneseNameSearchBuilder.getInstance();

            const nameSearchBuilderFactoryMock = createMockInstance(
              NameSearchBuilderFactory
            );

            nameSearchBuilderFactoryMock.getNameSearchBuilder.mockReturnValue(
              nameSearchBuilder
            );

            const calculateMinimumNameVariations = createMockInstance(
              CalculateMinimumNameVariations
            );

            const keywordAutocompleteResponseAdapterService =
              createMockInstance(KeywordAutocompleteResponseAdapterService);

            const featureFlagsService = mockFeatureFlagsService();

            const aggregationContainerBuilderService = createMockInstance(
              AggregationContainerBuilderService
            );
            const ccsrIcdMappingRepository = createMockInstance(
              CcsrIcdMappingRepository
            );

            const ccsrPxMappingRepository = createMockInstance(
              CcsrPxMappingRepository
            );

            const indicationsTreeSearchService = createMockInstance(
              IndicationsTreeSearchService
            );

            const keywordAutocompleteResourceService =
              new KeywordAutocompleteResourceService(
                configService,
                elasticSearchService,
                queryParserService,
                queryUnderstandingService,
                userClient,
                keywordFilterClauseBuilderService,
                parsedQueryTreeToElasticsearchQueriesService,
                keywordAutocompleteResponseAdapterService,
                featureFlagsService,
                aggregationContainerBuilderService,
                nameSearchBuilderFactoryMock,
                calculateMinimumNameVariations,
                ccsrIcdMappingRepository,
                ccsrPxMappingRepository,
                indicationsTreeSearchService
              );

            const projectId = faker.datatype.string();
            const projectFeatures = {
              engagementsV2: faker.datatype.boolean(),
              searchMultiLanguage: true
            };
            const userId = faker.datatype.string();
            const query = SURGERY_CMN;
            const filterField = KeywordFilterAutocompleteFilterField.SPECIALTY;
            const filterValue = faker.random.word();

            const suppliedFilters = generateFilters();

            mockAugmentedQueryInQueryUnderstandingServiceResponse(
              queryUnderstandingService,
              query
            );

            await keywordAutocompleteResourceService.nameAutocomplete({
              projectId,
              projectFeatures,
              userId,
              query,
              filterField,
              filterValue,
              suppliedFilters,
              language: CHINESE
            });

            const lang: Language = CHINESE as Language;

            expect(
              nameSearchBuilderFactoryMock.getNameSearchBuilder
            ).toHaveBeenCalledWith(lang);
            expect(
              calculateMinimumNameVariations.calculateMinimumFieldsToMatch
            ).not.toHaveBeenCalled();
          });

          it("should call getNameQueryShouldClauses with 'eng' when userClient.getUsersPreferredLanguage throws an error", async () => {
            const configService = createMockInstance(ConfigService);
            configService.elasticPeopleIndex = faker.datatype.string();

            const queryParserService = createMockInstance(QueryParserService);

            const elasticSearchService =
              createMockInstance(ElasticSearchService);

            elasticSearchService.query.mockResolvedValue(
              generateMockElasticsearchResponse()
            );
            const queryUnderstandingService = createMockInstance(
              QueryUnderstandingServiceClient
            );
            const userClient = createMockInstance(UserResourceClient);
            userClient.getUsersPreferredLanguage.mockRejectedValue(
              new Error(
                "userClient.getUsersPreferredLanguage threw an error for some reason"
              )
            );
            const keywordFilterClauseBuilderService = createMockInstance(
              KeywordFilterClauseBuilderService
            );
            keywordFilterClauseBuilderService.buildForAutocompleteRequest.mockResolvedValue(
              []
            );

            const parsedQueryTreeToElasticsearchQueriesService =
              createMockInstance(ParsedQueryTreeToElasticsearchQueriesService);

            const nameSearchBuilder = DefaultNameSearchBuilder.getInstance();

            const nameSearchBuilderFactoryMock = createMockInstance(
              NameSearchBuilderFactory
            );

            nameSearchBuilderFactoryMock.getNameSearchBuilder.mockReturnValue(
              nameSearchBuilder
            );

            const calculateMinimumNameVariations = createMockInstance(
              CalculateMinimumNameVariations
            );

            const keywordAutocompleteResponseAdapterService =
              createMockInstance(KeywordAutocompleteResponseAdapterService);

            const featureFlagsService = mockFeatureFlagsService();

            const aggregationContainerBuilderService = createMockInstance(
              AggregationContainerBuilderService
            );
            const ccsrIcdMappingRepository = createMockInstance(
              CcsrIcdMappingRepository
            );

            const ccsrPxMappingRepository = createMockInstance(
              CcsrPxMappingRepository
            );

            const indicationsTreeSearchService = createMockInstance(
              IndicationsTreeSearchService
            );

            const keywordAutocompleteResourceService =
              new KeywordAutocompleteResourceService(
                configService,
                elasticSearchService,
                queryParserService,
                queryUnderstandingService,
                userClient,
                keywordFilterClauseBuilderService,
                parsedQueryTreeToElasticsearchQueriesService,
                keywordAutocompleteResponseAdapterService,
                featureFlagsService,
                aggregationContainerBuilderService,
                nameSearchBuilderFactoryMock,
                calculateMinimumNameVariations,
                ccsrIcdMappingRepository,
                ccsrPxMappingRepository,
                indicationsTreeSearchService
              );

            const projectId = faker.datatype.string();
            const projectFeatures = {
              engagementsV2: faker.datatype.boolean(),
              searchMultiLanguage: true
            };
            const userId = faker.datatype.string();
            const query = faker.datatype.string();
            const filterField = KeywordFilterAutocompleteFilterField.SPECIALTY;
            const filterValue = faker.random.word();

            const suppliedFilters = generateFilters();

            mockAugmentedQueryInQueryUnderstandingServiceResponse(
              queryUnderstandingService,
              query
            );

            await keywordAutocompleteResourceService.nameAutocomplete({
              projectId,
              projectFeatures,
              userId,
              query,
              filterField,
              filterValue,
              suppliedFilters
            });

            const lang: Language = ENGLISH as Language;

            expect(
              nameSearchBuilderFactoryMock.getNameSearchBuilder
            ).toHaveBeenCalledWith(lang);
            expect(
              calculateMinimumNameVariations.calculateMinimumFieldsToMatch
            ).toHaveBeenCalledWith(
              elasticSearchService,
              configService.elasticPeopleIndex,
              query
            );
          });
        });
      });
    });
  });
});
