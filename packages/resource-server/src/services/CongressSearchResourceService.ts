/* eslint-disable @typescript-eslint/ban-ts-comment */
/* eslint-disable @typescript-eslint/no-non-null-assertion */
import { RpcMethod, RpcResourceService, RpcService } from "@h1nyc/systems-rpc";
import { createLogger } from "../lib/Logger";
import { Trace } from "../Tracer";
import { ConfigService } from "./ConfigService";
import _, { Dictionary } from "lodash";
import { estypes } from "@elastic/elasticsearch";
import { QueryUnderstandingServiceClient } from "./QueryUnderstandingServiceClient";

import {
  IndicationType,
  IndicationSource,
  IndicationSortBy,
  SearchIndicationsByQueryInput,
  RPC_NAMESPACE_CONGRESS_SEARCH,
  CongressSearchInput,
  CongressSearchResponse,
  CongressSearchResource,
  CongressSearchFilterAutocompleteInput,
  CongressSearchFilterAggregation,
  CongressSearchLocationFilterAggregation,
  CongressSearchFilters,
  CongressSearchSortOrder
} from "@h1nyc/search-sdk";
import {
  MsearchMultisearchBody,
  MsearchMultisearchHeader,
  MsearchRequest,
  QueryDslQueryContainer,
  SearchRequest,
  SearchResponse,
  SearchResponseBody,
  SearchTotalHits,
  Sort,
  SortCombinations
} from "@elastic/elasticsearch/lib/api/types";
import { QueryUnderstandingServiceResponse } from "../proto/query_understanding_service_pb";
import { Service } from "typedi";
import {
  ALL_ORs,
  ALL_ANDs,
  ALL_UNICODE_DOUBLE_QUOTES,
  ASCII_DOUBLE_QUOTES,
  MAXIMUM_QUERY_TOKENS,
  HCPDocument
} from "./KeywordSearchResourceServiceRewrite";
import {
  buildBoolShouldQuery,
  buildGreaterThanRangeQuery,
  buildMatchPhraseQuery,
  buildTermQuery,
  buildTermsQuery
} from "../util/QueryBuildingUtils";
import { IndicationsTreeSearchService } from "./IndicationsTreeSearchService";
import { ElasticSearchCongressService } from "./ElasticsearchCongressService";
import { CongressSearchResponseAdapterService } from "./CongressSearchResponseAdapterService";
import {
  featureFlagDefaults,
  NameSearchFeatureFlags,
  nameSearchFeatureFlagTypes
} from "./NameSearchResourceServiceRewrite";
import { ElasticSearchService } from "./ElasticSearchService";
import NameSearchBuilderFactory from "./queryBuilders/NameSearchBuilderFactory";
import {
  CHINESE,
  ENGLISH,
  JAPANESE,
  Language,
  LanguageDetectService
} from "./LanguageDetectService";
import { AppName, UserResourceClient } from "@h1nyc/account-sdk";
import { FeatureFlagsService } from "@h1nyc/systems-feature-flags";
import { EntityType } from "@h1nyc/account-user-entities";
import { UserOnboardingDataService } from "./UserOnboardingDataService";
import { DEFAULT_NAME_VARIATION_MATCH_COUNT } from "./queryBuilders/DefaultNameSearchBuilder";
import CalculateMinimumNameVariations from "./queryBuilders/CalculateMinimumNameVariations";
import { generateRegularExpressionForAutocompleteMatching } from "./InstitutionsResourceService";
import { REGION_CODES_BY_COUNTRY_CODE_AND_REGION_NAME } from "../lib/data/regions";
import { COUNTRY_NAME_TO_CODE } from "../lib/data/countries";
import {
  FollowRecordResourceClient,
  FollowType
} from "@h1nyc/notifications-sdk";
import Redis from "ioredis";
import { sha1 } from "object-hash";
import { TagsHelperService } from "./TagsHelperService";

type CongressSearchContext = "autocomplete" | "search";

export type ElasticsearchCongressDoc = {
  h1_conference_id: string;
  h1_series_id: string;
  name: string;
  series_name: string;
  society: string;
  addresses: {
    street1?: string;
    street2?: string;
    street3?: string;
    region?: string;
    region_code?: string;
    country?: string;
    country_code?: string;
    county?: string;
    district?: string;
    city?: string;
    postal_code?: string;
    language_code?: string;
  }[];
  speakers?: {
    h1_person_id?: string;
    name: string;
    role: string;
  }[];
  translations: {
    name: string;
    description: string;
    society: string;
    language_code: string;
  }[];
  "filters.congress_type": string;
  "filters.start_date": number;
  "filters.end_date": number;
};

const OR = " | ";

export const SOURCE_INCLUDES: string[] = [
  "h1_conference_id",
  "h1_series_id",
  "name",
  "series_name",
  "description",
  "society",
  "indication",
  "url",
  "translations",
  "addresses",
  "h1_person_ids",
  "speakers",
  "filters.congress_type",
  "filters.start_date",
  "filters.end_date"
];

const HAS_ADVANCED_OPERATORS = /(\sAND\s|\sOR\s|NOT\s)/;
const ALL_ASSETS_OPTIONAL = 0;
const LOCATIONS_IN_REGION_AGGREGATION_SIZE = 200;
const SECONDS = "ex";
const EXPIRATION_PERIOD = 600;
const INDICATIONS_CACHE_EXPIRATION_PERIOD = 3600;
const USER_INDICATIONS_BOOST = 2;
const EMPTY_STRING = "";
const NAME_SEARCH_OFFSET = 0;
const NAME_SEARCH_SIZE = 15;
const SEARCH_TYPE = "dfs_query_then_fetch";
const IGNORE_HITS = 0;
const SHARD_SIZE = 100;

function getHitsTotal(total: number | SearchTotalHits | undefined) {
  if (total === undefined) {
    return 0;
  }

  if (typeof total === "number") {
    return total;
  }

  return total.value;
}

export const queryableAddressFields = {
  country: ["addresses.country", "addresses.country_code"],
  region: ["addresses.region", "addresses.region_code"],
  city: ["addresses.city"],
  postal_code: ["addresses.postal_code"]
};

export const queryableRegionFields = {
  country_level_regions: "addresses.country_level_regions",
  state_level_regions: "addresses.state_level_regions",
  city_level_regions: "addresses.city_level_regions"
};

export enum CongressSearchFilterAutocompleteTypes {
  LOCATION = "location",
  INDICATION = "indication",
  SERIES = "series",
  SOCIETY = "society",
  TYPE = "type"
}

const fieldToAggregateForFilterAutocompleteType = {
  [CongressSearchFilterAutocompleteTypes.SERIES]: "filters.series",
  [CongressSearchFilterAutocompleteTypes.SOCIETY]: "filters.society",
  [CongressSearchFilterAutocompleteTypes.TYPE]: "filters.congress_type"
};

const fieldToAggregateForLocationFilterAutocompleteType = {
  country: "filters.country",
  region: "filters.region",
  city: "filters.city",
  postal_code: "filters.postal_code"
};

const regionFieldToAggregateForLocationFilterAutocompleteType = {
  country_level_regions: "filters.country_level_regions",
  state_level_regions: "filters.state_level_regions",
  city_level_regions: "filters.city_level_regions"
};

const fieldToMatchForFilterAutocompleteType = {
  [CongressSearchFilterAutocompleteTypes.SERIES]:
    "series_name.autocomplete_search",
  [CongressSearchFilterAutocompleteTypes.SOCIETY]:
    "society.autocomplete_search",
  [CongressSearchFilterAutocompleteTypes.TYPE]:
    "congress_type.autocomplete_search"
};

@Service()
@RpcService()
export class CongressSearchResourceService
  extends RpcResourceService
  implements CongressSearchResource
{
  private readonly logger = createLogger(this);
  private congressIndexName: string;
  private peopleIndexName: string;
  private redisClient;

  constructor(
    config: ConfigService,
    private congressSearchResponseAdapterService: CongressSearchResponseAdapterService,
    private queryUnderstandingServiceClient: QueryUnderstandingServiceClient,
    private calculateMinimumNameVariations: CalculateMinimumNameVariations,
    private indicationsTreeSearchService: IndicationsTreeSearchService,
    private elasticSearchCongressService: ElasticSearchCongressService,
    private userOnboardingDataService: UserOnboardingDataService,
    private nameSearchBuilderFactory: NameSearchBuilderFactory,
    private elasticSearchPeopleService: ElasticSearchService,
    private followRecordClient: FollowRecordResourceClient,
    private languageDetectService: LanguageDetectService,
    private featureFlagsService: FeatureFlagsService,
    private tagsHelperService: TagsHelperService,
    private userClient: UserResourceClient
  ) {
    super(
      config.searchRpcSigningKey,
      RPC_NAMESPACE_CONGRESS_SEARCH,
      config.searchRedisOptions
    );

    this.congressIndexName = config.elasticCongressIndex;
    this.peopleIndexName = config.elasticPeopleIndex;
    this.redisClient = new Redis(config.searchCacheRedisOptions);
  }

  @RpcMethod()
  async isReady(): Promise<boolean> {
    return true;
  }

  @RpcMethod()
  @Trace("h1-search.congress.autocompleteCountries")
  async autocompleteCountries(
    input: CongressSearchFilterAutocompleteInput
  ): Promise<CongressSearchLocationFilterAggregation[]> {
    return this.executeAutocompleteQuery(
      CongressSearchFilterAutocompleteTypes.LOCATION,
      _.omit(input, "filters.location.countries"),
      "country",
      "country_level_regions"
    );
  }

  @RpcMethod()
  @Trace("h1-search.congress.autocompleteRegions")
  async autocompleteRegions(
    input: CongressSearchFilterAutocompleteInput
  ): Promise<CongressSearchLocationFilterAggregation[]> {
    return this.executeAutocompleteQuery(
      CongressSearchFilterAutocompleteTypes.LOCATION,
      _.omit(input, "filters.location.regions"),
      "region",
      "state_level_regions"
    );
  }

  @RpcMethod()
  @Trace("h1-search.congress.autocompleteCities")
  async autocompleteCities(
    input: CongressSearchFilterAutocompleteInput
  ): Promise<CongressSearchLocationFilterAggregation[]> {
    return this.executeAutocompleteQuery(
      CongressSearchFilterAutocompleteTypes.LOCATION,
      _.omit(input, "filters.location.cities"),
      "city",
      "city_level_regions"
    );
  }

  @RpcMethod()
  @Trace("h1-search.congress.autocompletePostalCodes")
  async autocompletePostalCodes(
    input: CongressSearchFilterAutocompleteInput
  ): Promise<CongressSearchLocationFilterAggregation[]> {
    return this.executeAutocompleteQuery(
      CongressSearchFilterAutocompleteTypes.LOCATION,
      _.omit(input, "filters.location.postalCodes"),
      "postal_code",
      "city_level_regions"
    );
  }

  @RpcMethod()
  @Trace("h1-search.congress.autocompleteSeriesNames")
  async autocompleteSeriesNames(
    input: CongressSearchFilterAutocompleteInput
  ): Promise<CongressSearchFilterAggregation[]> {
    return this.executeAutocompleteQuery(
      CongressSearchFilterAutocompleteTypes.SERIES,
      _.omit(input, "filters.series.seriesNames")
    );
  }

  @RpcMethod()
  @Trace("h1-search.congress.autocompleteSocietyNames")
  async autocompleteSocietyNames(
    input: CongressSearchFilterAutocompleteInput
  ): Promise<CongressSearchFilterAggregation[]> {
    return this.executeAutocompleteQuery(
      CongressSearchFilterAutocompleteTypes.SOCIETY,
      _.omit(input, "filters.society.societyNames")
    );
  }

  @RpcMethod()
  @Trace("h1-search.congress.autocompleteCongressTypes")
  async autocompleteCongressTypes(
    input: CongressSearchFilterAutocompleteInput
  ): Promise<CongressSearchFilterAggregation[]> {
    return this.executeAutocompleteQuery(
      CongressSearchFilterAutocompleteTypes.TYPE,
      _.omit(input, "filters.type.congressTypes")
    );
  }

  @RpcMethod()
  @Trace("h1-search.congress.congressCountForIndications")
  async congressCountForIndications(
    input: CongressSearchInput,
    indications: string[]
  ): Promise<Dictionary<number>> {
    const queryUnderstandingServiceResponse =
      await this.retrieveQueryUnderstandingServiceResponse(input);
    const idsOfMatchingPeople = await this.findPeopleMatchingQuery(input);
    const followedCongressSeriesIds =
      await this.getAllFollowedCongressSeriesIdsForUserFromCacheOrfollowRecordClient(
        input.userId,
        input.projectId
      );

    const finalCountsForIndication: Dictionary<number> = {};
    const searchRequests = [];
    for (const indication of indications) {
      finalCountsForIndication[indication] = 0;

      const modifiedInputAndIndicationsParsedQuery =
        await this.addIndicationsToInput(input);

      let modifiedInput = modifiedInputAndIndicationsParsedQuery.modifiedInput;
      if (
        !modifiedInput.filters?.indication?.indications?.includes(indication)
      ) {
        modifiedInput = {
          ...input,
          filters: {
            ...input.filters,
            indication: {
              indications: [
                ...(input.filters?.indication?.indications ?? []),
                indication
              ]
            }
          }
        };
      }

      searchRequests.push(
        this.buildSearchRequest(
          modifiedInput,
          "autocomplete",
          idsOfMatchingPeople,
          followedCongressSeriesIds,
          [],
          queryUnderstandingServiceResponse,
          modifiedInputAndIndicationsParsedQuery.indicationsParsedQuery
        )
      );
    }
    const requests = await Promise.all(searchRequests);
    const msearches = [];
    const HEADER: Readonly<MsearchMultisearchHeader> = {};
    for (const request of requests) {
      if (!request) {
        return finalCountsForIndication;
      }

      msearches.push(HEADER);
      msearches.push(this.toMultiSearchBodyToFetchCount(request));
    }

    const multiSearchRequest: MsearchRequest = {
      index: this.congressIndexName,
      searches: msearches
    };
    const msearchResult = await this.elasticSearchCongressService.msearch(
      multiSearchRequest
    );
    msearchResult.responses.forEach((response, index) => {
      finalCountsForIndication[indications[index]] =
        ((response as SearchResponseBody).hits.total as SearchTotalHits)
          .value ?? 0;
    });

    return finalCountsForIndication;
  }

  @RpcMethod()
  @Trace("h1-search.congress.searchCongress")
  async search(
    input: Readonly<CongressSearchInput>
  ): Promise<CongressSearchResponse> {
    const isFollowingFilterPassedIn = input.filters?.following?.isFollowing;
    if (isFollowingFilterPassedIn && input.filters?.following?.isNotFollowing) {
      throw new Error("Cannot specify both isFollowing and isNotFollowing");
    }

    const queryUnderstandingServiceResponse =
      await this.retrieveQueryUnderstandingServiceResponse(input);

    const followedCongressSeriesIds =
      await this.getAllFollowedCongressSeriesIdsForUser(
        input.userId,
        input.projectId
      );

    if (isFollowingFilterPassedIn && !followedCongressSeriesIds.length) {
      return {
        total: 0,
        congresses: []
      };
    }

    const indicationsUserIsInterestedIn =
      await this.getIndicationsUserIsInterestedIn(
        input,
        followedCongressSeriesIds
      );

    this.logger.info({ input }, "congress search");

    const idsOfMatchingPeople = await this.findPeopleMatchingQuery(input);

    const { modifiedInput, indicationsParsedQuery } =
      await this.addIndicationsToInput(input);

    const request = await this.buildSearchRequest(
      modifiedInput,
      "search",
      idsOfMatchingPeople,
      followedCongressSeriesIds,
      indicationsUserIsInterestedIn,
      queryUnderstandingServiceResponse,
      indicationsParsedQuery
    );

    if (!request) {
      return {
        total: 0,
        congresses: []
      };
    }

    this.logger.info({ request }, "elastic search request");

    const results =
      await this.elasticSearchCongressService.query<ElasticsearchCongressDoc>(
        request
      );

    this.logger.info(
      { took: results.took, total: getHitsTotal(results.hits.total) },
      "execution time"
    );

    const setOfFollowedCongressSeriesIds = new Set(followedCongressSeriesIds);
    return this.congressSearchResponseAdapterService.adaptToCongressSearchResponse(
      input,
      results,
      setOfFollowedCongressSeriesIds
    );
  }

  private async addIndicationsToInput(input: Readonly<CongressSearchInput>) {
    const modifiedInput: CongressSearchInput = _.cloneDeep(input);

    if (_.isEmpty(modifiedInput.filters?.indication?.indications)) {
      const indications = await this.resolveInputQueryToIndications(input);

      if (
        indications?.length &&
        modifiedInput.query &&
        indications.includes(modifiedInput.query.trim().toLowerCase())
      ) {
        modifiedInput.filters = {
          ...modifiedInput.filters,
          indication: {
            ...modifiedInput.filters?.indication,
            indications
          }
        };
      }
    }

    const indicationsValues = modifiedInput.filters?.indication?.indications;
    let indicationsParsedQuery;

    if (indicationsValues?.length) {
      [indicationsParsedQuery] =
        await this.getIndicationSynonymsFromQueryUnderstandingService(
          indicationsValues,
          true
        );
    }

    return {
      modifiedInput,
      indicationsParsedQuery
    };
  }

  private async executeAutocompleteQuery(
    filterType: Exclude<
      CongressSearchFilterAutocompleteTypes,
      CongressSearchFilterAutocompleteTypes.INDICATION
    >,
    input: CongressSearchFilterAutocompleteInput,
    addressField?: keyof typeof queryableAddressFields,
    regionField?: keyof typeof queryableRegionFields
  ): Promise<CongressSearchFilterAggregation[]> {
    const queryUnderstandingServiceResponse =
      await this.retrieveQueryUnderstandingServiceResponse(input);

    const idsOfMatchingPeople: string[] = [];
    if (input.globalQuery) {
      idsOfMatchingPeople.push(...(await this.findPeopleMatchingQuery(input)));
    }

    return this.autocomplete(
      filterType,
      input,
      idsOfMatchingPeople,
      queryUnderstandingServiceResponse,
      addressField,
      regionField
    );
  }

  private async autocomplete(
    filterType: Exclude<
      CongressSearchFilterAutocompleteTypes,
      CongressSearchFilterAutocompleteTypes.INDICATION
    >,
    input: Readonly<CongressSearchFilterAutocompleteInput>,
    idsOfMatchingPeople: string[],
    queryUnderstandingServiceResponse?: Readonly<QueryUnderstandingServiceResponse>,
    addressField?: keyof typeof queryableAddressFields,
    regionField?: keyof typeof queryableRegionFields
  ) {
    this.logger.info(
      { filterType, input },
      "congress search filter autocomplete"
    );

    const followedCongressSeriesIds =
      await this.getAllFollowedCongressSeriesIdsForUserFromCacheOrfollowRecordClient(
        input.userId,
        input.projectId
      );

    const phraseQuery = this.buildMatchPhraseForAutocomplete(
      filterType,
      input,
      addressField,
      regionField
    );

    const { modifiedInput, indicationsParsedQuery } =
      await this.addIndicationsToInput(input);

    const request = await this.buildSearchRequest(
      modifiedInput,
      "autocomplete",
      idsOfMatchingPeople,
      followedCongressSeriesIds,
      [],
      queryUnderstandingServiceResponse,
      indicationsParsedQuery
    );

    if (!request) {
      return [];
    }

    if (input.filterQuery && phraseQuery) {
      if (input.globalQuery) {
        (request.query!.bool!.must as QueryDslQueryContainer).bool!.must = [
          phraseQuery
        ];
      } else {
        request.query!.bool!.must = [phraseQuery];
      }
    }

    request.size = IGNORE_HITS;
    request._source = false;
    request.track_total_hits = false;
    request.aggs = this.buildAggregationForFilterAutocomplete(
      filterType,
      input,
      addressField,
      regionField
    );
    request.sort = undefined;

    this.logger.info({ query: request }, "elasticsearch request");

    const data = await this.elasticSearchCongressService.query<never>(request);

    this.logger.info(
      { took: data.took, total: data.hits.total },
      "execution time"
    );

    return this.congressSearchResponseAdapterService.adaptToCongressSearchFilterAggregations(
      data,
      filterType,
      addressField,
      regionField
    );
  }

  private buildMatchPhraseForAutocomplete(
    filterType: Exclude<
      CongressSearchFilterAutocompleteTypes,
      CongressSearchFilterAutocompleteTypes.INDICATION
    >,
    input: CongressSearchFilterAutocompleteInput,
    addressField?: keyof typeof queryableAddressFields,
    regionField?: keyof typeof queryableRegionFields
  ) {
    if (filterType === CongressSearchFilterAutocompleteTypes.LOCATION) {
      return this.buildNestedMultiMatchPhrasePrefixForLocationFilterAutocomplete(
        input,
        addressField,
        regionField
      );
    } else {
      return this.buildMatchPhraseForNonLocationFilterAutocomplete(
        filterType,
        input
      );
    }
  }

  private buildNestedMultiMatchPhrasePrefixForLocationFilterAutocomplete(
    input: CongressSearchFilterAutocompleteInput,
    addressField?: keyof typeof queryableAddressFields,
    regionField?: keyof typeof queryableRegionFields
  ): QueryDslQueryContainer | null {
    if (addressField) {
      const fields = _.flatten([queryableAddressFields[addressField]]);

      if (regionField) {
        fields.push(queryableRegionFields[regionField]);
      }

      return {
        nested: {
          path: "addresses",
          query: {
            multi_match: {
              type: "phrase_prefix",
              query: input.filterQuery ?? "",
              fields
            }
          }
        }
      };
    }

    return null;
  }

  private buildMatchPhraseForNonLocationFilterAutocomplete(
    filterType: Exclude<
      CongressSearchFilterAutocompleteTypes,
      | CongressSearchFilterAutocompleteTypes.LOCATION
      | CongressSearchFilterAutocompleteTypes.INDICATION
    >,
    input: CongressSearchFilterAutocompleteInput
  ): QueryDslQueryContainer {
    return buildMatchPhraseQuery(
      fieldToMatchForFilterAutocompleteType[filterType],
      input.filterQuery ?? ""
    );
  }

  private buildAggregationForFilterAutocomplete(
    filterType: Exclude<
      CongressSearchFilterAutocompleteTypes,
      CongressSearchFilterAutocompleteTypes.INDICATION
    >,
    input: CongressSearchFilterAutocompleteInput,
    addressField?: keyof typeof queryableAddressFields,
    regionField?: keyof typeof queryableRegionFields
  ) {
    if (filterType === CongressSearchFilterAutocompleteTypes.LOCATION) {
      return this.buildAggregationForLocationFilterAutocomplete(
        input,
        addressField,
        regionField
      );
    } else {
      return this.buildAggregationForNonLocationFilterAutocomplete(
        filterType,
        input
      );
    }
  }

  private buildAggregationForLocationFilterAutocomplete(
    input: CongressSearchFilterAutocompleteInput,
    addressField?: keyof typeof queryableAddressFields,
    regionField?: keyof typeof queryableRegionFields
  ) {
    if (!addressField || !regionField) {
      return undefined;
    }

    const addressFieldToAggregate =
      fieldToAggregateForLocationFilterAutocompleteType[addressField];
    const regionFieldToAggregate =
      regionFieldToAggregateForLocationFilterAutocompleteType[regionField];

    let regionsAggregation;
    if (regionField) {
      regionsAggregation = {
        regions_in: {
          terms: {
            field: regionFieldToAggregate,
            exclude: EMPTY_STRING,
            shard_size: SHARD_SIZE
          }
        }
      };
    }

    const locationAggregation = {
      [addressField]: {
        filter: {
          bool: {
            filter: this.buildFilterForLocationAggregation(
              input.filterQuery,
              addressField
            )
          }
        },
        aggs: {
          filtered_matching: {
            terms: {
              field: addressFieldToAggregate,
              size: input.count,
              shard_size: SHARD_SIZE
            },
            aggs: regionsAggregation
          }
        }
      }
    };

    if (addressField !== "postal_code") {
      return {
        ...locationAggregation,
        [regionField]: {
          terms: {
            field: regionFieldToAggregate,
            exclude: EMPTY_STRING,
            include: input.filterQuery
              ? generateRegularExpressionForAutocompleteMatching(
                  input.filterQuery
                )
              : undefined,
            shard_size: SHARD_SIZE
          },
          aggs: {
            locations_in_region: {
              terms: {
                field: addressFieldToAggregate,
                size: LOCATIONS_IN_REGION_AGGREGATION_SIZE
              }
            }
          }
        }
      };
    }

    return locationAggregation;
  }

  private buildAggregationForNonLocationFilterAutocomplete(
    filterType: Exclude<
      CongressSearchFilterAutocompleteTypes,
      | CongressSearchFilterAutocompleteTypes.LOCATION
      | CongressSearchFilterAutocompleteTypes.INDICATION
    >,
    input: CongressSearchFilterAutocompleteInput
  ) {
    return {
      [filterType]: {
        filter: {
          bool: {
            filter: this.buildFilterForNonLocationAggregation(filterType, input)
          }
        },
        aggs: {
          filtered_matching: {
            terms: {
              field: fieldToAggregateForFilterAutocompleteType[filterType],
              size: input.count,
              shard_size: SHARD_SIZE
            }
          }
        }
      }
    };
  }

  private buildFilterForNonLocationAggregation(
    filterType: Exclude<
      CongressSearchFilterAutocompleteTypes,
      | CongressSearchFilterAutocompleteTypes.LOCATION
      | CongressSearchFilterAutocompleteTypes.INDICATION
    >,
    input: CongressSearchFilterAutocompleteInput
  ): estypes.QueryDslQueryContainer[] {
    return input.filterQuery
      ? [
          this.buildMatchPhraseForNonLocationFilterAutocomplete(
            filterType,
            input
          )
        ]
      : [];
  }

  private buildFilterForLocationAggregation(
    prefix: string | undefined,
    field: keyof typeof queryableAddressFields
  ): estypes.QueryDslQueryContainer[] {
    return prefix
      ? [
          {
            nested: {
              path: "addresses",
              query: {
                multi_match: {
                  type: "phrase_prefix",
                  query: prefix,
                  fields: queryableAddressFields[field]
                }
              }
            }
          }
        ]
      : [];
  }

  private async retrieveQueryUnderstandingServiceResponse(
    input: Readonly<CongressSearchInput | CongressSearchFilterAutocompleteInput>
  ): Promise<QueryUnderstandingServiceResponse | undefined> {
    let queryUnderstandingServiceResponse;
    let queryToAnalyze;

    if ("query" in input) {
      queryToAnalyze = input.query;
    } else if ("globalQuery" in input) {
      queryToAnalyze = input.globalQuery;
    }

    if (queryToAnalyze) {
      try {
        queryUnderstandingServiceResponse =
          await this.queryUnderstandingServiceClient.analyze(
            queryToAnalyze,
            "eng"
          );

        this.logger.info(
          {
            query: queryToAnalyze,
            response: queryUnderstandingServiceResponse.toObject()
          },
          "query understanding service"
        );
      } catch (error) {
        this.logger.error(
          { input, error },
          "Could not retrieve query understanding"
        );
      }
    }

    return queryUnderstandingServiceResponse;
  }

  private async resolveInputQueryToIndications(
    input: CongressSearchInput
  ): Promise<string[]> {
    if (input.query) {
      const indicationSearchInput: SearchIndicationsByQueryInput = {
        query: input.query,
        indicationType: [IndicationType.L3],
        size: 5,
        indicationSource: [IndicationSource.ALL],
        sortBy: IndicationSortBy.HCP_COMMUNITY_SIZE,
        projectId: input.projectId
      };
      const indicationNodes =
        await this.indicationsTreeSearchService.searchIndicationsByQuery(
          indicationSearchInput
        );
      const resolvedIndications: string[] = indicationNodes?.map((n) =>
        n.indicationName.trim().toLowerCase()
      );
      if (resolvedIndications) {
        return resolvedIndications;
      }
    }

    return [];
  }

  private async buildSearchRequest(
    input: Readonly<CongressSearchInput>,
    context: CongressSearchContext,
    idsOfMatchingPeople: string[],
    followedCongressSeriesIds: string[],
    indicationsUserIsInterestedIn: string[],
    queryUnderstandingServiceResponse?: Readonly<QueryUnderstandingServiceResponse>,
    indicationsToEnhanceSearchQuery?: string
  ): Promise<SearchRequest | null> {
    if (
      input.filters?.following?.isFollowing &&
      !followedCongressSeriesIds.length
    ) {
      return null;
    }

    const hasPersonNameIntent = this.hasPersonNameIntent(
      queryUnderstandingServiceResponse
    );
    const hasConferenceIntent = this.hasConferenceIntent(
      queryUnderstandingServiceResponse
    );
    const filters = await this.buildElasticsearchFiltersFromInputFilters(
      input,
      followedCongressSeriesIds
    );

    const searchQuery =
      this.getSynonymizedQuery(
        input.query,
        queryUnderstandingServiceResponse
      ) ?? EMPTY_STRING;
    const queryIndicationSeparator = searchQuery?.length ? "|" : EMPTY_STRING;
    const searchQueryWithIndications = searchQuery?.concat(
      indicationsToEnhanceSearchQuery
        ? queryIndicationSeparator + indicationsToEnhanceSearchQuery
        : EMPTY_STRING
    );
    const shoulds: QueryDslQueryContainer[] = [];
    const musts: QueryDslQueryContainer[] = this.buildMustsForSearchRequest(
      input.query,
      searchQueryWithIndications,
      idsOfMatchingPeople,
      hasPersonNameIntent,
      hasConferenceIntent
    );

    if (
      !input.filters?.following?.isFollowing &&
      indicationsUserIsInterestedIn.length &&
      !hasConferenceIntent
    ) {
      shoulds.push({
        terms: {
          indication: indicationsUserIsInterestedIn,
          boost: USER_INDICATIONS_BOOST
        }
      });
    }

    let query: QueryDslQueryContainer = {
      bool: {
        should: shoulds.length ? shoulds : undefined,
        minimum_should_match: ALL_ASSETS_OPTIONAL,
        filter: filters,
        must: musts.length ? buildBoolShouldQuery(musts) : undefined
      }
    };

    if (context === "search") {
      query = {
        function_score: {
          query,
          functions: [
            this.getLiveCongressScoringFunction(),
            this.getFutureCongressScoringFunction()
          ],
          score_mode: "sum",
          boost_mode: "replace"
        }
      };
    }

    const request: estypes.SearchRequest = {
      index: this.congressIndexName,
      track_total_hits: true,
      _source: {
        include: SOURCE_INCLUDES
      },
      query,
      sort: this.getSortForSearchRequest(input)
    };

    if (input.paging?.limit != null && input.paging.limit >= 0) {
      request.size = input.paging.limit;
    }
    if (input.paging?.offset) {
      request.from = input.paging.offset;
    }

    return request;
  }

  private getLiveCongressScoringFunction() {
    return {
      filter: {
        bool: {
          filter: [
            {
              range: {
                "filters.start_date": {
                  lt: getUTCTimestampMidnightTomorrow()
                }
              }
            },
            {
              range: {
                "filters.end_date": {
                  gte: getUTCTimestampMidnight()
                }
              }
            }
          ]
        }
      },
      weight: 1000
    };
  }

  private getFutureCongressScoringFunction() {
    return {
      filter: {
        range: {
          "filters.start_date": {
            gt: getUTCTimestampMidnight()
          }
        }
      },
      weight: 500
    };
  }

  private getSortForSearchRequest({
    sortOrder
  }: Readonly<CongressSearchInput>): Sort {
    switch (sortOrder) {
      case CongressSearchSortOrder.CHRONOLOGICAL:
        return [{ "filters.start_date": "asc" }, { speaker_count: "desc" }];
      case CongressSearchSortOrder.REVERSE_CHRONOLOGICAL:
        return [{ "filters.start_date": "desc" }, { speaker_count: "desc" }];
      // The scripts are used to sort future congresses in ascending order
      // and past congresses in descending order.
      case CongressSearchSortOrder.H1_RANKING:
      default:
        return [
          "_score",
          this.getScriptSortForFutureCongress(),
          this.getScriptSortForPastCongress(),
          { speaker_count: "desc" }
        ];
    }
  }

  private getScriptSortForFutureCongress(): SortCombinations {
    return {
      _script: {
        type: "number",
        order: "asc",
        script: {
          lang: "painless",
          source: `
            long ts = doc['filters.start_date'].value.toInstant().toEpochMilli(); 
            long now = params.now; 
            return ts >= now ? 0 : 1;
          `,
          params: {
            now: getUTCTimestampMidnightTomorrow()
          }
        }
      }
    };
  }

  private getScriptSortForPastCongress(): SortCombinations {
    return {
      _script: {
        type: "number",
        order: "asc",
        script: {
          lang: "painless",
          source: `
            long ts = doc['filters.end_date'].value.toInstant().toEpochMilli(); 
            long now = params.now;
            if (ts >= now) {
              // future: sort by ts ↑ (earliest future first)
              return ts;
            } else {
              // past: return negative ts so that more‐recent past (larger ts)
              // becomes more‐negative → appears earlier when ordering ↑
              return -ts;
            }
          `,
          params: {
            now: getUTCTimestampMidnight()
          }
        }
      }
    };
  }

  private buildMustsForSearchRequest(
    searchQuery: string | undefined,
    searchQueryWithIndications: string | undefined,
    idsOfMatchingPeople: string[],
    hasPersonNameIntent: boolean,
    hasConferenceIntent: boolean
  ): QueryDslQueryContainer[] {
    const musts: QueryDslQueryContainer[] = [];

    if (hasConferenceIntent && searchQuery?.length) {
      musts.push({
        simple_query_string: {
          query: searchQuery,
          fields: ["name^10", "series_name^10", "society^10"],
          default_operator: "and"
        }
      });

      return musts;
    }

    if (searchQueryWithIndications?.length) {
      musts.push({
        simple_query_string: {
          query: searchQueryWithIndications,
          fields: ["name^10", "series_name^10", "society", "indication"],
          default_operator: "and"
        }
      });
    }

    if (idsOfMatchingPeople.length && hasPersonNameIntent) {
      musts.push({
        terms: {
          h1_person_ids: idsOfMatchingPeople,
          boost: 10
        }
      });
    }

    return musts;
  }

  async findPeopleMatchingQuery(
    input: Readonly<CongressSearchInput>
  ): Promise<string[]> {
    if (!input.query || input.query.trim().length === 0) {
      return [];
    }

    const cachedMatchingPeopleIds = await this.getMatchingPeopleIdsFromCache(
      input.query,
      input.userId,
      input.projectId
    );

    if (cachedMatchingPeopleIds) {
      return cachedMatchingPeopleIds;
    }

    const { userId, projectId } = input;

    if (HAS_ADVANCED_OPERATORS.test(input.query)) {
      this.logger.info(
        { query: input.query },
        "Query has advanced operators. Skipping people name search..."
      );
      return [];
    }

    const nameSearchFeatureFlags = await this.getNameSearchFeatureFlagValues({
      userId,
      projectId
    });

    const query = this.sanitizeQuery(this.truncateQuery(input.query)) || "";

    const userLanguage = await this.getUsersPreferredLanguage(input);
    const languageDetector =
      this.languageDetectService.getLanguageDetector(userLanguage);
    const queryLang = languageDetector(query);

    const useCmnAndJpnFields =
      this.languageDetectService.shouldUseBothCmnAndJpnFields(
        queryLang as Language,
        userLanguage
      );
    const onboardingData =
      await this.userOnboardingDataService.getOnboardingData(userId, projectId);

    const nameSearchBuilder =
      this.nameSearchBuilderFactory.getNameSearchBuilder(queryLang as Language);

    const numberOfNameVariationsToMatch =
      queryLang === ENGLISH
        ? await this.calculateMinimumNameVariations.calculateMinimumFieldsToMatch(
            this.elasticSearchPeopleService,
            this.peopleIndexName,
            query
          )
        : DEFAULT_NAME_VARIATION_MATCH_COUNT;
    const queryUnderstandingServiceResponse =
      await this.queryUnderstandingServiceClient.analyze(
        query,
        queryLang as Language
      );
    const searchRequest = nameSearchBuilder.createNameSearchBody({
      sourceOverride: ["id"],
      query,
      from: NAME_SEARCH_OFFSET,
      size: NAME_SEARCH_SIZE,
      numberOfNameVariationsToMatch,
      filter: [buildGreaterThanRangeQuery("congressCount", 0)],
      language: queryLang as Language,
      featureFlags: nameSearchFeatureFlags,
      onboardingData,
      queryUnderstandingServiceResponse,
      projectId,
      useCmnAndJpnFields
    });

    searchRequest.index = this.peopleIndexName;
    searchRequest.search_type = SEARCH_TYPE;

    const response: SearchResponse<HCPDocument> =
      await this.elasticSearchPeopleService.query<HCPDocument>(searchRequest);

    const matchingPeopleIds = _.compact(
      _.map(response.hits.hits, (hit) => hit._source?.id)
    );

    this.addMatchingPeopleIdsToCache(
      input.query,
      input.userId,
      input.projectId,
      matchingPeopleIds
    );

    this.logger.info(
      {
        matchingPeopleIds
      },
      "people name search results"
    );

    return matchingPeopleIds;
  }

  private async getUsersPreferredLanguage({
    userId,
    projectId
  }: CongressSearchInput): Promise<Language> {
    try {
      const usersPreferredLanguage =
        await this.userClient.getUsersPreferredLanguage(userId, projectId);
      this.logger.debug(
        `users preferred language: ${usersPreferredLanguage?.language}`
      );

      switch (usersPreferredLanguage?.language) {
        case "japanese":
        case JAPANESE:
          return JAPANESE;
        case "chinese_simplified":
        case "chinese_traditional":
        case CHINESE:
          return CHINESE;
        case "english":
        case ENGLISH:
        default:
          return ENGLISH;
      }
    } catch (err) {
      this.logger.error(
        { err },
        "Error thrown fetching users preferred language"
      );
      return ENGLISH;
    }
  }

  private async buildElasticsearchFiltersFromInputFilters(
    { filters: inputFilters, projectId }: CongressSearchInput,
    followedCongressSeriesIds: string[]
  ): Promise<QueryDslQueryContainer[]> {
    const esFilters: QueryDslQueryContainer[] = [];

    if (!inputFilters) {
      return esFilters;
    }

    if (inputFilters.location) {
      esFilters.push(...this.buildLocationFilters(inputFilters.location));
    }

    if (inputFilters.indication?.indications) {
      esFilters.push(
        buildTermsQuery(
          "filters.indication",
          inputFilters.indication.indications
        )
      );
    }

    if (inputFilters.dateRange) {
      const congressMinDate = {
        gte: inputFilters.dateRange!.min
      };
      esFilters.push({
        range: {
          "filters.end_date": congressMinDate
        }
      });

      if (inputFilters.dateRange!.max) {
        const congressMaxDate = {
          lte: inputFilters.dateRange!.max
        };
        esFilters.push({
          range: {
            "filters.start_date": congressMaxDate
          }
        });
      }
    }

    if (inputFilters.series?.seriesNames) {
      esFilters.push(
        buildTermsQuery("filters.series", inputFilters.series.seriesNames)
      );
    }

    if (inputFilters.society?.societyNames) {
      esFilters.push(
        buildTermsQuery("filters.society", inputFilters.society.societyNames)
      );
    }

    if (inputFilters.type?.congressTypes) {
      esFilters.push(
        buildTermsQuery(
          "filters.congress_type",
          inputFilters.type.congressTypes
        )
      );
    }

    if (followedCongressSeriesIds.length) {
      if (inputFilters.following?.isFollowing) {
        esFilters.push(
          buildTermsQuery("h1_series_id", followedCongressSeriesIds)
        );
      }

      if (inputFilters.following?.isNotFollowing) {
        esFilters.push({
          bool: {
            must_not: buildTermsQuery("h1_series_id", followedCongressSeriesIds)
          }
        });
      }
    }

    if (inputFilters.tags) {
      esFilters.push(
        ...(await this.buildTagsFilters(inputFilters.tags, projectId))
      );
    }

    return esFilters;
  }

  private buildLocationFilters(
    inputLocationFilters: CongressSearchFilters["location"]
  ) {
    const esFilters: QueryDslQueryContainer[] = [];

    if (!inputLocationFilters) {
      return esFilters;
    }

    if (!_.isEmpty(inputLocationFilters.countries)) {
      const countryFilterValues = this.collectCountryFilterValues(
        inputLocationFilters.countries!
      );
      esFilters.push({
        bool: {
          should: [
            buildTermsQuery("filters.country", countryFilterValues),
            buildTermsQuery(
              "filters.country_level_regions",
              countryFilterValues
            )
          ]
        }
      });
    }

    if (!_.isEmpty(inputLocationFilters.excludeCountries)) {
      const countryFilterValues = this.collectCountryFilterValues(
        inputLocationFilters.excludeCountries!
      );
      esFilters.push({
        bool: {
          must_not: [
            buildTermsQuery("filters.country", countryFilterValues),
            buildTermsQuery(
              "filters.country_level_regions",
              countryFilterValues
            )
          ]
        }
      });
    }

    if (!_.isEmpty(inputLocationFilters.regions)) {
      const regionFilterValues = this.collectRegionFilterValues(
        inputLocationFilters.regions!
      );
      esFilters.push({
        bool: {
          should: [
            buildTermsQuery("filters.region", regionFilterValues),
            buildTermsQuery("filters.state_level_regions", regionFilterValues)
          ]
        }
      });
    }

    if (!_.isEmpty(inputLocationFilters.excludeRegions)) {
      const regionFilterValues = this.collectRegionFilterValues(
        inputLocationFilters.excludeRegions!
      );
      esFilters.push({
        bool: {
          must_not: [
            buildTermsQuery("filters.region", regionFilterValues),
            buildTermsQuery("filters.state_level_regions", regionFilterValues)
          ]
        }
      });
    }

    if (!_.isEmpty(inputLocationFilters.cities)) {
      const cityFilterValues = this.collectCityFilterValues(
        inputLocationFilters.cities!
      );
      esFilters.push({
        bool: {
          should: [
            buildTermsQuery("filters.city", cityFilterValues),
            buildTermsQuery("filters.city_level_regions", cityFilterValues)
          ]
        }
      });
    }

    if (!_.isEmpty(inputLocationFilters.excludeCities)) {
      const cityFilterValues = this.collectCityFilterValues(
        inputLocationFilters.excludeCities!
      );
      esFilters.push({
        bool: {
          must_not: [
            buildTermsQuery("filters.city", cityFilterValues),
            buildTermsQuery("filters.city_level_regions", cityFilterValues)
          ]
        }
      });
    }

    if (!_.isEmpty(inputLocationFilters.postalCodes)) {
      esFilters.push(
        this.collectPostalCodeFilterValues(inputLocationFilters.postalCodes!)
      );
    }

    if (!_.isEmpty(inputLocationFilters.excludePostalCodes)) {
      esFilters.push({
        bool: {
          must_not: this.collectPostalCodeFilterValues(
            inputLocationFilters.excludePostalCodes!
          )
        }
      });
    }

    if (inputLocationFilters.isVirtual) {
      esFilters.push(buildTermQuery("filters.is_virtual", true));
    }

    return esFilters;
  }

  private async buildTagsFilters(
    inputTagsFilters: CongressSearchFilters["tags"],
    projectId: string
  ): Promise<QueryDslQueryContainer[]> {
    const esFilters: QueryDslQueryContainer[] = [];

    if (!inputTagsFilters) {
      return esFilters;
    }

    const inclusionTagsFilters = await this.buildInclusionTagsFilters(
      projectId,
      inputTagsFilters.peopleTags,
      inputTagsFilters.intersectPeopleTags
    );
    const exclusionTagsFilters = await this.buildExclusionTagsFilters(
      projectId,
      inputTagsFilters.exclusionPeopleTags
    );

    esFilters.push(...inclusionTagsFilters, ...exclusionTagsFilters);

    return esFilters;
  }

  private async buildInclusionTagsFilters(
    projectId: string,
    inclusionTagIds?: string[],
    intersectTags?: boolean
  ): Promise<QueryDslQueryContainer[]> {
    if (!inclusionTagIds?.length) {
      return [];
    }

    let entities = await this.tagsHelperService.getEntitiesInProjectsByTags(
      projectId,
      inclusionTagIds
    );

    if (!entities?.length) {
      return [];
    }

    if (intersectTags === true && inclusionTagIds.length > 1) {
      const entityIdToTagIdMap = new Map<string, string[]>();
      entities.forEach((entity) => {
        entityIdToTagIdMap.has(entity.entityId)
          ? entityIdToTagIdMap.get(entity.entityId)!.push(entity.tagId)
          : entityIdToTagIdMap.set(entity.entityId, [entity.tagId]);
      });
      const intersectEntities: typeof entities = [];
      entities.forEach((entity) => {
        if (
          entityIdToTagIdMap.get(entity.entityId)?.length ===
          inclusionTagIds.length
        ) {
          intersectEntities.push(entity);
        }
      });
      this.logger.info(
        {
          inclusionTagIds,
          totalAssignments: entities.length,
          intersectedAssignments:
            intersectEntities.length / inclusionTagIds.length
        },
        "performing hcp tags intersection"
      );
      entities = intersectEntities;
    }

    if (intersectTags === true && entities.length === 0) {
      return [buildTermsQuery("h1_person_ids", ["no-match-hcp-id"])];
    }

    const peopleIds = this.getPeopleIdsFromTagEntities(entities);
    this.logger.info({ hcpIds: peopleIds.length }, "total HCPs in tags filter");
    return peopleIds.length
      ? [buildTermsQuery("h1_person_ids", peopleIds)]
      : [];
  }

  private async buildExclusionTagsFilters(
    projectId: string,
    exclusionTagIds?: string[]
  ): Promise<QueryDslQueryContainer[]> {
    if (!exclusionTagIds?.length) {
      return [];
    }

    const peopleIds = await this.collectPeopleIdsFromTags(
      projectId,
      exclusionTagIds
    );

    if (!peopleIds?.length) {
      return [];
    }

    this.logger.info(
      { hcpIds: peopleIds.length },
      "total HCPs in exclusion tags filter"
    );

    return peopleIds.length
      ? [buildTermsQuery("h1_person_ids", peopleIds)]
      : [];
  }

  private getSynonymizedQuery(
    query?: string,
    queryUnderstandingServiceResponse?: QueryUnderstandingServiceResponse
  ): string | undefined {
    let searchQuery = query;
    if (
      queryUnderstandingServiceResponse &&
      queryUnderstandingServiceResponse.getAugmentedQuery()
    ) {
      searchQuery = this.convertQueryToSimpleQueryStringSyntax(
        queryUnderstandingServiceResponse.getAugmentedQuery()
      );
    }
    return searchQuery;
  }

  private convertQueryToSimpleQueryStringSyntax(query: string): string {
    const orRegex = /\sOR\s/g;
    const andRegex = /\sAND\s/g;
    const ALL_UNICODE_DOUBLE_QUOTES = /[“”]/g;
    const ASCII_DOUBLE_QUOTES = '"';

    const simpleQueryString = query
      .replace(orRegex, " | ")
      .replace(andRegex, " + ")
      .replace(ALL_UNICODE_DOUBLE_QUOTES, ASCII_DOUBLE_QUOTES);
    return simpleQueryString;
  }

  private async getIndicationSynonymsFromQueryUnderstandingService(
    indications: string[],
    or: boolean,
    country = "us"
  ): Promise<[string, string]> {
    const queryUnderstandingServiceResponse =
      await this.queryUnderstandingServiceClient.getIndicationSynonymsAndIcdCodes(
        indications,
        "eng",
        or,
        country
      );

    return [
      queryUnderstandingServiceResponse
        .getIndicationsParsedQuery()
        .replace(ALL_ORs, OR)
        .replace(ALL_ANDs, EMPTY_STRING)
        .replace(ALL_UNICODE_DOUBLE_QUOTES, ASCII_DOUBLE_QUOTES),
      queryUnderstandingServiceResponse.getIndicationsIcdCodesQuery()
    ];
  }

  private truncateQuery(query?: string) {
    if (query) {
      return query.trim().split(/\s+/).slice(0, MAXIMUM_QUERY_TOKENS).join(" ");
    }
    return query;
  }

  private sanitizeQuery(query?: string) {
    return query?.replace(/[´]/g, "'");
  }

  private hasPersonNameIntent(
    queryUnderstandingServiceResponse:
      | QueryUnderstandingServiceResponse
      | undefined
  ): boolean {
    if (
      queryUnderstandingServiceResponse?.hasQueryIntent &&
      queryUnderstandingServiceResponse.getQueryIntent()?.hasPersonNameIntent &&
      queryUnderstandingServiceResponse
        .getQueryIntent()
        ?.getPersonNameIntent()
        ?.getScore() === 1.0
    ) {
      return true;
    } else {
      return false;
    }
  }

  private hasConferenceIntent(
    queryUnderstandingServiceResponse:
      | QueryUnderstandingServiceResponse
      | undefined
  ): boolean {
    return !!(
      queryUnderstandingServiceResponse?.hasQueryIntent &&
      queryUnderstandingServiceResponse.getQueryIntent()?.hasConferenceIntent()
    );
  }

  private toMultiSearchBodyToFetchCount(
    searchRequest: SearchRequest
  ): MsearchMultisearchBody {
    return {
      size: IGNORE_HITS,
      _source: false,
      track_total_hits: searchRequest.track_total_hits,
      query: searchRequest.query
    };
  }

  private collectCountryFilterValues(
    countryValues: Array<string> = []
  ): Array<string> {
    if (countryValues.every(is2CharacterISOCountryCode)) {
      return countryValues;
    }

    // REMOVE WHEN FIXED
    const countryCodes: Array<string> = [];
    for (const countryName of countryValues) {
      if (countryName in COUNTRY_NAME_TO_CODE) {
        countryCodes.push(COUNTRY_NAME_TO_CODE[countryName]);
      } else {
        countryCodes.push(countryName);
      }
    }

    return countryCodes;
  }

  private collectRegionFilterValues(
    regionValues: Array<string> = []
  ): Array<string> {
    if (regionValues.every(isFilterValueAlreadyPipeDelimited)) {
      return regionValues;
    }

    // REMOVE WHEN FIXED
    const regionFilterValues: Array<string> = [];
    for (const regionName of regionValues) {
      if (regionName in REGIONS_KEYED_BY_NAME) {
        const region = REGIONS_KEYED_BY_NAME[regionName];
        regionFilterValues.push(`${region.country}|${region.code}`);
      } else {
        regionFilterValues.push(regionName);
      }
    }

    return regionFilterValues;
  }

  private collectCityFilterValues(
    cityValues: Array<string> = []
  ): Array<string> {
    if (cityValues.every(isFilterValueAlreadyPipeDelimited)) {
      return cityValues;
    }

    // REMOVE WHEN FIXED
    const cityFilterValues: Array<string> = [];
    for (const city of cityValues) {
      const delimitedCityFilterValues = toDelimitedCityFilterValues(city);
      if (delimitedCityFilterValues) {
        cityFilterValues.push(...delimitedCityFilterValues);
      }
    }

    return cityFilterValues;
  }

  private collectPostalCodeFilterValues(
    postalCodeValues: Array<string> = []
  ): QueryDslQueryContainer {
    if (postalCodeValues.every(isFilterValueAlreadyPipeDelimited)) {
      return buildTermsQuery(`filters.postal_code`, postalCodeValues);
    }

    // REMOVE WHEN FIXED
    const pipeDelimitedPostalCodes = postalCodeValues
      .filter((value) => !!value)
      .join("|");
    const wildcardPrefixMatchForAmbiguousPostalCodes = `.+\\|(${pipeDelimitedPostalCodes})`;
    return {
      regexp: {
        [`filters.postal_code`]: {
          value: wildcardPrefixMatchForAmbiguousPostalCodes
        }
      }
    };
  }

  private async collectPeopleIdsFromTags(
    projectId: string,
    peopleTags: string[]
  ) {
    return this.getPeopleIdsFromTagEntities(
      await this.tagsHelperService.getEntitiesInProjectsByTags(
        projectId,
        peopleTags
      )
    );
  }

  private getPeopleIdsFromTagEntities(
    tagEntities: { entityId: string; entityType: EntityType }[]
  ) {
    return tagEntities
      ?.filter((entity) => entity.entityType === EntityType.PERSON)
      ?.map((person) => person.entityId);
  }

  private async getAllFollowedCongressSeriesIdsForUserFromCacheOrfollowRecordClient(
    userId: string,
    projectId: string
  ): Promise<string[]> {
    const cachedFollowedCongressSeriesIds =
      await this.getAllFollowedCongressSeriesIdsForUserFromCache(
        userId,
        projectId
      );

    if (cachedFollowedCongressSeriesIds) {
      return cachedFollowedCongressSeriesIds;
    } else {
      const followedCongressSeriesIds =
        await this.getAllFollowedCongressSeriesIdsForUser(userId, projectId);

      this.addAllFollowedCongressSeriesIdsForUserToCache(
        userId,
        projectId,
        followedCongressSeriesIds
      );

      return followedCongressSeriesIds;
    }
  }

  private async getAllFollowedCongressSeriesIdsForUser(
    userId: string,
    projectId: string
  ): Promise<string[]> {
    return (
      (
        await this.followRecordClient.getPaginatedFollowRecordIds({
          userId,
          projectId,
          followType: FollowType.Conference
        })
      )?.items.map((item) => item.followTypeId) ?? []
    );
  }

  private async getAllFollowedCongressSeriesIdsForUserFromCache(
    userId: string,
    projectId: string
  ): Promise<string[] | null> {
    const key = sha1({ userId, projectId, entity: "followedCongressSeries" });
    const raw = await this.getFromCache(key);
    if (raw) {
      const entities = JSON.parse(raw) as string[];
      this.logger.debug(entities, "followed congresses cache hit");
      return entities;
    }
    this.logger.debug(null, "followed congresses cache miss");
    return null;
  }

  private addAllFollowedCongressSeriesIdsForUserToCache(
    userId: string,
    projectId: string,
    followedCongressSeriesIds: Readonly<string[]>
  ) {
    const key = sha1({ userId, projectId, entity: "followedCongressSeries" });
    this.addToCache(
      key,
      JSON.stringify(followedCongressSeriesIds),
      EXPIRATION_PERIOD
    );
  }

  private async getIndicationsUserIsInterestedInFromCacheOrOnboardingData(
    userId: string,
    projectId: string,
    followedCongressSeriesIds: string[]
  ): Promise<string[] | null> {
    if (followedCongressSeriesIds.length) {
      return await this.getIndicationsUserIsInterestedInFromCache(
        userId,
        projectId
      );
    }

    return (
      await this.userOnboardingDataService.getOnboardingData(userId, projectId)
    )?.indications;
  }

  private async getIndicationsUserIsInterestedInFromCache(
    userId: string,
    projectId: string
  ): Promise<string[] | null> {
    const key = sha1({
      userId,
      projectId,
      entity: "indicationsUserIsInterestedIn"
    });
    const raw = await this.getFromCache(key);
    if (raw) {
      const entities = JSON.parse(raw) as string[];
      this.logger.debug(entities, "indications cache hit");
      return entities;
    }
    this.logger.debug(null, "indications cache miss");
    return null;
  }

  private addIndicationsUserIsInterestedInToCache(
    userId: string,
    projectId: string,
    indicationsUserIsInterestedIn: Readonly<string[]>
  ) {
    const key = sha1({
      userId,
      projectId,
      entity: "indicationsUserIsInterestedIn"
    });
    this.addToCache(
      key,
      JSON.stringify(indicationsUserIsInterestedIn),
      INDICATIONS_CACHE_EXPIRATION_PERIOD
    );
  }

  private async getMatchingPeopleIdsFromCache(
    query: string,
    userId: string,
    projectId: string
  ): Promise<string[] | null> {
    const key = sha1({ query, userId, projectId, entity: "matchingPeopleIds" });
    const raw = await this.getFromCache(key);
    if (raw) {
      const entities = JSON.parse(raw) as string[];
      this.logger.debug(entities, "matching people cache hit");
      return entities;
    }
    this.logger.debug(null, "matching people cache miss");
    return null;
  }

  private addMatchingPeopleIdsToCache(
    query: string,
    userId: string,
    projectId: string,
    matchingPeopleIds: Readonly<string[]>
  ) {
    const key = sha1({ query, userId, projectId, entity: "matchingPeopleIds" });
    this.addToCache(key, JSON.stringify(matchingPeopleIds), EXPIRATION_PERIOD);
  }

  private async getFromCache(key: string) {
    try {
      return await this.redisClient.get(key);
    } catch (err) {
      this.logger.error(err, "Error connecting redis");
      return null;
    }
  }

  private addToCache(
    key: string,
    value: string,
    ttl: number = EXPIRATION_PERIOD
  ) {
    try {
      this.redisClient.set(key, value, SECONDS, ttl);
    } catch (err) {
      this.logger.error(err, "Error connecting redis");
    }
  }

  private async shouldLookupIndicationsUserIsInterestedIn(
    followedCongressSeriesIds: string[],
    cachedFollowedCongressSeriesIds: string[] | null
  ) {
    if (!cachedFollowedCongressSeriesIds) {
      return true;
    }

    return (
      _.xor(followedCongressSeriesIds, cachedFollowedCongressSeriesIds).length >
      0
    );
  }

  private async getIndicationsUserIsInterestedIn(
    input: Readonly<CongressSearchInput>,
    followedCongressSeriesIds: string[]
  ) {
    let indicationsUserIsInterestedIn =
      (await this.getIndicationsUserIsInterestedInFromCacheOrOnboardingData(
        input.userId,
        input.projectId,
        followedCongressSeriesIds
      )) ?? [];

    if (!input.filters?.following?.isFollowing) {
      const cachedFollowedCongressSeriesIds =
        await this.getAllFollowedCongressSeriesIdsForUserFromCache(
          input.userId,
          input.projectId
        );

      const shouldLookupIndicationsUserIsInterestedIn =
        await this.shouldLookupIndicationsUserIsInterestedIn(
          followedCongressSeriesIds,
          cachedFollowedCongressSeriesIds
        );

      if (shouldLookupIndicationsUserIsInterestedIn) {
        indicationsUserIsInterestedIn =
          await this.getIndicationsUserIsInterestedInFromElasticSearch(
            followedCongressSeriesIds
          );
      }
    }

    this.addIndicationsUserIsInterestedInToCache(
      input.userId,
      input.projectId,
      indicationsUserIsInterestedIn
    );

    this.addAllFollowedCongressSeriesIdsForUserToCache(
      input.userId,
      input.projectId,
      followedCongressSeriesIds
    );

    return indicationsUserIsInterestedIn;
  }

  //TODO: Probably better to look this up from the PostgreSQL database
  private async getIndicationsUserIsInterestedInFromElasticSearch(
    followedCongressSeriesIds: string[]
  ) {
    if (!followedCongressSeriesIds.length) {
      return [];
    }

    const request = {
      _source: ["indication"],
      size: followedCongressSeriesIds.length,
      index: this.congressIndexName,
      query: {
        terms: {
          h1_series_id: followedCongressSeriesIds
        }
      }
    };

    const results = await this.elasticSearchCongressService.query<{
      indication: string;
    }>(request);

    return _.uniq(
      _.compact(
        _.flatten(results.hits.hits.map((hit) => hit._source?.indication))
      )
    );
  }

  private async getNameSearchFeatureFlagValues({
    userId,
    projectId,
    appName
  }: {
    userId?: string;
    projectId: string;
    appName?: AppName;
  }): Promise<NameSearchFeatureFlags> {
    const featureFlags: Partial<NameSearchFeatureFlags> = {};
    const user = userId ? { userId, projectId, appName } : undefined;
    const flagsState = await this.featureFlagsService.getAllFlags(user);

    for (const flag of nameSearchFeatureFlagTypes) {
      featureFlags[flag] =
        flagsState.getFlagValue(featureFlagDefaults[flag].key) ??
        featureFlagDefaults[flag].default;
    }

    return featureFlags as NameSearchFeatureFlags;
  }
}

// The following functions can be removed once the location filters are converted
// to use the pipe-delimited format and no longer need to be converted.

// this is required to assist the translation of "Pennsylvania" => "us|PA"
const REGIONS_KEYED_BY_NAME = Object.entries(
  REGION_CODES_BY_COUNTRY_CODE_AND_REGION_NAME
).reduce((acc, [country, regions]) => {
  Object.entries(regions).forEach(([regionName, code]) => {
    acc[regionName] = { code, country };
  });
  return acc;
}, {} as Record<string, Region>);

const IS_CITY_COUNTY_STATE_COUNTRY_FORMAT = 4;
const IS_AMBIGUOUS_CITY_STATEORCOUNTY_COUNTRY_FORMAT = 3;
const IS_CITY_COUNTRY_FORMAT = 2;
const IS_CITY_LEVEL_REGION = 1;

interface Region {
  code: string;
  country: string;
}

const indicesFor4FieldCityFilterFormat = {
  city: 0,
  county: 1,
  region: 2,
  country: 3
};

const COUNTRY_CODES = Object.values(COUNTRY_NAME_TO_CODE);

function is2CharacterISOCountryCode(countryName: string) {
  return COUNTRY_CODES.includes(countryName);
}

function lookupCountryCode(countryName: string) {
  return COUNTRY_NAME_TO_CODE[countryName as keyof typeof COUNTRY_NAME_TO_CODE];
}

function isFilterValueAlreadyPipeDelimited(filterValue: string) {
  return filterValue.includes("|");
}

function getCountyValueFromCityFilter(locationFields: string[]) {
  if (
    locationFields[indicesFor4FieldCityFilterFormat.country] ||
    locationFields[indicesFor4FieldCityFilterFormat.region]
  ) {
    return locationFields[indicesFor4FieldCityFilterFormat.county];
  }
  return "";
}

/*
inputs can be "Lancaster, PA, United States" and "Lancaster, United States" where the region is missing
REMOVE WHEN FIXED
*/
function getRegionValueFromCityFilter(locationFields: string[]) {
  let region;
  if (locationFields[indicesFor4FieldCityFilterFormat.country]) {
    region = locationFields[indicesFor4FieldCityFilterFormat.region];
  } else if (locationFields[indicesFor4FieldCityFilterFormat.region]) {
    region = locationFields[indicesFor4FieldCityFilterFormat.county];
  }

  if (region) {
    return hasRegionCode(region) ? REGIONS_KEYED_BY_NAME[region].code : region;
  }

  return "";
}

function getCountryValueFromCityFilter(locationFields: string[]) {
  if (locationFields[indicesFor4FieldCityFilterFormat.country]) {
    return locationFields[indicesFor4FieldCityFilterFormat.country];
  } else if (locationFields[indicesFor4FieldCityFilterFormat.region]) {
    return locationFields[indicesFor4FieldCityFilterFormat.region];
  }

  return locationFields[indicesFor4FieldCityFilterFormat.county];
}

function hasRegionCode(region: string) {
  return region in REGIONS_KEYED_BY_NAME;
}

function toDelimitedCityFilterValues(userCityFilter: string) {
  if (isFilterValueAlreadyPipeDelimited(userCityFilter)) {
    return [userCityFilter];
  }

  const locationFields = userCityFilter.split(/,/).map(_.trim);

  if (locationFields.length === IS_CITY_LEVEL_REGION) {
    return [userCityFilter];
  }

  if (
    locationFields.length !== IS_CITY_COUNTY_STATE_COUNTRY_FORMAT &&
    locationFields.length !== IS_AMBIGUOUS_CITY_STATEORCOUNTY_COUNTRY_FORMAT &&
    locationFields.length !== IS_CITY_COUNTRY_FORMAT
  ) {
    return null;
  }

  const city = locationFields[indicesFor4FieldCityFilterFormat.city];
  const county = getCountyValueFromCityFilter(locationFields);
  const region = getRegionValueFromCityFilter(locationFields);
  const countryName = getCountryValueFromCityFilter(locationFields);
  const countryCode = lookupCountryCode(countryName);

  if (!countryCode) {
    return null;
  }

  if (
    locationFields.length === IS_AMBIGUOUS_CITY_STATEORCOUNTY_COUNTRY_FORMAT
  ) {
    return [
      [countryCode, region, city].join("|"),
      [countryCode, county, city].join("|")
    ];
  }

  return [[countryCode, region, county, city].join("|")];
}

function getUTCTimestampMidnight() {
  const todayMidnightUtc = new Date(
    Date.UTC(
      new Date().getUTCFullYear(),
      new Date().getUTCMonth(),
      new Date().getUTCDate(),
      0,
      0,
      0,
      0
    )
  );
  return todayMidnightUtc.getTime();
}

function getUTCTimestampMidnightTomorrow() {
  const tomorrowMidnightUtc = new Date(
    Date.UTC(
      new Date().getUTCFullYear(),
      new Date().getUTCMonth(),
      new Date().getUTCDate() + 1,
      0,
      0,
      0,
      0
    )
  );
  return tomorrowMidnightUtc.getTime();
}
