import {
  SearchFieldCollapse,
  SearchHit,
  SearchInnerHits,
  QueryDslQueryContainer,
  SearchRequest,
  Sort,
  QueryDslTextQueryType
} from "@elastic/elasticsearch/lib/api/types";
import {
  IndicationNode,
  IndicationSortBy,
  IndicationSource,
  SearchIndicationsByQueryInput,
  SearchIndicationTreesByQueryInput,
  SearchRootIndicationsInput,
  IndicationsGetSubTreesInput,
  IndicationType
} from "@h1nyc/search-sdk";
import { FeatureFlagsService } from "@h1nyc/systems-feature-flags";
import { Service } from "typedi";
import { createLogger } from "../lib/Logger";
import { ConfigService } from "./ConfigService";
import { ElasticSearchIndicationsTreeService } from "./ElasticsearchIndicationsTreeService";
import { Trace } from "../Tracer";
import {
  Dictionary,
  difference,
  flatten,
  groupBy,
  last,
  mapValues,
  orderBy,
  sumBy,
  uniq
} from "lodash";
import {
  IndicationInstitutionCountsRepository,
  IndicationIcdInstitutionCountsRepository
} from "@h1nyc/pipeline-repositories";
import {
  IndicationInstitutionCounts,
  IndicationIcdInstitutionCounts
} from "@h1nyc/pipeline-entities";
import { SearchAnalyticsTracerService } from "./SearchAnalyticsTrackerService";
import { errors } from "@elastic/elasticsearch";

const PARENT_ID_FOR_ROOT_NODES = "NULL";
const collapseOnIndicationName: SearchFieldCollapse = {
  field: "indication_name"
};
const doNotCollapse = undefined;

interface IcdCode {
  icdCode: string;
  patientCount: number;
  description: string;
}

export interface IndicationDoc {
  h1_id: string;
  parent_h1id?: string[];
  indication_name: string;
  indication_source: string;
  indication_type: IndicationType;
  display_name?: string;
  description?: string;
  community_size?: number;
  claim_count?: number;
  patient_count?: number;
  icd_codes?: IcdCode[];
  ancestor_ids: string[];
  inner_hits: SearchInnerHits;
}

export type IndicationSearchFeatureFlags = {
  enablePhraseMatchForIndicationSearch: boolean;
  enableIcdDescriptionMatching: boolean;
};

export const indicationSearchFeatureFlagTypes = [
  "enablePhraseMatchForIndicationSearch",
  "enableIcdDescriptionMatching"
] as const;

export type IndicationSearchFeatureFlag =
  (typeof indicationSearchFeatureFlagTypes)[number];

export const featureFlagDefaults: Readonly<
  Record<keyof IndicationSearchFeatureFlags, { key: string; default: boolean }>
> = {
  enablePhraseMatchForIndicationSearch: {
    key: "search.enable-phrase-match--for-indication-search",
    default: false
  },
  enableIcdDescriptionMatching: {
    key: "search.enable-icd-description-matching-for-indication-search",
    default: false
  }
};

const DEFAULT_ROOT_NODES_TO_RETURN_COUNT = 100;
const DEFAULT_MATCH_NODES_TO_RETURN = 100;
const DEFAULT_SIZE_FOR_INSTITUTIONS = 100;
@Service()
export class IndicationsTreeSearchService {
  private readonly logger = createLogger(this);
  private indicationsIndex: string;

  constructor(
    config: ConfigService,
    private elasticSearchIndicationsTreeService: ElasticSearchIndicationsTreeService,
    private featureFlagsService: FeatureFlagsService,
    private indicationsInstitutionCountsRepository: IndicationInstitutionCountsRepository,
    private indicationIcdInstitutionCountsRepository: IndicationIcdInstitutionCountsRepository,
    private searchAnalyticsTracerService: SearchAnalyticsTracerService
  ) {
    this.indicationsIndex = config.elasticIndicationsIndex;
  }

  private buildQueryToGetAllRootIndications(
    indicationSource: IndicationSource[],
    sortBy?: IndicationSortBy,
    size?: number
  ): SearchRequest {
    let sortOrder: Sort = [
      { patient_count: "desc" },
      { _score: { order: "desc" } }
    ];

    if (sortBy === IndicationSortBy.HCP_COMMUNITY_SIZE) {
      sortOrder = [
        { sort_id: "asc" },
        { community_size: "desc" },
        { _score: { order: "desc" } }
      ];
    }

    return {
      index: this.indicationsIndex,
      size: size || DEFAULT_ROOT_NODES_TO_RETURN_COUNT,
      query: {
        bool: {
          must: [
            {
              match: {
                indication_type: "L1"
              }
            }
          ],
          filter: [
            {
              terms: {
                indication_source: indicationSource
              }
            }
          ]
        }
      },
      sort: sortOrder
    };
  }

  private buildQueryToGetIndicationsByUserQuery(
    query: string,
    size: number,
    indicationSource: IndicationSource[],
    indicationSearchFeatureFlags: IndicationSearchFeatureFlags,
    collapse: SearchFieldCollapse | undefined,
    indicationType?: IndicationType[],
    sortBy?: IndicationSortBy,
    rootId?: string,
    h1Ids?: string[]
  ): SearchRequest {
    let matchType: QueryDslTextQueryType = "bool_prefix";

    if (indicationSearchFeatureFlags.enablePhraseMatchForIndicationSearch) {
      matchType = "phrase_prefix";
    }

    const filters: QueryDslQueryContainer[] = [
      {
        terms: {
          indication_source: indicationSource
        }
      }
    ];

    const shoulds: QueryDslQueryContainer[] = [];

    if (query.length) {
      if (indicationSearchFeatureFlags.enableIcdDescriptionMatching) {
        shoulds.push({
          multi_match: {
            query,
            type: matchType,
            operator: "AND",
            fields: ["description", "description._2gram", "description._3gram"]
          }
        });
      }

      shoulds.push({
        multi_match: {
          query,
          type: matchType,
          operator: "AND",
          fields: [
            "indication_name.saut^2",
            "indication_name.saut._2gram^2",
            "indication_name.saut._3gram^2"
          ],
          fuzziness: "AUTO"
        }
      });
      shoulds.push({
        nested: {
          path: "synonyms",
          query: {
            multi_match: {
              query,
              type: matchType,
              operator: "AND",
              fields: [
                "synonyms.saut",
                "synonyms.saut._2gram",
                "synonyms.saut._3gram"
              ]
            }
          },
          inner_hits: {}
        }
      });
    }

    if (rootId) {
      filters.push({
        term: {
          ancestor_ids: rootId
        }
      });
    }

    if (indicationType?.length) {
      filters.push({
        terms: {
          indication_type: indicationType
        }
      });
    }

    if (h1Ids?.length) {
      filters.push({
        terms: {
          h1_id: h1Ids
        }
      });
    }

    //This is done to ensure that only L3 indications with icd codes qualify for TL widget(source:claims)
    if (indicationSource.includes(IndicationSource.CLAIMS)) {
      filters.push({
        bool: {
          minimum_should_match: 1,
          should: [
            {
              bool: {
                filter: [
                  {
                    term: {
                      indication_type: "L3"
                    }
                  },
                  {
                    nested: {
                      path: "icd_codes",
                      query: {
                        exists: {
                          field: "icd_codes"
                        }
                      }
                    }
                  }
                ]
              }
            },
            {
              terms: {
                indication_type: ["L1", "L2", "ICD"]
              }
            }
          ]
        }
      });
    }

    let sortOrder: Sort = [{ _score: { order: "desc" } }, { sort_id: "asc" }];

    if (sortBy === IndicationSortBy.HCP_COMMUNITY_SIZE) {
      sortOrder = [
        { sort_id: "asc" },
        { community_size: "desc" },
        { _score: { order: "desc" } }
      ];
    } else if (sortBy === IndicationSortBy.PATIENT_COUNT) {
      sortOrder = [{ patient_count: "desc" }, { _score: { order: "desc" } }];
    }

    return {
      index: this.indicationsIndex,
      size,
      query: {
        bool: {
          should: shoulds.length ? shoulds : undefined,
          minimum_should_match: shoulds.length ? 1 : undefined,
          filter: filters
        }
      },
      collapse,
      sort: sortOrder
    };
  }

  private buildQueryToGetAllChildNodes(
    rootId: string,
    indicationSource: IndicationSource[]
  ): SearchRequest {
    return {
      index: this.indicationsIndex,
      size: 10000, // TODO: Check if scroll should be used
      query: {
        bool: {
          must: {
            term: {
              ancestor_ids: rootId
            }
          },
          filter: [
            {
              terms: {
                indication_source: indicationSource
              }
            }
          ]
        }
      }
    };
  }

  private buildQueryToGetTreeNodesByAncestorAndParentIds(
    ancestorIds: string[],
    parentIds: string[]
  ): SearchRequest {
    return {
      index: this.indicationsIndex,
      size: 10000,
      query: {
        bool: {
          filter: [
            {
              bool: {
                should: [
                  {
                    terms: {
                      _id: ancestorIds
                    }
                  },
                  {
                    terms: {
                      ancestor_ids: parentIds
                    }
                  }
                ],
                minimum_should_match: 1
              }
            },
            {
              bool: {
                must_not: {
                  terms: {
                    _id: parentIds // Exclude nodes that were matched
                  }
                }
              }
            }
          ]
        }
      }
    };
  }

  @Trace("h1-search.keyword.getFeatureFlagValues")
  private async getFeatureFlagValues({
    userId,
    projectId
  }: {
    userId?: string;
    projectId: string;
  }): Promise<IndicationSearchFeatureFlags> {
    const featureFlags: Partial<IndicationSearchFeatureFlags> = {};

    const user = userId ? { userId, projectId } : undefined;
    const flagsState = await this.featureFlagsService.getAllFlags(user);

    for (const flag of indicationSearchFeatureFlagTypes) {
      featureFlags[flag] =
        flagsState.getFlagValue(featureFlagDefaults[flag].key) ??
        featureFlagDefaults[flag].default;
    }

    return featureFlags as IndicationSearchFeatureFlags;
  }

  private mapToIndicationNode(
    nodes: SearchHit<IndicationDoc>[],
    matchedNodeIds: string[] = []
  ): IndicationNode[] {
    return nodes.map((node) => {
      return {
        id: node._id,
        h1Id: node._source!.h1_id,
        parentH1Ids: node._source!.parent_h1id ?? [],
        indicationName: node._source!.indication_name,
        indicationType: node._source!.indication_type,
        matchedSynonyms:
          node.inner_hits?.synonyms.hits.hits.map((hit) => hit._source!.saut) ||
          [],
        displayName: node._source?.display_name,
        description: node._source?.description,
        hcpCommunitySize: node._source?.community_size,
        claimCount: node._source?.claim_count,
        patientCount: node._source?.patient_count,
        icdCodes: node._source?.icd_codes || [],
        match: matchedNodeIds.includes(node._id),
        children: []
      };
    });
  }

  private buildTrees(
    groupedNodes: Dictionary<IndicationNode[]>,
    rootId: string
  ) {
    let nodesToExpand: IndicationNode[] = [...(groupedNodes[rootId] || [])];

    while (nodesToExpand.length) {
      const tmpNodesToExpand: IndicationNode[] = [];
      nodesToExpand.forEach((node) => {
        const nodeId = node.id;
        node.children = groupedNodes[nodeId] || [];
        tmpNodesToExpand.push(...node.children);
      });
      nodesToExpand = tmpNodesToExpand;
    }
    return groupedNodes[rootId];
  }

  private buildTreeFromIndicationDocs(
    docs: SearchHit<IndicationDoc>[],
    rootId: string,
    matchedNodeIds: string[]
  ) {
    // TODO: Sort based on count
    const grouped_nodes = mapValues(
      groupBy(
        docs,
        (doc) => last(doc._source!.ancestor_ids) || PARENT_ID_FOR_ROOT_NODES
      ),
      (docGroup) => this.mapToIndicationNode(docGroup, matchedNodeIds)
    );
    return this.buildTrees(grouped_nodes, rootId);
  }

  private sortTrees(
    nodes: IndicationNode[] | undefined,
    countKey: IndicationSortBy
  ): IndicationNode[] {
    if (!nodes || nodes.length === 0) {
      return [];
    }

    nodes.forEach((node) => {
      node.children = this.sortTrees(node.children, countKey);
      if (node.children.length && !node[countKey]) {
        node[countKey] = sumBy(node.children, countKey);
      }
    });

    return orderBy(nodes, [countKey], ["desc"]);
  }

  async searchRootIndications(
    input: SearchRootIndicationsInput
  ): Promise<IndicationNode[]> {
    const { size, sortBy, indicationSource, institutionId } = input;
    this.logger.info({ input }, "searchRootIndications input");

    if (indicationSource.length) {
      const request = this.buildQueryToGetAllRootIndications(
        indicationSource,
        sortBy,
        size
      );

      let response;
      try {
        response =
          await this.elasticSearchIndicationsTreeService.query<IndicationDoc>(
            request
          );
      } catch (error) {
        this.logger.error({ error }, "searchRootIndications");

        throw new Error((error as errors.ResponseError).message);
      }

      let indicationToCountMapForInstitution: Map<string, number>,
        indicationIcdCodesToCountMapForInstitution: Map<string, number>;
      if (institutionId) {
        [
          indicationToCountMapForInstitution,
          indicationIcdCodesToCountMapForInstitution
        ] = await this.createMapsForIndicationToInstitutionCountFromPipelineDb(
          response.hits.hits,
          institutionId
        );
      }
      let matchedNodeHitsWithPipelineCountsIfAvailable = response.hits.hits;
      if (institutionId) {
        matchedNodeHitsWithPipelineCountsIfAvailable =
          this.replaceAndResortHitsBasedOnInstitutionCounts(
            response.hits?.hits,
            indicationToCountMapForInstitution!,
            indicationIcdCodesToCountMapForInstitution!
          );
      }

      const indications: IndicationNode[] = this.mapToIndicationNode(
        matchedNodeHitsWithPipelineCountsIfAvailable
      );

      this.logger.info(
        { request, indications },
        "searchRootIndications indications"
      );

      if (!indications.length) {
        this.searchAnalyticsTracerService.sendAnalyticsEvent({
          event: "indication_search.zero_result.searchRootIndications",
          properties: {
            ...input
          },
          timestamp: new Date()
        });
      }
      return indications;
    }

    return [];
  }

  async searchIndicationsByQuery(
    input: SearchIndicationsByQueryInput
  ): Promise<IndicationNode[]> {
    const {
      query,
      h1Ids,
      rootId,
      size,
      indicationSource,
      indicationType,
      sortBy,
      institutionId,
      includeDuplicates
    } = input;
    const indicationSearchFeatureFlags = await this.getFeatureFlagValues(input);
    this.logger.info({ input }, "searchIndicationsByQuery input");
    const largerSizeIfInstitutionIdPresent = institutionId
      ? DEFAULT_SIZE_FOR_INSTITUTIONS
      : size;

    let collapseType: SearchFieldCollapse | undefined =
      collapseOnIndicationName;
    if (includeDuplicates) {
      collapseType = doNotCollapse;
    }
    if (indicationSource.length) {
      const request = this.buildQueryToGetIndicationsByUserQuery(
        query,
        largerSizeIfInstitutionIdPresent,
        indicationSource,
        indicationSearchFeatureFlags,
        collapseType,
        indicationType,
        sortBy,
        rootId,
        h1Ids
      );

      let response;
      try {
        response =
          await this.elasticSearchIndicationsTreeService.query<IndicationDoc>(
            request
          );
      } catch (error) {
        this.logger.error({ error }, "searchIndicationsByQuery");

        throw new Error((error as errors.ResponseError).message);
      }
      let indicationToCountMapForInstitution: Map<string, number>,
        indicationIcdCodesToCountMapForInstitution: Map<string, number>;
      if (institutionId) {
        [
          indicationToCountMapForInstitution,
          indicationIcdCodesToCountMapForInstitution
        ] = await this.createMapsForIndicationToInstitutionCountFromPipelineDb(
          response.hits.hits,
          institutionId
        );
      }
      let matchedNodeHitsWithPipelineCountsIfAvailable = response.hits.hits;
      if (institutionId) {
        matchedNodeHitsWithPipelineCountsIfAvailable =
          this.replaceAndResortHitsBasedOnInstitutionCounts(
            response.hits?.hits,
            indicationToCountMapForInstitution!,
            indicationIcdCodesToCountMapForInstitution!
          );
      }
      const indications: IndicationNode[] = this.mapToIndicationNode(
        matchedNodeHitsWithPipelineCountsIfAvailable
      );

      this.logger.info(
        { request, indications },
        "searchIndicationsByQuery indications"
      );

      const slicedIndications = indications.slice(0, size);
      if (!slicedIndications.length) {
        this.searchAnalyticsTracerService.sendAnalyticsEvent({
          event: "indication_search.zero_result.searchIndicationsByQuery",
          properties: {
            ...input
          },
          timestamp: new Date()
        });
      }

      return slicedIndications;
    }
    return [];
  }

  async getSubTreesForRoot(input: IndicationsGetSubTreesInput) {
    const { rootId, indicationSource, sortBy, institutionId } = input;
    this.logger.info({ input });
    if (indicationSource.length) {
      const request = this.buildQueryToGetAllChildNodes(
        rootId,
        indicationSource
      );

      let response;
      try {
        response =
          await this.elasticSearchIndicationsTreeService.query<IndicationDoc>(
            request
          );
      } catch (error) {
        this.logger.error({ error }, "getSubTreesForRoot");
        throw new Error((error as errors.ResponseError).message);
      }

      let indicationToCountMapForInstitution: Map<string, number>,
        indicationIcdCodesToCountMapForInstitution: Map<string, number>;
      if (institutionId) {
        [
          indicationToCountMapForInstitution,
          indicationIcdCodesToCountMapForInstitution
        ] = await this.createMapsForIndicationToInstitutionCountFromPipelineDb(
          response.hits.hits,
          institutionId
        );
      }
      let matchedNodeHitsWithPipelineCountsIfAvailable = response.hits.hits;
      if (institutionId) {
        matchedNodeHitsWithPipelineCountsIfAvailable =
          this.replaceAndResortHitsBasedOnInstitutionCounts(
            response.hits?.hits,
            indicationToCountMapForInstitution!,
            indicationIcdCodesToCountMapForInstitution!
          );
      }

      const subTrees = this.buildTreeFromIndicationDocs(
        matchedNodeHitsWithPipelineCountsIfAvailable,
        input.rootId,
        []
      );

      const sortedIndications = this.sortTrees(subTrees, sortBy);

      if (!sortedIndications.length) {
        this.searchAnalyticsTracerService.sendAnalyticsEvent({
          event: "indication_search.zero_result.getSubTreesForRoot",
          properties: {
            ...input
          },
          timestamp: new Date()
        });
      }

      return sortedIndications;
    }

    return [];
  }

  async searchIndicationTreesByQuery(input: SearchIndicationTreesByQueryInput) {
    const {
      query,
      h1Ids,
      indicationSource,
      sortBy,
      indicationType,
      size,
      institutionId
    } = input;
    this.logger.info({ input }, "searchIndicationTreesByQuery input");

    if (indicationSource.length) {
      const indicationSearchFeatureFlags = await this.getFeatureFlagValues(
        input
      );

      const request = this.buildQueryToGetIndicationsByUserQuery(
        query,
        size || DEFAULT_MATCH_NODES_TO_RETURN,
        indicationSource,
        indicationSearchFeatureFlags,
        doNotCollapse,
        indicationType,
        sortBy,
        undefined,
        h1Ids
      );

      this.logger.info(
        { request },
        "buildQueryToGetIndicationsByUserQuery es request"
      );

      let response;
      try {
        response =
          await this.elasticSearchIndicationsTreeService.query<IndicationDoc>(
            request
          );
      } catch (error) {
        this.logger.error({ error }, "searchIndicationTreesByQuery");

        throw new Error((error as errors.ResponseError).message);
      }

      const matchedNodeHits: SearchHit<IndicationDoc>[] = response.hits.hits;

      if (!matchedNodeHits.length) {
        this.searchAnalyticsTracerService.sendAnalyticsEvent({
          event:
            "indication_search.zero_result.searchIndicationTreesByQuery.matched_nodes",
          properties: {
            ...input
          },
          timestamp: new Date(),
          userId: input.userId
        });
        return [];
      }
      let indicationToCountMapForInstitution: Map<string, number>,
        indicationIcdCodesToCountMapForInstitution: Map<string, number>;
      if (institutionId) {
        [
          indicationToCountMapForInstitution,
          indicationIcdCodesToCountMapForInstitution
        ] = await this.createMapsForIndicationToInstitutionCountFromPipelineDb(
          response.hits.hits,
          institutionId
        );
      }

      let matchedNodeHitsWithPipelineCounts = matchedNodeHits;
      if (institutionId) {
        matchedNodeHitsWithPipelineCounts =
          this.replaceAndResortHitsBasedOnInstitutionCounts(
            response.hits?.hits,
            indicationToCountMapForInstitution!,
            indicationIcdCodesToCountMapForInstitution!
          );
      }

      const matchedNodeIds = matchedNodeHitsWithPipelineCounts.map(
        (hit) => hit._id
      );
      const hasNonIcdMatch = matchedNodeHitsWithPipelineCounts.some(
        (hit) => hit._source?.indication_type !== IndicationType.ICD
      );
      const ancestorIds = difference(
        uniq([
          ...flatten(
            matchedNodeHitsWithPipelineCounts.map(
              (hit) => hit._source!.ancestor_ids
            )
          )
        ]),
        matchedNodeIds
      );

      this.logger.info({ ancestorIds, matchedNodeIds, hasNonIcdMatch });

      let ancestorAndDescendantNodes: SearchHit<IndicationDoc>[] = [];

      if (ancestorIds.length || hasNonIcdMatch) {
        const requestToGetNodesByIds =
          this.buildQueryToGetTreeNodesByAncestorAndParentIds(
            ancestorIds,
            matchedNodeIds
          );
        let response;
        try {
          response =
            await this.elasticSearchIndicationsTreeService.query<IndicationDoc>(
              requestToGetNodesByIds
            );
        } catch (error) {
          this.logger.error({ error }, "searchIndicationTreesByQuery");

          throw new Error((error as errors.ResponseError).message);
        }

        this.logger.info(
          { requestToGetNodesByIds },
          "buildQueryToGetTreeNodesByAncestorAndParentIds es request"
        );

        let indicationToCountMapForInstitution: Map<string, number>,
          indicationIcdCodesToCountMapForInstitution: Map<string, number>;
        if (institutionId) {
          [
            indicationToCountMapForInstitution,
            indicationIcdCodesToCountMapForInstitution
          ] =
            await this.createMapsForIndicationToInstitutionCountFromPipelineDb(
              response.hits.hits,
              institutionId
            );
        }
        ancestorAndDescendantNodes = !institutionId
          ? response.hits.hits
          : this.replaceAndResortHitsBasedOnInstitutionCounts(
              response.hits?.hits,
              indicationToCountMapForInstitution!,
              indicationIcdCodesToCountMapForInstitution!
            );
      }

      const nodes = [
        ...matchedNodeHitsWithPipelineCounts,
        ...ancestorAndDescendantNodes
      ];
      const trees = this.buildTreeFromIndicationDocs(
        nodes,
        PARENT_ID_FOR_ROOT_NODES,
        matchedNodeIds
      );

      const sortedIndications = this.sortTrees(trees, sortBy);

      if (!sortedIndications.length) {
        this.searchAnalyticsTracerService.sendAnalyticsEvent({
          event: "indication_search.zero_result.searchIndicationTreesByQuery",
          properties: {
            ...input
          },
          timestamp: new Date(),
          userId: input.userId
        });
      }

      this.logger.info({ sortedIndications }, "sortedIndications");

      return sortedIndications;
    }

    return [];
  }

  replaceAndResortHitsBasedOnInstitutionCounts(
    hits: SearchHit<IndicationDoc>[] | undefined,
    indicationCountMap: Map<string, number>,
    icdCountMap: Map<string, number>
  ): SearchHit<IndicationDoc>[] {
    if (hits) {
      let replacedHits: SearchHit<IndicationDoc>[] = hits
        .filter((hit: SearchHit<IndicationDoc>) => hit._source !== undefined)
        .map((hit: SearchHit<IndicationDoc>) => {
          const newHit: SearchHit<IndicationDoc> = {
            ...hit
          };
          const isICDcode = hit._source?.indication_type === IndicationType.ICD;
          const patientCount = !isICDcode
            ? Number(indicationCountMap.get(newHit._source!.indication_name))
            : Number(icdCountMap.get(newHit._source!.indication_name));
          newHit._source!.patient_count = !isNaN(patientCount)
            ? patientCount
            : 0;

          if (newHit._source!.icd_codes !== undefined) {
            newHit._source!.icd_codes = newHit
              ._source!.icd_codes.map((code: IcdCode) => {
                const icdPatientCount = Number(icdCountMap.get(code.icdCode));
                const newCode = {
                  ...code,
                  patientCount: !isNaN(icdPatientCount) ? icdPatientCount : 0
                };

                return newCode;
              })
              .filter((code: IcdCode) => code.patientCount > 0); // Filter out icd_codes with patientCount of 0

            newHit._source!.icd_codes.sort(
              (a: IcdCode, b: IcdCode) => b.patientCount - a.patientCount
            );
          }

          return newHit;
        });

      // Filter out IndicationDocs with patient_count of 0
      replacedHits = replacedHits.filter(
        (hit) => hit._source!.patient_count! > 0
      );

      return replacedHits.sort(
        (a: SearchHit<IndicationDoc>, b: SearchHit<IndicationDoc>) =>
          b._source!.patient_count! - a._source!.patient_count!
      );
    }

    return [];
  }

  private async getIndicationCountsFromPipelineDBforInstitution(
    institutionId: string,
    indications: string[]
  ): Promise<IndicationInstitutionCounts[]> {
    return await this.indicationsInstitutionCountsRepository.getCountsByIndicationsAndInstitutionId(
      institutionId,
      indications
    );
  }

  private async getIndicationIcdCountsFromPipelineDBforInstitution(
    institutionId: string,
    indications: string[]
  ): Promise<IndicationIcdInstitutionCounts[]> {
    return await this.indicationIcdInstitutionCountsRepository.getCountsByIndicationsAndInstitutionId(
      institutionId,
      indications
    );
  }

  getAllIcdCodes(response: SearchHit<IndicationDoc>[]): string[] {
    const icdCodes: string[] = [];

    if (response) {
      response.forEach((hit: SearchHit<IndicationDoc>) => {
        if (
          hit._source?.indication_type === IndicationType.ICD &&
          hit._source?.indication_name
        ) {
          icdCodes.push(hit._source?.indication_name);
        }
        if (hit._source?.icd_codes) {
          hit._source.icd_codes.forEach((icdCode: IcdCode) => {
            icdCodes.push(icdCode.icdCode);
          });
        }
      });
    }

    return icdCodes;
  }

  private async createMapsForIndicationToInstitutionCountFromPipelineDb(
    matchedHits: SearchHit<IndicationDoc>[],
    institutionId: string
  ) {
    const indicationListForPipeline = matchedHits.map(
      (indicationHit) => indicationHit._source!.indication_name!
    );
    const indicationIcdListForPipeline = this.getAllIcdCodes(matchedHits);
    const indicationIcdCountsFromPipelineDbPromise: Promise<
      IndicationIcdInstitutionCounts[]
    > =
      indicationIcdListForPipeline.length > 0
        ? this.getIndicationIcdCountsFromPipelineDBforInstitution(
            institutionId,
            indicationIcdListForPipeline
          )
        : Promise.resolve([]);
    const indicationCountsFromPipelineDbPromise: Promise<
      IndicationInstitutionCounts[]
    > =
      indicationListForPipeline.length > 0
        ? this.getIndicationCountsFromPipelineDBforInstitution(
            institutionId,
            indicationListForPipeline
          )
        : Promise.resolve([]);
    const [indicationIcdCountsFromPipelineDb, indicationCountsFromPipelineDb] =
      await Promise.all([
        indicationIcdCountsFromPipelineDbPromise,
        indicationCountsFromPipelineDbPromise
      ]);
    const indicationToCountMapForInstitution: Map<string, number> = new Map();
    const indicationIcdCodesToCountMapForInstitution: Map<string, number> =
      new Map();
    indicationCountsFromPipelineDb.forEach(
      (indicationMatch: IndicationInstitutionCounts) =>
        indicationToCountMapForInstitution.set(
          indicationMatch.indication,
          indicationMatch.count
        )
    );
    indicationIcdCountsFromPipelineDb.forEach(
      (indicationMatch: IndicationIcdInstitutionCounts) =>
        indicationIcdCodesToCountMapForInstitution.set(
          indicationMatch.icdCode,
          indicationMatch.count
        )
    );
    return [
      indicationToCountMapForInstitution,
      indicationIcdCodesToCountMapForInstitution
    ];
  }
}
