import { Service } from "typedi";
import { QueryUnderstandingServiceClient } from "./QueryUnderstandingServiceClient";
import {
  ExtractedEntitiesV2,
  KeywordSearchInput,
} from "@h1nyc/search-sdk";
import { createLogger } from "../lib/Logger";
import {
  getLanguageDetector,
  KeywordSearchFeatureFlags
} from "./KeywordSearchResourceServiceRewrite";
import { estypes } from "@elastic/elasticsearch";
import { ElasticSearchService } from "./ElasticSearchService";
import _ from "lodash";
import { ConfigService } from "./ConfigService";
import { ErrorResponseBase } from "@elastic/elasticsearch/lib/api/types";

type MSearchRequest = [
  estypes.MsearchMultisearchHeader,
  estypes.MsearchMultisearchBody
];


const HEADER: Readonly<estypes.MsearchMultisearchHeader> = {};
export const ELASTICSEARCH_FIELD_MAPPING: Record<string, { path?: string; field: string; isNested: boolean }> = {
  "locations.country": {
    path: "addressesForHCPU",
    field: "addressesForHCPU.country",
    isNested: true
  },
  "locations.state": {
    path: "addressesForHCPU",
    field: "addressesForHCPU.region",
    isNested: true
  },
  "locations.city": {
    path: "addressesForHCPU",
    field: "addressesForHCPU.city",
    isNested: true
  },
  "claims.diagnoses": {
    path: "DRG_diagnoses",
    field: "DRG_diganoses.codeAndDescription_eng",
    isNested: true
  },
  "claims.procedures": {
    path: "DRG_procedures",
    field: "DRG_procedures.codeAndDescription_eng",
    isNested: true
  },
  specialty: {
    field: "specialty_eng",
    isNested: false
  },
  work_institution: {
    field: "presentWorkInstitutionNames_eng",
    isNested: false
  }
}

@Service()
export class EntitySearchProcessorService {
  private readonly logger = createLogger(this);
  private peopleIndexName: string;

  constructor(
    config: ConfigService,
    private queryUnderstandingServiceClient: QueryUnderstandingServiceClient,
    private elasticService: ElasticSearchService
  ) {
    this.peopleIndexName = config.elasticPeopleIndex
  }

  /**
   * Process a search query through entity detection and modify the input accordingly
   * - If searchFor is "people", execute the search
   * - Any detected indication will be added to query
   * - rankBy columns will be weighted in sortBy field
   * - Institution names will be applied as institution filters
   * - Location filters will be applied as is
   */
  async processSearchWithEntityDetection(
    input: KeywordSearchInput,
    keywordSearchFeatureFlags: KeywordSearchFeatureFlags
  ): Promise<{
    shouldExecuteSearch: boolean;
    modifiedInput: KeywordSearchInput;
    detectedEntities?: ExtractedEntitiesV2;
  }> {
    const advancedOperatorExists =
      input.query?.includes("OR") || input.query?.includes("AND");
    const bothOperatorExists = input.query?.includes("BOTH");
    const lesserThan2Words = !!input.query && input.query.split(" ").length < 2;
    if (
      !keywordSearchFeatureFlags.enableLlmQueryParserForSearch ||
      !input.query ||
      advancedOperatorExists ||
      bothOperatorExists ||
      lesserThan2Words
    ) {
      return {
        shouldExecuteSearch: true,
        modifiedInput: input,
        detectedEntities: undefined
      };
    }
    try {
      const detectedEntitiesV2 = await this.extractEntitiesFromQueryV2(
        input.query,
        input.language || "EN"
      );

      this.logger.info({ detectedEntitiesV2 }, "Entities from LLM");

      console.log("YASH: LLM", JSON.stringify(detectedEntitiesV2));

      const entitiesFieldMap = this.createFieldMapFromEntities(detectedEntitiesV2);
      const msearchPayload = this.buildMsearchPayload(entitiesFieldMap);

      const { took, responses }: estypes.MsearchResponse = await this.elasticService.msearch(msearchPayload);

      console.log("YASH: msearch responses", JSON.stringify(responses));

      this.logger.info({ took: took }, "msearch execution time");

      const newEntitiesV2 = this.applyMSearchResults(detectedEntitiesV2, entitiesFieldMap, responses);

      console.log("YASH: msearch", JSON.stringify(newEntitiesV2));

      this.logger.info({ newEntitiesV2 }, "Entities after msearch");

      const modifiedInput: KeywordSearchInput = {
        ...input,
        suppliedFilters: { ...input.suppliedFilters },
        sortBy: { ...input.sortBy }
      };

      // Apply detected entities v2 to the search input
      this.applyDetectedEntitiesV2(modifiedInput, newEntitiesV2);

      // Only execute search if searchFor has value 'people'
      const shouldExecuteSearch =
        newEntitiesV2.search_for.toLowerCase() === "people";
      if (shouldExecuteSearch && !newEntitiesV2.indication.length) {
        modifiedInput.query = "";
      }
      return {
        shouldExecuteSearch,
        modifiedInput,
        detectedEntities: newEntitiesV2
      };
    } catch (error) {
      this.logger.error(`Error in processSearchWithEntityDetection: ${error}`);

      // If there's an error, return the original input but still execute the search
      return {
        shouldExecuteSearch: true,
        modifiedInput: input,
        detectedEntities: this.buildDefaultEntitiesV2()
      };
    }
  }

  private buildDefaultEntitiesV2(): ExtractedEntitiesV2 {
    const defaultResult: ExtractedEntitiesV2 = {
      search_for: "",
      indication: [],
      locations: {
        country: [],
        state: [],
        city: []
      },
      claims: {
        diagnoses: [],
        min_diagnoses_claim_count: null,
        max_diagnoses_claim_count: null,
        procedures: [],
        min_procedures_claim_count: null,
        max_procedures_claim_count: null,
        timeframe: null
      },
      specialty: [],
      trial: {
        min_count: null,
        max_count: null,
        status: null,
        phase: null
      },
      work_institution: []
    };

    return defaultResult;
  }

  private async extractEntitiesFromQueryV2(
    query: string,
    language: string
  ): Promise<ExtractedEntitiesV2> {
    // Default fallback
    const defaultResult: ExtractedEntitiesV2 = this.buildDefaultEntitiesV2();
    if (!query) {
      return defaultResult;
    }

    const languageDetector = getLanguageDetector(language);
    const languageCode = languageDetector(query);
    const queryUnderstandingResponse =
      await this.queryUnderstandingServiceClient.getEntitiesForQuery(
        query,
        languageCode
      );

    const entities = queryUnderstandingResponse.getEntitiesV2();
    if (!entities) return defaultResult;

    const result: ExtractedEntitiesV2 = {
      search_for: (entities.getSearchFor() ?? "") as ExtractedEntitiesV2["search_for"],
      indication: entities.getIndicationList() ?? [],
      locations: {
        country: entities.getLocations()?.getCountryList() ?? [],
        state: entities.getLocations()?.getStateList() ?? [],
        city: entities.getLocations()?.getCityList() ?? []
      },
      claims: {
        diagnoses: entities.getClaims()?.getDiagnosesList() ?? [],
        min_diagnoses_claim_count: entities.getClaims()?.getMinDiagnosesClaimCount() ?? null,
        max_diagnoses_claim_count: entities.getClaims()?.getMaxDiagnosesClaimCount() ?? null,

        procedures: entities.getClaims()?.getProceduresList() ?? [],
        min_procedures_claim_count: entities.getClaims()?.getMinProceduresClaimCount() ?? null,
        max_procedures_claim_count: entities.getClaims()?.getMaxProceduresClaimCount() ?? null,

        timeframe: (entities.getClaims()?.getTimeframe() ?? null) as ExtractedEntitiesV2["claims"]["timeframe"]
      },
      specialty: entities.getSpecialtyList() ?? [],
      trial: {
        min_count: entities.getTrial()?.getMinCount() ?? null,
        max_count: entities.getTrial()?.getMaxCount() ?? null,
        status: (entities.getTrial()?.getStatus() ?? null) as ExtractedEntitiesV2["trial"]["status"],
        phase: (entities.getTrial()?.getPhase() ?? null) as ExtractedEntitiesV2["trial"]["phase"]
      },
      work_institution: entities.getWorkInstitutionList() ?? []
    }

    return result;
  }


  private buildMsearchPayload(fieldMap: Record<
    string,
    { field: string; path?: string; isNested: boolean; value: string }
  >): estypes.MsearchRequest {
    const queries: MSearchRequest[] = []

    for (const { field, path, isNested, value } of Object.values(fieldMap)) {
      if (isNested) {
        queries.push(this.buildNestedQuery(path!, field, value))
      } else {
        queries.push(this.buildNonNestedQuery(field, value))
      }
    }
    const multiSearchRequest: estypes.MsearchRequest = {
      index: this.peopleIndexName,
      searches: queries.flat()
    }

    return multiSearchRequest;
  }

  private createFieldMapFromEntities(entities: ExtractedEntitiesV2): Record<
    string,
    { field: string; path?: string; isNested: boolean; value: string }
  > {
    const fieldsToSearch: Record<
      string,
      { field: string; path?: string; isNested: boolean; value: string }
    > = {};

    for (const [key, { field, path, isNested }] of Object.entries(ELASTICSEARCH_FIELD_MAPPING)) {
      const oldValues = (_.get(entities, key) as string[]) ?? [];
      if (_.isEmpty(oldValues)) continue;

      for (const [idx, value] of oldValues.entries()) {
        fieldsToSearch[`${key}_${idx}`] = {
          field,
          path,
          isNested,
          value
        }
      }
    }

    return fieldsToSearch;
  }

  private buildNestedQuery(path: string, field: string, valueToSearch: string): MSearchRequest {
    return [
      HEADER,
      {
        size: 1,
        _source: false,
        query: {
          nested: {
            path: path,
            query: {
              match: {
                [field]: {
                  query: valueToSearch,
                  fuzziness: "AUTO"
                }
              }
            },
            inner_hits: {
              _source: field
            }
          }
        }
      }
    ]
  }

  private buildNonNestedQuery(field: string, valueToSearch: string): MSearchRequest {
    return [
      HEADER,
      {
        _source: field,
        size: 1,
        query: {
          match: {
            [field]: {
              query: valueToSearch,
              fuzziness: "AUTO"
            }
          }
        }
      }
    ]
  }

  private applyMSearchResults(
    originalEntities: ExtractedEntitiesV2,
    fieldMap: Record<
      string,
      { field: string; path?: string; isNested: boolean; value: string }
    >,
    msearchResponses: estypes.MsearchResponseItem[]
  ): ExtractedEntitiesV2 {
    const newEntities = _.cloneDeep(originalEntities);
    newEntities.locations = {
      country: [],
      state: [],
      city: []
    }
    newEntities.claims.diagnoses = [];
    newEntities.claims.procedures = [];
    newEntities.specialty = [];
    newEntities.work_institution = [];

    let msearchResponsesIdx = 0;
    for (const [key, { field, isNested, path }] of Object.entries(fieldMap)) {
      const keyParts: string[] = key.split("_");
      const objPath = keyParts[0];
      const msearchResponse = msearchResponses[msearchResponsesIdx++];
      if (this.isErrorResponse(msearchResponse)) {
        this.logger.error(`Error in msearch response for ${key}`, msearchResponse);
        continue;
      }
      if (isNested) {
        const hits = msearchResponse.hits.hits;
        const sourcePath = field.split(".").pop();
        if (_.isEmpty(hits)) continue;
        if (!hits[0]?.inner_hits) continue;
        const innerHits = hits[0]?.inner_hits[path!]?.hits.hits;
        if (_.isEmpty(innerHits)) continue;
        const values = innerHits.map((hit) => hit._source[sourcePath!]);
        const existing = _.get(newEntities, objPath) as string[] | undefined;
        const updated = [...(existing ?? []), ...values];
        _.set(newEntities, objPath, _.uniq(updated));
      } else {
        const hits = msearchResponse.hits.hits;
        if (_.isEmpty(hits)) continue;
        const values = hits.map((hit: any) => hit._source[field] as string[]);
        const flattenedValues = values.flat();
        const existing = _.get(newEntities, objPath) as string[] | undefined;
        const updated = [...(existing ?? []), ...flattenedValues];
        _.set(newEntities, objPath, _.uniq(updated));
      }
    }

    return newEntities;
  }

  private isErrorResponse(
    response: Readonly<estypes.MsearchResponseItem>
  ): response is ErrorResponseBase {
    return !!(response as ErrorResponseBase).error;
  }

  private applyDetectedEntitiesV2(
    input: KeywordSearchInput,
    entities: ExtractedEntitiesV2
  ): void {
    // Apply indication as a filter
    if (!_.isEmpty(entities.indication)) {
      input.query = entities.indication[0];
    }

    // Apply location filters
    if (!_.isEmpty(entities.locations.country)) {
      input.suppliedFilters.country = { values: entities.locations.country };
    }
    if (!_.isEmpty(entities.locations.state)) {
      input.suppliedFilters.state = { values: entities.locations.state };
    }
    if (!_.isEmpty(entities.locations.city)) {
      input.suppliedFilters.city = { values: entities.locations.city };
    }

    //apply claim filters
    if (!_.isEmpty(entities.claims.diagnoses)) {
      input.suppliedFilters.claims.diagnosesICD = { values: entities.claims.diagnoses };
    }
    if (!_.isEmpty(entities.claims.procedures)) {
      input.suppliedFilters.claims.proceduresCPT = { values: entities.claims.procedures };
    }
    if (entities.claims.min_diagnoses_claim_count) {
      input.suppliedFilters.claims.diagnosesICDMinCount = { value: entities.claims.min_diagnoses_claim_count };
    }
    if (entities.claims.min_procedures_claim_count) {
      input.suppliedFilters.claims.proceduresCPTMinCount = { value: entities.claims.min_procedures_claim_count };
    }
    if (entities.claims.max_diagnoses_claim_count) {
      input.suppliedFilters.claims.diagnosesICDMaxCount = { value: entities.claims.max_diagnoses_claim_count };
    }
    if (entities.claims.max_procedures_claim_count) {
      input.suppliedFilters.claims.proceduresCPTMaxCount = { value: entities.claims.max_procedures_claim_count };
    }
    if (entities.claims.timeframe) {
      input.suppliedFilters.claims.timeFrame = { value: Number(entities.claims.timeframe) }
    }

    //turn off showunique patients as it does not work with entity search
    input.suppliedFilters.claims.showUniquePatients = { value: false };

    //apply specialty filters
    if (!_.isEmpty(entities.specialty)) {
      input.suppliedFilters.specialty = { values: entities.specialty };
    }

    //apply trial filters
    if (entities.trial.min_count) {
      input.suppliedFilters.trials.minCount = { value: entities.trial.min_count }
    }
    if (entities.trial.max_count) {
      input.suppliedFilters.trials.maxCount = { value: entities.trial.max_count }
    }
    if (entities.trial.status) {
      input.suppliedFilters.trials.status = { values: [entities.trial.status] }
    }
    if (entities.trial.phase) {
      input.suppliedFilters.trials.phase = { values: [entities.trial.phase] }
    }
  }
}
