import { Service } from "typedi";
import { QueryUnderstandingServiceClient } from "./QueryUnderstandingServiceClient";
import {
  ExtractedEntities,
  ExtractedEntityLocations,
  KeywordSearchInput,
  FilterInterface,
  WeightedSortBy
} from "@h1nyc/search-sdk";
import { createLogger } from "../lib/Logger";
import {
  getLanguageDetector,
  KeywordSearchFeatureFlags
} from "./KeywordSearchResourceServiceRewrite";

@Service()
export class EntitySearchProcessorService {
  private readonly logger = createLogger(this);

  constructor(
    private queryUnderstandingServiceClient: QueryUnderstandingServiceClient
  ) {}

  /**
   * Process a search query through entity detection and modify the input accordingly
   * - If searchFor is "people", execute the search
   * - Any detected indication will be added to query
   * - rankBy columns will be weighted in sortBy field
   * - Institution names will be applied as institution filters
   * - Location filters will be applied as is
   */
  async processSearchWithEntityDetection(
    input: KeywordSearchInput,
    keywordSearchFeatureFlags: KeywordSearchFeatureFlags
  ): Promise<{
    shouldExecuteSearch: boolean;
    modifiedInput: KeywordSearchInput;
    detectedEntities?: ExtractedEntities;
  }> {
    const advancedOperatorExists =
      input.query?.includes("OR") || input.query?.includes("AND");
    const bothOperatorExists = input.query?.includes("BOTH");
    const lesserThan5Words = !!input.query && input.query.split(" ").length < 5;
    if (
      !keywordSearchFeatureFlags.enableLlmQueryParserForSearch ||
      !input.query ||
      advancedOperatorExists ||
      bothOperatorExists ||
      lesserThan5Words
    ) {
      return {
        shouldExecuteSearch: true,
        modifiedInput: input,
        detectedEntities: undefined
      };
    }
    try {
      const detectedEntities = await this.extractEntitiesFromQuery(
        input.query,
        input.language || "EN"
      );

      const modifiedInput: KeywordSearchInput = {
        ...input,
        suppliedFilters: { ...input.suppliedFilters },
        sortBy: { ...input.sortBy }
      };

      // Apply detected entities to the search input
      this.applyDetectedEntities(modifiedInput, detectedEntities);

      // Only execute search if searchFor has value 'people'
      const shouldExecuteSearch =
        detectedEntities.searchFor.toLowerCase() === "people";
      if (shouldExecuteSearch && !detectedEntities.indication.length) {
        modifiedInput.query = "";
      }
      return {
        shouldExecuteSearch,
        modifiedInput,
        detectedEntities
      };
    } catch (error) {
      this.logger.error(`Error in processSearchWithEntityDetection: ${error}`);

      // If there's an error, return the original input but still execute the search
      return {
        shouldExecuteSearch: true,
        modifiedInput: input,
        detectedEntities: {
          searchFor: "",
          indication: "",
          rankBy: ""
        }
      };
    }
  }

  private async extractEntitiesFromQuery(
    query: string,
    language: string
  ): Promise<ExtractedEntities> {
    if (!query) {
      return {
        searchFor: "",
        indication: "",
        rankBy: ""
      };
    }

    const languageDetector = getLanguageDetector(language);
    const languageCode = languageDetector(query);
    const queryUnderstandingResponse =
      await this.queryUnderstandingServiceClient.getEntitiesForQuery(
        query,
        languageCode
      );

    const entities = queryUnderstandingResponse.getEntities();

    const extractedEntities: ExtractedEntities = {
      searchFor: entities ? entities.getSearchFor() : "",
      indication: entities ? entities.getIndication() : "",
      rankBy: entities ? entities.getRankBy() : ""
    };

    if (entities && entities.hasNames()) {
      const names = entities.getNames();
      extractedEntities.names = {
        people: names ? names.getPeople() : "",
        institutions: names ? names.getInstitutions() : ""
      };
    }

    if (entities && entities.hasLocations()) {
      const locations = entities.getLocations();
      extractedEntities.locations = {
        country: locations ? locations.getCountry() : "",
        state: locations ? locations.getState() : "",
        city: locations ? locations.getCity() : "",
        zipcode: locations ? locations.getZipcode() : ""
      };
    }

    return extractedEntities;
  }

  private applyDetectedEntities(
    input: KeywordSearchInput,
    entities: ExtractedEntities
  ): void {
    // Apply indication as a filter
    if (entities.indication) {
      input.query = entities.indication;
    }

    // Apply rank by to sort weights
    if (entities.rankBy) {
      this.applyRankByToSortWeights(input.sortBy, entities.rankBy);
    }

    // Apply institution name as a filter
    if (entities.names?.institutions) {
      if (!input.suppliedFilters.institution) {
        input.suppliedFilters.institution = { values: [] };
      }

      // Add the institution if it doesn't already exist
      if (
        Array.isArray(input.suppliedFilters.institution.values) &&
        !input.suppliedFilters.institution.values.includes(
          entities.names.institutions
        )
      ) {
        input.suppliedFilters.institution.values.push(
          entities.names.institutions
        );
      }
    }

    // Apply location filters
    if (entities.locations) {
      this.applyLocationFilters(input.suppliedFilters, entities.locations);
    }
  }

  private applyRankByToSortWeights(
    sortBy: WeightedSortBy,
    rankBy: string
  ): void {
    // Reset all weights to 0
    const oldSortBy = { ...sortBy };
    Object.keys(sortBy).forEach((key) => {
      if (typeof sortBy[key as keyof WeightedSortBy] === "number") {
        (sortBy[key as keyof WeightedSortBy] as number) = 0;
      }
    });
    const rankByPriorities = rankBy.split(",").filter(Boolean);
    let weight = 1;
    for (const item of rankByPriorities) {
      // Apply weight based on the rankBy value
      switch (item.toLowerCase()) {
        case "publications":
          sortBy.publication = weight;
          break;
        case "trials":
          sortBy.trial = weight;
          break;
        case "congresses":
          sortBy.congress = weight;
          break;
        case "patients":
          sortBy.diagnoses = weight;
          break;
        default:
          // If not recognized, restore original weights
          Object.keys(oldSortBy).forEach((key) => {
            if (typeof oldSortBy[key as keyof WeightedSortBy] === "number") {
              (sortBy[key as keyof WeightedSortBy] as number) = oldSortBy[
                key as keyof WeightedSortBy
              ] as number;
            }
          });
          return;
      }
      weight = weight / 2;
    }
  }

  private applyLocationFilters(
    filters: FilterInterface,
    locations: ExtractedEntityLocations
  ): void {
    if (locations.country) {
      if (!filters.country) {
        filters.country = { values: [] };
      }
      if (
        Array.isArray(filters.country.values) &&
        !filters.country.values.includes(locations.country)
      ) {
        filters.country.values.push(locations.country);
      }
    }

    if (locations.state) {
      if (!filters.state) {
        filters.state = { values: [] };
      }
      if (
        Array.isArray(filters.state.values) &&
        !filters.state.values.includes(locations.state)
      ) {
        filters.state.values.push(locations.state);
      }
    }

    if (locations.city) {
      if (!filters.city) {
        filters.city = { values: [] };
      }
      if (
        Array.isArray(filters.city.values) &&
        !filters.city.values.includes(locations.city)
      ) {
        filters.city.values.push(locations.city);
      }
    }

    if (locations.zipcode) {
      if (!filters.zipCode) {
        filters.zipCode = { values: [] };
      }
      if (
        Array.isArray(filters.zipCode.values) &&
        !filters.zipCode.values.includes(locations.zipcode)
      ) {
        filters.zipCode.values.push(locations.zipcode);
      }
    }
  }
}
