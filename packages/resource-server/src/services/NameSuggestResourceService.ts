import {
  KolNameSuggestionInput,
  NameSuggestResource,
  RootSearchObject,
  PersonSearchResponse,
  PersonCard,
  FilterInterface,
  RPC_NAMESPACE_NAME_SUGGEST,
  FormattedTopAffiliations,
  FormattedTopAffiliationsH1dn,
  FormattedTopTLAffiliations
} from "@h1nyc/search-sdk";
import { RpcMethod, RpcResourceService, RpcService } from "@h1nyc/systems-rpc";
import { createLogger } from "../lib/Logger";
import { getNameSuggestBuilder } from "../lib/NameSuggestBuilder";
import { Trace } from "../Tracer";
import { ConfigService } from "./ConfigService";
import { ElasticSearchService } from "./ElasticSearchService";
import {
  ENGLISH,
  Language,
  LanguageDetectService
} from "./LanguageDetectService";
import { UserResourceClient } from "@h1nyc/account-sdk";
import { Inject, Service } from "typedi";
// import { generateFilters } from "./KeywordSearchResourceServiceRewrite.test";
import { NameSearchResourceServiceRewrite } from "./NameSearchResourceServiceRewrite";
import { NameSearchInput } from "@h1nyc/search-sdk/dist/interfaces/NameSearchInput";
import { FeatureFlagsService, LDUserInput } from "@h1nyc/systems-feature-flags";
import { AffiliationAdapterService } from "./AffiliationAdapterService";
import {
  InstitutionDocument,
  lookupInstitutions,
  NO_AFFILIATION,
  toAffiliation
} from "./KeywordSearchResponseAdapterService";
import _ from "lodash";
import {
  AffiliationNestedDocument,
  MAXIMUM_QUERY_TOKENS
} from "./KeywordSearchResourceServiceRewrite";
import { SearchAnalyticsTracerService } from "./SearchAnalyticsTrackerService";
import { coerceStringLanguageToTypeLanguage } from "./KeywordAutocompleteResourceService";
import { SearchHit } from "@elastic/elasticsearch/lib/api/types";

export type NameSuggestFeatureFlags = {
  enableNameSearchFallbackForNameSuggest: boolean;
  enableNameSuggestWithAllPermutations: boolean;
};

export const nameSuggestFeatureFlagTypes = [
  "enableNameSearchFallbackForNameSuggest",
  "enableNameSuggestWithAllPermutations"
] as const;
export type NameSuggestFeatureFlag =
  (typeof nameSuggestFeatureFlagTypes)[number];

export const featureFlagDefaults: Readonly<
  Record<keyof NameSuggestFeatureFlags, { key: string; default: boolean }>
> = {
  enableNameSearchFallbackForNameSuggest: {
    key: "search.enable-name-search-fallback-for-name-suggest",
    default: false
  },
  enableNameSuggestWithAllPermutations: {
    key: "enable-all-permutation-name-suggest",
    default: false
  }
};

type NameSuggestSearchResponse = {
  total: number;
  results: Array<NameSuggestSearchResult>;
};

type NameSuggestSearchResult = {
  personId: string;
  h1dnId: string | null;
  name: string;
  specialties?: Array<string>;
  designations: Array<string>;
  formattedTopAffiliations?:
    | FormattedTopAffiliations
    | FormattedTopAffiliationsH1dn;
  formattedTopTLAffiliations?:
    | FormattedTopTLAffiliations
    | FormattedTopAffiliationsH1dn;
  topL3Indications: Array<string>;
  isOutsideUsersSlice?: boolean | null;
};

const HAS_ADVANCED_OPERATORS = /(\sAND\s|\sOR\s|NOT\s)/;

export function generateEmptyFilters(): FilterInterface {
  const baseFilters: FilterInterface = {
    dateRangePicker: {
      min: 0,
      max: null,
      active: false
    },
    designations: {
      values: []
    },
    npi: {
      values: []
    },
    institutionType: {
      values: []
    },
    specialty: {
      values: []
    },
    country: {
      values: []
    },
    state: {
      values: []
    },
    city: {
      values: []
    },
    zipCode: {
      values: []
    },
    institution: {
      values: []
    },
    presentWorkInstitutions: {
      values: []
    },
    pastAndPresentWorkInstitutions: {
      values: []
    },
    studentInstitutions: {
      values: []
    },
    graduationYearRange: {
      min: null,
      max: null
    },
    publications: {
      minCount: {
        value: null
      },
      socialMediaMinCount: {
        value: null
      },
      journal: {
        values: []
      },
      type: {
        values: []
      }
    },
    tags: {
      name: {
        values: []
      },
      publicTagIds: [],
      privateTagIds: [],
      programmaticTagIds: []
    },
    claims: {
      diagnosesICDMinCount: {
        value: null
      },
      diagnosesICD: {
        values: []
      },
      proceduresCPTMinCount: {
        value: null
      },
      proceduresCPT: {
        values: []
      },
      proceduresHCPCMinCount: {
        value: null
      },
      proceduresHCPC: {
        values: []
      },
      genericNames: {
        values: []
      },
      brandNames: {
        values: []
      },
      drugClasses: {
        values: []
      },
      prescriptionsMinCount: {
        value: null
      },
      timeFrame: {
        value: null
      }
    },
    trials: {
      minCount: {
        value: null
      },
      status: {
        values: []
      },
      phase: {
        values: []
      },
      studyType: {
        values: []
      },
      funderType: {
        values: []
      },
      sponsor: {
        values: []
      },
      sponsorType: {
        values: []
      },
      id: {
        values: []
      },
      timeFrame: {
        min: null,
        max: null,
        key: "max"
      },
      biomarkers: {
        values: []
      }
    },
    congresses: {
      minCount: {
        value: null
      },
      name: {
        values: []
      },
      type: {
        values: []
      },
      organizerName: {
        values: []
      },
      sessionType: {
        values: []
      }
    },
    engagements: {
      minCount: {
        value: null
      }
    },
    grants: {
      minAmount: {
        value: null
      },
      funder: {
        values: []
      }
    },
    payments: {
      minAmount: {
        value: null
      },
      company: {
        values: []
      },
      drugOrDevice: {
        values: []
      },
      fundingType: {
        values: []
      },
      category: {
        values: []
      }
    },
    referrals: {
      serviceLine: {
        values: []
      },
      minReferralsReceived: {
        value: null
      },
      minReferralsSent: {
        value: null
      }
    },
    patientsDiversity: {
      ageRange: {
        values: []
      },
      sex: {
        values: []
      },
      race: {
        values: []
      },
      raceMix: {
        thresholdValue: []
      }
    },
    providerDiversity: {
      languagesSpoken: {
        values: []
      },
      sex: {
        values: []
      },
      race: {
        values: []
      }
    },
    hasLinkedin: {
      value: null
    },
    hasTwitter: {
      value: null
    },
    hasCTMSData: {
      value: null
    },
    isFacultyOpinionsMember: {
      value: null
    },
    precision: {
      value: null
    },
    geoBoundingBox: {
      value: null
    },
    digitalLeader: {
      value: null
    },
    getMatchedClaims: {
      value: null
    },
    geoStatsRegionLevel: {
      value: null
    }
  };

  return baseFilters;
}

export function getNameSearchInput(
  input: KolNameSuggestionInput,
  projectId: string,
  languageCode: string,
  searchMultiLanguage: boolean | undefined,
  userId: string,
  translateTaiwan: boolean | undefined = undefined
): NameSearchInput {
  return {
    page: {
      from: input.offset,
      size: input.limit
    },
    projectId,
    query: Array.isArray(input.query) ? input.query.join() : undefined,
    suppliedFilters: generateEmptyFilters(),
    language: languageCode,
    projectFeatures: {
      claims: false,
      referrals: false,
      engagementsV2: false,
      searchMultiLanguage: searchMultiLanguage || false,
      translateTaiwan: !!translateTaiwan
    },
    userId
  };
}

function translateNameSearchResponseToNameSuggestResponse(
  nameSearchResponse: PersonSearchResponse
) {
  const suggestResults: Array<NameSuggestSearchResult> =
    nameSearchResponse.results.map(
      (result: PersonCard): NameSuggestSearchResult => {
        return {
          personId: result.personId,
          h1dnId: result.h1dnId,
          name: result.name,
          specialties: result.specialty,
          designations: result.designations || [],
          topL3Indications: result.topL3Indications || [],
          formattedTopAffiliations: result.formattedTopAffiliations,
          formattedTopTLAffiliations: result.formattedTopTLAffiliations,
          isOutsideUsersSlice: result.isOutsideUsersSlice || false
        };
      }
    );

  return {
    total: nameSearchResponse.total,
    results: suggestResults
  };
}

@Service()
@RpcService()
export class NameSuggestResourceService
  extends RpcResourceService
  implements NameSuggestResource
{
  private readonly logger = createLogger(this);

  constructor(
    config: ConfigService,
    private elasticService: ElasticSearchService,
    private languageDetectService: LanguageDetectService,
    private userClient: UserResourceClient,
    private searchAnalyticsTracerService: SearchAnalyticsTracerService,
    private featureFlagsService: FeatureFlagsService,
    @Inject()
    private nameSearchResourceServiceRewrite: NameSearchResourceServiceRewrite,
    private affiliationsAdapterService: AffiliationAdapterService
  ) {
    super(
      config.searchRpcSigningKey,
      RPC_NAMESPACE_NAME_SUGGEST,
      config.searchRedisOptions
    );
  }

  @RpcMethod()
  async isReady(): Promise<boolean> {
    return true;
  }

  @RpcMethod()
  @Trace("h1-search.name.suggest")
  async nameSuggestSearch(
    input: KolNameSuggestionInput,
    peopleIndex: string,
    userId: string,
    projectId: string,
    multiLangEnabled?: boolean,
    translateTaiwan?: boolean
  ): Promise<NameSuggestSearchResponse> {
    if (HAS_ADVANCED_OPERATORS.test(input.query?.join() || "")) {
      return {
        total: 0,
        results: []
      };
    }

    input.query = [
      this.sanitizeQuery(
        this.truncateQuery(input.query?.join())?.replace(/[´]/g, "'")
      ) || ""
    ];

    const featureFlags = await this.getFeatureFlagValues({ userId, projectId });
    let languageCode = ENGLISH;
    // TODO: the effect of this is that userLanguage is completely ignored and is a superfluous
    //       call when multiLang is disabled since the logic will use "eng" regardless.  Find out
    //       if this is actually the desired behavior.
    const userLanguage =
      coerceStringLanguageToTypeLanguage(input.language) ??
      (await this.userClient.getUsersPreferredLanguage(userId, projectId))
        ?.language;

    if (multiLangEnabled) {
      // TODO: should an empty input.query array be supported?  the conditional suggests that it should
      //       even though the query builders expect a non-empty array and will throw an exception when
      //       one is supplied
      languageCode = input.query?.length
        ? await this.languageDetectService.determineSupportedLanguageWithUserPreference(
            input.query.join(),
            userLanguage
          )
        : ENGLISH;
    }

    const response = await this.doNameSuggest(
      input,
      peopleIndex,
      projectId,
      languageCode,
      userId,
      featureFlags,
      multiLangEnabled,
      translateTaiwan
    );

    // Currently Name search logic handles lot more name variations than name suggest which can't be natively part of Name suggest due to latency concerns.
    // In scenarios where name suggest couldn't find any results, we want to have a name search fall back so that we can improve Name Suggest recall
    if (
      featureFlags.enableNameSearchFallbackForNameSuggest &&
      response.total == 0
    ) {
      const nameSearchResponse =
        await this.nameSearchResourceServiceRewrite.runNameSearchRewrite(
          getNameSearchInput(
            input,
            projectId,
            languageCode,
            multiLangEnabled,
            userId,
            translateTaiwan
          )
        );

      this.searchAnalyticsTracerService.sendAnalyticsEvent({
        event: "name_suggest_results",
        properties: {
          query: input.query?.join(" "),
          input,
          total: nameSearchResponse.total,
          personIds: nameSearchResponse.results.map(
            (result: { personId: string }) => result.personId
          ),
          projectId,
          type: "NAME_SEARCH"
        },
        timestamp: new Date(),
        userId
      });

      return translateNameSearchResponseToNameSuggestResponse(
        nameSearchResponse
      );
    }

    this.searchAnalyticsTracerService.sendAnalyticsEvent({
      event: "name_suggest_results",
      properties: {
        query: input.query?.join(" "),
        input,
        total: response.total,
        personIds: response.results.map((result) => result.personId),
        projectId,
        type: "NAME_SUGGEST_SEARCH"
      },
      timestamp: new Date(),
      userId
    });

    return response;
  }

  private truncateQuery(query?: string) {
    if (query) {
      return query.trim().split(/\s+/).slice(0, MAXIMUM_QUERY_TOKENS).join(" ");
    }
    return query;
  }

  private sanitizeQuery(query?: string) {
    return query?.replace(/[´]/g, "'");
  }

  @Trace("h1-search.name.suggest.query")
  private async doNameSuggest(
    input: KolNameSuggestionInput,
    peopleIndex: string,
    projectId: string,
    languageCode: string,
    userId: string,
    featureFlags: NameSuggestFeatureFlags,
    multiLangEnabled?: boolean,
    translateTaiwan?: boolean
  ): Promise<NameSuggestSearchResponse> {
    const buildNameSuggestQuery = getNameSuggestBuilder(languageCode);
    const body = await buildNameSuggestQuery({
      input,
      projectId,
      featureFlags
    });
    this.logger.debug({ data: body }, "Requesting name suggest");
    const result =
      await this.elasticService.getSignedElasticRequest<RootSearchObject>(
        body,
        peopleIndex,
        {
          searchType: "dfs_query_then_fetch"
        }
      );

    const hitsWithSource = result.hits.hits.filter(
      (doc: SearchHit) => !!doc._source
    );
    const institutionsKeyedById = lookupInstitutions(hitsWithSource as any);

    const nameSearchInput = getNameSearchInput(
      input,
      projectId,
      languageCode,
      multiLangEnabled,
      userId,
      translateTaiwan
    );
    return {
      total: result.hits.total.value,
      results: result.hits.hits.map((hit) =>
        this.hitToNameSuggestServiceResult(
          languageCode,
          institutionsKeyedById,
          nameSearchInput,
          hit
        )
      )
    };
  }

  private async getFeatureFlagValues(
    ldUser: LDUserInput
  ): Promise<NameSuggestFeatureFlags> {
    const featureFlags: Partial<NameSuggestFeatureFlags> = {};

    const flagsState = await this.featureFlagsService.getAllFlags(ldUser);

    for (const flag of nameSuggestFeatureFlagTypes) {
      featureFlags[flag] =
        flagsState.getFlagValue(featureFlagDefaults[flag].key) ??
        featureFlagDefaults[flag].default;
    }

    return featureFlags as NameSuggestFeatureFlags;
  }

  // TODO: this is convoluted and requires casts because SearchHit does not take a generic
  private hitToNameSuggestServiceResult(
    languageCode: string,
    institutionsKeyedById: Map<number, InstitutionDocument>,
    input: NameSearchInput,
    { _source, inner_hits }: SearchHit<any>
  ): NameSuggestSearchResult {
    const name = _source[
      `name_${languageCode}` as keyof typeof _source
    ] as string;

    const specialties = (_source[
      `specialty_${languageCode}` as keyof typeof _source
    ] ?? []) as string[];
    const designations = _source.designations ?? ([] as string[]);
    const affiliations = _.defaultTo(_source?.affiliations, [])
      .filter(
        (affiliation: AffiliationNestedDocument) =>
          affiliation.type !== NO_AFFILIATION
      )
      .map((affiliation: AffiliationNestedDocument) =>
        toAffiliation(
          affiliation,
          languageCode as Language,
          institutionsKeyedById
        )
      );
    const isH1dn = !!(!_source?.id && _source?.h1dn_id);
    const territoryAffiliations =
      inner_hits?.custom_territory_affiliations?.hits.hits;
    const territoryAffiliationNames = territoryAffiliations
      ? territoryAffiliations.map(
          (innerHit) =>
            _.get(innerHit.fields, "affiliations.institution.name.keyword")[0]
        )
      : [];
    const formattedTopAffiliations =
      this.affiliationsAdapterService.searchTopAffiliations(
        affiliations,
        input,
        isH1dn,
        _source?.locations,
        territoryAffiliationNames
      );

    const formattedTopTLAffiliations =
      this.affiliationsAdapterService.searchTopTLAffiliations(
        affiliations,
        input,
        isH1dn,
        _source?.locations,
        territoryAffiliationNames
      );

    // Some indications do not have scores. This trick sorts those to the end of the array
    // while still properly sorting the ones with a score by descending
    const topL3Indications = _source.indications
      ?.filter(
        (i: any) => i.indicationType === "L3" && i.indicationSource === "all"
      )
      .sort((a: any, b: any) => {
        const aHasScore = typeof a.indicationScore !== "undefined";
        const bHasScore = typeof b.indicationScore !== "undefined";
        return (
          +bHasScore - +aHasScore ||
          (bHasScore === true && b.indicationScore - a.indicationScore) ||
          0
        );
      })
      .map((i: any) => i.indication)
      .slice(0, 5);

    return {
      personId: _source.id || "",
      h1dnId: _source.h1dn_id || null,
      name,
      specialties,
      designations,
      topL3Indications: topL3Indications || [],
      formattedTopAffiliations,
      formattedTopTLAffiliations
    };
  }
}
