import { Service } from "typedi";
import { ClaimCodesRepository } from "@h1nyc/pipeline-repositories";
import { createLogger } from "../lib/Logger";
import { ClaimCode } from "@h1nyc/pipeline-entities";

@Service()
export class ClaimCodeService {
  private readonly logger = createLogger(this);
  constructor(private claimCodesRepository: ClaimCodesRepository) {}

  private buildClaimCodeToMetadataDictionary(
    claimCodes: ClaimCode[]
  ): Map<string, { scheme: string; description: string }> {
    const codeCountryToMetadata = new Map<
      string,
      { scheme: string; description: string }
    >();
    for (const claimCode of claimCodes) {
      for (const countryDesc of claimCode.countryDescriptions) {
        const key = this.claimCodeMetadataMapKeyGenerator(claimCode.code, countryDesc.Country)
        codeCountryToMetadata.set(key, {
          scheme: claimCode.scheme,
          description: countryDesc.Description
        });
      }
    }
    return codeCountryToMetadata;
  }

  public claimCodeMetadataMapKeyGenerator(
    code: string,
    country: string
  ): string{
    return `${code}::${country}`
  }

  public async getDiagnosesClaimCodeMetadataMap(
    codesToFilter: Array<string>
  ): Promise<
    Map<
      string,
      {
        scheme: string;
        description: string;
      }
    >
  > {
    this.logger.info(
      codesToFilter,
      "Diagnoses codes to filter ClaimCodesService"
    );
    //fetch claim code metadata(code, scheme, schemeType, description) from pipeline DB
    const diagnosesClaimCodesMetadata =
      await this.claimCodesRepository.getClaimCodesBySchemeType(
        codesToFilter,
        "D"
      );

    const diagnosesClaimCodeMetadataMap =
      this.buildClaimCodeToMetadataDictionary(diagnosesClaimCodesMetadata);

    return diagnosesClaimCodeMetadataMap;
  }

  public async getProcedureClaimCodeMetadataMap(
    codesToFilter: Array<string>
  ): Promise<
    Map<
      string,
      {
        scheme: string;
        description: string;
      }
    >
  > {
    this.logger.info(
      codesToFilter,
      "Procedure codes to filter ClaimCodesService"
    );
    //fetch claim code metadata(code, scheme, schemeType, description) from pipeline DB
    const procedureClaimCodesMetadata =
      await this.claimCodesRepository.getClaimCodesBySchemeType(
        codesToFilter,
        "P"
      );

    //construct a mapping between (code,country)=>(scheme, description) country because codes are country specific.
    //For ex - 5363 is a diagnoses code in US but a procedure code in Italy
    const procedureClaimCodeMetadataMap =
      this.buildClaimCodeToMetadataDictionary(procedureClaimCodesMetadata);

    return procedureClaimCodeMetadataMap;
  }
}
