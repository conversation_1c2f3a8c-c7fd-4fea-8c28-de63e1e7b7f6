import {
  SearchHit,
  SearchHitsMetadata,
  SearchInnerHits,
  QueryDslQueryContainer,
  SearchRequest,
  SearchResponse,
  SortCombinations
} from "@elastic/elasticsearch/lib/api/types";
import { Service } from "typedi";
import { createLogger } from "../lib/Logger";
import { ConfigService } from "./ConfigService";
import {
  HCPDocument,
  AssetsRequiringAggregationForMatchedCount,
  RootFieldsForScoring,
  KeywordSearchFeatureFlags
} from "./KeywordSearchResourceServiceRewrite";

import { Trace } from "../Tracer";
import { ElasticSearchService } from "./ElasticSearchService";
import _, { Dictionary, cloneDeep, isEmpty, zipObject } from "lodash";
import { errors } from "@elastic/elasticsearch";
import { KeywordSearchInput, PatientDiversityEnum } from "@h1nyc/search-sdk";
import { getHitsTotal } from "./KeywordSearchResponseAdapterService";
import { AggregationsTermsAggregateBase } from "@elastic/elasticsearch/lib/api/typesWithBodyKey";
import { DocCountBucket } from "./InstitutionsResourceService";
import { KeywordFilterClauseBuilderService } from "./KeywordFilterClauseBuilderService";

export type PeopleIdToAssetMatchedCount = Readonly<
  Record<string, AssetToMatchedCount>
>;

export type AssetToESMatchedCountQuery = Readonly<{
  field: AssetsRequiringAggregationForMatchedCount;
  query: SearchRequest;
}>;

export type AssetToMatchedCount = Record<
  AssetsRequiringAggregationForMatchedCount,
  number
>;

const MATCH_ALL = {
  match_all: {}
};

export const SORT_TIEBREAKER_FOR_INDICATIONS: SortCombinations[] = [
  {
    _score: {
      order: "desc"
    }
  },
  {
    ["indications.indication.keyword"]: {
      order: "asc"
    }
  }
];

@Service()
export class KeywordSearchMatchedCountService {
  private logger = createLogger(this);
  private peopleIndex: string;

  constructor(
    config: ConfigService,
    private elasticService: ElasticSearchService,
    private keywordFilterClauseBuilderService: KeywordFilterClauseBuilderService
  ) {
    this.peopleIndex = config.elasticPeopleIndex;
  }

  private hasSource(doc: SearchHit<HCPDocument>) {
    return !!doc._source;
  }

  private getDocIdsFromResults(hits: SearchHit<HCPDocument>[]) {
    return hits.map((hit: SearchHit<HCPDocument>) => {
      return hit._id;
    });
  }

  private async getMatchingPatientCountAndDiversityDistributionForHcp(
    claimsFilter: QueryDslQueryContainer[],
    hits: SearchHit<Readonly<HCPDocument>>[]
  ) {
    let matchedPatientCountMap: Dictionary<number> = {};
    let patientDiversityDistributionMap: Dictionary<{
      race: DocCountBucket[];
      gender: DocCountBucket[];
      age: DocCountBucket[];
    }> = {};

    const hcpDocIds = this.getDocIdsFromResults(hits);

    if (hcpDocIds.length) {
      const searchRequests: Promise<SearchResponse<HCPDocument>>[] = [];
      hcpDocIds.forEach((hcpDocId) => {
        const query: QueryDslQueryContainer = {
          bool: {
            filter: [
              {
                term: {
                  _routing: hcpDocId
                }
              },
              ...claimsFilter
            ]
          }
        };
        const patientDiversityAggregations = {
          patient_race_count: {
            terms: {
              field: "patientClaims.diversity",
              size: Object.keys(PatientDiversityEnum).length
            }
          },
          patient_gender_count: {
            terms: {
              field: "patientClaims.gender",
              size: 10
            }
          },
          patient_age_count: {
            terms: {
              field: "patientClaims.age",
              size: 20
            }
          }
        };
        const searchRequest: SearchRequest = {
          index: this.peopleIndex,
          query,
          track_total_hits: true,
          size: 0,
          aggs: patientDiversityAggregations
        };
        searchRequests.push(this.elasticService.query(searchRequest));
      });

      const responses = await Promise.all(searchRequests);
      const results = responses.map((response) =>
        getHitsTotal(response.hits.total ?? 0)
      );

      const diversityDistributions = responses.map((response) => {
        return {
          race: response.aggregations
            ? ((
                response.aggregations
                  .patient_race_count as AggregationsTermsAggregateBase<DocCountBucket>
              ).buckets as DocCountBucket[])
            : ([] as DocCountBucket[]),
          gender: response.aggregations
            ? ((
                response.aggregations
                  .patient_gender_count as AggregationsTermsAggregateBase<DocCountBucket>
              ).buckets as DocCountBucket[])
            : ([] as DocCountBucket[]),
          age: response.aggregations
            ? ((
                response.aggregations
                  .patient_age_count as AggregationsTermsAggregateBase<DocCountBucket>
              ).buckets as DocCountBucket[])
            : ([] as DocCountBucket[])
        };
      });

      matchedPatientCountMap = zipObject(hcpDocIds, results);
      patientDiversityDistributionMap = zipObject(
        hcpDocIds,
        diversityDistributions
      );
    }
    return { matchedPatientCountMap, patientDiversityDistributionMap };
  }

  @Trace("h1-search.MatchedCountService.getMatchedCountForAssets")
  async getMatchedCountForAssets(
    input: Readonly<KeywordSearchInput>,
    hits: Readonly<SearchHitsMetadata<Readonly<HCPDocument>>>,
    filters: Array<QueryDslQueryContainer>,
    assetToShouldClause: Map<
      RootFieldsForScoring,
      {
        isEmptyClauseQuery: boolean;
        query: QueryDslQueryContainer;
      }
    >,
    featureFlags: KeywordSearchFeatureFlags
  ): Promise<
    [
      PeopleIdToAssetMatchedCount,
      Map<string, string[]>,
      Map<string, string[]>,
      Dictionary<{
        race: DocCountBucket[];
        gender: DocCountBucket[];
        age: DocCountBucket[];
      }>
    ]
  > {
    const hitsWithSource = hits.hits.filter(this.hasSource);
    const matchedCountQueries = this.collectSearchCardMatchedCountsQueries(
      hitsWithSource,
      filters,
      assetToShouldClause
    );

    //this.logger.debug(
    //  { data: matchedCountQueries },
    //  "matched count elasticsearch query"
    //);
    const l1IndicationsIndex = matchedCountQueries.findIndex(
      (query) => query.field === "l1_indications"
    );
    const l3IndicationsIndex = matchedCountQueries.findIndex(
      (query) => query.field === "l3_indications"
    );
    const matchedQueryResponses: Array<
      SearchResponse<HCPDocument> | errors.ResponseError
    > = await this.queryElasticsearchForMatchedCountQueries(
      matchedCountQueries
    );

    const l1IndicationsResponse: Map<string, string[]> =
      this.createIndicationsResponse(
        matchedQueryResponses,
        l1IndicationsIndex,
        "l1_indications"
      );

    const topL3IndicationsResponse: Map<string, string[]> =
      this.createIndicationsResponse(
        matchedQueryResponses,
        l3IndicationsIndex,
        "l3_indications"
      );

    const matchedCountResults = this.mapSearchResponsesToMatchedCountsResults(
      matchedCountQueries,
      matchedQueryResponses,
      this.getDocIdsFromResults(hitsWithSource)
    );
    let patientDiversityDistribution = {};
    const patientPopulationFilter =
      this.keywordFilterClauseBuilderService.buildPatientPopulationFilter(
        input,
        featureFlags
      );
    if (
      patientPopulationFilter.length > 0 &&
      !!input.suppliedFilters.claims.showUniquePatients?.value
    ) {
      const { matchedPatientCountMap, patientDiversityDistributionMap } =
        await this.getMatchingPatientCountAndDiversityDistributionForHcp(
          patientPopulationFilter,
          hitsWithSource
        );

      // populating the unique patient count for the top k results
      if (!isEmpty(matchedPatientCountMap)) {
        hitsWithSource.forEach((hit) => {
          matchedCountResults[hit._id]["patient_claims_matching_count"] =
            matchedPatientCountMap[hit._id] ?? 0;
        });
      }
      patientDiversityDistribution = patientDiversityDistributionMap;
    }
    return [
      matchedCountResults,
      l1IndicationsResponse,
      topL3IndicationsResponse,
      patientDiversityDistribution
    ];
  }
  createIndicationsResponse(
    matchedQueryResponses: Array<
      SearchResponse<HCPDocument> | errors.ResponseError
    >,
    indicationsIndex: number,
    fieldName: "l1_indications" | "l3_indications"
  ): Map<string, string[]> {
    return (
      matchedQueryResponses[indicationsIndex] as SearchResponse<HCPDocument>
    ).hits.hits.reduce((acc: Map<string, string[]>, hit) => {
      acc.set(
        hit._id,
        hit?.inner_hits?.[fieldName]?.hits.hits.map(
          this.extractIndicationNameFromInnerHit
        ) || []
      );
      return acc;
    }, new Map<string, string[]>());
  }
  extractIndicationNameFromInnerHit(
    indicationInnerHits: SearchHit<Record<string, any>>
  ) {
    const l1IndicationName =
      _.get(
        indicationInnerHits,
        'fields["indications.indication.keyword"][0]'
      ) ?? "";
    return l1IndicationName;
  }

  isErrorResponse(response: errors.ResponseError | SearchResponse) {
    return response instanceof errors.ResponseError;
  }

  mapSearchResponsesToMatchedCountsResults(
    matchedCountQueries: Array<AssetToESMatchedCountQuery>,
    matchedQueryResponses: Array<
      SearchResponse<HCPDocument> | errors.ResponseError
    >,
    docIds: string[]
  ): PeopleIdToAssetMatchedCount {
    const matchedCountResults = {} as Record<string, AssetToMatchedCount>;

    docIds.forEach((id) => {
      matchedCountResults[id] = {} as AssetToMatchedCount;
    });

    if (matchedCountQueries.length != matchedQueryResponses.length) {
      this.logger.error(
        "Mismatch in count found for MatchedCount queries and responses."
      );
      return matchedCountResults;
    }

    for (let i = 0; i < matchedCountQueries.length; i++) {
      const query = matchedCountQueries[i];
      const response = matchedQueryResponses[i];

      // Initialize the counts as 0 so that if there is no hit some an asset then matched count is 0.
      docIds.forEach((id) => {
        matchedCountResults[id][query.field] = 0;
      });

      if (!response) {
        continue;
      }

      if (this.isErrorResponse(response)) {
        this.logger.error(response);
        continue;
      }

      (response as SearchResponse<HCPDocument>).hits.hits.forEach((hit) => {
        matchedCountResults[hit._id][query.field] = hit._score ?? 0;
      });
    }
    return matchedCountResults;
  }

  private async queryElasticsearchForMatchedCountQueries(
    matchedCountQueries: Array<AssetToESMatchedCountQuery>
  ): Promise<Array<SearchResponse<HCPDocument> | errors.ResponseError>> {
    if (!matchedCountQueries.length) {
      return [];
    }

    const matchedQueryResponses: Array<
      SearchResponse<HCPDocument> | errors.ResponseError
    > = await Promise.all(
      matchedCountQueries.map((query) =>
        this.elasticService.query<HCPDocument>(query.query)
      )
    );

    return matchedQueryResponses;
  }

  collectSearchCardMatchedCountsQueries(
    hits: SearchHit<Readonly<HCPDocument>>[],
    filters: Array<QueryDslQueryContainer>,
    assetToShouldClause: Map<
      RootFieldsForScoring,
      {
        isEmptyClauseQuery: boolean;
        query: QueryDslQueryContainer;
      }
    >
  ) {
    const docIds = this.getDocIdsFromResults(hits);
    const matchedCountQueries: Array<AssetToESMatchedCountQuery> = [];

    const docIdFilter = {
      terms: {
        _id: [...docIds]
      }
    };

    filters.push(docIdFilter);
    const pageSize = docIds.length;

    filters = this.removeNestedFilters(filters);
    const l1IndicationForHCP: QueryDslQueryContainer = {
      nested: {
        path: "indications",
        query: {
          bool: {
            filter: [
              {
                terms: {
                  ["indications.indicationType"]: ["L1"]
                }
              },
              {
                terms: {
                  ["indications.indicationSource"]: ["all"]
                }
              }
            ]
          }
        },
        inner_hits: {
          _source: false,
          sort: SORT_TIEBREAKER_FOR_INDICATIONS,
          size: 1000,
          name: "l1_indications",
          docvalue_fields: ["indications.indication.keyword"]
        }
      }
    };

    const topL3IndicationForHCP: QueryDslQueryContainer = {
      nested: {
        path: "indications",
        query: {
          function_score: {
            query: {
              bool: {
                filter: [
                  {
                    terms: {
                      ["indications.indicationType"]: ["L3"]
                    }
                  },
                  {
                    terms: {
                      ["indications.indicationSource"]: ["all"]
                    }
                  }
                ]
              }
            },
            boost_mode: "replace",
            functions: [
              {
                field_value_factor: {
                  field: "indications.indicationScore",
                  missing: 0
                }
              }
            ]
          }
        },
        inner_hits: {
          _source: false,
          sort: SORT_TIEBREAKER_FOR_INDICATIONS,
          size: 5,
          name: "l3_indications",
          docvalue_fields: ["indications.indication.keyword"]
        }
      }
    };

    const l1IndicationsQuery = this.buildMatchedCountQuery(
      MATCH_ALL,
      [...filters, l1IndicationForHCP],
      pageSize
    );
    matchedCountQueries.push({
      field: "l1_indications",
      query: l1IndicationsQuery
    });
    const topL3IndicationsQuery = this.buildMatchedCountQuery(
      MATCH_ALL,
      [...filters, topL3IndicationForHCP],
      pageSize
    );
    matchedCountQueries.push({
      field: "l3_indications",
      query: topL3IndicationsQuery
    });
    const citationBooleanQuery = assetToShouldClause.get("citationTotal");
    // If these is no query then matchedCount will be equal to total count and we don't need a separate matchedQuery
    if (
      !citationBooleanQuery?.isEmptyClauseQuery &&
      citationBooleanQuery?.query?.function_score?.query
    ) {
      const citationQuery = this.buildMatchedCountQuery(
        citationBooleanQuery.query.function_score?.query,
        filters,
        pageSize
      );

      matchedCountQueries.push({ field: "citations", query: citationQuery });
    }

    const microBloggingBooleanQuery =
      assetToShouldClause.get("microBloggingTotal");
    if (
      !microBloggingBooleanQuery?.isEmptyClauseQuery &&
      microBloggingBooleanQuery?.query.function_score?.query
    ) {
      const microBloggingQuery = this.buildMatchedCountQuery(
        microBloggingBooleanQuery.query.function_score?.query,
        filters,
        pageSize
      );
      matchedCountQueries.push({
        field: "microBlogging",
        query: microBloggingQuery
      });
    }

    const diagnosesBooleanQuery = assetToShouldClause.get("DRG_diagnosesCount");
    if (diagnosesBooleanQuery && !diagnosesBooleanQuery?.isEmptyClauseQuery) {
      const queryFromShouldClauses = extractClaimsNestedFiltersFromShouldClause(
        diagnosesBooleanQuery
      );
      const diagnosesMustClause =
        queryFromShouldClauses.length > 0
          ? queryFromShouldClauses[0].function_score?.query
          : undefined;
      const diagnosesMustNotClause =
        queryFromShouldClauses?.length == 2
          ? (queryFromShouldClauses[1] as QueryDslQueryContainer)
          : undefined;
      const diagnoses_query = this.buildMatchedCountQueryForClaims(
        filters,
        pageSize,
        diagnosesMustClause,
        diagnosesMustNotClause
      );
      matchedCountQueries.push({
        field: "diagnoses_amount",
        query: diagnoses_query
      });
    }

    const ccsrBooleanQuery = assetToShouldClause.get("ccsr");
    if (ccsrBooleanQuery && !ccsrBooleanQuery?.isEmptyClauseQuery) {
      const queryFromShouldClauses =
        extractClaimsNestedFiltersFromShouldClause(ccsrBooleanQuery);
      const ccsrMustClause =
        queryFromShouldClauses.length > 0
          ? queryFromShouldClauses[0].function_score?.query
          : undefined;
      const ccsrMustNotClause =
        queryFromShouldClauses?.length == 2
          ? (queryFromShouldClauses[1] as QueryDslQueryContainer)
          : undefined;
      const ccsr_query = this.buildMatchedCountQueryForClaims(
        filters,
        pageSize,
        ccsrMustClause,
        ccsrMustNotClause
      );
      matchedCountQueries.push({
        field: "ccsr_amount",
        query: ccsr_query
      });
    }

    const proceduresBooleanQuery = assetToShouldClause.get(
      "DRG_proceduresCount"
    );
    if (proceduresBooleanQuery && !proceduresBooleanQuery?.isEmptyClauseQuery) {
      const queryFromShouldClauses = extractClaimsNestedFiltersFromShouldClause(
        proceduresBooleanQuery
      );

      const procedureMustClause =
        queryFromShouldClauses.length > 0
          ? queryFromShouldClauses[0].function_score?.query
          : undefined;
      const procedureMustNotClause =
        queryFromShouldClauses?.length == 2
          ? (queryFromShouldClauses[1] as QueryDslQueryContainer)
          : undefined;
      const procedures_query = this.buildMatchedCountQueryForClaims(
        filters,
        pageSize,
        procedureMustClause,
        procedureMustNotClause
      );
      matchedCountQueries.push({
        field: "procedures_amount",
        query: procedures_query
      });
    }
    const ccsrPxBooleanQuery = assetToShouldClause.get("ccsr_px");
    if (ccsrPxBooleanQuery && !ccsrPxBooleanQuery?.isEmptyClauseQuery) {
      const queryFromShouldClauses =
        extractClaimsNestedFiltersFromShouldClause(ccsrPxBooleanQuery);
      const ccsrPxMustClause =
        queryFromShouldClauses.length > 0
          ? queryFromShouldClauses[0].function_score?.query
          : undefined;
      const ccsrPxMustNotClause =
        queryFromShouldClauses?.length == 2
          ? (queryFromShouldClauses[1] as QueryDslQueryContainer)
          : undefined;
      const ccsr_px_query = this.buildMatchedCountQueryForClaims(
        filters,
        pageSize,
        ccsrPxMustClause,
        ccsrPxMustNotClause
      );
      matchedCountQueries.push({
        field: "ccsr_px_amount",
        query: ccsr_px_query
      });
    }

    const prescriptionsBooleanQuery =
      assetToShouldClause.get("num_prescriptions");

    if (
      prescriptionsBooleanQuery &&
      !prescriptionsBooleanQuery.isEmptyClauseQuery
    ) {
      const queryFromShouldClauses = extractClaimsNestedFiltersFromShouldClause(
        prescriptionsBooleanQuery
      );

      const prescriptionsMustClause =
        queryFromShouldClauses.length > 0
          ? queryFromShouldClauses[0].function_score?.query
          : undefined;
      const prescriptions_query = this.buildMatchedCountQueryForClaims(
        filters,
        pageSize,
        prescriptionsMustClause
      );
      matchedCountQueries.push({
        field: "prescriptions_amount",
        query: prescriptions_query
      });
    }

    const paymentsBooleanQuery = assetToShouldClause.get("paymentTotal");
    if (
      !paymentsBooleanQuery?.isEmptyClauseQuery &&
      paymentsBooleanQuery?.query.function_score?.query
    ) {
      const paymentsQuery = this.buildMatchedCountQuery(
        paymentsBooleanQuery.query.function_score?.query,
        filters,
        pageSize
      );
      matchedCountQueries.push({ field: "payments", query: paymentsQuery });
    }

    const trialsBooleanQuery = assetToShouldClause.get("trialCount");
    if (
      !trialsBooleanQuery?.isEmptyClauseQuery &&
      trialsBooleanQuery?.query.function_score?.query
    ) {
      const trialsOngoingCountQuery = this.buildMatchedCountQuery(
        this.getTrialOngoingQuery(
          trialsBooleanQuery.query.function_score.query
        ),
        filters,
        pageSize
      );
      matchedCountQueries.push({
        field: "trial_ongoing_count",
        query: trialsOngoingCountQuery
      });

      const trialsActivelyRecruitingCountQuery = this.buildMatchedCountQuery(
        this.getTrialActivelyRecruitingQuery(
          trialsBooleanQuery.query.function_score.query
        ),
        filters,
        pageSize
      );
      matchedCountQueries.push({
        field: "trial_actively_recruiting_count",
        query: trialsActivelyRecruitingCountQuery
      });
    }

    return matchedCountQueries;
  }

  private getTrialOngoingQuery(
    query: QueryDslQueryContainer
  ): QueryDslQueryContainer {
    const trialQuery = cloneDeep(query);

    if (!trialQuery!.nested!.query.bool!.filter) {
      trialQuery!.nested!.query.bool!.filter = [];
    }
    (trialQuery!.nested!.query.bool!.filter as QueryDslQueryContainer[]).push({
      terms: {
        "trials.status_eng": [
          "Not yet recruiting",
          "Recruiting",
          "Enrolling by invitation",
          "Active, not recruiting",
          "Active"
        ]
      }
    });

    //empty the should clause in the query as we use the score as the count of the trials. Having a should clause manipulates the score.
    if (trialQuery!.nested!.query.bool!.should) {
      (trialQuery!.nested!.query.bool!.should as QueryDslQueryContainer[]) = [];
    }

    return trialQuery;
  }

  private getTrialActivelyRecruitingQuery(
    query: QueryDslQueryContainer
  ): QueryDslQueryContainer {
    const trialQuery = cloneDeep(query);

    if (!trialQuery!.nested!.query.bool!.filter) {
      trialQuery!.nested!.query.bool!.filter = [];
    }
    (trialQuery!.nested!.query.bool!.filter as QueryDslQueryContainer[]).push({
      terms: {
        "trials.status_eng": ["Recruiting", "Enrolling by invitation"]
      }
    });

    //empty the should clause in the query as we use the score as the count of the trials. Having a should clause manipulates the score.
    if (trialQuery!.nested!.query.bool!.should) {
      (trialQuery!.nested!.query.bool!.should as QueryDslQueryContainer[]) = [];
    }

    return trialQuery;
  }

  buildMatchedCountQuery(
    must: QueryDslQueryContainer,
    filters: Array<QueryDslQueryContainer>,
    pageSize: number
  ): SearchRequest {
    // In future, Inner hits might be enabled for snippetting use case. This makes sure that inner hits size is always 0 if present.
    const innerHits: SearchInnerHits = {
      size: 0
    };

    if (must.nested) {
      must.nested.inner_hits = innerHits;
    }
    const query: SearchRequest = {
      index: this.peopleIndex,
      from: 0,
      size: pageSize,
      track_total_hits: false,
      _source_includes: ["id"],
      query: {
        bool: {
          filter: filters,
          must
        }
      }
    };

    return query;
  }
  buildMatchedCountQueryForClaims(
    filters: Array<QueryDslQueryContainer>,
    pageSize: number,
    must?: QueryDslQueryContainer,
    must_not?: QueryDslQueryContainer
  ): SearchRequest {
    // In future, Inner hits might be enabled for snippetting use case. This makes sure that inner hits size is always 0 if present.
    const innerHits: SearchInnerHits = {
      size: 0
    };
    if (must?.nested) {
      must.nested.inner_hits = innerHits;
    }
    const query: SearchRequest = {
      index: this.peopleIndex,
      from: 0,
      size: pageSize,
      track_total_hits: false,
      _source_includes: ["id"],
      query: {
        bool: {
          filter: filters,
          must,
          must_not
        }
      }
    };

    return query;
  }

  removeNestedFilters(
    filters: Array<QueryDslQueryContainer>
  ): Array<QueryDslQueryContainer> {
    const filtersWithoutNestedFilters = filters.filter(this.isNotNestedFilter);
    return filtersWithoutNestedFilters;
  }

  isNotNestedFilter(filter: QueryDslQueryContainer) {
    return !filter.nested;
  }
}
function extractClaimsNestedFiltersFromShouldClause(shouldQueryClause: {
  isEmptyClauseQuery: boolean;
  query: QueryDslQueryContainer;
}): QueryDslQueryContainer[] {
  if (!_.isEmpty(shouldQueryClause.query.function_score?.query)) {
    return [shouldQueryClause.query];
  } else if (
    _.isArray(shouldQueryClause.query.bool?.must) &&
    _.isArray(shouldQueryClause.query.bool?.must_not)
  ) {
    return [
      shouldQueryClause.query.bool!.must[0],
      shouldQueryClause.query.bool!.must_not[0]
    ];
  } else return [];
}
