import { faker } from "@faker-js/faker";
import {
  IndicationSortBy,
  IndicationSource,
  IndicationType
} from "@h1nyc/search-sdk";
import { FeatureFlagsService, LDUserInput } from "@h1nyc/systems-feature-flags";
import { LDFlagsState } from "launchdarkly-node-server-sdk";
import { createMockInstance } from "../util/TestUtils";
import { ConfigService } from "./ConfigService";
import { ElasticSearchIndicationsTreeService } from "./ElasticsearchIndicationsTreeService";
import {
  featureFlagDefaults,
  IndicationDoc,
  IndicationSearchFeatureFlags,
  IndicationsTreeSearchService
} from "./IndicationsTreeSearchService";
import {
  IndicationIcdInstitutionCountsRepository,
  IndicationInstitutionCountsRepository
} from "@h1nyc/pipeline-repositories";
import { SearchHit } from "@elastic/elasticsearch/lib/api/types";
import { SearchAnalyticsTracerService } from "./SearchAnalyticsTrackerService";

function mockFeatureFlagsService(
  overrides: Partial<IndicationSearchFeatureFlags> = {}
) {
  const values = Object.entries(featureFlagDefaults).reduce(
    (acc, [key, value]) => {
      if (overrides[key as keyof IndicationSearchFeatureFlags]) {
        acc[value.key] = overrides[key as keyof IndicationSearchFeatureFlags]!;
      } else {
        acc[value.key] = value.default;
      }
      return acc;
    },
    {} as Record<string, boolean>
  );

  const featureFlagsService = createMockInstance(
    FeatureFlagsService as any
  ) as jest.Mocked<FeatureFlagsService>;

  featureFlagsService.getFlag.mockImplementation(
    (flagName: string): Promise<boolean> => {
      return Promise.resolve(values[flagName]);
    }
  );

  featureFlagsService.getAllFlags.mockImplementation(
    (_user?: LDUserInput): Promise<LDFlagsState> => {
      return Promise.resolve({
        valid: true,
        getFlagValue: jest.fn().mockImplementation((flagName: string) => {
          return values[flagName];
        }),
        getFlagReason: jest.fn(),
        allValues: jest.fn(),
        toJSON: jest.fn()
      });
    }
  );

  return featureFlagsService;
}

describe("IndicationsTreeSearchService", () => {
  describe("searchIndicationsByQuery", () => {
    it("should return empty array if indicationSource is empty", async () => {
      const configService = createMockInstance(ConfigService);
      configService.elasticIndicationsIndex = faker.datatype.string();
      const elasticSearchIndicationsTreeService = createMockInstance(
        ElasticSearchIndicationsTreeService
      );
      const featureFlagsService = mockFeatureFlagsService();
      const indicationIcdInstitutionCountsRepository = createMockInstance(
        IndicationIcdInstitutionCountsRepository
      );
      const indicationInstitutionCountsRepository = createMockInstance(
        IndicationInstitutionCountsRepository
      );
      const searchAnalyticsTracerService = createMockInstance(
        SearchAnalyticsTracerService
      );
      const indicationsTreeSearchService = new IndicationsTreeSearchService(
        configService,
        elasticSearchIndicationsTreeService,
        featureFlagsService,
        indicationInstitutionCountsRepository,
        indicationIcdInstitutionCountsRepository,
        searchAnalyticsTracerService
      );

      elasticSearchIndicationsTreeService.query.mockRejectedValue(
        new Error("elasticSearchIndicationsTreeService.query threw an error")
      );

      const indications =
        await indicationsTreeSearchService.searchIndicationsByQuery({
          query: faker.datatype.string(),
          indicationSource: [],
          size: faker.datatype.number(),
          projectId: faker.datatype.string()
        });

      expect(indications).toEqual([]);
    });

    it("should return throw error if ES query errored out", async () => {
      const configService = createMockInstance(ConfigService);
      configService.elasticIndicationsIndex = faker.datatype.string();
      const elasticSearchIndicationsTreeService = createMockInstance(
        ElasticSearchIndicationsTreeService
      );
      const elasticSearchError = {
        message: faker.random.words(),
        statusCode: 400,
        body: undefined,
        headers: undefined,
        name: ""
      };

      elasticSearchIndicationsTreeService.query.mockRejectedValue(
        elasticSearchError as any
      );
      const featureFlagsService = mockFeatureFlagsService();
      const indicationIcdInstitutionCountsRepository = createMockInstance(
        IndicationIcdInstitutionCountsRepository
      );
      const indicationInstitutionCountsRepository = createMockInstance(
        IndicationInstitutionCountsRepository
      );
      const searchAnalyticsTracerService = createMockInstance(
        SearchAnalyticsTracerService
      );
      const indicationsTreeSearchService = new IndicationsTreeSearchService(
        configService,
        elasticSearchIndicationsTreeService,
        featureFlagsService,
        indicationInstitutionCountsRepository,
        indicationIcdInstitutionCountsRepository,
        searchAnalyticsTracerService
      );

      await expect(
        indicationsTreeSearchService.searchIndicationsByQuery({
          query: faker.datatype.string(),
          indicationSource: [IndicationSource.ALL],
          size: faker.datatype.number(),
          projectId: faker.datatype.string()
        })
      ).rejects.toThrow(new Error(elasticSearchError.message));
    });

    it("should return empty array if there are no matched indications", async () => {
      const configService = createMockInstance(ConfigService);
      configService.elasticIndicationsIndex = faker.datatype.string();
      const elasticSearchIndicationsTreeService = createMockInstance(
        ElasticSearchIndicationsTreeService
      );

      elasticSearchIndicationsTreeService.query.mockResolvedValue({
        hits: {
          hits: []
        }
      } as any);
      const featureFlagsService = mockFeatureFlagsService();
      const indicationIcdInstitutionCountsRepository = createMockInstance(
        IndicationIcdInstitutionCountsRepository
      );
      const indicationInstitutionCountsRepository = createMockInstance(
        IndicationInstitutionCountsRepository
      );
      const searchAnalyticsTracerService = createMockInstance(
        SearchAnalyticsTracerService
      );
      const indicationsTreeSearchService = new IndicationsTreeSearchService(
        configService,
        elasticSearchIndicationsTreeService,
        featureFlagsService,
        indicationInstitutionCountsRepository,
        indicationIcdInstitutionCountsRepository,
        searchAnalyticsTracerService
      );

      const indications =
        await indicationsTreeSearchService.searchIndicationsByQuery({
          query: faker.datatype.string(),
          indicationSource: [IndicationSource.ALL],
          size: faker.datatype.number(),
          projectId: faker.datatype.string()
        });

      expect(elasticSearchIndicationsTreeService.query).toHaveBeenCalled();

      expect(indications).toEqual([]);
      expect(
        searchAnalyticsTracerService.sendAnalyticsEvent
      ).toHaveBeenCalled();
    });

    it("should return matched nodes limited to size param", async () => {
      const configService = createMockInstance(ConfigService);
      configService.elasticIndicationsIndex = faker.datatype.string();
      const elasticSearchIndicationsTreeService = createMockInstance(
        ElasticSearchIndicationsTreeService
      );

      const ids = [faker.datatype.string(), faker.datatype.string()];
      const h1_ids = [faker.datatype.string(), faker.datatype.string()];
      const indicationNames = [
        faker.datatype.string(),
        faker.datatype.string()
      ];
      const icdCodes = [
        {
          icdCode: faker.datatype.string(),
          description: faker.datatype.string(),
          patientCount: faker.datatype.number()
        },
        {
          icdCode: faker.datatype.string(),
          description: faker.datatype.string(),
          patientCount: faker.datatype.number()
        }
      ];
      elasticSearchIndicationsTreeService.query.mockResolvedValue({
        hits: {
          hits: [
            {
              _id: ids[0],
              _source: {
                h1_id: h1_ids[0],
                indication_name: indicationNames[0],
                indication_type: "L1"
              }
            },
            {
              _id: ids[1],
              _source: {
                h1_id: h1_ids[1],
                indication_name: indicationNames[1],
                indication_type: "L3",
                icd_codes: icdCodes
              }
            }
          ]
        }
      } as any);
      const featureFlagsService = mockFeatureFlagsService();
      const indicationIcdInstitutionCountsRepository = createMockInstance(
        IndicationIcdInstitutionCountsRepository
      );
      const indicationInstitutionCountsRepository = createMockInstance(
        IndicationInstitutionCountsRepository
      );
      const searchAnalyticsTracerService = createMockInstance(
        SearchAnalyticsTracerService
      );
      const indicationsTreeSearchService = new IndicationsTreeSearchService(
        configService,
        elasticSearchIndicationsTreeService,
        featureFlagsService,
        indicationInstitutionCountsRepository,
        indicationIcdInstitutionCountsRepository,
        searchAnalyticsTracerService
      );

      const indications =
        await indicationsTreeSearchService.searchIndicationsByQuery({
          query: faker.datatype.string(),
          indicationSource: [IndicationSource.ALL],
          size: 2,
          projectId: faker.datatype.string()
        });

      expect(elasticSearchIndicationsTreeService.query).toHaveBeenCalledWith(
        expect.objectContaining({
          index: configService.elasticIndicationsIndex,
          size: 2
        })
      );

      expect(indications).toEqual([
        {
          id: ids[0],
          h1Id: h1_ids[0],
          indicationName: indicationNames[0],
          matchedSynonyms: [],
          icdCodes: [],
          match: false,
          children: [],
          indicationType: "L1",
          parentH1Ids: []
        },
        {
          id: ids[1],
          h1Id: h1_ids[1],
          indicationName: indicationNames[1],
          matchedSynonyms: [],
          icdCodes: icdCodes,
          match: false,
          children: [],
          indicationType: "L3",
          parentH1Ids: []
        }
      ]);
    });

    it("should apply term filter on ancestor_ids if rootId is passed", async () => {
      const configService = createMockInstance(ConfigService);
      configService.elasticIndicationsIndex = faker.datatype.string();
      const elasticSearchIndicationsTreeService = createMockInstance(
        ElasticSearchIndicationsTreeService
      );

      const ids = [faker.datatype.string(), faker.datatype.string()];
      const indicationNames = [
        faker.datatype.string(),
        faker.datatype.string()
      ];
      elasticSearchIndicationsTreeService.query.mockResolvedValue({
        hits: {
          hits: [
            {
              _id: ids[0],
              _source: {
                indication_name: indicationNames[0]
              }
            },
            {
              _id: ids[1],
              _source: {
                indication_name: indicationNames[1]
              }
            }
          ]
        }
      } as any);
      const featureFlagsService = mockFeatureFlagsService();
      const indicationIcdInstitutionCountsRepository = createMockInstance(
        IndicationIcdInstitutionCountsRepository
      );
      const indicationInstitutionCountsRepository = createMockInstance(
        IndicationInstitutionCountsRepository
      );
      const searchAnalyticsTracerService = createMockInstance(
        SearchAnalyticsTracerService
      );
      const indicationsTreeSearchService = new IndicationsTreeSearchService(
        configService,
        elasticSearchIndicationsTreeService,
        featureFlagsService,
        indicationInstitutionCountsRepository,
        indicationIcdInstitutionCountsRepository,
        searchAnalyticsTracerService
      );
      const rootId = faker.datatype.string();
      const indications =
        await indicationsTreeSearchService.searchIndicationsByQuery({
          query: faker.datatype.string(),
          indicationSource: [IndicationSource.ALL],
          size: 2,
          rootId,
          projectId: faker.datatype.string()
        });

      expect(elasticSearchIndicationsTreeService.query).toHaveBeenCalledWith(
        expect.objectContaining({
          index: configService.elasticIndicationsIndex,
          query: expect.objectContaining({
            bool: expect.objectContaining({
              filter: expect.arrayContaining([
                {
                  term: {
                    ancestor_ids: rootId
                  }
                }
              ])
            })
          })
        })
      );

      expect(indications).toEqual([
        {
          id: ids[0],
          indicationName: indicationNames[0],
          matchedSynonyms: [],
          icdCodes: [],
          match: false,
          children: [],
          parentH1Ids: []
        },
        {
          id: ids[1],
          indicationName: indicationNames[1],
          matchedSynonyms: [],
          icdCodes: [],
          match: false,
          children: [],
          parentH1Ids: []
        }
      ]);
    });

    it("should apply term filter on h1Ids if present", async () => {
      const configService = createMockInstance(ConfigService);
      configService.elasticIndicationsIndex = faker.datatype.string();
      const elasticSearchIndicationsTreeService = createMockInstance(
        ElasticSearchIndicationsTreeService
      );

      const ids = [faker.datatype.string(), faker.datatype.string()];
      const indicationNames = [
        faker.datatype.string(),
        faker.datatype.string()
      ];
      const parentH1Ids = [
        [faker.datatype.string(), faker.datatype.string()],
        [faker.datatype.string()]
      ];
      elasticSearchIndicationsTreeService.query.mockResolvedValue({
        hits: {
          hits: [
            {
              _id: ids[0],
              _source: {
                indication_name: indicationNames[0],
                parent_h1id: parentH1Ids[0]
              }
            },
            {
              _id: ids[1],
              _source: {
                indication_name: indicationNames[1],
                parent_h1id: parentH1Ids[1]
              }
            }
          ]
        }
      } as any);
      const featureFlagsService = mockFeatureFlagsService();
      const indicationIcdInstitutionCountsRepository = createMockInstance(
        IndicationIcdInstitutionCountsRepository
      );
      const indicationInstitutionCountsRepository = createMockInstance(
        IndicationInstitutionCountsRepository
      );
      const searchAnalyticsTracerService = createMockInstance(
        SearchAnalyticsTracerService
      );
      const indicationsTreeSearchService = new IndicationsTreeSearchService(
        configService,
        elasticSearchIndicationsTreeService,
        featureFlagsService,
        indicationInstitutionCountsRepository,
        indicationIcdInstitutionCountsRepository,
        searchAnalyticsTracerService
      );
      const h1Id = faker.datatype.string();
      const indications =
        await indicationsTreeSearchService.searchIndicationsByQuery({
          query: faker.datatype.string(),
          indicationSource: [IndicationSource.ALL],
          size: 2,
          projectId: faker.datatype.string(),
          h1Ids: [h1Id]
        });

      expect(elasticSearchIndicationsTreeService.query).toHaveBeenCalledWith(
        expect.objectContaining({
          index: configService.elasticIndicationsIndex,
          query: expect.objectContaining({
            bool: expect.objectContaining({
              filter: expect.arrayContaining([
                {
                  terms: {
                    h1_id: [h1Id]
                  }
                }
              ])
            })
          })
        })
      );

      expect(indications).toEqual([
        {
          id: ids[0],
          indicationName: indicationNames[0],
          matchedSynonyms: [],
          icdCodes: [],
          match: false,
          children: [],
          parentH1Ids: parentH1Ids[0]
        },
        {
          id: ids[1],
          indicationName: indicationNames[1],
          matchedSynonyms: [],
          icdCodes: [],
          match: false,
          children: [],
          parentH1Ids: parentH1Ids[1]
        }
      ]);
    });

    it("should set matchedSynonyms to inner_hits", async () => {
      const configService = createMockInstance(ConfigService);
      configService.elasticIndicationsIndex = faker.datatype.string();
      const elasticSearchIndicationsTreeService = createMockInstance(
        ElasticSearchIndicationsTreeService
      );

      const ids = [
        faker.datatype.string(),
        faker.datatype.string(),
        faker.datatype.string()
      ];
      const indicationNames = [
        faker.datatype.string(),
        faker.datatype.string(),
        faker.datatype.string()
      ];

      const matchedSynonyms = [
        faker.datatype.string(),
        faker.datatype.string(),
        faker.datatype.string()
      ];
      elasticSearchIndicationsTreeService.query.mockResolvedValue({
        hits: {
          hits: [
            {
              _id: ids[0],
              _source: {
                indication_name: indicationNames[0]
              },
              inner_hits: {
                synonyms: {
                  hits: {
                    hits: [
                      {
                        _source: {
                          saut: matchedSynonyms[0]
                        }
                      }
                    ]
                  }
                }
              }
            },
            {
              _id: ids[1],
              _source: {
                indication_name: indicationNames[1]
              },
              inner_hits: {
                synonyms: {
                  hits: {
                    hits: [
                      {
                        _source: {
                          saut: matchedSynonyms[1]
                        }
                      },
                      {
                        _source: {
                          saut: matchedSynonyms[2]
                        }
                      }
                    ]
                  }
                }
              }
            },
            {
              _id: ids[2],
              _source: {
                indication_name: indicationNames[2]
              },
              inner_hits: {
                synonyms: {
                  hits: {
                    hits: []
                  }
                }
              }
            }
          ]
        }
      } as any);
      const featureFlagsService = mockFeatureFlagsService();
      const indicationIcdInstitutionCountsRepository = createMockInstance(
        IndicationIcdInstitutionCountsRepository
      );
      const indicationInstitutionCountsRepository = createMockInstance(
        IndicationInstitutionCountsRepository
      );
      const searchAnalyticsTracerService = createMockInstance(
        SearchAnalyticsTracerService
      );
      const indicationsTreeSearchService = new IndicationsTreeSearchService(
        configService,
        elasticSearchIndicationsTreeService,
        featureFlagsService,
        indicationInstitutionCountsRepository,
        indicationIcdInstitutionCountsRepository,
        searchAnalyticsTracerService
      );

      const indications =
        await indicationsTreeSearchService.searchIndicationsByQuery({
          query: faker.datatype.string(),
          indicationSource: [IndicationSource.ALL],
          size: 3,
          projectId: faker.datatype.string()
        });

      expect(elasticSearchIndicationsTreeService.query).toHaveBeenCalledWith(
        expect.objectContaining({
          index: configService.elasticIndicationsIndex,
          size: 3
        })
      );

      expect(indications).toEqual([
        {
          id: ids[0],
          indicationName: indicationNames[0],
          matchedSynonyms: [matchedSynonyms[0]],
          icdCodes: [],
          match: false,
          children: [],
          parentH1Ids: []
        },
        {
          id: ids[1],
          indicationName: indicationNames[1],
          matchedSynonyms: [matchedSynonyms[1], matchedSynonyms[2]],
          icdCodes: [],
          match: false,
          children: [],
          parentH1Ids: []
        },
        {
          id: ids[2],
          indicationName: indicationNames[2],
          matchedSynonyms: [],
          icdCodes: [],
          match: false,
          children: [],
          parentH1Ids: []
        }
      ]);
    });

    it("should sort by ES score if sortBy value is not provided", async () => {
      const configService = createMockInstance(ConfigService);
      configService.elasticIndicationsIndex = faker.datatype.string();
      const elasticSearchIndicationsTreeService = createMockInstance(
        ElasticSearchIndicationsTreeService
      );

      elasticSearchIndicationsTreeService.query.mockResolvedValue({
        hits: {
          hits: []
        }
      } as any);
      const featureFlagsService = mockFeatureFlagsService();
      const indicationIcdInstitutionCountsRepository = createMockInstance(
        IndicationIcdInstitutionCountsRepository
      );
      const indicationInstitutionCountsRepository = createMockInstance(
        IndicationInstitutionCountsRepository
      );
      const searchAnalyticsTracerService = createMockInstance(
        SearchAnalyticsTracerService
      );
      const indicationsTreeSearchService = new IndicationsTreeSearchService(
        configService,
        elasticSearchIndicationsTreeService,
        featureFlagsService,
        indicationInstitutionCountsRepository,
        indicationIcdInstitutionCountsRepository,
        searchAnalyticsTracerService
      );

      await indicationsTreeSearchService.searchIndicationsByQuery({
        query: faker.datatype.string(),
        indicationSource: [IndicationSource.ALL],
        size: faker.datatype.number(),
        projectId: faker.datatype.string()
      });

      expect(elasticSearchIndicationsTreeService.query).toHaveBeenCalledWith(
        expect.objectContaining({
          index: configService.elasticIndicationsIndex,
          sort: [{ _score: { order: "desc" } }, { sort_id: "asc" }]
        })
      );
    });

    it("should sort by HCP community size when sortBy is HCP_COMMUNITY_SIZE", async () => {
      const configService = createMockInstance(ConfigService);
      configService.elasticIndicationsIndex = faker.datatype.string();
      const elasticSearchIndicationsTreeService = createMockInstance(
        ElasticSearchIndicationsTreeService
      );

      elasticSearchIndicationsTreeService.query.mockResolvedValue({
        hits: {
          hits: []
        }
      } as any);
      const featureFlagsService = mockFeatureFlagsService();
      const indicationIcdInstitutionCountsRepository = createMockInstance(
        IndicationIcdInstitutionCountsRepository
      );
      const indicationInstitutionCountsRepository = createMockInstance(
        IndicationInstitutionCountsRepository
      );
      const searchAnalyticsTracerService = createMockInstance(
        SearchAnalyticsTracerService
      );
      const indicationsTreeSearchService = new IndicationsTreeSearchService(
        configService,
        elasticSearchIndicationsTreeService,
        featureFlagsService,
        indicationInstitutionCountsRepository,
        indicationIcdInstitutionCountsRepository,
        searchAnalyticsTracerService
      );

      await indicationsTreeSearchService.searchIndicationsByQuery({
        query: faker.datatype.string(),
        indicationSource: [IndicationSource.ALL],
        size: faker.datatype.number(),
        sortBy: IndicationSortBy.HCP_COMMUNITY_SIZE,
        projectId: faker.datatype.string()
      });

      expect(elasticSearchIndicationsTreeService.query).toHaveBeenCalledWith(
        expect.objectContaining({
          index: configService.elasticIndicationsIndex,
          sort: [
            { sort_id: "asc" },
            { community_size: "desc" },
            { _score: { order: "desc" } }
          ]
        })
      );
    });

    it("should sort by patient count when sortBy is PATIENT_COUNT", async () => {
      const configService = createMockInstance(ConfigService);
      configService.elasticIndicationsIndex = faker.datatype.string();
      const elasticSearchIndicationsTreeService = createMockInstance(
        ElasticSearchIndicationsTreeService
      );

      elasticSearchIndicationsTreeService.query.mockResolvedValue({
        hits: {
          hits: []
        }
      } as any);
      const featureFlagsService = mockFeatureFlagsService();
      const indicationIcdInstitutionCountsRepository = createMockInstance(
        IndicationIcdInstitutionCountsRepository
      );
      const indicationInstitutionCountsRepository = createMockInstance(
        IndicationInstitutionCountsRepository
      );
      const searchAnalyticsTracerService = createMockInstance(
        SearchAnalyticsTracerService
      );
      const indicationsTreeSearchService = new IndicationsTreeSearchService(
        configService,
        elasticSearchIndicationsTreeService,
        featureFlagsService,
        indicationInstitutionCountsRepository,
        indicationIcdInstitutionCountsRepository,
        searchAnalyticsTracerService
      );

      await indicationsTreeSearchService.searchIndicationsByQuery({
        query: faker.datatype.string(),
        indicationSource: [IndicationSource.ALL],
        size: faker.datatype.number(),
        sortBy: IndicationSortBy.PATIENT_COUNT,
        projectId: faker.datatype.string()
      });

      expect(elasticSearchIndicationsTreeService.query).toHaveBeenCalledWith(
        expect.objectContaining({
          index: configService.elasticIndicationsIndex,
          sort: [{ patient_count: "desc" }, { _score: { order: "desc" } }]
        })
      );
    });

    it("should match using bool_prefix when enablePhraseMatchForIndicationSearch feature flag is OFF", async () => {
      const configService = createMockInstance(ConfigService);
      configService.elasticIndicationsIndex = faker.datatype.string();
      const elasticSearchIndicationsTreeService = createMockInstance(
        ElasticSearchIndicationsTreeService
      );

      elasticSearchIndicationsTreeService.query.mockResolvedValue({
        hits: {
          hits: []
        }
      } as any);
      const featureFlagsService = mockFeatureFlagsService({
        enablePhraseMatchForIndicationSearch: false
      });
      const indicationIcdInstitutionCountsRepository = createMockInstance(
        IndicationIcdInstitutionCountsRepository
      );
      const indicationInstitutionCountsRepository = createMockInstance(
        IndicationInstitutionCountsRepository
      );
      const searchAnalyticsTracerService = createMockInstance(
        SearchAnalyticsTracerService
      );
      const indicationsTreeSearchService = new IndicationsTreeSearchService(
        configService,
        elasticSearchIndicationsTreeService,
        featureFlagsService,
        indicationInstitutionCountsRepository,
        indicationIcdInstitutionCountsRepository,
        searchAnalyticsTracerService
      );

      const query = faker.datatype.string();
      await indicationsTreeSearchService.searchIndicationsByQuery({
        query,
        indicationSource: [IndicationSource.ALL],
        size: faker.datatype.number(),
        sortBy: IndicationSortBy.PATIENT_COUNT,
        projectId: faker.datatype.string()
      });

      expect(elasticSearchIndicationsTreeService.query).toHaveBeenCalledWith(
        expect.objectContaining({
          index: configService.elasticIndicationsIndex,
          query: expect.objectContaining({
            bool: expect.objectContaining({
              should: expect.arrayContaining([
                expect.objectContaining({
                  multi_match: expect.objectContaining({
                    query,
                    type: "bool_prefix"
                  })
                }),
                expect.objectContaining({
                  nested: expect.objectContaining({
                    path: "synonyms",
                    query: expect.objectContaining({
                      multi_match: expect.objectContaining({
                        query,
                        type: "bool_prefix"
                      })
                    })
                  })
                })
              ])
            })
          }),
          collapse: {
            field: "indication_name"
          }
        })
      );
    });

    it("should match using phrase_prefix when enablePhraseMatchForIndicationSearch feature flag is ON", async () => {
      const configService = createMockInstance(ConfigService);
      configService.elasticIndicationsIndex = faker.datatype.string();
      const elasticSearchIndicationsTreeService = createMockInstance(
        ElasticSearchIndicationsTreeService
      );

      elasticSearchIndicationsTreeService.query.mockResolvedValue({
        hits: {
          hits: []
        }
      } as any);
      const featureFlagsService = mockFeatureFlagsService({
        enablePhraseMatchForIndicationSearch: true
      });
      const indicationIcdInstitutionCountsRepository = createMockInstance(
        IndicationIcdInstitutionCountsRepository
      );
      const indicationInstitutionCountsRepository = createMockInstance(
        IndicationInstitutionCountsRepository
      );
      const searchAnalyticsTracerService = createMockInstance(
        SearchAnalyticsTracerService
      );
      const indicationsTreeSearchService = new IndicationsTreeSearchService(
        configService,
        elasticSearchIndicationsTreeService,
        featureFlagsService,
        indicationInstitutionCountsRepository,
        indicationIcdInstitutionCountsRepository,
        searchAnalyticsTracerService
      );

      const query = faker.datatype.string();
      await indicationsTreeSearchService.searchIndicationsByQuery({
        query,
        indicationSource: [IndicationSource.ALL],
        size: faker.datatype.number(),
        sortBy: IndicationSortBy.PATIENT_COUNT,
        projectId: faker.datatype.string()
      });

      expect(elasticSearchIndicationsTreeService.query).toHaveBeenCalledWith(
        expect.objectContaining({
          index: configService.elasticIndicationsIndex,
          query: expect.objectContaining({
            bool: expect.objectContaining({
              should: expect.arrayContaining([
                expect.objectContaining({
                  multi_match: expect.objectContaining({
                    query,
                    type: "phrase_prefix"
                  })
                }),
                expect.objectContaining({
                  nested: expect.objectContaining({
                    path: "synonyms",
                    query: expect.objectContaining({
                      multi_match: expect.objectContaining({
                        query,
                        type: "phrase_prefix"
                      })
                    })
                  })
                })
              ])
            })
          })
        })
      );
    });

    it("should search on description if enableIcdDescriptionMatching feature flag is ON", async () => {
      const configService = createMockInstance(ConfigService);
      configService.elasticIndicationsIndex = faker.datatype.string();
      const elasticSearchIndicationsTreeService = createMockInstance(
        ElasticSearchIndicationsTreeService
      );

      const ids = [faker.datatype.string(), faker.datatype.string()];
      const indicationNames = [
        faker.datatype.string(),
        faker.datatype.string()
      ];
      elasticSearchIndicationsTreeService.query.mockResolvedValue({
        hits: {
          hits: [
            {
              _id: ids[0],
              _source: {
                indication_name: indicationNames[0],
                indication_type: "L1"
              }
            },
            {
              _id: ids[1],
              _source: {
                indication_name: indicationNames[1],
                indication_type: "L1"
              }
            }
          ]
        }
      } as any);
      const featureFlagsService = mockFeatureFlagsService({
        enableIcdDescriptionMatching: true
      });
      const indicationIcdInstitutionCountsRepository = createMockInstance(
        IndicationIcdInstitutionCountsRepository
      );
      const indicationInstitutionCountsRepository = createMockInstance(
        IndicationInstitutionCountsRepository
      );
      const searchAnalyticsTracerService = createMockInstance(
        SearchAnalyticsTracerService
      );
      const indicationsTreeSearchService = new IndicationsTreeSearchService(
        configService,
        elasticSearchIndicationsTreeService,
        featureFlagsService,
        indicationInstitutionCountsRepository,
        indicationIcdInstitutionCountsRepository,
        searchAnalyticsTracerService
      );

      const query = faker.datatype.string();

      const indications =
        await indicationsTreeSearchService.searchIndicationsByQuery({
          query,
          indicationSource: [IndicationSource.ALL],
          size: 2,
          projectId: faker.datatype.string()
        });

      expect(elasticSearchIndicationsTreeService.query).toHaveBeenCalledWith(
        expect.objectContaining({
          index: configService.elasticIndicationsIndex,
          size: 2,
          query: expect.objectContaining({
            bool: expect.objectContaining({
              should: expect.arrayContaining([
                expect.objectContaining({
                  multi_match: {
                    query,
                    operator: "AND",
                    type: "bool_prefix",
                    fields: [
                      "description",
                      "description._2gram",
                      "description._3gram"
                    ]
                  }
                })
              ])
            })
          })
        })
      );

      expect(indications).toEqual([
        {
          id: ids[0],
          indicationName: indicationNames[0],
          indicationType: "L1",
          children: [],
          matchedSynonyms: [],
          icdCodes: [],
          match: false,
          parentH1Ids: []
        },
        {
          id: ids[1],
          indicationName: indicationNames[1],
          indicationType: "L1",
          children: [],
          matchedSynonyms: [],
          icdCodes: [],
          match: false,
          parentH1Ids: []
        }
      ]);
    });

    it("should not search on description if enableIcdDescriptionMatching feature flag is OFF", async () => {
      const configService = createMockInstance(ConfigService);
      configService.elasticIndicationsIndex = faker.datatype.string();
      const elasticSearchIndicationsTreeService = createMockInstance(
        ElasticSearchIndicationsTreeService
      );

      const ids = [faker.datatype.string(), faker.datatype.string()];
      const indicationNames = [
        faker.datatype.string(),
        faker.datatype.string()
      ];
      elasticSearchIndicationsTreeService.query.mockResolvedValue({
        hits: {
          hits: [
            {
              _id: ids[0],
              _source: {
                indication_name: indicationNames[0],
                indication_type: "L1"
              }
            },
            {
              _id: ids[1],
              _source: {
                indication_name: indicationNames[1],
                indication_type: "L1"
              }
            }
          ]
        }
      } as any);
      const featureFlagsService = mockFeatureFlagsService({
        enableIcdDescriptionMatching: false
      });
      const indicationIcdInstitutionCountsRepository = createMockInstance(
        IndicationIcdInstitutionCountsRepository
      );
      const indicationInstitutionCountsRepository = createMockInstance(
        IndicationInstitutionCountsRepository
      );
      const searchAnalyticsTracerService = createMockInstance(
        SearchAnalyticsTracerService
      );
      const indicationsTreeSearchService = new IndicationsTreeSearchService(
        configService,
        elasticSearchIndicationsTreeService,
        featureFlagsService,
        indicationInstitutionCountsRepository,
        indicationIcdInstitutionCountsRepository,
        searchAnalyticsTracerService
      );

      const query = faker.datatype.string();

      const indications =
        await indicationsTreeSearchService.searchIndicationsByQuery({
          query,
          indicationSource: [IndicationSource.ALL],
          size: 2,
          projectId: faker.datatype.string()
        });

      expect(elasticSearchIndicationsTreeService.query).toHaveBeenCalledWith(
        expect.objectContaining({
          index: configService.elasticIndicationsIndex,
          size: 2,
          query: expect.objectContaining({
            bool: expect.objectContaining({
              should: expect.not.arrayContaining([
                expect.objectContaining({
                  multi_match: {
                    query,
                    operator: "AND",
                    type: "bool_prefix",
                    fields: [
                      "description",
                      "description._2gram",
                      "description._3gram"
                    ]
                  }
                })
              ])
            })
          })
        })
      );

      expect(indications).toEqual([
        {
          id: ids[0],
          indicationName: indicationNames[0],
          indicationType: "L1",
          children: [],
          matchedSynonyms: [],
          icdCodes: [],
          match: false,
          parentH1Ids: []
        },
        {
          id: ids[1],
          indicationName: indicationNames[1],
          indicationType: "L1",
          children: [],
          matchedSynonyms: [],
          icdCodes: [],
          match: false,
          parentH1Ids: []
        }
      ]);
    });

    it("should filter on indication_type if indicationType is not empty", async () => {
      const configService = createMockInstance(ConfigService);
      configService.elasticIndicationsIndex = faker.datatype.string();
      const elasticSearchIndicationsTreeService = createMockInstance(
        ElasticSearchIndicationsTreeService
      );

      const ids = [faker.datatype.string(), faker.datatype.string()];
      const indicationNames = [
        faker.datatype.string(),
        faker.datatype.string()
      ];
      elasticSearchIndicationsTreeService.query.mockResolvedValue({
        hits: {
          hits: [
            {
              _id: ids[0],
              _source: {
                indication_name: indicationNames[0],
                indication_type: "ICD"
              }
            },
            {
              _id: ids[1],
              _source: {
                indication_name: indicationNames[1],
                indication_type: "ICD"
              }
            }
          ]
        }
      } as any);
      const featureFlagsService = mockFeatureFlagsService({
        enableIcdDescriptionMatching: true
      });
      const indicationIcdInstitutionCountsRepository = createMockInstance(
        IndicationIcdInstitutionCountsRepository
      );
      const indicationInstitutionCountsRepository = createMockInstance(
        IndicationInstitutionCountsRepository
      );
      const searchAnalyticsTracerService = createMockInstance(
        SearchAnalyticsTracerService
      );
      const indicationsTreeSearchService = new IndicationsTreeSearchService(
        configService,
        elasticSearchIndicationsTreeService,
        featureFlagsService,
        indicationInstitutionCountsRepository,
        indicationIcdInstitutionCountsRepository,
        searchAnalyticsTracerService
      );

      const query = faker.datatype.string();

      const indications =
        await indicationsTreeSearchService.searchIndicationsByQuery({
          query,
          indicationSource: [IndicationSource.ALL],
          indicationType: [IndicationType.ICD],
          projectId: faker.datatype.string(),
          size: 10
        });

      expect(elasticSearchIndicationsTreeService.query).toHaveBeenCalledWith(
        expect.objectContaining({
          index: configService.elasticIndicationsIndex,
          size: 10,
          query: expect.objectContaining({
            bool: expect.objectContaining({
              filter: expect.arrayContaining([
                expect.termsQuery("indication_type", [IndicationType.ICD])
              ])
            })
          })
        })
      );

      expect(indications).toEqual([
        {
          id: ids[0],
          indicationName: indicationNames[0],
          indicationType: "ICD",
          children: [],
          matchedSynonyms: [],
          icdCodes: [],
          match: false,
          parentH1Ids: []
        },
        {
          id: ids[1],
          indicationName: indicationNames[1],
          indicationType: "ICD",
          children: [],
          matchedSynonyms: [],
          icdCodes: [],
          match: false,
          parentH1Ids: []
        }
      ]);
    });

    it("should collapse indications on name if includeDuplicates is falsey", async () => {
      const configService = createMockInstance(ConfigService);
      configService.elasticIndicationsIndex = faker.datatype.string();
      const elasticSearchIndicationsTreeService = createMockInstance(
        ElasticSearchIndicationsTreeService
      );

      const ids = [faker.datatype.string(), faker.datatype.string()];
      const indicationNames = [
        faker.datatype.string(),
        faker.datatype.string()
      ];
      elasticSearchIndicationsTreeService.query.mockResolvedValue({
        hits: {
          hits: [
            {
              _id: ids[0],
              _source: {
                indication_name: indicationNames[0],
                indication_type: "L3"
              }
            },
            {
              _id: ids[1],
              _source: {
                indication_name: indicationNames[1],
                indication_type: "L2"
              }
            }
          ]
        }
      } as any);
      const featureFlagsService = mockFeatureFlagsService();
      const indicationIcdInstitutionCountsRepository = createMockInstance(
        IndicationIcdInstitutionCountsRepository
      );
      const indicationInstitutionCountsRepository = createMockInstance(
        IndicationInstitutionCountsRepository
      );
      const searchAnalyticsTracerService = createMockInstance(
        SearchAnalyticsTracerService
      );
      const indicationsTreeSearchService = new IndicationsTreeSearchService(
        configService,
        elasticSearchIndicationsTreeService,
        featureFlagsService,
        indicationInstitutionCountsRepository,
        indicationIcdInstitutionCountsRepository,
        searchAnalyticsTracerService
      );

      const query = faker.datatype.string();

      await indicationsTreeSearchService.searchIndicationsByQuery({
        query,
        indicationSource: [IndicationSource.ALL],
        indicationType: [IndicationType.ICD],
        projectId: faker.datatype.string(),
        size: 10,
        includeDuplicates: faker.helpers.arrayElement([undefined, false])
      });

      expect(elasticSearchIndicationsTreeService.query).toHaveBeenCalledWith(
        expect.objectContaining({
          index: configService.elasticIndicationsIndex,
          size: 10,
          collapse: {
            field: "indication_name"
          }
        })
      );
    });

    it("should not collapse indications on name if includeDuplicates is true", async () => {
      const configService = createMockInstance(ConfigService);
      configService.elasticIndicationsIndex = faker.datatype.string();
      const elasticSearchIndicationsTreeService = createMockInstance(
        ElasticSearchIndicationsTreeService
      );

      const ids = [faker.datatype.string(), faker.datatype.string()];
      const indicationNames = [
        faker.datatype.string(),
        faker.datatype.string()
      ];
      elasticSearchIndicationsTreeService.query.mockResolvedValue({
        hits: {
          hits: [
            {
              _id: ids[0],
              _source: {
                indication_name: indicationNames[0],
                indication_type: "L3"
              }
            },
            {
              _id: ids[1],
              _source: {
                indication_name: indicationNames[1],
                indication_type: "L2"
              }
            }
          ]
        }
      } as any);
      const featureFlagsService = mockFeatureFlagsService();
      const indicationIcdInstitutionCountsRepository = createMockInstance(
        IndicationIcdInstitutionCountsRepository
      );
      const indicationInstitutionCountsRepository = createMockInstance(
        IndicationInstitutionCountsRepository
      );
      const searchAnalyticsTracerService = createMockInstance(
        SearchAnalyticsTracerService
      );
      const indicationsTreeSearchService = new IndicationsTreeSearchService(
        configService,
        elasticSearchIndicationsTreeService,
        featureFlagsService,
        indicationInstitutionCountsRepository,
        indicationIcdInstitutionCountsRepository,
        searchAnalyticsTracerService
      );

      const query = faker.datatype.string();

      await indicationsTreeSearchService.searchIndicationsByQuery({
        query,
        indicationSource: [IndicationSource.ALL],
        indicationType: [IndicationType.ICD],
        projectId: faker.datatype.string(),
        size: 10,
        includeDuplicates: true
      });

      expect(elasticSearchIndicationsTreeService.query).toHaveBeenCalledWith(
        expect.objectContaining({
          index: configService.elasticIndicationsIndex,
          size: 10,
          collapse: undefined
        })
      );
    });
  });

  describe("searchRootIndications", () => {
    it("should return empty array if indicationSource is empty", async () => {
      const configService = createMockInstance(ConfigService);
      configService.elasticIndicationsIndex = faker.datatype.string();
      const elasticSearchIndicationsTreeService = createMockInstance(
        ElasticSearchIndicationsTreeService
      );
      const featureFlagsService = mockFeatureFlagsService();
      const indicationIcdInstitutionCountsRepository = createMockInstance(
        IndicationIcdInstitutionCountsRepository
      );
      const indicationInstitutionCountsRepository = createMockInstance(
        IndicationInstitutionCountsRepository
      );
      const searchAnalyticsTracerService = createMockInstance(
        SearchAnalyticsTracerService
      );
      const indicationsTreeSearchService = new IndicationsTreeSearchService(
        configService,
        elasticSearchIndicationsTreeService,
        featureFlagsService,
        indicationInstitutionCountsRepository,
        indicationIcdInstitutionCountsRepository,
        searchAnalyticsTracerService
      );
      elasticSearchIndicationsTreeService.query.mockRejectedValue(
        new Error("elasticSearchIndicationsTreeService.query threw an error")
      );

      const indications =
        await indicationsTreeSearchService.searchRootIndications({
          indicationSource: []
        });

      expect(indications).toEqual([]);
    });

    it("should return empty array if there are no hits", async () => {
      const configService = createMockInstance(ConfigService);
      configService.elasticIndicationsIndex = faker.datatype.string();
      const elasticSearchIndicationsTreeService = createMockInstance(
        ElasticSearchIndicationsTreeService
      );
      const featureFlagsService = mockFeatureFlagsService();
      const indicationIcdInstitutionCountsRepository = createMockInstance(
        IndicationIcdInstitutionCountsRepository
      );
      const indicationInstitutionCountsRepository = createMockInstance(
        IndicationInstitutionCountsRepository
      );
      const searchAnalyticsTracerService = createMockInstance(
        SearchAnalyticsTracerService
      );
      const indicationsTreeSearchService = new IndicationsTreeSearchService(
        configService,
        elasticSearchIndicationsTreeService,
        featureFlagsService,
        indicationInstitutionCountsRepository,
        indicationIcdInstitutionCountsRepository,
        searchAnalyticsTracerService
      );
      elasticSearchIndicationsTreeService.query.mockResolvedValue({
        hits: {
          hits: [],
          total: 0
        }
      } as any);

      const indications =
        await indicationsTreeSearchService.searchRootIndications({
          indicationSource: [IndicationSource.ALL]
        });

      expect(indications).toEqual([]);
      expect(
        searchAnalyticsTracerService.sendAnalyticsEvent
      ).toHaveBeenCalled();
    });

    it("should set result size to default size if size is undefined", async () => {
      const configService = createMockInstance(ConfigService);
      configService.elasticIndicationsIndex = faker.datatype.string();
      const elasticSearchIndicationsTreeService = createMockInstance(
        ElasticSearchIndicationsTreeService
      );
      const featureFlagsService = mockFeatureFlagsService();
      const indicationIcdInstitutionCountsRepository = createMockInstance(
        IndicationIcdInstitutionCountsRepository
      );
      const indicationInstitutionCountsRepository = createMockInstance(
        IndicationInstitutionCountsRepository
      );
      const searchAnalyticsTracerService = createMockInstance(
        SearchAnalyticsTracerService
      );
      const indicationsTreeSearchService = new IndicationsTreeSearchService(
        configService,
        elasticSearchIndicationsTreeService,
        featureFlagsService,
        indicationInstitutionCountsRepository,
        indicationIcdInstitutionCountsRepository,
        searchAnalyticsTracerService
      );
      const ids = [faker.datatype.string(), faker.datatype.string()];
      const indicationNames = [
        faker.datatype.string(),
        faker.datatype.string()
      ];
      elasticSearchIndicationsTreeService.query.mockResolvedValue({
        hits: {
          hits: [
            {
              _id: ids[0],
              _source: {
                indication_name: indicationNames[0]
              }
            },
            {
              _id: ids[1],
              _source: {
                indication_name: indicationNames[1]
              }
            }
          ]
        }
      } as any);

      const indications =
        await indicationsTreeSearchService.searchRootIndications({
          indicationSource: [IndicationSource.ALL]
        });

      expect(indications).toEqual([
        {
          id: ids[0],
          indicationName: indicationNames[0],
          matchedSynonyms: [],
          icdCodes: [],
          match: false,
          children: [],
          parentH1Ids: []
        },
        {
          id: ids[1],
          indicationName: indicationNames[1],
          matchedSynonyms: [],
          icdCodes: [],
          match: false,
          children: [],
          parentH1Ids: []
        }
      ]);

      expect(elasticSearchIndicationsTreeService.query).toHaveBeenCalledWith(
        expect.objectContaining({
          index: configService.elasticIndicationsIndex,
          size: 100
        })
      );
    });

    it("should set result size to passed value", async () => {
      const configService = createMockInstance(ConfigService);
      configService.elasticIndicationsIndex = faker.datatype.string();
      const elasticSearchIndicationsTreeService = createMockInstance(
        ElasticSearchIndicationsTreeService
      );
      const featureFlagsService = mockFeatureFlagsService();
      const indicationIcdInstitutionCountsRepository = createMockInstance(
        IndicationIcdInstitutionCountsRepository
      );
      const indicationInstitutionCountsRepository = createMockInstance(
        IndicationInstitutionCountsRepository
      );
      const searchAnalyticsTracerService = createMockInstance(
        SearchAnalyticsTracerService
      );
      const indicationsTreeSearchService = new IndicationsTreeSearchService(
        configService,
        elasticSearchIndicationsTreeService,
        featureFlagsService,
        indicationInstitutionCountsRepository,
        indicationIcdInstitutionCountsRepository,
        searchAnalyticsTracerService
      );
      const ids = [faker.datatype.string(), faker.datatype.string()];
      const indicationNames = [
        faker.datatype.string(),
        faker.datatype.string()
      ];
      elasticSearchIndicationsTreeService.query.mockResolvedValue({
        hits: {
          hits: [
            {
              _id: ids[0],
              _source: {
                indication_name: indicationNames[0]
              }
            },
            {
              _id: ids[1],
              _source: {
                indication_name: indicationNames[1]
              }
            }
          ]
        }
      } as any);

      const indications =
        await indicationsTreeSearchService.searchRootIndications({
          indicationSource: [IndicationSource.ALL],
          size: 2
        });

      expect(indications).toEqual([
        {
          id: ids[0],
          indicationName: indicationNames[0],
          matchedSynonyms: [],
          icdCodes: [],
          match: false,
          children: [],
          parentH1Ids: []
        },
        {
          id: ids[1],
          indicationName: indicationNames[1],
          matchedSynonyms: [],
          icdCodes: [],
          match: false,
          children: [],
          parentH1Ids: []
        }
      ]);

      expect(elasticSearchIndicationsTreeService.query).toHaveBeenCalledWith(
        expect.objectContaining({
          index: configService.elasticIndicationsIndex,
          size: 2
        })
      );
    });
  });

  describe("getSubTreesForRoot", () => {
    it("should return empty array if indicationSource is empty", async () => {
      const configService = createMockInstance(ConfigService);
      configService.elasticIndicationsIndex = faker.datatype.string();
      const elasticSearchIndicationsTreeService = createMockInstance(
        ElasticSearchIndicationsTreeService
      );
      const featureFlagsService = mockFeatureFlagsService();
      const indicationIcdInstitutionCountsRepository = createMockInstance(
        IndicationIcdInstitutionCountsRepository
      );
      const indicationInstitutionCountsRepository = createMockInstance(
        IndicationInstitutionCountsRepository
      );
      const searchAnalyticsTracerService = createMockInstance(
        SearchAnalyticsTracerService
      );
      const indicationsTreeSearchService = new IndicationsTreeSearchService(
        configService,
        elasticSearchIndicationsTreeService,
        featureFlagsService,
        indicationInstitutionCountsRepository,
        indicationIcdInstitutionCountsRepository,
        searchAnalyticsTracerService
      );

      elasticSearchIndicationsTreeService.query.mockRejectedValue(
        new Error("elasticSearchIndicationsTreeService.query threw an error")
      );

      const indications = await indicationsTreeSearchService.getSubTreesForRoot(
        {
          indicationSource: [],
          rootId: faker.datatype.string(),
          sortBy: IndicationSortBy.HCP_COMMUNITY_SIZE
        }
      );

      expect(indications).toEqual([]);
    });

    it("should return empty array if no hits", async () => {
      const configService = createMockInstance(ConfigService);
      configService.elasticIndicationsIndex = faker.datatype.string();
      const elasticSearchIndicationsTreeService = createMockInstance(
        ElasticSearchIndicationsTreeService
      );
      const featureFlagsService = mockFeatureFlagsService();
      const indicationIcdInstitutionCountsRepository = createMockInstance(
        IndicationIcdInstitutionCountsRepository
      );
      const indicationInstitutionCountsRepository = createMockInstance(
        IndicationInstitutionCountsRepository
      );
      const searchAnalyticsTracerService = createMockInstance(
        SearchAnalyticsTracerService
      );
      const indicationsTreeSearchService = new IndicationsTreeSearchService(
        configService,
        elasticSearchIndicationsTreeService,
        featureFlagsService,
        indicationInstitutionCountsRepository,
        indicationIcdInstitutionCountsRepository,
        searchAnalyticsTracerService
      );

      elasticSearchIndicationsTreeService.query.mockResolvedValue({
        hits: {
          hits: []
        }
      } as any);

      const indications = await indicationsTreeSearchService.getSubTreesForRoot(
        {
          indicationSource: [IndicationSource.CLAIMS],
          rootId: faker.datatype.string(),
          sortBy: IndicationSortBy.HCP_COMMUNITY_SIZE
        }
      );

      expect(indications).toEqual([]);
      expect(
        searchAnalyticsTracerService.sendAnalyticsEvent
      ).toHaveBeenCalled();
    });

    it("should return throw error if ES query errored out", async () => {
      const configService = createMockInstance(ConfigService);
      configService.elasticIndicationsIndex = faker.datatype.string();
      const elasticSearchIndicationsTreeService = createMockInstance(
        ElasticSearchIndicationsTreeService
      );
      const elasticSearchError = {
        message: faker.random.words(),
        statusCode: 400,
        body: undefined,
        headers: undefined,
        name: ""
      };

      elasticSearchIndicationsTreeService.query.mockRejectedValue(
        elasticSearchError as any
      );
      const featureFlagsService = mockFeatureFlagsService();
      const indicationIcdInstitutionCountsRepository = createMockInstance(
        IndicationIcdInstitutionCountsRepository
      );
      const indicationInstitutionCountsRepository = createMockInstance(
        IndicationInstitutionCountsRepository
      );
      const searchAnalyticsTracerService = createMockInstance(
        SearchAnalyticsTracerService
      );
      const indicationsTreeSearchService = new IndicationsTreeSearchService(
        configService,
        elasticSearchIndicationsTreeService,
        featureFlagsService,
        indicationInstitutionCountsRepository,
        indicationIcdInstitutionCountsRepository,
        searchAnalyticsTracerService
      );

      await expect(
        indicationsTreeSearchService.getSubTreesForRoot({
          rootId: faker.datatype.string(),
          indicationSource: [IndicationSource.CLAIMS],
          sortBy: IndicationSortBy.HCP_COMMUNITY_SIZE
        })
      ).rejects.toThrow(new Error(elasticSearchError.message));
    });

    it("should construct subtrees for the rootId and sort them based on HCP size", async () => {
      const configService = createMockInstance(ConfigService);
      configService.elasticIndicationsIndex = faker.datatype.string();
      const elasticSearchIndicationsTreeService = createMockInstance(
        ElasticSearchIndicationsTreeService
      );

      const rootId = faker.datatype.string();
      const l2Ids = [faker.datatype.string(), faker.datatype.string()];
      const l3Ids = [
        faker.datatype.string(),
        faker.datatype.string(),
        faker.datatype.string(),
        faker.datatype.string(),
        faker.datatype.string()
      ];
      const l2IndicationNames = [
        faker.datatype.string(),
        faker.datatype.string()
      ];

      const l3IndicationNames = [
        faker.datatype.string(),
        faker.datatype.string(),
        faker.datatype.string(),
        faker.datatype.string(),
        faker.datatype.string()
      ];

      const l3HcpSizeForTreeA = [
        faker.datatype.number(),
        faker.datatype.number()
      ].sort((a, b) => b - a);
      const l3HcpSizeForTreeB = [
        faker.datatype.number(),
        faker.datatype.number(),
        faker.datatype.number()
      ].sort((a, b) => b - a);
      const l3HcpSizes = [...l3HcpSizeForTreeA, ...l3HcpSizeForTreeB];

      elasticSearchIndicationsTreeService.query.mockResolvedValue({
        hits: {
          hits: [
            {
              _id: l2Ids[0],
              _source: {
                indication_name: l2IndicationNames[0],
                ancestor_ids: [rootId]
              }
            },
            {
              _id: l2Ids[1],
              _source: {
                indication_name: l2IndicationNames[1],
                ancestor_ids: [rootId]
              }
            },
            {
              _id: l3Ids[0],
              _source: {
                indication_name: l3IndicationNames[0],
                community_size: l3HcpSizes[0],
                ancestor_ids: [rootId, l2Ids[0]]
              }
            },
            {
              _id: l3Ids[1],
              _source: {
                indication_name: l3IndicationNames[1],
                community_size: l3HcpSizes[1],
                ancestor_ids: [rootId, l2Ids[0]]
              }
            },
            {
              _id: l3Ids[2],
              _source: {
                indication_name: l3IndicationNames[2],
                community_size: l3HcpSizes[2],
                ancestor_ids: [rootId, l2Ids[1]]
              }
            },
            {
              _id: l3Ids[3],
              _source: {
                indication_name: l3IndicationNames[3],
                community_size: l3HcpSizes[3],
                ancestor_ids: [rootId, l2Ids[1]]
              }
            },
            {
              _id: l3Ids[4],
              _source: {
                indication_name: l3IndicationNames[4],
                community_size: l3HcpSizes[4],
                ancestor_ids: [rootId, l2Ids[1]]
              }
            }
          ]
        }
      } as any);
      const featureFlagsService = mockFeatureFlagsService();
      const indicationIcdInstitutionCountsRepository = createMockInstance(
        IndicationIcdInstitutionCountsRepository
      );
      const indicationInstitutionCountsRepository = createMockInstance(
        IndicationInstitutionCountsRepository
      );
      const searchAnalyticsTracerService = createMockInstance(
        SearchAnalyticsTracerService
      );
      const indicationsTreeSearchService = new IndicationsTreeSearchService(
        configService,
        elasticSearchIndicationsTreeService,
        featureFlagsService,
        indicationInstitutionCountsRepository,
        indicationIcdInstitutionCountsRepository,
        searchAnalyticsTracerService
      );

      const subTrees = await indicationsTreeSearchService.getSubTreesForRoot({
        rootId,
        indicationSource: [IndicationSource.CLAIMS],
        sortBy: IndicationSortBy.HCP_COMMUNITY_SIZE
      });

      expect(elasticSearchIndicationsTreeService.query).toHaveBeenCalled();

      if (
        l3HcpSizes[0] + l3HcpSizes[1] >=
        l3HcpSizes[2] + l3HcpSizes[3] + l3HcpSizes[4]
      ) {
        expect(subTrees).toEqual([
          expect.objectContaining({
            id: l2Ids[0],
            indicationName: l2IndicationNames[0],
            hcpCommunitySize: l3HcpSizes[0] + l3HcpSizes[1],
            children: [
              expect.objectContaining({
                id: l3Ids[0],
                indicationName: l3IndicationNames[0],
                hcpCommunitySize: l3HcpSizes[0],
                children: []
              }),
              expect.objectContaining({
                id: l3Ids[1],
                indicationName: l3IndicationNames[1],
                hcpCommunitySize: l3HcpSizes[1],
                children: []
              })
            ]
          }),
          expect.objectContaining({
            id: l2Ids[1],
            indicationName: l2IndicationNames[1],
            hcpCommunitySize: l3HcpSizes[2] + l3HcpSizes[3] + l3HcpSizes[4],
            children: [
              expect.objectContaining({
                id: l3Ids[2],
                indicationName: l3IndicationNames[2],
                hcpCommunitySize: l3HcpSizes[2],
                children: []
              }),
              expect.objectContaining({
                id: l3Ids[3],
                indicationName: l3IndicationNames[3],
                hcpCommunitySize: l3HcpSizes[3],
                children: []
              }),
              expect.objectContaining({
                id: l3Ids[4],
                indicationName: l3IndicationNames[4],
                hcpCommunitySize: l3HcpSizes[4],
                children: []
              })
            ]
          })
        ]);
      } else {
        expect(subTrees).toEqual([
          expect.objectContaining({
            id: l2Ids[1],
            indicationName: l2IndicationNames[1],
            hcpCommunitySize: l3HcpSizes[2] + l3HcpSizes[3] + l3HcpSizes[4],
            children: [
              expect.objectContaining({
                id: l3Ids[2],
                indicationName: l3IndicationNames[2],
                hcpCommunitySize: l3HcpSizes[2],
                children: []
              }),
              expect.objectContaining({
                id: l3Ids[3],
                indicationName: l3IndicationNames[3],
                hcpCommunitySize: l3HcpSizes[3],
                children: []
              }),
              expect.objectContaining({
                id: l3Ids[4],
                indicationName: l3IndicationNames[4],
                hcpCommunitySize: l3HcpSizes[4],
                children: []
              })
            ]
          }),
          expect.objectContaining({
            id: l2Ids[0],
            indicationName: l2IndicationNames[0],
            hcpCommunitySize: l3HcpSizes[0] + l3HcpSizes[1],
            children: [
              expect.objectContaining({
                id: l3Ids[0],
                indicationName: l3IndicationNames[0],
                hcpCommunitySize: l3HcpSizes[0],
                children: []
              }),
              expect.objectContaining({
                id: l3Ids[1],
                indicationName: l3IndicationNames[1],
                hcpCommunitySize: l3HcpSizes[1],
                children: []
              })
            ]
          })
        ]);
      }
    });
  });

  describe("searchIndicationTreesByQuery", () => {
    it("should return empty array if indicationSource is empty", async () => {
      const configService = createMockInstance(ConfigService);
      configService.elasticIndicationsIndex = faker.datatype.string();
      const elasticSearchIndicationsTreeService = createMockInstance(
        ElasticSearchIndicationsTreeService
      );
      const featureFlagsService = mockFeatureFlagsService();
      const indicationIcdInstitutionCountsRepository = createMockInstance(
        IndicationIcdInstitutionCountsRepository
      );
      const indicationInstitutionCountsRepository = createMockInstance(
        IndicationInstitutionCountsRepository
      );
      const searchAnalyticsTracerService = createMockInstance(
        SearchAnalyticsTracerService
      );
      const indicationsTreeSearchService = new IndicationsTreeSearchService(
        configService,
        elasticSearchIndicationsTreeService,
        featureFlagsService,
        indicationInstitutionCountsRepository,
        indicationIcdInstitutionCountsRepository,
        searchAnalyticsTracerService
      );

      elasticSearchIndicationsTreeService.query.mockRejectedValue(
        new Error("elasticSearchIndicationsTreeService.query threw an error")
      );

      const indications =
        await indicationsTreeSearchService.searchIndicationTreesByQuery({
          indicationSource: [],
          query: faker.datatype.string(),
          projectId: faker.datatype.string(),
          sortBy: IndicationSortBy.PATIENT_COUNT
        });

      expect(indications).toEqual([]);
    });

    it("should return empty array if there are no matched nodes", async () => {
      const configService = createMockInstance(ConfigService);
      configService.elasticIndicationsIndex = faker.datatype.string();
      const elasticSearchIndicationsTreeService = createMockInstance(
        ElasticSearchIndicationsTreeService
      );
      const featureFlagsService = mockFeatureFlagsService();
      const indicationIcdInstitutionCountsRepository = createMockInstance(
        IndicationIcdInstitutionCountsRepository
      );
      const indicationInstitutionCountsRepository = createMockInstance(
        IndicationInstitutionCountsRepository
      );
      const searchAnalyticsTracerService = createMockInstance(
        SearchAnalyticsTracerService
      );
      const indicationsTreeSearchService = new IndicationsTreeSearchService(
        configService,
        elasticSearchIndicationsTreeService,
        featureFlagsService,
        indicationInstitutionCountsRepository,
        indicationIcdInstitutionCountsRepository,
        searchAnalyticsTracerService
      );

      elasticSearchIndicationsTreeService.query.mockResolvedValueOnce({
        hits: {
          hits: []
        }
      } as any);

      elasticSearchIndicationsTreeService.query.mockRejectedValueOnce(
        new Error("elasticSearchIndicationsTreeService.query threw an error")
      );

      const indications =
        await indicationsTreeSearchService.searchIndicationTreesByQuery({
          indicationSource: [IndicationSource.CLAIMS],
          query: faker.datatype.string(),
          projectId: faker.datatype.string(),
          sortBy: IndicationSortBy.PATIENT_COUNT
        });

      expect(indications).toEqual([]);
      expect(elasticSearchIndicationsTreeService.query).toHaveBeenCalledTimes(
        1
      );
      expect(
        searchAnalyticsTracerService.sendAnalyticsEvent
      ).toHaveBeenCalled();
    });

    it("should return throw error if ES query errored out", async () => {
      const configService = createMockInstance(ConfigService);
      configService.elasticIndicationsIndex = faker.datatype.string();
      const elasticSearchIndicationsTreeService = createMockInstance(
        ElasticSearchIndicationsTreeService
      );
      const elasticSearchError = {
        message: faker.random.words(),
        statusCode: 400,
        body: undefined,
        headers: undefined,
        name: ""
      };

      elasticSearchIndicationsTreeService.query.mockRejectedValue(
        elasticSearchError as any
      );
      const featureFlagsService = mockFeatureFlagsService();
      const indicationIcdInstitutionCountsRepository = createMockInstance(
        IndicationIcdInstitutionCountsRepository
      );
      const indicationInstitutionCountsRepository = createMockInstance(
        IndicationInstitutionCountsRepository
      );
      const searchAnalyticsTracerService = createMockInstance(
        SearchAnalyticsTracerService
      );
      const indicationsTreeSearchService = new IndicationsTreeSearchService(
        configService,
        elasticSearchIndicationsTreeService,
        featureFlagsService,
        indicationInstitutionCountsRepository,
        indicationIcdInstitutionCountsRepository,
        searchAnalyticsTracerService
      );

      await expect(
        indicationsTreeSearchService.searchIndicationTreesByQuery({
          query: faker.datatype.string(),
          indicationSource: [IndicationSource.CLAIMS],
          projectId: faker.datatype.string(),
          sortBy: IndicationSortBy.PATIENT_COUNT
        })
      ).rejects.toThrowError(elasticSearchError);
    });

    it("should construct trees out of matched nodes and sort then based on HCP size", async () => {
      const configService = createMockInstance(ConfigService);
      configService.elasticIndicationsIndex = faker.datatype.string();
      const elasticSearchIndicationsTreeService = createMockInstance(
        ElasticSearchIndicationsTreeService
      );

      const query = faker.datatype.string();
      const matchedNodeIds = [
        faker.datatype.string(),
        faker.datatype.string(),
        faker.datatype.string()
      ];
      const matchedIndicationNames = [
        faker.datatype.string(),
        faker.datatype.string(),
        faker.datatype.string()
      ];
      const matchedL3HcpSize = faker.datatype.number();

      const l1Ids = [faker.datatype.string()];
      const l2Ids = [faker.datatype.string(), faker.datatype.string()];
      const l3Ids = [
        faker.datatype.string(),
        faker.datatype.string(),
        faker.datatype.string(),
        faker.datatype.string(),
        faker.datatype.string()
      ];
      const l1IndicationNames = [faker.datatype.string()];
      const l2IndicationNames = [
        faker.datatype.string(),
        faker.datatype.string()
      ];

      const l3IndicationNames = [
        faker.datatype.string(),
        faker.datatype.string(),
        faker.datatype.string(),
        faker.datatype.string(),
        faker.datatype.string()
      ];

      const l3HcpSizeForTreeA = [
        faker.datatype.number(),
        faker.datatype.number()
      ].sort((a, b) => b - a);
      const l3HcpSizeForTreeB = [
        faker.datatype.number(),
        faker.datatype.number(),
        faker.datatype.number()
      ].sort((a, b) => b - a);
      const l3HcpSizes = [...l3HcpSizeForTreeA, ...l3HcpSizeForTreeB];

      elasticSearchIndicationsTreeService.query.mockResolvedValueOnce({
        hits: {
          hits: [
            {
              _id: matchedNodeIds[0],
              _source: {
                indication_name: matchedIndicationNames[0],
                indication_type: "L1",
                ancestor_ids: [] // L1
              }
            },
            {
              _id: matchedNodeIds[1],
              _source: {
                indication_name: matchedIndicationNames[1],
                indication_type: "L2",
                ancestor_ids: [l1Ids[0]] // L2
              }
            },
            {
              _id: matchedNodeIds[2],
              _source: {
                indication_name: matchedIndicationNames[2],
                indication_type: "L3",
                community_size: matchedL3HcpSize,
                ancestor_ids: [l1Ids[0], matchedNodeIds[1]] // L3
              }
            }
          ]
        }
      } as any);
      elasticSearchIndicationsTreeService.query.mockResolvedValueOnce({
        hits: {
          hits: [
            {
              _id: l2Ids[0],
              _source: {
                indication_name: l2IndicationNames[0],
                indication_type: "L2",
                ancestor_ids: [matchedNodeIds[0]]
              }
            },
            {
              _id: l2Ids[1],
              _source: {
                indication_name: l2IndicationNames[1],
                indication_type: "L2",
                ancestor_ids: [matchedNodeIds[0]]
              }
            },
            {
              _id: l3Ids[0],
              _source: {
                indication_name: l3IndicationNames[0],
                indication_type: "L3",
                community_size: l3HcpSizes[0],
                ancestor_ids: [matchedNodeIds[0], l2Ids[0]]
              }
            },
            {
              _id: l3Ids[1],
              _source: {
                indication_name: l3IndicationNames[1],
                indication_type: "L3",
                community_size: l3HcpSizes[1],
                ancestor_ids: [matchedNodeIds[0], l2Ids[0]]
              }
            },
            {
              _id: l3Ids[2],
              _source: {
                indication_name: l3IndicationNames[2],
                indication_type: "L3",
                community_size: l3HcpSizes[2],
                ancestor_ids: [matchedNodeIds[0], l2Ids[1]]
              }
            },
            {
              _id: l3Ids[3],
              _source: {
                indication_name: l3IndicationNames[3],
                indication_type: "L3",
                community_size: l3HcpSizes[3],
                ancestor_ids: [matchedNodeIds[0], l2Ids[1]]
              }
            },
            {
              _id: l3Ids[4],
              _source: {
                indication_name: l3IndicationNames[4],
                indication_type: "L3",
                community_size: l3HcpSizes[4],
                ancestor_ids: [matchedNodeIds[0], l2Ids[1]]
              }
            },
            {
              _id: l1Ids[0],
              _source: {
                indication_name: l1IndicationNames[0],
                indication_type: "L1",
                ancestor_ids: []
              }
            }
          ]
        }
      } as any);
      const featureFlagsService = mockFeatureFlagsService();
      const indicationIcdInstitutionCountsRepository = createMockInstance(
        IndicationIcdInstitutionCountsRepository
      );
      const indicationInstitutionCountsRepository = createMockInstance(
        IndicationInstitutionCountsRepository
      );
      const searchAnalyticsTracerService = createMockInstance(
        SearchAnalyticsTracerService
      );
      const indicationsTreeSearchService = new IndicationsTreeSearchService(
        configService,
        elasticSearchIndicationsTreeService,
        featureFlagsService,
        indicationInstitutionCountsRepository,
        indicationIcdInstitutionCountsRepository,
        searchAnalyticsTracerService
      );

      const trees =
        await indicationsTreeSearchService.searchIndicationTreesByQuery({
          query,
          size: 3,
          indicationSource: [IndicationSource.CLAIMS],
          projectId: faker.datatype.string(),
          sortBy: IndicationSortBy.HCP_COMMUNITY_SIZE,
          indicationType: undefined
        });

      expect(elasticSearchIndicationsTreeService.query).toHaveBeenCalledTimes(
        2
      );

      expect(elasticSearchIndicationsTreeService.query).toHaveBeenNthCalledWith(
        1,
        expect.objectContaining({
          index: configService.elasticIndicationsIndex,
          size: 3,
          query: expect.objectContaining({
            bool: expect.objectContaining({
              filter: expect.arrayContaining([
                expect.termsQuery("indication_source", [
                  IndicationSource.CLAIMS
                ]),
                expect.objectContaining({
                  bool: {
                    minimum_should_match: 1,
                    should: [
                      {
                        bool: {
                          filter: [
                            {
                              term: {
                                indication_type: "L3"
                              }
                            },
                            {
                              nested: {
                                path: "icd_codes",
                                query: {
                                  exists: {
                                    field: "icd_codes"
                                  }
                                }
                              }
                            }
                          ]
                        }
                      },
                      {
                        terms: {
                          indication_type: ["L1", "L2", "ICD"]
                        }
                      }
                    ]
                  }
                })
              ])
            })
          }),
          collapse: undefined
        })
      );

      expect(elasticSearchIndicationsTreeService.query).toHaveBeenNthCalledWith(
        2,
        expect.objectContaining({
          index: configService.elasticIndicationsIndex,
          query: expect.objectContaining({
            bool: expect.objectContaining({
              filter: [
                expect.anything(),
                {
                  bool: {
                    must_not: expect.termsQuery("_id", matchedNodeIds)
                  }
                }
              ]
            })
          })
        })
      );

      expect(trees[0].hcpCommunitySize).toBeGreaterThanOrEqual(
        trees[1].hcpCommunitySize!
      );

      if (trees[0].id === matchedNodeIds[0]) {
        // Tree 1
        expect(trees[0].children?.length).toBe(2);
        expect(
          trees[0].children![0].children!.length +
            trees[0].children![1].children!.length
        ).toBe(5);
        // Tree 2
        expect(trees[1].children?.length).toBe(1);
        expect(trees[1].children![0].children!.length).toBe(1);
      } else {
        // Tree 1
        expect(trees[0].children?.length).toBe(1);
        expect(trees[0].children![0].children!.length).toBe(1);
        // Tree 2
        expect(trees[1].children?.length).toBe(2);
        expect(
          trees[1].children![0].children!.length +
            trees[1].children![1].children!.length
        ).toBe(5);
      }
    });

    it("should apply filter on indication_type if indicationType is passed", async () => {
      const configService = createMockInstance(ConfigService);
      configService.elasticIndicationsIndex = faker.datatype.string();
      const elasticSearchIndicationsTreeService = createMockInstance(
        ElasticSearchIndicationsTreeService
      );

      const query = faker.datatype.string();
      const matchedNodeIds = [
        faker.datatype.string(),
        faker.datatype.string(),
        faker.datatype.string()
      ];
      const matchedIndicationNames = [
        faker.datatype.string(),
        faker.datatype.string(),
        faker.datatype.string()
      ];
      const matchedL3HcpSize = faker.datatype.number();

      const l1Ids = [faker.datatype.string()];
      const l2Ids = [faker.datatype.string(), faker.datatype.string()];
      const l3Ids = [
        faker.datatype.string(),
        faker.datatype.string(),
        faker.datatype.string(),
        faker.datatype.string(),
        faker.datatype.string()
      ];
      const l1IndicationNames = [faker.datatype.string()];
      const l2IndicationNames = [
        faker.datatype.string(),
        faker.datatype.string()
      ];

      const l3IndicationNames = [
        faker.datatype.string(),
        faker.datatype.string(),
        faker.datatype.string(),
        faker.datatype.string(),
        faker.datatype.string()
      ];

      const l3HcpSizeForTreeA = [
        faker.datatype.number(),
        faker.datatype.number()
      ].sort((a, b) => b - a);
      const l3HcpSizeForTreeB = [
        faker.datatype.number(),
        faker.datatype.number(),
        faker.datatype.number()
      ].sort((a, b) => b - a);
      const l3HcpSizes = [...l3HcpSizeForTreeA, ...l3HcpSizeForTreeB];

      elasticSearchIndicationsTreeService.query.mockResolvedValueOnce({
        hits: {
          hits: [
            {
              _id: matchedNodeIds[0],
              _source: {
                indication_name: matchedIndicationNames[0],
                indication_type: "L1",
                ancestor_ids: [] // L1
              }
            },
            {
              _id: matchedNodeIds[1],
              _source: {
                indication_name: matchedIndicationNames[1],
                indication_type: "L2",
                ancestor_ids: [l1Ids[0]] // L2
              }
            },
            {
              _id: matchedNodeIds[2],
              _source: {
                indication_name: matchedIndicationNames[2],
                indication_type: "L3",
                community_size: matchedL3HcpSize,
                ancestor_ids: [l1Ids[0], matchedNodeIds[1]] // L3
              }
            }
          ]
        }
      } as any);
      elasticSearchIndicationsTreeService.query.mockResolvedValueOnce({
        hits: {
          hits: [
            {
              _id: l2Ids[0],
              _source: {
                indication_name: l2IndicationNames[0],
                indication_type: "L2",
                ancestor_ids: [matchedNodeIds[0]]
              }
            },
            {
              _id: l2Ids[1],
              _source: {
                indication_name: l2IndicationNames[1],
                indication_type: "L2",
                ancestor_ids: [matchedNodeIds[0]]
              }
            },
            {
              _id: l3Ids[0],
              _source: {
                indication_name: l3IndicationNames[0],
                indication_type: "L3",
                community_size: l3HcpSizes[0],
                ancestor_ids: [matchedNodeIds[0], l2Ids[0]]
              }
            },
            {
              _id: l3Ids[1],
              _source: {
                indication_name: l3IndicationNames[1],
                indication_type: "L3",
                community_size: l3HcpSizes[1],
                ancestor_ids: [matchedNodeIds[0], l2Ids[0]]
              }
            },
            {
              _id: l3Ids[2],
              _source: {
                indication_name: l3IndicationNames[2],
                indication_type: "L3",
                community_size: l3HcpSizes[2],
                ancestor_ids: [matchedNodeIds[0], l2Ids[1]]
              }
            },
            {
              _id: l3Ids[3],
              _source: {
                indication_name: l3IndicationNames[3],
                indication_type: "L3",
                community_size: l3HcpSizes[3],
                ancestor_ids: [matchedNodeIds[0], l2Ids[1]]
              }
            },
            {
              _id: l3Ids[4],
              _source: {
                indication_name: l3IndicationNames[4],
                indication_type: "L3",
                community_size: l3HcpSizes[4],
                ancestor_ids: [matchedNodeIds[0], l2Ids[1]]
              }
            },
            {
              _id: l1Ids[0],
              _source: {
                indication_name: l1IndicationNames[0],
                indication_type: "L1",
                ancestor_ids: []
              }
            }
          ]
        }
      } as any);
      const featureFlagsService = mockFeatureFlagsService();
      const indicationIcdInstitutionCountsRepository = createMockInstance(
        IndicationIcdInstitutionCountsRepository
      );
      const indicationInstitutionCountsRepository = createMockInstance(
        IndicationInstitutionCountsRepository
      );
      const searchAnalyticsTracerService = createMockInstance(
        SearchAnalyticsTracerService
      );
      const indicationsTreeSearchService = new IndicationsTreeSearchService(
        configService,
        elasticSearchIndicationsTreeService,
        featureFlagsService,
        indicationInstitutionCountsRepository,
        indicationIcdInstitutionCountsRepository,
        searchAnalyticsTracerService
      );

      const trees =
        await indicationsTreeSearchService.searchIndicationTreesByQuery({
          query,
          indicationSource: [IndicationSource.CLAIMS],
          projectId: faker.datatype.string(),
          sortBy: IndicationSortBy.HCP_COMMUNITY_SIZE,
          h1Ids: [],
          indicationType: [
            IndicationType.L1,
            IndicationType.L2,
            IndicationType.L3
          ]
        });

      expect(elasticSearchIndicationsTreeService.query).toHaveBeenCalledTimes(
        2
      );

      expect(elasticSearchIndicationsTreeService.query).toHaveBeenNthCalledWith(
        1,
        expect.objectContaining({
          index: configService.elasticIndicationsIndex,
          query: expect.objectContaining({
            bool: expect.objectContaining({
              filter: [
                expect.termsQuery("indication_source", [
                  IndicationSource.CLAIMS
                ]),
                expect.termsQuery("indication_type", [
                  IndicationType.L1,
                  IndicationType.L2,
                  IndicationType.L3
                ]),
                expect.objectContaining({
                  bool: {
                    minimum_should_match: 1,
                    should: [
                      {
                        bool: {
                          filter: [
                            {
                              term: {
                                indication_type: "L3"
                              }
                            },
                            {
                              nested: {
                                path: "icd_codes",
                                query: {
                                  exists: {
                                    field: "icd_codes"
                                  }
                                }
                              }
                            }
                          ]
                        }
                      },
                      {
                        terms: {
                          indication_type: ["L1", "L2", "ICD"]
                        }
                      }
                    ]
                  }
                })
              ]
            })
          })
        })
      );

      expect(trees[0].hcpCommunitySize).toBeGreaterThanOrEqual(
        trees[1].hcpCommunitySize!
      );

      if (trees[0].id === matchedNodeIds[0]) {
        // Tree 1
        expect(trees[0].children?.length).toBe(2);
        expect(
          trees[0].children![0].children!.length +
            trees[0].children![1].children!.length
        ).toBe(5);
        // Tree 2
        expect(trees[1].children?.length).toBe(1);
        expect(trees[1].children![0].children!.length).toBe(1);
      } else {
        // Tree 1
        expect(trees[0].children?.length).toBe(1);
        expect(trees[0].children![0].children!.length).toBe(1);
        // Tree 2
        expect(trees[1].children?.length).toBe(2);
        expect(
          trees[1].children![0].children!.length +
            trees[1].children![1].children!.length
        ).toBe(5);
      }
    });

    it("should apply filter on h1Ids if passed", async () => {
      const configService = createMockInstance(ConfigService);
      configService.elasticIndicationsIndex = faker.datatype.string();
      const elasticSearchIndicationsTreeService = createMockInstance(
        ElasticSearchIndicationsTreeService
      );

      const query = "";
      const matchedNodeIds = [
        faker.datatype.string(),
        faker.datatype.string(),
        faker.datatype.string()
      ];
      const matchedIndicationNames = [
        faker.datatype.string(),
        faker.datatype.string(),
        faker.datatype.string()
      ];
      const matchedL3HcpSize = faker.datatype.number();

      const h1Ids = [
        faker.datatype.string(),
        faker.datatype.string(),
        faker.datatype.string(),
        faker.datatype.string(),
        faker.datatype.string(),
        faker.datatype.string()
      ];
      const l1Ids = [faker.datatype.string()];
      const l2Ids = [faker.datatype.string(), faker.datatype.string()];
      const l3Ids = [
        faker.datatype.string(),
        faker.datatype.string(),
        faker.datatype.string(),
        faker.datatype.string(),
        faker.datatype.string()
      ];
      const l1IndicationNames = [faker.datatype.string()];
      const l2IndicationNames = [
        faker.datatype.string(),
        faker.datatype.string()
      ];

      const l3IndicationNames = [
        faker.datatype.string(),
        faker.datatype.string(),
        faker.datatype.string(),
        faker.datatype.string(),
        faker.datatype.string()
      ];

      const l3HcpSizeForTreeA = [
        faker.datatype.number(),
        faker.datatype.number()
      ].sort((a, b) => b - a);
      const l3HcpSizeForTreeB = [
        faker.datatype.number(),
        faker.datatype.number(),
        faker.datatype.number()
      ].sort((a, b) => b - a);
      const l3HcpSizes = [...l3HcpSizeForTreeA, ...l3HcpSizeForTreeB];

      elasticSearchIndicationsTreeService.query.mockResolvedValueOnce({
        hits: {
          hits: [
            {
              _id: matchedNodeIds[0],
              _source: {
                indication_name: matchedIndicationNames[0],
                indication_type: "L1",
                ancestor_ids: [], // L1,
                h1_id: h1Ids[0],
                parent_h1id: []
              }
            },
            {
              _id: matchedNodeIds[1],
              _source: {
                indication_name: matchedIndicationNames[1],
                indication_type: "L2",
                ancestor_ids: [l1Ids[0]], // L2
                h1_id: h1Ids[1],
                parent_h1id: [h1Ids[5]]
              }
            },
            {
              _id: matchedNodeIds[2],
              _source: {
                indication_name: matchedIndicationNames[2],
                indication_type: "L3",
                community_size: matchedL3HcpSize,
                ancestor_ids: [l1Ids[0], matchedNodeIds[1]], // L3
                h1_id: h1Ids[2],
                parent_h1id: [h1Ids[1]]
              }
            }
          ]
        }
      } as any);
      elasticSearchIndicationsTreeService.query.mockResolvedValueOnce({
        hits: {
          hits: [
            {
              _id: l2Ids[0],
              _source: {
                indication_name: l2IndicationNames[0],
                indication_type: "L2",
                ancestor_ids: [matchedNodeIds[0]],
                h1_id: h1Ids[4],
                parent_h1id: [h1Ids[0]]
              }
            },
            {
              _id: l2Ids[1],
              _source: {
                indication_name: l2IndicationNames[1],
                indication_type: "L2",
                ancestor_ids: [matchedNodeIds[0]],
                h1_id: h1Ids[3],
                parent_h1id: [h1Ids[0]]
              }
            },
            {
              _id: l3Ids[0],
              _source: {
                indication_name: l3IndicationNames[0],
                indication_type: "L3",
                community_size: l3HcpSizes[0],
                ancestor_ids: [matchedNodeIds[0], l2Ids[0]],
                h1_id: faker.datatype.string(),
                parent_h1id: [h1Ids[4]]
              }
            },
            {
              _id: l3Ids[1],
              _source: {
                indication_name: l3IndicationNames[1],
                indication_type: "L3",
                community_size: l3HcpSizes[1],
                ancestor_ids: [matchedNodeIds[0], l2Ids[0]],
                h1_id: faker.datatype.string(),
                parent_h1id: [h1Ids[4]]
              }
            },
            {
              _id: l3Ids[2],
              _source: {
                indication_name: l3IndicationNames[2],
                indication_type: "L3",
                community_size: l3HcpSizes[2],
                ancestor_ids: [matchedNodeIds[0], l2Ids[1]],
                h1_id: faker.datatype.string(),
                parent_h1id: [h1Ids[3]]
              }
            },
            {
              _id: l3Ids[3],
              _source: {
                indication_name: l3IndicationNames[3],
                indication_type: "L3",
                community_size: l3HcpSizes[3],
                ancestor_ids: [matchedNodeIds[0], l2Ids[1]],
                h1_id: faker.datatype.string(),
                parent_h1id: [h1Ids[3]]
              }
            },
            {
              _id: l3Ids[4],
              _source: {
                indication_name: l3IndicationNames[4],
                indication_type: "L3",
                community_size: l3HcpSizes[4],
                ancestor_ids: [matchedNodeIds[0], l2Ids[1]],
                h1_id: faker.datatype.string(),
                parent_h1id: [h1Ids[3]]
              }
            },
            {
              _id: l1Ids[0],
              _source: {
                indication_name: l1IndicationNames[0],
                indication_type: "L1",
                ancestor_ids: [],
                h1_id: h1Ids[5],
                parent_h1id: []
              }
            }
          ]
        }
      } as any);
      const featureFlagsService = mockFeatureFlagsService();
      const indicationIcdInstitutionCountsRepository = createMockInstance(
        IndicationIcdInstitutionCountsRepository
      );
      const indicationInstitutionCountsRepository = createMockInstance(
        IndicationInstitutionCountsRepository
      );
      const searchAnalyticsTracerService = createMockInstance(
        SearchAnalyticsTracerService
      );
      const indicationsTreeSearchService = new IndicationsTreeSearchService(
        configService,
        elasticSearchIndicationsTreeService,
        featureFlagsService,
        indicationInstitutionCountsRepository,
        indicationIcdInstitutionCountsRepository,
        searchAnalyticsTracerService
      );

      const trees =
        await indicationsTreeSearchService.searchIndicationTreesByQuery({
          query,
          indicationSource: [IndicationSource.CLAIMS],
          projectId: faker.datatype.string(),
          sortBy: IndicationSortBy.HCP_COMMUNITY_SIZE,
          h1Ids: h1Ids,
          indicationType: [
            IndicationType.L1,
            IndicationType.L2,
            IndicationType.L3
          ]
        });

      expect(elasticSearchIndicationsTreeService.query).toHaveBeenCalledTimes(
        2
      );

      expect(elasticSearchIndicationsTreeService.query).toHaveBeenNthCalledWith(
        1,
        expect.objectContaining({
          index: configService.elasticIndicationsIndex,
          query: expect.objectContaining({
            bool: expect.objectContaining({
              should: undefined,
              minimum_should_match: undefined,
              filter: [
                expect.termsQuery("indication_source", [
                  IndicationSource.CLAIMS
                ]),
                expect.termsQuery("indication_type", [
                  IndicationType.L1,
                  IndicationType.L2,
                  IndicationType.L3
                ]),
                expect.termsQuery("h1_id", h1Ids),
                expect.objectContaining({
                  bool: {
                    minimum_should_match: 1,
                    should: [
                      {
                        bool: {
                          filter: [
                            {
                              term: {
                                indication_type: "L3"
                              }
                            },
                            {
                              nested: {
                                path: "icd_codes",
                                query: {
                                  exists: {
                                    field: "icd_codes"
                                  }
                                }
                              }
                            }
                          ]
                        }
                      },
                      {
                        terms: {
                          indication_type: ["L1", "L2", "ICD"]
                        }
                      }
                    ]
                  }
                })
              ]
            })
          })
        })
      );

      trees.forEach((l1) => {
        expect(l1.parentH1Ids).toEqual([]);

        l1.children.forEach((l2) => {
          expect(l2.parentH1Ids).toEqual([l1.h1Id]);

          l2.children.forEach((l3) => {
            expect(l3.parentH1Ids).toEqual([l2.h1Id]);
          });
        });
      });
      expect(trees[0].hcpCommunitySize).toBeGreaterThanOrEqual(
        trees[1].hcpCommunitySize!
      );

      if (trees[0].id === matchedNodeIds[0]) {
        // Tree 1
        expect(trees[0].children?.length).toBe(2);
        expect(
          trees[0].children![0].children!.length +
            trees[0].children![1].children!.length
        ).toBe(5);
        // Tree 2
        expect(trees[1].children?.length).toBe(1);
        expect(trees[1].children![0].children!.length).toBe(1);
      } else {
        // Tree 1
        expect(trees[0].children?.length).toBe(1);
        expect(trees[0].children![0].children!.length).toBe(1);
        // Tree 2
        expect(trees[1].children?.length).toBe(2);
        expect(
          trees[1].children![0].children!.length +
            trees[1].children![1].children!.length
        ).toBe(5);
      }
    });

    it("should return default size if size is not specified", async () => {
      const configService = createMockInstance(ConfigService);
      configService.elasticIndicationsIndex = faker.datatype.string();
      const elasticSearchIndicationsTreeService = createMockInstance(
        ElasticSearchIndicationsTreeService
      );

      const query = faker.datatype.string();
      const nodeIds = [
        faker.datatype.string(),
        faker.datatype.string(),
        faker.datatype.string()
      ];
      const indicationNames = [
        faker.datatype.string(),
        faker.datatype.string(),
        faker.datatype.string()
      ];
      elasticSearchIndicationsTreeService.query.mockResolvedValueOnce({
        hits: {
          hits: [
            {
              _id: nodeIds[0],
              _source: {
                indication_name: indicationNames[0],
                indication_type: "ICD",
                ancestor_ids: []
              }
            },
            {
              _id: nodeIds[1],
              _source: {
                indication_name: indicationNames[1],
                indication_type: "ICD",
                ancestor_ids: []
              }
            },
            {
              _id: nodeIds[2],
              _source: {
                indication_name: indicationNames[2],
                indication_type: "ICD",
                ancestor_ids: []
              }
            }
          ]
        }
      } as any);
      const featureFlagsService = mockFeatureFlagsService();
      const indicationIcdInstitutionCountsRepository = createMockInstance(
        IndicationIcdInstitutionCountsRepository
      );
      const indicationInstitutionCountsRepository = createMockInstance(
        IndicationInstitutionCountsRepository
      );
      const searchAnalyticsTracerService = createMockInstance(
        SearchAnalyticsTracerService
      );
      const indicationsTreeSearchService = new IndicationsTreeSearchService(
        configService,
        elasticSearchIndicationsTreeService,
        featureFlagsService,
        indicationInstitutionCountsRepository,
        indicationIcdInstitutionCountsRepository,
        searchAnalyticsTracerService
      );

      await indicationsTreeSearchService.searchIndicationTreesByQuery({
        query,
        indicationSource: [IndicationSource.CLAIMS],
        size: undefined,
        projectId: faker.datatype.string(),
        sortBy: IndicationSortBy.PATIENT_COUNT,
        indicationType: [IndicationType.ICD]
      });

      expect(elasticSearchIndicationsTreeService.query).toHaveBeenCalledTimes(
        1
      );

      expect(elasticSearchIndicationsTreeService.query).toHaveBeenNthCalledWith(
        1,
        expect.objectContaining({
          index: configService.elasticIndicationsIndex,
          size: 100,
          query: expect.objectContaining({
            bool: expect.objectContaining({
              filter: [
                expect.termsQuery("indication_source", [
                  IndicationSource.CLAIMS
                ]),
                expect.termsQuery("indication_type", [IndicationType.ICD]),
                expect.objectContaining({
                  bool: {
                    minimum_should_match: 1,
                    should: [
                      {
                        bool: {
                          filter: [
                            {
                              term: {
                                indication_type: "L3"
                              }
                            },
                            {
                              nested: {
                                path: "icd_codes",
                                query: {
                                  exists: {
                                    field: "icd_codes"
                                  }
                                }
                              }
                            }
                          ]
                        }
                      },
                      {
                        terms: {
                          indication_type: ["L1", "L2", "ICD"]
                        }
                      }
                    ]
                  }
                })
              ]
            })
          })
        })
      );
    });

    it("should return return all tree nodes when ancestor ids are not present", async () => {
      const configService = createMockInstance(ConfigService);
      configService.elasticIndicationsIndex = faker.datatype.string();
      const elasticSearchIndicationsTreeService = createMockInstance(
        ElasticSearchIndicationsTreeService
      );
      const featureFlagsService = mockFeatureFlagsService();
      const indicationIcdInstitutionCountsRepository = createMockInstance(
        IndicationIcdInstitutionCountsRepository
      );
      const indicationInstitutionCountsRepository = createMockInstance(
        IndicationInstitutionCountsRepository
      );
      const searchAnalyticsTracerService = createMockInstance(
        SearchAnalyticsTracerService
      );
      const indicationsTreeSearchService = new IndicationsTreeSearchService(
        configService,
        elasticSearchIndicationsTreeService,
        featureFlagsService,
        indicationInstitutionCountsRepository,
        indicationIcdInstitutionCountsRepository,
        searchAnalyticsTracerService
      );

      const l1Id = faker.datatype.string();
      const l1Name = faker.datatype.string();
      const l2Id = faker.datatype.string();
      const l2Name = faker.datatype.string();
      const l3Ids = [faker.datatype.string(), faker.datatype.string()];
      const l3Names = [faker.datatype.string(), faker.datatype.string()];

      elasticSearchIndicationsTreeService.query.mockResolvedValueOnce({
        hits: {
          hits: [
            {
              _id: l1Id,
              _source: {
                indication_name: l1Name,
                indication_type: "L1",
                ancestor_ids: []
              }
            }
          ]
        }
      } as any);

      elasticSearchIndicationsTreeService.query.mockResolvedValueOnce({
        hits: {
          hits: [
            {
              _id: l2Id,
              _source: {
                indication_name: l2Name,
                indication_type: "L2",
                ancestor_ids: [l1Id]
              }
            },
            {
              _id: l3Ids[0],
              _source: {
                indication_name: l3Names[0],
                indication_type: "L3",
                ancestor_ids: [l1Id, l2Id],
                patient_count: 90000
              }
            },
            {
              _id: l3Ids[1],
              _source: {
                indication_name: l3Names[1],
                indication_type: "L3",
                ancestor_ids: [l1Id, l2Id],
                patient_count: 100000
              }
            }
          ]
        }
      } as any);

      const indications =
        await indicationsTreeSearchService.searchIndicationTreesByQuery({
          indicationSource: [IndicationSource.CLAIMS],
          query: faker.datatype.string(),
          projectId: faker.datatype.string(),
          sortBy: IndicationSortBy.PATIENT_COUNT
        });

      expect(indications).toEqual([
        {
          id: l1Id,
          indicationName: l1Name,
          matchedSynonyms: [],
          icdCodes: [],
          match: true,
          indicationType: "L1",
          patientCount: 190000,
          children: [
            {
              id: l2Id,
              indicationName: l2Name,
              matchedSynonyms: [],
              icdCodes: [],
              match: false,
              indicationType: "L2",
              patientCount: 190000,
              children: [
                {
                  id: l3Ids[1],
                  indicationName: l3Names[1],
                  matchedSynonyms: [],
                  icdCodes: [],
                  match: false,
                  indicationType: "L3",
                  children: [],
                  parentH1Ids: [],
                  patientCount: 100000
                },
                {
                  id: l3Ids[0],
                  indicationName: l3Names[0],
                  matchedSynonyms: [],
                  icdCodes: [],
                  match: false,
                  indicationType: "L3",
                  children: [],
                  parentH1Ids: [],
                  patientCount: 90000
                }
              ],
              parentH1Ids: []
            }
          ],
          parentH1Ids: []
        }
      ]);
      expect(elasticSearchIndicationsTreeService.query).toHaveBeenCalledTimes(
        2
      );
    });
  });

  describe("Institution KG widget", () => {
    it("replaceAndResortHitsBasedOnInstitutionCounts should update patient_count based on indicationCountMap", () => {
      const configService = createMockInstance(ConfigService);
      configService.elasticIndicationsIndex = faker.datatype.string();
      const elasticSearchIndicationsTreeService = createMockInstance(
        ElasticSearchIndicationsTreeService
      );
      const featureFlagsService = mockFeatureFlagsService();
      const indicationIcdInstitutionCountsRepository = createMockInstance(
        IndicationIcdInstitutionCountsRepository
      );
      const indicationInstitutionCountsRepository = createMockInstance(
        IndicationInstitutionCountsRepository
      );
      const searchAnalyticsTracerService = createMockInstance(
        SearchAnalyticsTracerService
      );
      const indicationsTreeSearchService = new IndicationsTreeSearchService(
        configService,
        elasticSearchIndicationsTreeService,
        featureFlagsService,
        indicationInstitutionCountsRepository,
        indicationIcdInstitutionCountsRepository,
        searchAnalyticsTracerService
      );
      const hits: SearchHit<IndicationDoc>[] = [
        {
          _index: faker.datatype.string(),
          _id: faker.datatype.string(),
          _source: {
            indication_name: "test_indication",
            h1_id: faker.datatype.string(),
            patient_count: 50,
            icd_codes: [],
            indication_source: faker.datatype.string(),
            indication_type: IndicationType.L3,
            ancestor_ids: [],
            inner_hits: {}
          }
        }
      ];

      const indicationCountMap = new Map<string, number>();
      indicationCountMap.set("test_indication", 100);

      const icdCountMap = new Map<string, number>();

      const result =
        indicationsTreeSearchService.replaceAndResortHitsBasedOnInstitutionCounts(
          hits,
          indicationCountMap,
          icdCountMap
        );

      expect(result[0]._source!.patient_count!).toBe(100);
    });

    it("replaceAndResortHitsBasedOnInstitutionCounts should update patientCount in icd_codes based on icdCountMap", () => {
      const configService = createMockInstance(ConfigService);
      configService.elasticIndicationsIndex = faker.datatype.string();
      const elasticSearchIndicationsTreeService = createMockInstance(
        ElasticSearchIndicationsTreeService
      );
      const featureFlagsService = mockFeatureFlagsService();
      const indicationIcdInstitutionCountsRepository = createMockInstance(
        IndicationIcdInstitutionCountsRepository
      );
      const indicationInstitutionCountsRepository = createMockInstance(
        IndicationInstitutionCountsRepository
      );
      const searchAnalyticsTracerService = createMockInstance(
        SearchAnalyticsTracerService
      );
      const indicationsTreeSearchService = new IndicationsTreeSearchService(
        configService,
        elasticSearchIndicationsTreeService,
        featureFlagsService,
        indicationInstitutionCountsRepository,
        indicationIcdInstitutionCountsRepository,
        searchAnalyticsTracerService
      );
      const hits: SearchHit<IndicationDoc>[] = [
        {
          _index: faker.datatype.string(),
          _id: faker.datatype.string(),
          _source: {
            indication_name: "test_indication",
            h1_id: faker.datatype.string(),
            patient_count: 50,
            indication_source: faker.datatype.string(),
            indication_type: IndicationType.L3,
            ancestor_ids: [],
            inner_hits: {},
            icd_codes: [
              {
                icdCode: "code1",
                patientCount: 10,
                description: faker.datatype.string()
              }
            ]
          }
        }
      ];

      const indicationCountMap = new Map<string, number>();
      indicationCountMap.set("test_indication", 100);

      const icdCountMap = new Map<string, number>();
      icdCountMap.set("code1", 200);

      const result =
        indicationsTreeSearchService.replaceAndResortHitsBasedOnInstitutionCounts(
          hits,
          indicationCountMap,
          icdCountMap
        );
      expect(result[0]._source!.patient_count!).toBe(100);
      expect(result[0]._source!.icd_codes![0].patientCount).toBe(200);
    });

    it("replaceAndResortHitsBasedOnInstitutionCounts should sort hits based on updated patient_count in descending order", () => {
      const configService = createMockInstance(ConfigService);
      configService.elasticIndicationsIndex = faker.datatype.string();
      const elasticSearchIndicationsTreeService = createMockInstance(
        ElasticSearchIndicationsTreeService
      );
      const featureFlagsService = mockFeatureFlagsService();
      const indicationIcdInstitutionCountsRepository = createMockInstance(
        IndicationIcdInstitutionCountsRepository
      );
      const indicationInstitutionCountsRepository = createMockInstance(
        IndicationInstitutionCountsRepository
      );
      const searchAnalyticsTracerService = createMockInstance(
        SearchAnalyticsTracerService
      );
      const indicationsTreeSearchService = new IndicationsTreeSearchService(
        configService,
        elasticSearchIndicationsTreeService,
        featureFlagsService,
        indicationInstitutionCountsRepository,
        indicationIcdInstitutionCountsRepository,
        searchAnalyticsTracerService
      );

      const hits: SearchHit<IndicationDoc>[] = [
        {
          _index: faker.datatype.string(),
          _id: faker.datatype.string(),
          _source: {
            indication_name: "test_indication_1",
            h1_id: faker.datatype.string(),
            patient_count: 100,
            icd_codes: [],
            indication_source: faker.datatype.string(),
            indication_type: IndicationType.L3,
            ancestor_ids: [],
            inner_hits: {}
          }
        },
        {
          _index: faker.datatype.string(),
          _id: faker.datatype.string(),
          _source: {
            indication_name: "test_indication_2",
            h1_id: faker.datatype.string(),
            patient_count: 50,
            icd_codes: [],
            indication_source: faker.datatype.string(),
            indication_type: IndicationType.L3,
            ancestor_ids: [],
            inner_hits: {}
          }
        }
      ];

      const indicationCountMap = new Map<string, number>();
      indicationCountMap.set("test_indication_1", 150);
      indicationCountMap.set("test_indication_2", 200);

      const icdCountMap = new Map<string, number>();

      const result =
        indicationsTreeSearchService.replaceAndResortHitsBasedOnInstitutionCounts(
          hits,
          indicationCountMap,
          icdCountMap
        );

      expect(result[0]._source!.indication_name).toBe("test_indication_2");
      expect(result[1]._source!.indication_name).toBe("test_indication_1");
    });

    it("replaceAndResortHitsBasedOnInstitutionCounts should filter out indications with patient_count of 0 after replacing", () => {
      const configService = createMockInstance(ConfigService);
      configService.elasticIndicationsIndex = faker.datatype.string();
      const elasticSearchIndicationsTreeService = createMockInstance(
        ElasticSearchIndicationsTreeService
      );
      const featureFlagsService = mockFeatureFlagsService();
      const indicationIcdInstitutionCountsRepository = createMockInstance(
        IndicationIcdInstitutionCountsRepository
      );
      const indicationInstitutionCountsRepository = createMockInstance(
        IndicationInstitutionCountsRepository
      );
      const searchAnalyticsTracerService = createMockInstance(
        SearchAnalyticsTracerService
      );
      const indicationsTreeSearchService = new IndicationsTreeSearchService(
        configService,
        elasticSearchIndicationsTreeService,
        featureFlagsService,
        indicationInstitutionCountsRepository,
        indicationIcdInstitutionCountsRepository,
        searchAnalyticsTracerService
      );

      const hits: SearchHit<IndicationDoc>[] = [
        {
          _index: faker.datatype.string(),
          _id: faker.datatype.string(),
          _source: {
            indication_name: "test_indication_1",
            h1_id: faker.datatype.string(),
            patient_count: 100,
            icd_codes: [],
            indication_source: faker.datatype.string(),
            indication_type: IndicationType.L3,
            ancestor_ids: [],
            inner_hits: {}
          }
        },
        {
          _index: faker.datatype.string(),
          _id: faker.datatype.string(),
          _source: {
            indication_name: "test_indication_2",
            h1_id: faker.datatype.string(),
            patient_count: 50,
            icd_codes: [],
            indication_source: faker.datatype.string(),
            indication_type: IndicationType.L3,
            ancestor_ids: [],
            inner_hits: {}
          }
        }
      ];

      const indicationCountMap = new Map<string, number>();
      indicationCountMap.set("test_indication_1", 0);
      indicationCountMap.set("test_indication_2", 200);

      const icdCountMap = new Map<string, number>();

      const result =
        indicationsTreeSearchService.replaceAndResortHitsBasedOnInstitutionCounts(
          hits,
          indicationCountMap,
          icdCountMap
        );

      expect(result.length).toBe(1);
      expect(result[0]._source!.indication_name).toBe("test_indication_2");
    });

    it("replaceAndResortHitsBasedOnInstitutionCounts should filter out icd_codes with patientCount of 0 after replacing", () => {
      const configService = createMockInstance(ConfigService);
      configService.elasticIndicationsIndex = faker.datatype.string();
      const elasticSearchIndicationsTreeService = createMockInstance(
        ElasticSearchIndicationsTreeService
      );
      const featureFlagsService = mockFeatureFlagsService();
      const indicationIcdInstitutionCountsRepository = createMockInstance(
        IndicationIcdInstitutionCountsRepository
      );
      const indicationInstitutionCountsRepository = createMockInstance(
        IndicationInstitutionCountsRepository
      );
      const searchAnalyticsTracerService = createMockInstance(
        SearchAnalyticsTracerService
      );
      const indicationsTreeSearchService = new IndicationsTreeSearchService(
        configService,
        elasticSearchIndicationsTreeService,
        featureFlagsService,
        indicationInstitutionCountsRepository,
        indicationIcdInstitutionCountsRepository,
        searchAnalyticsTracerService
      );

      const hits: SearchHit<IndicationDoc>[] = [
        {
          _index: faker.datatype.string(),
          _id: faker.datatype.string(),
          _source: {
            indication_name: "test_indication",
            h1_id: faker.datatype.string(),
            patient_count: 50,
            indication_source: faker.datatype.string(),
            indication_type: IndicationType.L3,
            ancestor_ids: [],
            inner_hits: {},
            icd_codes: [
              {
                icdCode: "code1",
                patientCount: 10,
                description: faker.datatype.string()
              },
              {
                icdCode: "code2",
                patientCount: 20,
                description: faker.datatype.string()
              }
            ]
          }
        }
      ];

      const indicationCountMap = new Map<string, number>();
      indicationCountMap.set("test_indication", 100);

      const icdCountMap = new Map<string, number>();
      icdCountMap.set("code1", 0);
      icdCountMap.set("code2", 200);

      const result =
        indicationsTreeSearchService.replaceAndResortHitsBasedOnInstitutionCounts(
          hits,
          indicationCountMap,
          icdCountMap
        );

      expect(result[0]._source!.icd_codes!.length).toBe(1);
      expect(result[0]._source!.icd_codes![0].icdCode).toBe("code2");
    });

    it("replaceAndResortHitsBasedOnInstitutionCounts should default patient count to 0 if indication or icd code is not present in maps", () => {
      const configService = createMockInstance(ConfigService);
      configService.elasticIndicationsIndex = faker.datatype.string();
      const elasticSearchIndicationsTreeService = createMockInstance(
        ElasticSearchIndicationsTreeService
      );
      const featureFlagsService = mockFeatureFlagsService();
      const indicationIcdInstitutionCountsRepository = createMockInstance(
        IndicationIcdInstitutionCountsRepository
      );
      const indicationInstitutionCountsRepository = createMockInstance(
        IndicationInstitutionCountsRepository
      );
      const searchAnalyticsTracerService = createMockInstance(
        SearchAnalyticsTracerService
      );
      const indicationsTreeSearchService = new IndicationsTreeSearchService(
        configService,
        elasticSearchIndicationsTreeService,
        featureFlagsService,
        indicationInstitutionCountsRepository,
        indicationIcdInstitutionCountsRepository,
        searchAnalyticsTracerService
      );

      const hits: SearchHit<IndicationDoc>[] = [
        {
          _index: faker.datatype.string(),
          _id: faker.datatype.string(),
          _source: {
            indication_name: "test_indication",
            h1_id: faker.datatype.string(),
            patient_count: 50,
            indication_source: faker.datatype.string(),
            indication_type: IndicationType.L3,
            ancestor_ids: [],
            inner_hits: {},
            icd_codes: [
              {
                icdCode: "code1",
                patientCount: 10,
                description: faker.datatype.string()
              }
            ]
          }
        }
      ];

      const indicationCountMap = new Map<string, number>();

      const icdCountMap = new Map<string, number>();

      const result =
        indicationsTreeSearchService.replaceAndResortHitsBasedOnInstitutionCounts(
          hits,
          indicationCountMap,
          icdCountMap
        );

      expect(result.length).toBe(0);
    });
    it("replaceAndResortHitsBasedOnInstitutionCounts should use icdCountMap for patient count if indication_type is ICD", () => {
      const configService = createMockInstance(ConfigService);
      configService.elasticIndicationsIndex = faker.datatype.string();
      const elasticSearchIndicationsTreeService = createMockInstance(
        ElasticSearchIndicationsTreeService
      );
      const featureFlagsService = mockFeatureFlagsService();
      const indicationIcdInstitutionCountsRepository = createMockInstance(
        IndicationIcdInstitutionCountsRepository
      );
      const indicationInstitutionCountsRepository = createMockInstance(
        IndicationInstitutionCountsRepository
      );
      const searchAnalyticsTracerService = createMockInstance(
        SearchAnalyticsTracerService
      );
      const indicationsTreeSearchService = new IndicationsTreeSearchService(
        configService,
        elasticSearchIndicationsTreeService,
        featureFlagsService,
        indicationInstitutionCountsRepository,
        indicationIcdInstitutionCountsRepository,
        searchAnalyticsTracerService
      );
      const hits: SearchHit<IndicationDoc>[] = [
        {
          _index: faker.datatype.string(),
          _id: faker.datatype.string(),
          _source: {
            indication_name: "test_icd",
            h1_id: faker.datatype.string(),
            patient_count: 50,
            indication_source: faker.datatype.string(),
            indication_type: IndicationType.ICD,
            ancestor_ids: [],
            inner_hits: {},
            icd_codes: []
          }
        }
      ];

      const indicationCountMap = new Map<string, number>();
      indicationCountMap.set("test_indication", 100);

      const icdCountMap = new Map<string, number>();
      icdCountMap.set("test_icd", 200);

      const result =
        indicationsTreeSearchService.replaceAndResortHitsBasedOnInstitutionCounts(
          hits,
          indicationCountMap,
          icdCountMap
        );

      expect(result[0]._source!.patient_count!).toBe(200);
    });

    it("getAllIcdCodes should include indication_name in icdCodes if indication_type is ICD", () => {
      const configService = createMockInstance(ConfigService);
      configService.elasticIndicationsIndex = faker.datatype.string();
      const elasticSearchIndicationsTreeService = createMockInstance(
        ElasticSearchIndicationsTreeService
      );
      const featureFlagsService = mockFeatureFlagsService();
      const indicationIcdInstitutionCountsRepository = createMockInstance(
        IndicationIcdInstitutionCountsRepository
      );
      const indicationInstitutionCountsRepository = createMockInstance(
        IndicationInstitutionCountsRepository
      );
      const searchAnalyticsTracerService = createMockInstance(
        SearchAnalyticsTracerService
      );
      const indicationsTreeSearchService = new IndicationsTreeSearchService(
        configService,
        elasticSearchIndicationsTreeService,
        featureFlagsService,
        indicationInstitutionCountsRepository,
        indicationIcdInstitutionCountsRepository,
        searchAnalyticsTracerService
      );
      const response: SearchHit<IndicationDoc>[] = [
        {
          _index: faker.datatype.string(),
          _id: faker.datatype.string(),
          _source: {
            indication_name: "test_icd",
            h1_id: faker.datatype.string(),
            patient_count: 50,
            indication_source: faker.datatype.string(),
            indication_type: IndicationType.ICD,
            ancestor_ids: [],
            inner_hits: {},
            icd_codes: []
          }
        }
      ];

      const result = indicationsTreeSearchService.getAllIcdCodes(response);

      expect(result).toContain("test_icd");
    });
  });
});
