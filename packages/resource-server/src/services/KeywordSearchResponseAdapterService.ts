/* eslint-disable @typescript-eslint/no-non-null-assertion */
import {
  AggregateName,
  AggregationsAggregate,
  SearchHit,
  SearchHitsMetadata,
  SearchInnerHitsResult,
  SearchTotalHits
} from "@elastic/elasticsearch/lib/api/types";
import {
  DocumentRanges,
  PatientDiversity,
  PersonCard,
  PersonSearchResponse,
  PersonSearchDebug,
  ProviderDiversity,
  ScoredDocumentResult,
  TagInterface,
  AffiliationInstitution,
  FilterInterface,
  ClaimsProcedure,
  ClaimsDiagnosis,
  QueryIntent,
  Affiliation,
  AffiliationIol,
  AffiliationHighlights,
  KeywordSearchInput,
  ZERO_SCORED_DOCUMENT_RESULT,
  SearchSliceOptionsEnum,
  SpeakerCongressSession
} from "@h1nyc/search-sdk";
import _, {
  Dictionary,
  flatten,
  pickBy,
  startsWith,
  toNumber,
  values
} from "lodash";
import { Service } from "typedi";
import {
  AffiliationInstitutionNestedDocument,
  AffiliationNestedDocument,
  CongressNestedDocument,
  DRG_diagnosesCount,
  DRG_proceduresCount,
  HCPDocument,
  KeywordSearchFeatureFlags,
  MatchedClaimCountsFromIEForExports,
  PrescriptionsCount,
  Translation
} from "./KeywordSearchResourceServiceRewrite";
import {
  ALTERNATE_CHINESE_PREFIX,
  ALTERNATE_ENGLISH_PREFIX,
  ALTERNATE_JAPANESE_PREFIX,
  CHINESE,
  ENGLISH,
  JAPANESE,
  Language
} from "./LanguageDetectService";
import { createLogger } from "../lib/Logger";
import { PeopleIdToAssetMatchedCount } from "./KeywordSearchMatchedCountService";
import { AffiliationInstitutionAddress } from "@h1nyc/search-sdk";
import { AFFILIATION_HIGHLIGHT_NAME_PREFIX } from "./queryBuilders/DefaultNameSearchBuilder";
import { AffiliationAdapterService } from "./AffiliationAdapterService";
import { extractLocationField } from "./NameSearchResponseAdapterService";
import { AggregationsFilterAggregate } from "@elastic/elasticsearch/lib/api/typesWithBodyKey";
import { SavedTerritory } from "@h1nyc/account-user-entities";
import { DocCountBucket } from "./InstitutionsResourceService";
import { PatientRace } from "@h1nyc/search-sdk";
import { DiversityRankingEnum } from "@h1nyc/search-sdk";

type NameTranslation = Readonly<{
  name: string;
  languageCode: Language;
}>;

type CongressSession = Readonly<{
  id: string;
  title_eng: string;
  role: string;
}>;

type RawInstitutionDocument = {
  id: number;
  institutionId: string;
  masterOrganizationId?: number;
  isIol: boolean;
  ultimate_parent_id?: number;
  name: string;
  nameTranslations: Readonly<Array<NameTranslation>>;
  type: string;
  orgTypes: string | Readonly<Array<string>>;
  region: string;
};

export type InstitutionDocument = Omit<
  RawInstitutionDocument,
  "institutionId"
> & {
  institutionId: number;
};

const ALL_AGGREGATIONS: Readonly<Array<string>> = [
  "institutions",
  "affiliations",
  "journals",
  "zipCode",
  "country",
  "specialty",
  "city",
  "procedureCodes",
  "payerCompanies",
  "trialPhases",
  "publicationTypes",
  "congressConferenceNames",
  "medSchool",
  "diagnosisCodes",
  "natureOfPayments",
  "designations",
  "paymentAssociatedDrugs",
  "congressOrganizerNames",
  "referralsServiceLine",
  "trialStatuses",
  "state",
  "trialTypes"
];

export const WHITE_NON_HISPANIC_RACE_CATEGORY = "White Non-Hispanic";
export const ASIAN_PACIFIC_ISLANDER_RACE_CATEGORY = "Asian Pacific Islander";
export const ASIAN_RACE_CATEGORY = "Asian";
export const PACIFIC_ISLANDER_RACE_CATEGORY = "Pacific Islander";
export const OTHER_RACE_CATEGORY = "Other";
export const NOT_DISCLOSED_RACE_CATEGORY = "Not Disclosed";

export type Page = {
  from: number;
  size: number;
};

const NO_DATA = 0;
const NO_SPECIALTIES: Readonly<Array<string>> = [];
type MinMax = Readonly<{
  min: number;
  max: number;
}>;
export const ZERO_MIN_MAX: Readonly<MinMax> = {
  min: 0,
  max: 0
};
const NO_H1DN_ID = null;

export const EMPTY_RANGES: Readonly<DocumentRanges> = {
  publicationCount: ZERO_MIN_MAX,
  citationCount: ZERO_MIN_MAX,
  trialCount: ZERO_MIN_MAX,
  paymentCount: ZERO_MIN_MAX,
  paymentSum: ZERO_MIN_MAX,
  grantCount: ZERO_MIN_MAX,
  grantSum: ZERO_MIN_MAX,
  congressCount: ZERO_MIN_MAX,
  totalCollaborators: ZERO_MIN_MAX,
  socialMediaMentions: ZERO_MIN_MAX,
  procedures: ZERO_MIN_MAX,
  diagnoses: ZERO_MIN_MAX,
  referralsReceived: ZERO_MIN_MAX,
  referralsSent: ZERO_MIN_MAX
};

export const EMPTY_FILTER_COUNTS = ALL_AGGREGATIONS.map((field) => ({
  field,
  buckets: []
}));

type Names = Pick<
  PersonCard,
  | "firstName"
  | "lastName"
  | "middleName"
  | "firstNameEng"
  | "lastNameEng"
  | "name"
  | "nameEng"
>;

type ClaimType = "DRG_diagnoses" | "DRG_procedures";

function getPersonTranslation(
  languages: Array<Language>,
  hit: Readonly<SearchHit<HCPDocument>>
) {
  const language = languages.find(
    (language) => !!hit._source![`name_${language}`]
  );

  if (!language) {
    return undefined;
  }

  return {
    fullName: hit._source![`name_${language}`],
    lastName: hit._source![`lastName_${language}`],
    firstName: hit._source![`firstName_${language}`],
    middleName: hit._source![`middleName_${language}`],
    languageCode: language
  };
}

const getEnglishNameTranslation = getPersonTranslation.bind(this, [ENGLISH]);
const getNonEnglishNameTranslation = getPersonTranslation.bind(this, [
  JAPANESE,
  CHINESE
]);

export const NO_AFFILIATION = "No Affiliation";

@Service()
export class KeywordSearchResponseAdapterService {
  // @ts-ignore
  private readonly logger = createLogger(this);

  constructor(private affiliationsAdapterService: AffiliationAdapterService) {}

  emptyResponse(page: Readonly<Page>): Readonly<PersonSearchResponse> {
    return {
      from: page.from,
      pageSize: page.size,
      total: 0,
      results: [],
      ranges: { ...EMPTY_RANGES },
      normalizedRange: { ...ZERO_MIN_MAX },
      filterCounts: EMPTY_FILTER_COUNTS,
      synonyms: []
    };
  }

  async adapt(
    page: Readonly<Page>,
    language: string,
    hits: Readonly<SearchHitsMetadata<Readonly<HCPDocument>>>,
    aggregations:
      | Readonly<Record<AggregateName, AggregationsAggregate>>
      | undefined,
    synonyms: Readonly<Array<string>>,
    suppliedFilters: FilterInterface,
    queryIntents: QueryIntent[],
    indicationSynonyms: Readonly<Array<string>>,
    featureFlags: KeywordSearchFeatureFlags,
    keywordIcdCodeSynonyms: Readonly<Array<string>>,
    indicationsIcdCodeSynonyms: Readonly<Array<string>>,
    spellSuggesterResult: string | undefined = undefined,
    matchedCountResults: PeopleIdToAssetMatchedCount | undefined = undefined,
    input: KeywordSearchInput | undefined = undefined,
    patientDiversityDistribution:
      | Dictionary<{
          race: DocCountBucket[];
          gender: DocCountBucket[];
          age: DocCountBucket[];
        }>
      | undefined = undefined,
    l1IndicationsResponse?: Map<string, string[]>,
    topL3IndicationsResponse?: Map<string, string[]>,
    territories?: SavedTerritory[],
    matchedClaimsCountForExport?: MatchedClaimCountsFromIEForExports
  ): Promise<Readonly<PersonSearchResponse>> {
    const total = getHitsTotal(hits.total ?? 0);

    const hitsWithSource = hits.hits.filter(this.hasSource);

    const affiliationInstitutions = lookupInstitutions(hitsWithSource);

    const allowDebugDataFlag = featureFlags.enableInternalUsersForRankingDebug;

    const results = hitsWithSource.map((hit) => {
      const baseResult = this.toSearchResult(
        hit,
        language,
        suppliedFilters,
        affiliationInstitutions,
        matchedCountResults,
        input,
        featureFlags,
        patientDiversityDistribution,
        l1IndicationsResponse,
        topL3IndicationsResponse,
        territories,
        matchedClaimsCountForExport
      );

      //Allow only internal users to get debug results and if
      const debugResult = allowDebugDataFlag
        ? this.toDebugSearchResult(hit, suppliedFilters)
        : null;

      return { ...baseResult, debugData: debugResult };
    });

    let totalResultsOutsideUserSlice = 0;
    if (input?.sliceOption === SearchSliceOptionsEnum.FullCorpus) {
      // if the search query included results outside users slice also, then return the number of results within users slice.
      const totalResultsInsideUserSliceBucket =
        aggregations?.user_slice_result_count as AggregationsFilterAggregate;
      totalResultsOutsideUserSlice =
        total - totalResultsInsideUserSliceBucket.doc_count;
    } else if (input?.sliceOption === SearchSliceOptionsEnum.OutsideSlice) {
      totalResultsOutsideUserSlice = total;
    }

    return {
      from: page.from,
      pageSize: page.size,
      total,
      results,
      ranges: { ...EMPTY_RANGES },
      normalizedRange: ZERO_MIN_MAX,
      filterCounts: EMPTY_FILTER_COUNTS,
      synonyms: [...synonyms, ...indicationSynonyms],
      icdCodeSynonyms: [
        ...keywordIcdCodeSynonyms,
        ...indicationsIcdCodeSynonyms
      ],
      queryIntent: [...queryIntents],
      spellSuggesterResult,
      totalResultsOutsideUserSlice
    };
  }

  private hasSource(doc: SearchHit<HCPDocument>) {
    return !!doc._source;
  }

  private extractPublicationHighlight(
    highlight: SearchHit<Record<string, any>>
  ) {
    const publicationsId = _.get(highlight, 'fields["publications.id"][0]');
    if (publicationsId) {
      const publicationAbstract = _.get(
        highlight,
        'highlight["publications.publicationAbstract_eng"][0]'
      );
      const keywords = _.get(
        highlight,
        'highlight["publications.keywords_eng"][0]'
      );
      const title = _.get(highlight, 'highlight["publications.title_eng"][0]');

      return {
        publicationsId,
        highlight: {
          publicationAbstract,
          keywords,
          title
        }
      };
    } else return undefined;
  }

  private extractTrialsHighlight(highlight: SearchHit<Record<string, any>>) {
    const trialsId = _.get(highlight, 'fields["trials.id"][0]');
    if (trialsId) {
      const officialTitle = _.get(
        highlight,
        'highlight["trials.officialTitle_eng"][0]'
      );
      const briefTitle = _.get(
        highlight,
        'highlight["trials.briefTitle_eng"][0]'
      );
      const conditions = _.get(
        highlight,
        'highlight["trials.conditions_eng"][0]'
      );
      const interventions = _.get(
        highlight,
        'highlight["trials.interventions_eng"][0]'
      );
      const keywords = _.get(highlight, 'highlight["trials.keywords_eng"][0]');
      const summary = _.get(highlight, 'highlight["trials.summary_eng"][0]');

      return {
        trialsId,
        highlight: {
          officialTitle,
          briefTitle,
          conditions,
          interventions,
          keywords,
          summary
        }
      };
    } else return undefined;
  }

  private extractCongressHighlight(highlight: SearchHit<Record<string, any>>) {
    const congressId = _.get(highlight, 'fields["congress.id"][0]');
    if (congressId) {
      const keywords = _.get(
        highlight,
        'highlight["congress.keywords_eng"][0]'
      );
      const title = _.get(highlight, 'highlight["congress.title_eng"][0]');

      return {
        congressId,
        highlight: {
          keywords,
          title
        }
      };
    } else return undefined;
  }

  private extractClaimsHighlight(
    highlight: SearchHit<Record<string, any>>,
    claimType: ClaimType
  ) {
    const codeAndDescription = _.get(
      highlight,
      `highlight["${claimType}.codeAndDescription_eng"][0]`
    );

    return {
      highlight: {
        codeAndDescription
      }
    };
  }

  private extractAffiliationInnerHits(hit: SearchHit<Readonly<HCPDocument>>) {
    const allValidAffiliationsInnerHits = pickBy(hit.inner_hits, (_, key) => {
      return startsWith(key, AFFILIATION_HIGHLIGHT_NAME_PREFIX);
    });

    return flatten(
      values(allValidAffiliationsInnerHits).map(
        (inner_hits) => inner_hits.hits.hits
      )
    );
  }

  private extractAffiliationHighlight(
    highlight: SearchHit<Record<string, any>>
  ): AffiliationHighlights {
    const affiliationId = _.get(highlight, 'fields["affiliations.id"][0]');
    const institutionId = _.get(
      highlight,
      'fields["affiliations.institution.id"][0]'
    );
    if (affiliationId && institutionId) {
      const city = _.get(
        highlight,
        'highlight["affiliations.institution.address.city"][0]'
      );
      const region = _.get(
        highlight,
        'highlight["affiliations.institution.address.region"][0]'
      );
      const country = _.get(
        highlight,
        'highlight["affiliations.institution.address.country"][0]'
      );

      return {
        affiliationId,
        highlight: {
          institution: {
            institutionId,
            address: {
              city,
              region,
              country
            }
          }
        }
      };
    } else return undefined;
  }

  private getDiversityRanking(
    races: PatientRace[],
    featureFlags: KeywordSearchFeatureFlags
  ) {
    if (!races.length) {
      return null;
    }

    let excludedRaces = [
      WHITE_NON_HISPANIC_RACE_CATEGORY,
      OTHER_RACE_CATEGORY,
      "Not identified",
      "Not Identified",
      "Unknown"
    ];

    if (featureFlags.enableAsianPacificIslanderDiversityBreakOut) {
      excludedRaces = [...excludedRaces, ASIAN_PACIFIC_ISLANDER_RACE_CATEGORY];
    } else {
      excludedRaces = [
        ...excludedRaces,
        ASIAN_RACE_CATEGORY,
        PACIFIC_ISLANDER_RACE_CATEGORY
      ];
    }

    const nonWhiteRacesPercentage = races.reduce(
      (totalPercentage, { race, percent }) => {
        if (!excludedRaces.includes(race) && percent) {
          return totalPercentage + percent;
        } else {
          return totalPercentage;
        }
      },
      0
    );

    if (nonWhiteRacesPercentage >= 50) {
      return DiversityRankingEnum.HIGH;
    } else if (nonWhiteRacesPercentage >= 25) {
      return DiversityRankingEnum.MEDIUM;
    } else {
      return DiversityRankingEnum.LOW;
    }
  }

  private toSearchResult(
    doc: SearchHit<HCPDocument>,
    language: string,
    suppliedFilters: FilterInterface,
    institutionsKeyedById: Map<number, InstitutionDocument>,
    matchedCountResults: PeopleIdToAssetMatchedCount | undefined = undefined,
    input: KeywordSearchInput | undefined,
    featureFlags: KeywordSearchFeatureFlags,
    patientDiversityDistribution:
      | Dictionary<{
          race: DocCountBucket[];
          gender: DocCountBucket[];
          age: DocCountBucket[];
        }>
      | undefined = undefined,
    l1IndicationsResponse?: Map<string, string[]>,
    topL3IndicationsResponse?: Map<string, string[]>,
    territories?: SavedTerritory[],
    matchedClaimsCountForExport?: MatchedClaimCountsFromIEForExports
  ): PersonCard {
    const nameValues = this.getNameValues(doc._source!, language);

    const UNUSED_NUMBER_ARRAY: Array<number> = [];
    const TAGS_POPULATED_BY_ANOTHER_GRAPHQL_RESOLVER: Array<TagInterface> = [];

    const affiliations = _.defaultTo(doc._source?.affiliations, [])
      .filter((affiliation) => affiliation.type !== NO_AFFILIATION)
      .map((affiliation) =>
        toAffiliation(affiliation, language as Language, institutionsKeyedById)
      );
    const isH1dn = !!(!doc._source?.id && doc._source?.h1dn_id);
    const customTerritoryAffiliations =
      doc.inner_hits?.custom_territory_affiliations?.hits.hits;
    const customTerritoryAffiliationNames = customTerritoryAffiliations
      ? customTerritoryAffiliations.map(
          (innerHit) =>
            _.get(innerHit.fields, "affiliations.institution.name.keyword")[0]
        )
      : [];
    const formattedTopAffiliations =
      this.affiliationsAdapterService.searchTopAffiliations(
        affiliations,
        input,
        isH1dn,
        doc._source?.locations,
        customTerritoryAffiliationNames,
        territories
      );

    const formattedTopTLAffiliations =
      this.affiliationsAdapterService.searchTopTLAffiliations(
        affiliations,
        input,
        isH1dn,
        doc._source?.locations,
        customTerritoryAffiliationNames,
        territories
      );

    const publicationInnerHits = doc.inner_hits?.publications?.hits.hits;
    const trialsInnerHits = doc.inner_hits?.trials?.hits.hits;
    const congressInnerHits = doc.inner_hits?.congress?.hits.hits;
    const claimsDiagnosesInnerHits = doc.inner_hits?.DRG_diagnoses?.hits.hits;
    const claimsProceduresInnerHits = doc.inner_hits?.DRG_procedures?.hits.hits;
    const affiliationInnerHits = this.extractAffiliationInnerHits(doc);

    const uniquePatientToggleFromInput =
      suppliedFilters.claims.showUniquePatients?.value ?? false;
    let diagnosesCount = doc._source!.DRG_diagnosesCount || 0;
    const prescriptionsCount = doc._source!.num_prescriptions || 0;

    const congressSessions = extractMatchingCongressSessions(
      input,
      doc._source!.congress
    );

    if (uniquePatientToggleFromInput) {
      diagnosesCount = doc._source!.totalPatientDocs || 0; // this is last two years patient count, as we don't have time frame filters
    } else if (featureFlags.enableUniquePatientCountForClaims) {
      diagnosesCount = doc._source!.DRG_diagnosesUniqueCount || 0;
    }

    let hasCTMSData: boolean | undefined;
    if (featureFlags.enableCTMSV2) {
      hasCTMSData = doc._source?.inCtmsNetwork ?? doc._source?.hasCtms;
    }
    const {
      enableUniquePatientCountForClaims,
      disableUniquePatientCountForOnlyProcedures
    } = featureFlags;
    const matchedDiagnosesIEForExports = suppliedFilters.getMatchedClaims?.value
      ? this.getDiagnosesFromIeMap(doc, matchedClaimsCountForExport)
      : undefined;
    const matchedDiagnosesNonIEForExports = suppliedFilters.getMatchedClaims
      ?.value
      ? this.getClaimsDiagnoses(doc, suppliedFilters, featureFlags)
      : undefined;
    const matchedProceduresIEForExports = suppliedFilters.getMatchedClaims
      ?.value
      ? this.getProceduresFromIeMap(doc, matchedClaimsCountForExport)
      : undefined;
    const matchedProceduresNonIEForExports = suppliedFilters.getMatchedClaims
      ?.value
      ? this.getClaimsProcedures(doc, suppliedFilters, featureFlags)
      : undefined;

    const trialOngoingCount = matchedCountResults
      ? matchedCountResults[doc._id].trial_ongoing_count ?? 0
      : 0;
    const trialActivelyRecruitingCount = matchedCountResults
      ? matchedCountResults[doc._id].trial_actively_recruiting_count ?? 0
      : 0;
    const patientsDiversity = this.getPatientsDiversity(
      doc._source!,
      patientDiversityDistribution &&
        _.has(patientDiversityDistribution, doc._id)
        ? patientDiversityDistribution[doc._id]
        : { race: [], gender: [], age: [] },
      featureFlags
    );
    const diversityRanking = this.getDiversityRanking(
      patientsDiversity.races || [],
      featureFlags
    );

    return {
      personId: doc._source!.id || "",
      h1dnId: doc._source!.h1dn_id || NO_H1DN_ID,
      ...nameValues,
      isFacultyOpinionsMember: doc._source!.isFacultyOpinionsMember,
      hasCTMSData,
      personTranslationEng: getEnglishNameTranslation(doc),
      personTranslation: getNonEnglishNameTranslation(doc),
      affiliations,
      formattedTopAffiliations,
      formattedTopTLAffiliations,
      scores: this.toScoredDocumentData(
        doc,
        input,
        suppliedFilters,
        matchedCountResults,
        doc._id,
        featureFlags
      ),
      countPresentWorkAffiliations: doc._source!.presentWorkInstitutionCount,
      score: doc._score || NO_DATA,
      totalWorks: doc._source!.totalWorks || 0,
      countPublications: doc._source!.publicationCount || 0,
      countClinicalTrials: doc._source!.trialCount || 0,
      publicationsHighlights: publicationInnerHits?.map(
        this.extractPublicationHighlight
      ),
      trialsHighlights: trialsInnerHits?.map(this.extractTrialsHighlight),
      congressHighlights: congressInnerHits?.map(this.extractCongressHighlight),
      claimsDiagnosesHighlights: claimsDiagnosesInnerHits?.map((highlight) =>
        this.extractClaimsHighlight(highlight, "DRG_diagnoses")
      ),
      claimsProceduresHighlights: claimsProceduresInnerHits?.map((highlight) =>
        this.extractClaimsHighlight(highlight, "DRG_procedures")
      ),
      affiliationHighlights: affiliationInnerHits
        ?.map(this.extractAffiliationHighlight)
        .filter((highlight) => highlight !== undefined),
      socialMediaMentionsTotal: doc._source!.microBloggingTotal || 0,
      referralsReceivedCount: doc._source!.referralsReceivedCount || 0,
      referralsSentCount: doc._source!.referralsSentCount || 0,
      sumPayments: doc._source!.paymentTotal || 0,
      diagnosesCount,
      proceduresCount:
        !disableUniquePatientCountForOnlyProcedures &&
        enableUniquePatientCountForClaims &&
        uniquePatientToggleFromInput
          ? doc._source!.DRG_proceduresUniqueCount || 0
          : doc._source!.DRG_proceduresCount || 0,
      prescriptionsCount,
      congresses: doc._source!.congressCount || 0,
      grants: NO_DATA,
      sumGrants: NO_DATA,
      specialty: doc._source!.specialty_eng ?? NO_SPECIALTIES,
      specialtyCmn: doc._source!.specialty_cmn,
      specialtyJpn: doc._source!.specialty_jpn,
      congressesDates: UNUSED_NUMBER_ARRAY,
      paymentDates: UNUSED_NUMBER_ARRAY,
      publicationDates: UNUSED_NUMBER_ARRAY,
      trialDates: UNUSED_NUMBER_ARRAY,
      trialOngoingCount,
      trialEnrollmentRate: doc._source!.trialEnrollmentRate,
      trialActivelyRecruitingCount,
      citationCount: doc._source!.citationTotal || 0,
      citationCountAvg: NO_DATA,
      infoRequestsResolved: null, // hard-coded for unknown reasons
      tags: TAGS_POPULATED_BY_ANOTHER_GRAPHQL_RESOLVER,
      languageCode: language,
      designations: doc._source!.designations || [],
      patientsDiversity,
      providerDiversity: this.getProviderDiversity(doc._source!),
      diversityPercentile: doc._source!.patientsDiversityPercentile,
      digitalRank: doc._source!.digitalRank,
      top1PercentileDigitalRank: doc._source!.top1PercentileDigitalRank,
      top10PercentileDigitalRank: doc._source!.top10PercentileDigitalRank,
      twitterFollowersCount: doc._source!.twitterFollowersCount,
      twitterTweetCount: doc._source!.twitterTweetCount,
      matchedDiagnoses: matchedDiagnosesIEForExports?.length
        ? matchedDiagnosesIEForExports
        : matchedDiagnosesNonIEForExports,
      matchedProcedures: matchedProceduresIEForExports?.length
        ? matchedProceduresIEForExports
        : matchedProceduresNonIEForExports,
      l1Indications: l1IndicationsResponse?.get(doc._id),
      topL3Indications: topL3IndicationsResponse?.get(doc._id),
      locations: extractLocationField(doc._source),
      isInactive: doc._source!.isInactive,
      isIndustry: doc._source!.isIndustry,
      isSocietyMember: doc._source!.hasSocietyAffiliation,
      isOutsideUsersSlice: !_.includes(
        doc._source?.projectIds,
        input?.projectId
      ),
      diversityRanking,
      congressSessions
    };
  }

  private toDebugSearchResult(
    { inner_hits, _source }: SearchHit<HCPDocument>,
    suppliedFilters: FilterInterface
  ): PersonSearchDebug {
    // Initialize the debug result object
    const debugResult: PersonSearchDebug = {
      risingStar: undefined
    };

    if (suppliedFilters.isRisingStar?.value) {
      const indicationsCount = getInnerHitsTotal(inner_hits?.indications);

      // Extract overall rising score from _source
      const overallRisingScore = _source?.overallRisingScore ?? 0;

      const totalIndicationRisingScore = sumInnerHitsField(
        inner_hits?.indications,
        "indications.indicationRisingScore"
      );

      const avgIndicationRisingScore =
        indicationsCount > 0
          ? totalIndicationRisingScore / indicationsCount
          : 0;

      debugResult.risingStar = {
        overallRisingScore,
        avgIndicationRisingScore: avgIndicationRisingScore
      };
    }
    return debugResult;
  }

  private getClaimsDiagnoses(
    { _source, inner_hits }: SearchHit<HCPDocument>,
    suppliedFilters: FilterInterface,
    { enableUniquePatientCountForClaims }: KeywordSearchFeatureFlags
  ): ClaimsDiagnosis[] {
    const diagnosis = inner_hits?.diagnoses_collection;
    if (!diagnosis) return [];
    const uniquePatientToggleFromInput =
      suppliedFilters.claims.showUniquePatients?.value ?? false;
    let claimsCountField =
      enableUniquePatientCountForClaims && uniquePatientToggleFromInput
        ? "internalUniqueCount"
        : "internalCount";
    if (suppliedFilters.claims.timeFrame?.value) {
      claimsCountField = `${claimsCountField}_${suppliedFilters.claims.timeFrame.value}_year`;
    }
    let timeFrame = "";
    if (suppliedFilters.claims.timeFrame?.value) {
      timeFrame = `_${suppliedFilters.claims.timeFrame.value}_year`;
    }
    const claimsCountTotalFieldForDiagnoses =
      enableUniquePatientCountForClaims && uniquePatientToggleFromInput
        ? "DRG_diagnosesUniqueCount"
        : "DRG_diagnosesCount";

    const diagnosesCount =
      `${claimsCountTotalFieldForDiagnoses}${timeFrame}` as keyof typeof _source;
    const totalDiagnoses = _source ? _source[diagnosesCount] : 0;

    const results = diagnosis?.hits?.hits?.map((data) => {
      const internalCount: number = _.get(
        data.fields,
        [`DRG_diagnoses.${claimsCountField}`, "0"],
        0
      );
      const description: string = _.get(
        data.fields,
        [`DRG_diagnoses.description_eng.keyword`, "0"],
        ""
      );
      const diagnosisCode: string = _.get(
        data.fields,
        [`DRG_diagnoses.diagnosisCode_eng`, "0"],
        ""
      );
      const codeScheme: string = _.get(
        data.fields,
        [`DRG_diagnoses.codeScheme.keyword`, "0"],
        ""
      );
      const percentageField =
        enableUniquePatientCountForClaims && uniquePatientToggleFromInput
          ? "pctOfUniqueClaims"
          : "pctOfClaims";
      return {
        description,
        diagnosisCode,
        percentOfClaims: parseFloat(
          _.get(
            data.fields,
            [`DRG_diagnoses.${percentageField}${timeFrame}`, "0"],
            0
          )
        ),
        percentage:
          totalDiagnoses > 0 ? (internalCount / totalDiagnoses) * 100 : 0,
        internalCount,
        codeScheme,
        count: internalCount.toString()
      } as ClaimsDiagnosis;
    });
    return results;
  }
  private getDiagnosesFromIeMap(
    hit: SearchHit<HCPDocument>,
    matchedClaimsForPeopleMap?: MatchedClaimCountsFromIEForExports
  ): ClaimsDiagnosis[] {
    if (matchedClaimsForPeopleMap) {
      const totalPatientDocs = hit._source?.totalPatientDocs ?? 0;
      const claimsDictionary =
        matchedClaimsForPeopleMap.diagnosesCountMap[hit._id] ?? {};
      const matchedClaimsPatientDetails = [];
      for (const [key, value] of Object.entries(claimsDictionary)) {
        const details = {
          diagnosisCode: key,
          internalCount: value.count,
          count: value.count.toString(),
          percentage:
            totalPatientDocs > 0 ? (value.count / totalPatientDocs) * 100 : 0,
          codeScheme: value.scheme ?? "",
          description: value.description ?? "",
          percentOfClaims: 0
        };
        matchedClaimsPatientDetails.push(details);
      }
      return matchedClaimsPatientDetails;
    }
    return [];
  }
  private getProceduresFromIeMap(
    hit: SearchHit<HCPDocument>,
    matchedClaimsForPeopleMap?: MatchedClaimCountsFromIEForExports
  ): ClaimsProcedure[] {
    if (matchedClaimsForPeopleMap) {
      const totalPatientDocs = hit._source?.totalPatientDocs ?? 0;
      const claimsDictionary =
        matchedClaimsForPeopleMap.proceduresCountMap[hit._id] ?? {};
      const matchedClaimsPatientDetails = [];
      for (const [key, value] of Object.entries(claimsDictionary)) {
        const details = {
          procedureCode: key,
          internalCount: value.count,
          count: value.count.toString(),
          percentage:
            totalPatientDocs > 0 ? (value.count / totalPatientDocs) * 100 : 0,
          codeScheme: value.scheme ?? "",
          description: value.description ?? "",
          percentOfClaims: 0
        };
        matchedClaimsPatientDetails.push(details);
      }
      return matchedClaimsPatientDetails;
    }
    return [];
  }
  private getClaimsProcedures(
    { _source, inner_hits }: SearchHit<HCPDocument>,
    suppliedFilters: FilterInterface,
    featureFlags: KeywordSearchFeatureFlags
  ): ClaimsProcedure[] {
    const procedures = inner_hits?.procedures_collection;
    if (!procedures) return [];
    const {
      enableUniquePatientCountForClaims,
      disableUniquePatientCountForOnlyProcedures
    } = featureFlags;
    const uniquePatientToggleFromInput =
      suppliedFilters.claims.showUniquePatients?.value ?? false;
    let claimsCountField =
      !disableUniquePatientCountForOnlyProcedures &&
      enableUniquePatientCountForClaims &&
      uniquePatientToggleFromInput
        ? "internalUniqueCount"
        : "internalCount";
    if (suppliedFilters.claims.timeFrame?.value) {
      claimsCountField = `${claimsCountField}_${suppliedFilters.claims.timeFrame.value}_year`;
    }
    let timeFrame = "";
    if (suppliedFilters.claims.timeFrame?.value) {
      timeFrame = `_${suppliedFilters.claims.timeFrame.value}_year`;
    }
    const claimsCountTotalFieldForProcedures =
      !disableUniquePatientCountForOnlyProcedures &&
      enableUniquePatientCountForClaims &&
      uniquePatientToggleFromInput
        ? "DRG_proceduresUniqueCount"
        : "DRG_proceduresCount";
    const procedureCount =
      `${claimsCountTotalFieldForProcedures}${timeFrame}` as keyof typeof _source;
    const totalProcedures = _source ? _source[procedureCount] : 0;

    const results = procedures.hits.hits.map((data) => {
      const internalCount: number = _.get(
        data.fields,
        [`DRG_procedures.${claimsCountField}`, "0"],
        0
      );
      const description: string = _.get(
        data.fields,
        ["DRG_procedures.description_eng.keyword", "0"],
        ""
      );
      const procedureCode: string = _.get(
        data.fields,
        ["DRG_procedures.procedureCode_eng.keyword", "0"],
        ""
      );
      const codeScheme: string = _.get(
        data.fields,
        ["DRG_procedures.codeScheme", "0"],
        ""
      );
      const percentageField =
        !disableUniquePatientCountForOnlyProcedures &&
        enableUniquePatientCountForClaims &&
        uniquePatientToggleFromInput
          ? "uniquePercentage"
          : "percentage";
      return {
        description,
        procedureCode,
        percentOfClaims: _.get(
          data.fields,
          [`DRG_procedures.${percentageField}${timeFrame}`, "0"],
          0
        ),
        percentage:
          totalProcedures > 0 ? (internalCount / totalProcedures) * 100 : 0,
        internalCount,
        codeScheme,
        count: internalCount.toString()
      } as ClaimsProcedure;
    });
    return results;
  }

  private toScoredDocumentData(
    { _source, _score, inner_hits }: SearchHit<HCPDocument>,
    input: KeywordSearchInput | undefined,
    suppliedFilters: FilterInterface,
    matchedCountResults: PeopleIdToAssetMatchedCount | undefined = undefined,
    docId: string,
    featureFlags: KeywordSearchFeatureFlags
  ) {
    const publicationsCount = getInnerHitsTotal(inner_hits?.publications);
    const congressCount = getInnerHitsTotal(inner_hits?.congress);
    const trialsCount = getInnerHitsTotal(inner_hits?.trials);
    const referralsReceivedCount = _source!.referralsReceivedCount;
    const referralsSentCount = _source!.referralsSentCount;
    const paymentsCount = getInnerHitsTotal(inner_hits?.payments);

    let claimsCountField = "";
    let prescriptionsCountField = "";
    const isTimeFrameApplied = _.isNumber(
      suppliedFilters.claims.timeFrame?.value
    );
    const isIcdCode = !_.isEmpty(suppliedFilters.claims.diagnosesICD?.values);
    const isCptCode = !_.isEmpty(suppliedFilters.claims.proceduresCPT?.values);
    const isCcsr = !_.isEmpty(suppliedFilters.claims.ccsr?.values);
    const isCcsrPx = !_.isEmpty(suppliedFilters.claims.ccsrPx?.values);

    if (isTimeFrameApplied) {
      claimsCountField =
        claimsCountField + `_${suppliedFilters.claims.timeFrame!.value}_year`;
    }
    const isPrescriptionsTimeFrameApplied = _.isNumber(
      suppliedFilters.claims.prescriptionsTimeFrame?.value
    );
    if (isPrescriptionsTimeFrameApplied) {
      prescriptionsCountField =
        prescriptionsCountField +
        `_${suppliedFilters.claims.prescriptionsTimeFrame!.value}_year`;
    }
    const {
      enableUniquePatientCountForClaims,
      disableUniquePatientCountForOnlyProcedures
    } = featureFlags;
    const uniquePatientToggleFromInput =
      suppliedFilters.claims.showUniquePatients?.value ?? false;
    const claimCountPickedForDiagnoses =
      enableUniquePatientCountForClaims && uniquePatientToggleFromInput
        ? "internalUniqueCount"
        : "internalCount";
    const claimCountPickedForProcedures =
      !disableUniquePatientCountForOnlyProcedures &&
      enableUniquePatientCountForClaims &&
      uniquePatientToggleFromInput
        ? "internalUniqueCount"
        : "internalCount";
    const claimsCountTotalFieldForDiagnoses =
      enableUniquePatientCountForClaims && uniquePatientToggleFromInput
        ? "DRG_diagnosesUniqueCount"
        : "DRG_diagnosesCount";
    const claimsCountTotalFieldForProcedures =
      !disableUniquePatientCountForOnlyProcedures &&
      enableUniquePatientCountForClaims &&
      uniquePatientToggleFromInput
        ? "DRG_proceduresUniqueCount"
        : "DRG_proceduresCount";
    const diagnosisTimeframe =
      `${claimsCountTotalFieldForDiagnoses}${claimsCountField}` as keyof DRG_diagnosesCount;

    const diagnosisTimeframeCountOrZero =
      isTimeFrameApplied && !isIcdCode && !isCptCode && !isCcsr
        ? _source![diagnosisTimeframe]!
        : 0;

    const ccsrOrIcdEncounters = matchedCountResults
      ? (matchedCountResults[docId]?.diagnoses_amount ??
          diagnosisTimeframeCountOrZero) +
        (matchedCountResults[docId]?.ccsr_amount ?? 0)
      : diagnosisTimeframeCountOrZero;

    let diagnosesSum = matchedCountResults
      ? ccsrOrIcdEncounters
      : sumInnerHitsField(
          inner_hits?.diagnoses_amount,
          `DRG_diagnoses.${claimCountPickedForDiagnoses}${claimsCountField}`,
          diagnosisTimeframeCountOrZero
        ) +
        sumInnerHitsField(
          inner_hits?.ccsr_amount,
          `ccsr.${claimCountPickedForDiagnoses}${claimsCountField}`,
          diagnosisTimeframeCountOrZero
        );

    if (inner_hits?.patient_claims_matching_count) {
      // matching unique patient count from patient child docs
      diagnosesSum = getInnerHitsTotal(
        inner_hits.patient_claims_matching_count
      );
    } else if (input?.suppliedFilters?.claims?.showUniquePatients?.value) {
      diagnosesSum =
        matchedCountResults &&
        matchedCountResults[docId]?.patient_claims_matching_count
          ? matchedCountResults[docId].patient_claims_matching_count
          : 0;
    }

    const prescriptionsTimeframe =
      `num_prescriptions${prescriptionsCountField}` as keyof PrescriptionsCount;
    const prescriptionsTimeframeCountOrZero = isPrescriptionsTimeFrameApplied
      ? _source![prescriptionsTimeframe]!
      : 0;
    const prescriptionsSum = matchedCountResults
      ? matchedCountResults[docId]?.prescriptions_amount ??
        prescriptionsTimeframeCountOrZero
      : sumInnerHitsField(
          inner_hits?.prescriptions_amount,
          `prescriptions.num_prescriptions${prescriptionsCountField}`
        );

    const procedureTimeframe =
      `${claimsCountTotalFieldForProcedures}${claimsCountField}` as keyof DRG_proceduresCount;
    const procedureTimeframeCountOrZero =
      isTimeFrameApplied && !isIcdCode && !isCptCode && !isCcsrPx
        ? _source![procedureTimeframe]!
        : 0;
    const proceduresSum = matchedCountResults
      ? (matchedCountResults[docId]?.procedures_amount ??
          procedureTimeframeCountOrZero) +
        (matchedCountResults[docId]?.ccsr_px_amount ?? 0)
      : sumInnerHitsField(
          inner_hits?.procedures_amount,
          `DRG_procedures.${claimCountPickedForProcedures}${claimsCountField}`,
          procedureTimeframeCountOrZero
        ) +
        sumInnerHitsField(
          inner_hits?.ccsr_px_amount,
          `ccsr_px.${claimCountPickedForProcedures}${claimsCountField}`,
          procedureTimeframeCountOrZero
        );

    const paymentsSum = matchedCountResults
      ? matchedCountResults[docId].payments ?? 0
      : sumInnerHitsField(inner_hits?.payments, "payments.amount");
    const citationsSum = matchedCountResults
      ? matchedCountResults[docId].citations ?? 0
      : sumInnerHitsField(inner_hits?.citations, "publications.citationCount");
    const microBloggingSum = matchedCountResults
      ? matchedCountResults[docId].microBlogging ?? 0
      : sumInnerHitsField(
          inner_hits?.microBlogging,
          "publications.microBloggingCount"
        );

    const twitterFollowersCount = _source!.twitterFollowersCount ?? 0;
    const twitterTweetCount = _source!.twitterTweetCount ?? 0;

    return {
      normalizedRange: ZERO_MIN_MAX,
      personId: _source!.id || "",
      h1Score: _score!,
      publications: this.toScoredDocumentResult(publicationsCount),
      citations: this.toScoredDocumentResult(citationsSum),
      trials: this.toScoredDocumentResult(trialsCount),
      payments: this.toScoredDocumentResult(paymentsSum),
      paymentsCount: this.toScoredDocumentResult(paymentsCount),
      grants: ZERO_SCORED_DOCUMENT_RESULT,
      grantsCount: ZERO_SCORED_DOCUMENT_RESULT,
      congresses: this.toScoredDocumentResult(congressCount),
      collaborators: ZERO_SCORED_DOCUMENT_RESULT,
      socialMediaMentions: this.toScoredDocumentResult(microBloggingSum),
      procedures: this.toScoredDocumentResult(proceduresSum),
      diagnoses: this.toScoredDocumentResult(diagnosesSum),
      prescriptions: this.toScoredDocumentResult(prescriptionsSum),
      referralsReceived: this.toScoredDocumentResult(referralsReceivedCount),
      referralsSent: this.toScoredDocumentResult(referralsSentCount),
      twitterFollowersCount: this.toScoredDocumentResult(twitterFollowersCount),
      twitterTweetCount: this.toScoredDocumentResult(twitterTweetCount)
    };
  }

  private toScoredDocumentResult(value: number): ScoredDocumentResult {
    return {
      minValue: 0,
      maxValue: 0,
      normalizedValue: 0,
      percentile: 0,
      value
    };
  }

  // TODO: This incredibly tortured logic is (essentially) copied from the original in
  //       sdk/util/transformer.ts just to get this out the door.  I have no idea why it
  //       does certain things but presumably it's due to data anomalies.  I've done
  //       my best to reflect all the conditionals in tests.
  //
  //       This same incredibly tortured logic does strange things with certain HCPs.  For
  //       example, when the user's language is "jpn" and HCP 1199780 is returned, the names
  //       are returned as:
  //         "firstName": "Hagop M. Kantarjian",
  //			   "lastName": "",
  //		     "middleName": null,
  //			   "firstNameEng": "",
  //		     "lastNameEng": "M.",
  //     	 	 "middleNameEng": null
  //
  //       This is pretty useless as-is and probably results in complicated UI logic.  This
  //       should be revisited in the near future.
  private getNameValues(source: HCPDocument, language: string): Names {
    const names: Names = {
      firstName: "",
      middleName: "",
      lastName: "",
      firstNameEng: "",
      lastNameEng: "",
      name: "",
      nameEng: ""
    };

    switch (language) {
      case ENGLISH:
        names.firstName = source.firstName_eng;
        names.lastName = source.lastName_eng;
        names.middleName = source.middleName_eng || undefined;
        names.name = source.name_eng;
        break;
      case CHINESE:
        names.firstName = source.firstName_cmn;
        names.lastName = source.lastName_cmn;
        names.middleName = null;
        names.name = source.name_cmn;
        break;
      default:
        names.firstName = source.firstName_jpn;
        names.lastName = source.lastName_jpn;
        names.middleName = null;
        names.name = source.name_jpn;
    }

    if (!names.firstName && names.lastName !== source.name_eng?.trim()) {
      names.firstName = source.name_eng;
    } else {
      names.firstNameEng = source.firstName_eng;
      names.lastNameEng = source.lastName_eng;
    }

    if (!names.lastNameEng && source?.middleName_eng) {
      names.lastNameEng = source.middleName_eng;
    }

    names.firstName = names.firstName || "";
    names.lastName = names.lastName || "";
    names.firstNameEng = names.firstNameEng || "";
    names.lastNameEng = names.lastNameEng || "";
    names.name = names.name || "";
    names.nameEng = source.name_eng || "";

    return names;
  }

  private getPatientsDiversity(
    doc: HCPDocument,
    patientDiversityDistribution: {
      race: DocCountBucket[];
      gender: DocCountBucket[];
      age: DocCountBucket[];
    },
    featureFlags: KeywordSearchFeatureFlags
  ): PatientDiversity {
    return {
      races: this.getRaceDistribution(
        doc,
        patientDiversityDistribution.race,
        featureFlags
      ),
      age: this.getAgeDistribution(doc, patientDiversityDistribution.age),
      sex: this.getGenderDistribution(doc, patientDiversityDistribution.gender)
    };
  }

  private getRaceDistribution(
    { patientsDiversity, patientsDiversityRatio }: HCPDocument,
    raceDistribution: DocCountBucket[],
    featureFlags: KeywordSearchFeatureFlags
  ) {
    // use patient diversity from child docs if it is available
    if (raceDistribution.length > 0) {
      const races = raceDistribution
        .map((bucket, index) => {
          return { race: bucket.key, count: bucket.doc_count, rank: index };
        })
        .filter(
          (r) =>
            r.race !== OTHER_RACE_CATEGORY &&
            r.race !== NOT_DISCLOSED_RACE_CATEGORY
        ); // remove 'other' and 'not disclosed' race for the calcutions of diversity distribution

      const distribution = races.map((race) =>
        assignPercentage(race, _.sumBy(races, "count"))
      );
      return this.breakoutAsianPacificIslanderDistribution(
        distribution,
        patientsDiversityRatio,
        featureFlags
      );
    } else if (patientsDiversity?.[0]?.races_eng) {
      const races = patientsDiversity[0].races_eng.filter(
        (race) =>
          race.race !== ASIAN_RACE_CATEGORY &&
          race.race !== PACIFIC_ISLANDER_RACE_CATEGORY &&
          race.race !== OTHER_RACE_CATEGORY &&
          race.race !== NOT_DISCLOSED_RACE_CATEGORY
      );
      const totalPatientsByRace = _.sumBy(races, "count");
      const distribution = races.map((race) =>
        assignPercentage(race, totalPatientsByRace)
      );
      return this.breakoutAsianPacificIslanderDistribution(
        distribution,
        patientsDiversityRatio,
        featureFlags
      );
    } else {
      return [];
    }
  }

  private breakoutAsianPacificIslanderDistribution(
    races: Array<{
      race: string;
      count: number;
      rank: number;
      percent: number;
    }>,
    patientsDiversityRatio: HCPDocument["patientsDiversityRatio"],
    { enableAsianPacificIslanderDiversityBreakOut }: KeywordSearchFeatureFlags
  ) {
    if (
      !enableAsianPacificIslanderDiversityBreakOut ||
      !patientsDiversityRatio.asianPacificIslander
    ) {
      return races;
    }

    const asianPacificIslander = races.find(
      (race) => race.race === "Asian Pacific Islander"
    );

    if (
      asianPacificIslander &&
      Number(patientsDiversityRatio.asianPacificIslander) &&
      patientsDiversityRatio.asian !== undefined &&
      patientsDiversityRatio.pacificIslander !== undefined
    ) {
      const percentOfAsianOverCombined =
        patientsDiversityRatio.asian /
        patientsDiversityRatio.asianPacificIslander;
      const asian = {
        race: ASIAN_RACE_CATEGORY,
        count: percentOfAsianOverCombined * asianPacificIslander.count,
        rank: asianPacificIslander.rank,
        percent: percentOfAsianOverCombined * asianPacificIslander.percent
      };

      const percentOfPacificIslanderOverCombined =
        patientsDiversityRatio.pacificIslander /
        patientsDiversityRatio.asianPacificIslander;
      const pacificIslander = {
        race: PACIFIC_ISLANDER_RACE_CATEGORY,
        count:
          percentOfPacificIslanderOverCombined * asianPacificIslander.count,
        rank: asianPacificIslander.rank,
        percent:
          percentOfPacificIslanderOverCombined * asianPacificIslander.percent
      };

      return [...races, asian, pacificIslander];
    }

    return races;
  }

  private getAgeDistribution(
    { patientsDiversity }: HCPDocument,
    ageDistribution: DocCountBucket[]
  ) {
    if (ageDistribution.length > 0) {
      const ages = ageDistribution.map((bucket) => {
        return { range: bucket.key, count: bucket.doc_count };
      });
      return ages.map((ageRange) =>
        assignPercentage(ageRange, _.sumBy(ages, "count"))
      );
    } else if (patientsDiversity?.[0]?.age) {
      const ages = patientsDiversity[0].age;
      const totalPatientsByAge = _.sumBy(ages, "count");
      return ages.map((age) => assignPercentage(age, totalPatientsByAge));
    } else {
      return [];
    }
  }

  private getGenderDistribution(
    { patientsDiversity }: HCPDocument,
    genderDistribution: DocCountBucket[]
  ) {
    if (genderDistribution.length > 0) {
      const genders = genderDistribution.map((bucket) => {
        return { value: bucket.key, count: bucket.doc_count };
      });
      return genders.map((gender) =>
        assignPercentage(gender, _.sumBy(genders, "count"))
      );
    } else if (patientsDiversity?.[0]?.sex) {
      const genders = patientsDiversity[0].sex;
      const totalPatientByGender = _.sumBy(genders, "count");
      return genders.map((gender) =>
        assignPercentage(gender, totalPatientByGender)
      );
    } else {
      return [];
    }
  }

  private getProviderDiversity({
    providerDiversity
  }: HCPDocument): ProviderDiversity {
    if (!providerDiversity || providerDiversity.length === 0) {
      return {
        races: [],
        sex: [],
        languagesSpoken: []
      };
    }

    return {
      races: providerDiversity[0].races_eng || [],
      sex: providerDiversity[0].sex || [],
      languagesSpoken: providerDiversity[0].languagesSpoken || []
    };
  }
}

export function getHitsTotal(total: number | SearchTotalHits) {
  if (typeof total === "number") {
    return total;
  }

  return total.value;
}

function getInnerHitsTotal(
  innerHits?: SearchInnerHitsResult,
  defaultCount = 0
) {
  if (!innerHits) {
    return defaultCount;
  }

  return getHitsTotal(innerHits.hits.total ?? 0);
}

function assignPercentage<T extends { count: number; percent?: number }>(
  countable: T,
  total: number
) {
  return {
    ...countable,
    percent: total === 0 ? 0 : Math.round((countable.count / total) * 100)
  };
}

function sumInnerHitsField(
  innerHits: SearchInnerHitsResult | undefined,
  field: string,
  defaultCount = 0
) {
  if (!innerHits) {
    return defaultCount;
  }

  return innerHits.hits.hits.reduce((sum, innerHit) => {
    if (innerHit.fields) {
      let count = 0;
      if (innerHit.fields![field]) {
        count = innerHit.fields![field][0];
      }
      return sum + count;
    }
    return sum;
  }, 0);
}

export function lookupInstitutions(hcpHits: Iterable<SearchHit<HCPDocument>>) {
  const institutionMap: Map<number, InstitutionDocument> = new Map();
  for (const { _source: hcpDocument } of hcpHits) {
    const nestedAffiliationDocuments = hcpDocument!.affiliations || [];

    for (const { institution } of nestedAffiliationDocuments) {
      institutionMap.set(institution.id, {
        id: institution.id,
        institutionId: toNumber(institution.id),
        masterOrganizationId: institution.masterOrganizationId,
        isIol: institution.isIol,
        ultimate_parent_id: toNumber(institution.ultimateParentId),
        name: institution.name,
        nameTranslations: institution.nameTranslations,
        type: institution.type,
        orgTypes: institution.orgTypes,
        region: institution.region
      });

      if (
        institution.ultimateParentId &&
        !institutionMap.has(toNumber(institution.ultimateParentId))
      ) {
        const ultimateParentInstitution = institution.ultimateParentInstitution;
        institutionMap.set(toNumber(institution.ultimateParentId), {
          id: ultimateParentInstitution.id,
          isIol: ultimateParentInstitution.isIol,
          masterOrganizationId: toNumber(
            ultimateParentInstitution.masterOrganizationId
          ),
          type: ultimateParentInstitution.type,
          name: ultimateParentInstitution.name,
          nameTranslations: ultimateParentInstitution.nameTranslations,
          orgTypes: [],
          region: ultimateParentInstitution.region || "", // TODO: Add region to index if needed
          institutionId: ultimateParentInstitution.id
        });
      }
    }
  }

  return institutionMap;
}

export function toAffiliation(
  affiliation: AffiliationNestedDocument,
  language: Language,
  institutionsKeyedById: Map<number, InstitutionDocument>
): Affiliation {
  const requestedTitleTranslation = getRequestedTranslation(
    affiliation.titleTranslations,
    language
  )?.titles;

  const requestedDepartmentTranslation = getRequestedTranslation(
    affiliation.departmentTranslations,
    language
  )?.department;

  const titles = requestedTitleTranslation || affiliation.titles;
  const department = requestedDepartmentTranslation || affiliation.department;

  const institution = buildInstitution(
    affiliation.institution,
    institutionsKeyedById,
    language
  );

  return {
    id: affiliation.id,
    type: affiliation.type,
    titles,
    department,
    school: undefined,
    isPastAffiliation: !affiliation.isCurrent,
    institution,
    accuracyScore: affiliation.accuracyScore,
    tlAccuracyScore: affiliation.tlAccuracyScore,
    otherSignals: affiliation.otherSignals,
    claimsNumbers: affiliation.claimsNumbers,
    claimsStates: affiliation.claimsStates
  };
}

function buildInstitution(
  affiliationInstitution: AffiliationInstitutionNestedDocument,
  institutionsKeyedById: Map<number, InstitutionDocument>,
  language: Language
): AffiliationInstitution {
  if (!institutionsKeyedById.has(affiliationInstitution.id)) {
    throw new Error(`Failed to build institution ${affiliationInstitution.id}`);
  }

  const requestedNameTranslation = getRequestedTranslation(
    affiliationInstitution.nameTranslations,
    language
  )?.name;

  const name = requestedNameTranslation || affiliationInstitution.name;

  const address = getTranslatedAddress(affiliationInstitution, language);

  const institutionDocument = institutionsKeyedById.get(
    affiliationInstitution.id
  )!;

  const ultimateParent = toUltimateParent(
    affiliationInstitution.ultimateParentId,
    institutionsKeyedById,
    language
  );

  return {
    id: institutionDocument.id,
    orgTypes: _.castArray(institutionDocument.orgTypes),
    iol: institutionDocument.isIol ? toIol(institutionDocument) : undefined,
    name,
    address,
    type: institutionDocument.type,
    ultimateParent
  };
}

function getTranslatedAddress(
  institution: AffiliationInstitutionNestedDocument,
  language: Language
): AffiliationInstitutionAddress | undefined {
  const requestedAddressTranslation = getRequestedTranslation(
    institution.addressTranslations,
    language
  );

  const originalAddress = institution.address;

  if (requestedAddressTranslation) {
    return {
      id: requestedAddressTranslation.id,
      street1: requestedAddressTranslation.street1,
      street2: requestedAddressTranslation.street2,
      street3: requestedAddressTranslation.street3,
      city: requestedAddressTranslation.city,
      region: requestedAddressTranslation.region,
      regionCode: requestedAddressTranslation.region_code,
      postalCode: requestedAddressTranslation.postal_code,
      country: requestedAddressTranslation.country,
      countryCode: requestedAddressTranslation.country_code,
      latitude: institution.location?.lat,
      longitude: institution.location?.lon,
      latLongPrecision: institution.lat_long_precision,
      country_level_regions: originalAddress.country_level_regions,
      state_level_regions: originalAddress.state_level_regions,
      city_level_regions: originalAddress.city_level_regions
    };
  }

  if (!_.isEmpty(originalAddress)) {
    return {
      id: originalAddress.id,
      street1: originalAddress.street1,
      street2: originalAddress.street2,
      street3: originalAddress.street3,
      city: originalAddress.city,
      region: originalAddress.region,
      regionCode: originalAddress.region_code,
      postalCode: originalAddress.postal_code,
      country: originalAddress.country,
      countryCode: originalAddress.country_code,
      latitude: institution.location?.lat,
      longitude: institution.location?.lon,
      latLongPrecision: institution.lat_long_precision,
      country_level_regions: originalAddress.country_level_regions,
      state_level_regions: originalAddress.state_level_regions,
      city_level_regions: originalAddress.city_level_regions
    };
  }

  return undefined;
}

export function getRequestedTranslation<T extends Translation>(
  translations: Readonly<Array<T>> = [],
  languageCode: Language
): T | undefined {
  let requestedTranslation;

  if (languageCode === CHINESE) {
    requestedTranslation = getChineseTranslation(translations);
  } else if (languageCode === JAPANESE) {
    requestedTranslation = getJapaneseTranslation(translations);
  } else {
    requestedTranslation = getEnglishTranslation(translations);
  }

  if (requestedTranslation) {
    return requestedTranslation;
  }

  return getEnglishTranslation(translations);
}

function getChineseTranslation<T extends Translation>(
  translations: Readonly<Array<T>> = []
): T | undefined {
  return translations.find(
    (translation) =>
      translation.languageCode === CHINESE ||
      translation.languageCode.startsWith(ALTERNATE_CHINESE_PREFIX)
  );
}

function getJapaneseTranslation<T extends Translation>(
  translations: Readonly<Array<T>> = []
): T | undefined {
  return translations.find(
    (translation) =>
      translation.languageCode === JAPANESE ||
      translation.languageCode.startsWith(ALTERNATE_JAPANESE_PREFIX)
  );
}

function getEnglishTranslation<T extends Translation>(
  translations: Readonly<Array<T>> = []
): T | undefined {
  return translations.find(
    (translation) =>
      translation.languageCode === ENGLISH ||
      translation.languageCode.startsWith(ALTERNATE_ENGLISH_PREFIX)
  );
}

function toIol(institutionDocument: InstitutionDocument): AffiliationIol {
  return {
    id: institutionDocument.masterOrganizationId!,
    region: institutionDocument.region
  };
}

function toUltimateParent(
  ultimateParentId: string | number | undefined,
  institutionsKeyedById: Map<number, InstitutionDocument>,
  language: Language
): AffiliationInstitution | undefined {
  if (!ultimateParentId) {
    return undefined;
  }

  const ultimateParent = institutionsKeyedById.get(
    _.toNumber(ultimateParentId)
  );

  if (!ultimateParent) {
    return undefined;
  }

  const requestedNameTranslation = getRequestedTranslation(
    ultimateParent.nameTranslations,
    language
  )?.name;

  return {
    id: ultimateParent.id,
    orgTypes: [],
    iol: ultimateParent.isIol ? toIol(ultimateParent) : undefined,
    name: requestedNameTranslation || ultimateParent.name,
    type: ultimateParent.type
  };
}

function toSpeakerCongressSession(
  congressSession: CongressSession
): SpeakerCongressSession {
  return {
    id: congressSession.id,
    name: congressSession.title_eng,
    role: congressSession.role
  };
}

function extractMatchingCongressSessions(
  searchInput?: KeywordSearchInput,
  congressSessions?: CongressNestedDocument[]
) {
  if (!searchInput?.sortBy.congressContributerRank || !congressSessions) {
    return [];
  }

  const congressName = searchInput.suppliedFilters.congresses.name.values[0];

  return congressSessions
    .filter((congressSession) => congressSession.name_eng === congressName)
    .map(toSpeakerCongressSession);
}
