import { faker } from "@faker-js/faker";
import { createMockInstance } from "../util/TestUtils";
import { ElasticSearchService } from "./ElasticSearchService";
import { PatientDiversityStatsService } from "./PatientDiversityStatsService";
import { RulesParserService } from "./RulesParserService";
import {
  DiversityStats,
  RuleCombinatorEnum,
  RuleFieldEnum,
  RuleOperatorEnum,
  RuleTypeEnum
} from "@h1nyc/search-sdk";
import {
  AggregationsTermsAggregateBase,
  SearchResponse
} from "@elastic/elasticsearch/lib/api/types";
import { DocCountBucket } from "./KeywordAutocompleteResponseAdapterService";
import { keys } from "lodash";

function generateMockDiversityAggregation(): SearchResponse {
  return {
    took: faker.datatype.number(),
    timed_out: false,
    _shards: {
      total: 1,
      successful: 1,
      skipped: 0,
      failed: 0
    },
    hits: {
      total: {
        value: 0,
        relation: "eq"
      },
      hits: []
    },
    aggregations: {
      income: {
        doc_count_error_upper_bound: 0,
        sum_other_doc_count: 0,
        buckets: [
          {
            key: "50K-75K",
            doc_count: faker.datatype.number()
          },
          {
            key: "125K+",
            doc_count: faker.datatype.number()
          },
          {
            key: "75K-100K",
            doc_count: faker.datatype.number()
          },
          {
            key: "30K-40K",
            doc_count: faker.datatype.number()
          },
          {
            key: "40K-50K",
            doc_count: faker.datatype.number()
          },
          {
            key: "100K-125K",
            doc_count: faker.datatype.number()
          },
          {
            key: "20K-30K",
            doc_count: faker.datatype.number()
          },
          {
            key: "15K-20K",
            doc_count: faker.datatype.number()
          },
          {
            key: "<15K",
            doc_count: faker.datatype.number()
          }
        ]
      },
      education: {
        doc_count_error_upper_bound: 0,
        sum_other_doc_count: 0,
        buckets: [
          {
            key: "High School Diploma",
            doc_count: faker.datatype.number()
          },
          {
            key: "College Degree",
            doc_count: faker.datatype.number()
          },
          {
            key: "Advanced Degree",
            doc_count: faker.datatype.number()
          },
          {
            key: "Vocational School Certificate",
            doc_count: faker.datatype.number()
          }
        ]
      },
      race: {
        doc_count_error_upper_bound: 0,
        sum_other_doc_count: 0,
        buckets: [
          {
            key: "White Non-Hispanic",
            doc_count: faker.datatype.number()
          },
          {
            key: "Hispanic",
            doc_count: faker.datatype.number()
          },
          {
            key: "Black Non-Hispanic",
            doc_count: faker.datatype.number()
          },
          {
            key: "Asian Pacific Islander",
            doc_count: faker.datatype.number()
          },
          {
            key: "Other",
            doc_count: faker.datatype.number()
          },
          {
            key: "Not Disclosed",
            doc_count: faker.datatype.number()
          },
          {
            key: "American Indian Or Alaska Native",
            doc_count: faker.datatype.number()
          }
        ]
      },
      sex: {
        doc_count_error_upper_bound: 0,
        sum_other_doc_count: 0,
        buckets: [
          {
            key: "F",
            doc_count: faker.datatype.number()
          },
          {
            key: "M",
            doc_count: faker.datatype.number()
          }
        ]
      },
      age: {
        doc_count_error_upper_bound: 0,
        sum_other_doc_count: 0,
        buckets: [
          {
            key: "75-79",
            doc_count: faker.datatype.number()
          },
          {
            key: "70-74",
            doc_count: faker.datatype.number()
          },
          {
            key: "80-84",
            doc_count: faker.datatype.number()
          },
          {
            key: "65-69",
            doc_count: faker.datatype.number()
          },
          {
            key: "60-64",
            doc_count: faker.datatype.number()
          },
          {
            key: ">84",
            doc_count: faker.datatype.number()
          },
          {
            key: "55-59",
            doc_count: faker.datatype.number()
          },
          {
            key: "50-54",
            doc_count: faker.datatype.number()
          },
          {
            key: "45-49",
            doc_count: faker.datatype.number()
          },
          {
            key: "35-39",
            doc_count: faker.datatype.number()
          },
          {
            key: "40-44",
            doc_count: faker.datatype.number()
          }
        ]
      }
    }
  };
}

describe("PatientDiversityStatsService", () => {
  it("should compute stats using patient claims filters when present", async () => {
    const rulesParserService = createMockInstance(RulesParserService);
    const elasticSearchService = createMockInstance(ElasticSearchService);

    const patientDiversityStatsService = new PatientDiversityStatsService(
      rulesParserService,
      elasticSearchService
    );

    const routingId = faker.datatype.string();
    const indexName = faker.datatype.string();

    rulesParserService.parseRulesToEsQueries.mockReturnValue([
      {
        bool: {
          must: [
            {
              bool: {
                must: [
                  {
                    terms: {
                      "patientClaims.diagnosisIcdCode": ["C50919"]
                    }
                  }
                ]
              }
            }
          ]
        }
      }
    ]);

    const elasticResponse = generateMockDiversityAggregation();
    elasticSearchService.query.mockResolvedValue(elasticResponse);
    const stats = await patientDiversityStatsService.getPatientDiversityStats(
      {
        query: faker.datatype.string(),
        patientClaimsFilterV2: {
          inclusionCriteria: {
            type: RuleTypeEnum.RULE_GROUP,
            ruleGroup: {
              combinator: RuleCombinatorEnum.ALL,
              rules: [
                {
                  type: RuleTypeEnum.RULE,
                  rule: {
                    field: RuleFieldEnum.DIAGNOSES_CODE,
                    value: ["C50919"],
                    operator: RuleOperatorEnum.EQUAL
                  }
                }
              ]
            }
          }
        }
      },
      routingId,
      indexName,
      undefined
    );

    expect(elasticSearchService.query).toHaveBeenCalledWith({
      index: indexName,
      size: 0,
      query: {
        bool: {
          filter: [
            {
              term: {
                _routing: routingId
              }
            },
            {
              bool: {
                must: [
                  {
                    bool: {
                      must: [
                        {
                          terms: {
                            "patientClaims.diagnosisIcdCode": ["C50919"]
                          }
                        }
                      ]
                    }
                  }
                ]
              }
            }
          ]
        }
      },
      aggs: expect.anything()
    });

    keys(stats).forEach((statKey) => {
      const buckets = (
        elasticResponse!.aggregations![
          statKey
        ] as AggregationsTermsAggregateBase<DocCountBucket>
      ).buckets as DocCountBucket[];

      const total = buckets.reduce((acc, bucket) => acc + bucket.doc_count, 0);

      const expectedStats: DiversityStats[] = buckets
        .map((bucket) => ({
          value: bucket.key,
          count: bucket.doc_count,
          percentage: bucket.doc_count / total
        }))
        .filter(
          (stats) => stats.value !== "Other" && stats.value !== "Not Disclosed"
        );

      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      expect((stats as any)[statKey]).toEqual(expectedStats);
    });
  });

  it("should compute stats using query when present and patient claims is missing", async () => {
    const rulesParserService = createMockInstance(RulesParserService);
    const elasticSearchService = createMockInstance(ElasticSearchService);

    const patientDiversityStatsService = new PatientDiversityStatsService(
      rulesParserService,
      elasticSearchService
    );

    const routingId = faker.datatype.string();
    const indexName = faker.datatype.string();

    const elasticResponse = generateMockDiversityAggregation();
    elasticSearchService.query.mockResolvedValue(elasticResponse);
    const query = faker.datatype.string();
    const stats = await patientDiversityStatsService.getPatientDiversityStats(
      {
        query
      },
      routingId,
      indexName,
      undefined
    );

    expect(elasticSearchService.query).toHaveBeenCalledWith({
      index: indexName,
      size: 0,
      query: {
        bool: {
          filter: [
            {
              term: {
                _routing: routingId
              }
            },
            {
              term: {
                "patientClaims.diagnosisIndications": query.trim().toLowerCase()
              }
            }
          ]
        }
      },
      aggs: expect.anything()
    });

    keys(stats).forEach((statKey) => {
      const buckets = (
        elasticResponse!.aggregations![
          statKey
        ] as AggregationsTermsAggregateBase<DocCountBucket>
      ).buckets as DocCountBucket[];

      const total = buckets.reduce((acc, bucket) => acc + bucket.doc_count, 0);

      const expectedStats: DiversityStats[] = buckets
        .map((bucket) => ({
          value: bucket.key,
          count: bucket.doc_count,
          percentage: bucket.doc_count / total
        }))
        .filter(
          (stats) => stats.value !== "Other" && stats.value !== "Not Disclosed"
        );

      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      expect((stats as any)[statKey]).toEqual(expectedStats);
    });
  });

  it("should split asian-pacific distribution if ratios are present", async () => {
    const rulesParserService = createMockInstance(RulesParserService);
    const elasticSearchService = createMockInstance(ElasticSearchService);

    const patientDiversityStatsService = new PatientDiversityStatsService(
      rulesParserService,
      elasticSearchService
    );

    const routingId = faker.datatype.string();
    const indexName = faker.datatype.string();

    const elasticResponse = generateMockDiversityAggregation();
    elasticSearchService.query.mockResolvedValue(elasticResponse);
    const query = faker.datatype.string();
    const stats = await patientDiversityStatsService.getPatientDiversityStats(
      {
        query
      },
      routingId,
      indexName,
      {
        asian: 0.1,
        pacificIslander: 0.3,
        asianPacificIslander: 0.4
      }
    );

    const raceStats = stats["race"];

    const asianPacificIslanderStat = raceStats.find(
      (raceStat) => raceStat.value === "Asian Pacific Islander"
    );
    const asianStat = raceStats.find((raceStat) => raceStat.value === "Asian");
    const pacificIslanderStat = raceStats.find(
      (raceStat) => raceStat.value === "Pacific Islander"
    );

    expect(asianStat?.count).toBeCloseTo(
      ((asianPacificIslanderStat?.count ?? 0) * 0.1) / 0.4
    );
    expect(asianStat?.percentage).toBeCloseTo(
      ((asianPacificIslanderStat?.percentage ?? 0) * 0.1) / 0.4
    );

    expect(pacificIslanderStat?.count).toBeCloseTo(
      ((asianPacificIslanderStat?.count ?? 0) * 0.3) / 0.4
    );
    expect(pacificIslanderStat?.percentage).toBeCloseTo(
      ((asianPacificIslanderStat?.percentage ?? 0) * 0.3) / 0.4
    );
  });
});
