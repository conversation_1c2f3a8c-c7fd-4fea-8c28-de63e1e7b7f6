import { Service } from "typedi";
import { createLogger } from "../lib/Logger";
import {
  BaseDiversityStatsInput,
  DiversityStats,
  PatientDiversityStatsResponse,
  PatientsDiversityRaceInfo
} from "@h1nyc/search-sdk";
import { RulesParserService } from "./RulesParserService";
import {
  AggregationsTermsAggregateBase,
  QueryDslQueryContainer,
  SearchRequest
} from "@elastic/elasticsearch/lib/api/types";
import { ElasticSearchService } from "./ElasticSearchService";
import { DocCountBucket } from "./KeywordAutocompleteResponseAdapterService";
import {
  ASIAN_RACE_CATEGORY,
  PACIFIC_ISLANDER_RACE_CATEGORY
} from "./KeywordSearchResponseAdapterService";

@Service()
export class PatientDiversityStatsService {
  private readonly logger = createLogger(this);

  constructor(
    private rulesParserService: RulesParserService,
    private elasticSearchService: ElasticSearchService
  ) {}

  private computeStats(buckets: DocCountBucket[]): DiversityStats[] {
    const total = buckets.reduce((acc, bucket) => acc + bucket.doc_count, 0);

    return buckets
      .map((bucket) => ({
        value: bucket.key,
        count: bucket.doc_count,
        percentage: bucket.doc_count / total
      }))
      .filter(
        (stats) => stats.value !== "Other" && stats.value !== "Not Disclosed"
      );
  }

  private breakoutAsianPacificIslanderDistribution(
    races: DiversityStats[],
    patientsDiversityRatio: PatientsDiversityRaceInfo | undefined
  ): DiversityStats[] {
    if (!patientsDiversityRatio?.asianPacificIslander) {
      return races;
    }

    const asianPacificIslander = races.find(
      (race) => race.value === "Asian Pacific Islander"
    );

    if (
      asianPacificIslander &&
      Number(patientsDiversityRatio.asianPacificIslander) &&
      patientsDiversityRatio.asian !== undefined &&
      patientsDiversityRatio.pacificIslander !== undefined
    ) {
      const percentOfAsianOverCombined =
        patientsDiversityRatio.asian /
        patientsDiversityRatio.asianPacificIslander;
      const asian: DiversityStats = {
        value: ASIAN_RACE_CATEGORY,
        count: percentOfAsianOverCombined * asianPacificIslander.count,
        percentage: percentOfAsianOverCombined * asianPacificIslander.percentage
      };

      const percentOfPacificIslanderOverCombined =
        patientsDiversityRatio.pacificIslander /
        patientsDiversityRatio.asianPacificIslander;
      const pacificIslander: DiversityStats = {
        value: PACIFIC_ISLANDER_RACE_CATEGORY,
        count:
          percentOfPacificIslanderOverCombined * asianPacificIslander.count,
        percentage:
          percentOfPacificIslanderOverCombined * asianPacificIslander.percentage
      };

      return [...races, asian, pacificIslander];
    }

    return races;
  }

  async getPatientDiversityStats(
    patientDiversityStatsInput: BaseDiversityStatsInput,
    routingId: string,
    indexName: string,
    patientsDiversityRatio: PatientsDiversityRaceInfo | undefined
  ): Promise<PatientDiversityStatsResponse> {
    const filters: QueryDslQueryContainer[] = [];

    filters.push({
      term: {
        _routing: routingId
      }
    });

    if (patientDiversityStatsInput.patientClaimsFilterV2) {
      filters.push(
        ...this.rulesParserService.parseRulesToEsQueries(
          patientDiversityStatsInput
        )
      );
    } else if (patientDiversityStatsInput.query) {
      filters.push({
        term: {
          "patientClaims.diagnosisIndications": patientDiversityStatsInput.query
            .trim()
            .toLowerCase()
        }
      });
    } else {
      // No filter is present
    }

    const searchRequest: SearchRequest = {
      index: indexName,
      size: 0,
      query: {
        bool: {
          filter: filters
        }
      },
      aggs: {
        sex: {
          terms: {
            field: "patientClaims.gender",
            size: 3
          }
        },
        age: {
          terms: {
            field: "patientClaims.age",
            size: 20
          }
        },
        income: {
          terms: {
            field: "patientClaims.income",
            size: 20
          }
        },
        education: {
          terms: {
            field: "patientClaims.education",
            size: 20
          }
        },
        race: {
          terms: {
            field: "patientClaims.diversity",
            size: 20
          }
        }
      }
    };

    this.logger.debug({ searchRequest });

    const { aggregations } = await this.elasticSearchService.query(
      searchRequest
    );

    return {
      sex: this.computeStats(
        (aggregations!.sex as AggregationsTermsAggregateBase<DocCountBucket>)
          ?.buckets as DocCountBucket[]
      ),
      race: this.breakoutAsianPacificIslanderDistribution(
        this.computeStats(
          (aggregations!.race as AggregationsTermsAggregateBase<DocCountBucket>)
            ?.buckets as DocCountBucket[]
        ),
        patientsDiversityRatio
      ),
      education: this.computeStats(
        (
          aggregations!
            .education as AggregationsTermsAggregateBase<DocCountBucket>
        )?.buckets as DocCountBucket[]
      ),
      age: this.computeStats(
        (aggregations!.age as AggregationsTermsAggregateBase<DocCountBucket>)
          ?.buckets as DocCountBucket[]
      ),
      income: this.computeStats(
        (aggregations!.income as AggregationsTermsAggregateBase<DocCountBucket>)
          ?.buckets as DocCountBucket[]
      )
    };
  }
}
