import { createMockInstance } from "../util/TestUtils";
import { ConfigService } from "./ConfigService";
import { faker } from "@faker-js/faker";
import { LocationLabelFormatterService } from "./LocationLabelFormatterService";

describe("LocationLabelFormatterService", () => {
  describe("country", () => {
    it("should expand known country codes", () => {
      const countryCode = faker.address.countryCode();
      const countryName = faker.address.country();

      const configService = createMockInstance(ConfigService);
      configService.countries.mockReturnValue({
        [countryName]: countryCode
      });
      configService.regions.mockReturnValue({});

      const locationLabelFormatterService = new LocationLabelFormatterService(
        configService
      );

      expect(locationLabelFormatterService.country(countryCode)).toEqual(
        countryName
      );
    });

    it("should not expand unknown country codes", () => {
      const countryCode = faker.address.countryCode();

      const configService = createMockInstance(ConfigService);
      configService.countries.mockReturnValue({});
      configService.regions.mockReturnValue({});

      const locationLabelFormatterService = new LocationLabelFormatterService(
        configService
      );

      expect(locationLabelFormatterService.country(countryCode)).toEqual(
        countryCode
      );
    });

    it("should expand known country names", () => {
      const countryCode = faker.address.countryCode();
      const countryName = faker.address.country();

      const configService = createMockInstance(ConfigService);
      configService.countries.mockReturnValue({
        [countryName]: countryCode
      });
      configService.regions.mockReturnValue({});

      const locationLabelFormatterService = new LocationLabelFormatterService(
        configService
      );

      expect(locationLabelFormatterService.getCountryCode(countryName)).toEqual(
        countryCode
      );
    });

    it("should not expand unknown country names", () => {
      const countryName = faker.address.country();

      const configService = createMockInstance(ConfigService);
      configService.countries.mockReturnValue({});
      configService.regions.mockReturnValue({});

      const locationLabelFormatterService = new LocationLabelFormatterService(
        configService
      );

      expect(locationLabelFormatterService.country(countryName)).toEqual(
        countryName
      );
    });
  });

  describe("region", () => {
    it("should use state name for known region abbreviations", () => {
      const countryCode = faker.address.countryCode();
      const countryName = faker.address.country();
      const regionCode = faker.address.stateAbbr();
      const regionName = faker.address.state();

      const input = `${countryCode}|${regionCode}`;

      const configService = createMockInstance(ConfigService);
      configService.countries.mockReturnValue({
        [countryName]: countryCode
      });
      configService.regions.mockReturnValue({
        [countryCode]: {
          [regionName]: regionCode
        }
      });

      const locationLabelFormatterService = new LocationLabelFormatterService(
        configService
      );

      expect(locationLabelFormatterService.region(input)).toEqual(regionName);
    });

    it("should return 2nd column when it's not known as an abbreviation", () => {
      const countryCode = faker.address.countryCode();
      const regionName = faker.address.state();

      const input = `${countryCode}|${regionName}`;

      const configService = createMockInstance(ConfigService);
      configService.countries.mockReturnValue({});
      configService.regions.mockReturnValue({
        [countryCode]: {}
      });

      const locationLabelFormatterService = new LocationLabelFormatterService(
        configService
      );

      expect(locationLabelFormatterService.region(input)).toEqual(regionName);
    });

    it("should return value when it's not pipe-delimited", () => {
      const countryCode = faker.address.countryCode();
      const regionName = faker.address.state();

      const configService = createMockInstance(ConfigService);
      configService.countries.mockReturnValue({});
      configService.regions.mockReturnValue({
        [countryCode]: {}
      });

      const locationLabelFormatterService = new LocationLabelFormatterService(
        configService
      );

      expect(locationLabelFormatterService.region(regionName)).toEqual(
        regionName
      );
    });

    it("should return countryCode|regionCode for known countryCode and regionName combination", () => {
      const countryCode = faker.address.countryCode();
      const countryName = faker.address.country();
      const regionCode = faker.address.stateAbbr();
      const regionName = faker.address.state();

      const configService = createMockInstance(ConfigService);
      configService.countries.mockReturnValue({
        [countryName]: countryCode
      });
      configService.regions.mockReturnValue({
        [countryCode]: {
          [regionName]: regionCode
        }
      });

      const locationLabelFormatterService = new LocationLabelFormatterService(
        configService
      );

      expect(
        locationLabelFormatterService.getRegionCodeAndCountryCode(
          countryCode,
          regionName
        )
      ).toEqual(`${countryCode}|${regionCode}`);
    });

    it("should return countryCode|regionName for unknown countryCode and regionName combination", () => {
      const countryCode = faker.address.countryCode();
      const regionName = faker.address.state();

      const configService = createMockInstance(ConfigService);
      configService.countries.mockReturnValue({});
      configService.regions.mockReturnValue({});

      const locationLabelFormatterService = new LocationLabelFormatterService(
        configService
      );

      expect(
        locationLabelFormatterService.getRegionCodeAndCountryCode(
          countryCode,
          regionName
        )
      ).toEqual(`${countryCode}|${regionName}`);
    });
  });

  describe("city", () => {
    it("should return value as-is when it does not contain at least 4 pipe-delimited fields or 4th field is zero-length", () => {
      const countryCode = faker.address.countryCode();
      const regionCode = faker.address.stateAbbr();
      const countyName = faker.address.county();

      const values = [
        faker.address.cityName(),
        `${countryCode}|${regionCode}|${countyName}`,
        `${countryCode}|${regionCode}|${countyName}|`
      ];

      const configService = createMockInstance(ConfigService);
      configService.regions.mockReturnValue({
        [countryCode]: {
          [faker.address.state()]: regionCode
        }
      });

      const locationLabelFormatterService = new LocationLabelFormatterService(
        configService
      );

      for (const value of values) {
        expect(locationLabelFormatterService.city(value)).toEqual(value);
      }
    });

    it("should expand non-'us' regions into to full names when code is found", () => {
      const countryCode = faker.address.countryCode().toUpperCase();
      const countryName = faker.address.country();
      const regionCode = faker.address.stateAbbr();
      const regionName = faker.address.state();
      const countyName = faker.address.county();
      const cityName = faker.address.cityName();

      const input = `${countryCode}|${regionCode}|${countyName}|${cityName}`;

      const configService = createMockInstance(ConfigService);
      configService.countries.mockReturnValue({
        [countryName]: countryCode
      });
      configService.regions.mockReturnValue({
        [countryCode]: {
          [regionName]: regionCode
        }
      });

      const locationLabelFormatterService = new LocationLabelFormatterService(
        configService
      );

      expect(locationLabelFormatterService.city(input)).toEqual(
        `${cityName}, ${countyName}, ${regionName}, ${countryName}`
      );
    });

    it("should use countryCode when expanded full name is not found", () => {
      const countryCode = faker.address.countryCode().toUpperCase();
      const regionCode = faker.address.stateAbbr();
      const regionName = faker.address.state();
      const countyName = faker.address.county();
      const cityName = faker.address.cityName();

      const input = `${countryCode}|${regionCode}|${countyName}|${cityName}`;

      const configService = createMockInstance(ConfigService);
      configService.countries.mockReturnValue({});
      configService.regions.mockReturnValue({
        [countryCode]: {
          [regionName]: regionCode
        }
      });

      const locationLabelFormatterService = new LocationLabelFormatterService(
        configService
      );

      expect(locationLabelFormatterService.city(input)).toEqual(
        `${cityName}, ${countyName}, ${regionName}, ${countryCode}`
      );
    });

    it("should not expand 'us' regions into to full names when code is found", () => {
      const countryCode = "us";
      const countryName = faker.address.country();
      const regionCode = faker.address.stateAbbr();
      const regionName = faker.address.state();
      const countyName = faker.address.county();
      const cityName = faker.address.cityName();

      const input = `${countryCode}|${regionCode}|${countyName}|${cityName}`;

      const configService = createMockInstance(ConfigService);
      configService.countries.mockReturnValue({
        [countryName]: countryCode
      });
      configService.regions.mockReturnValue({
        [countryCode]: {
          [regionName]: regionCode
        }
      });

      const locationLabelFormatterService = new LocationLabelFormatterService(
        configService
      );

      expect(locationLabelFormatterService.city(input)).toEqual(
        `${cityName}, ${countyName}, ${regionCode}, ${countryName}`
      );
    });

    it("should format as city,county,country name when region field is blank", () => {
      const countryCode = "us";
      const countryName = faker.address.country();
      const countyName = faker.address.county();
      const cityName = faker.address.cityName();

      const input = `${countryCode}||${countyName}|${cityName}`;

      const configService = createMockInstance(ConfigService);
      configService.countries.mockReturnValue({
        [countryName]: countryCode
      });
      configService.regions.mockReturnValue({
        [countryCode]: {}
      });

      const locationLabelFormatterService = new LocationLabelFormatterService(
        configService
      );

      expect(locationLabelFormatterService.city(input)).toEqual(
        `${cityName}, ${countyName}, ${countryName}`
      );
    });

    it("should format as city,county,region name when country field is blank", () => {
      const countryCode = faker.address.countryCode().toUpperCase();
      const countryName = faker.address.country();
      const regionCode = faker.address.stateAbbr();
      const regionName = faker.address.state();
      const countyName = faker.address.county();
      const cityName = faker.address.cityName();

      const input = `|${regionCode}|${countyName}|${cityName}`;

      const configService = createMockInstance(ConfigService);
      configService.countries.mockReturnValue({
        [countryName]: countryCode
      });
      configService.regions.mockReturnValue({
        [countryCode]: {
          [regionName]: regionCode
        }
      });

      const locationLabelFormatterService = new LocationLabelFormatterService(
        configService
      );

      expect(locationLabelFormatterService.city(input)).toEqual(
        `${cityName}, ${countyName}, ${regionCode}`
      );
    });

    it("should format as city,region,country name when county field is blank", () => {
      const countryCode = faker.address.countryCode().toUpperCase();
      const countryName = faker.address.country();
      const regionCode = faker.address.stateAbbr();
      const regionName = faker.address.state();
      const cityName = faker.address.cityName();

      const input = `${countryCode}|${regionCode}||${cityName}`;

      const configService = createMockInstance(ConfigService);
      configService.countries.mockReturnValue({
        [countryName]: countryCode
      });
      configService.regions.mockReturnValue({
        [countryCode]: {
          [regionName]: regionCode
        }
      });

      const locationLabelFormatterService = new LocationLabelFormatterService(
        configService
      );

      expect(locationLabelFormatterService.city(input)).toEqual(
        `${cityName}, ${regionName}, ${countryName}`
      );
    });

    it("should format as city,country name when region and county field is blank", () => {
      const countryCode = "us";
      const countryName = faker.address.country();
      const cityName = faker.address.cityName();

      const input = `${countryCode}|||${cityName}`;

      const configService = createMockInstance(ConfigService);
      configService.countries.mockReturnValue({
        [countryName]: countryCode
      });
      configService.regions.mockReturnValue({
        [countryCode]: {}
      });

      const locationLabelFormatterService = new LocationLabelFormatterService(
        configService
      );

      expect(locationLabelFormatterService.city(input)).toEqual(
        `${cityName}, ${countryName}`
      );
    });

    it("should format as city,region name when country and county field is blank", () => {
      const countryCode = faker.address.countryCode().toUpperCase();
      const countryName = faker.address.country();
      const regionCode = faker.address.stateAbbr();
      const regionName = faker.address.state();
      const cityName = faker.address.cityName();

      const input = `|${regionCode}||${cityName}`;

      const configService = createMockInstance(ConfigService);
      configService.countries.mockReturnValue({
        [countryName]: countryCode
      });
      configService.regions.mockReturnValue({
        [countryCode]: {
          [regionName]: regionCode
        }
      });

      const locationLabelFormatterService = new LocationLabelFormatterService(
        configService
      );

      expect(locationLabelFormatterService.city(input)).toEqual(
        `${cityName}, ${regionCode}`
      );
    });
  });

  describe("cityWithoutCounty", () => {
    it("should return value as-is when it does not contain at least 3 pipe-delimited fields or 3rd field is zero-length", () => {
      const countryCode = faker.address.countryCode();
      const regionCode = faker.address.stateAbbr();

      const values = [
        faker.address.cityName(),
        `${countryCode}|${regionCode}`,
        `${countryCode}|${regionCode}|`
      ];

      const configService = createMockInstance(ConfigService);
      configService.countries.mockReturnValue({});
      configService.regions.mockReturnValue({});

      const locationLabelFormatterService = new LocationLabelFormatterService(
        configService
      );

      for (const value of values) {
        expect(locationLabelFormatterService.cityWithoutCounty(value)).toEqual(
          value
        );
      }
    });

    it("should format as city,region name when country field is blank", () => {
      const countryCode = faker.address.countryCode().toUpperCase();
      const countryName = faker.address.country();
      const regionCode = faker.address.stateAbbr();
      const regionName = faker.address.state();
      const cityName = faker.address.cityName();

      const input = `|${regionCode}|${cityName}`;

      const configService = createMockInstance(ConfigService);
      configService.countries.mockReturnValue({
        [countryName]: countryCode
      });
      configService.regions.mockReturnValue({
        [countryCode]: {
          [regionName]: regionCode
        }
      });

      const locationLabelFormatterService = new LocationLabelFormatterService(
        configService
      );

      expect(locationLabelFormatterService.cityWithoutCounty(input)).toEqual(
        `${cityName}, ${regionCode}`
      );
    });

    it("should format as city,country name when region field is blank", () => {
      const countryCode = "us";
      const countryName = faker.address.country();
      const cityName = faker.address.cityName();

      const input = `${countryCode}||${cityName}`;

      const configService = createMockInstance(ConfigService);
      configService.countries.mockReturnValue({
        [countryName]: countryCode
      });
      configService.regions.mockReturnValue({
        [countryCode]: {}
      });

      const locationLabelFormatterService = new LocationLabelFormatterService(
        configService
      );

      expect(locationLabelFormatterService.cityWithoutCounty(input)).toEqual(
        `${cityName}, ${countryName}`
      );
    });

    it("should expand non-'us' regions into to full names when code is found", () => {
      const countryCode = faker.address.countryCode().toUpperCase();
      const countryName = faker.address.country();
      const regionCode = faker.address.stateAbbr();
      const regionName = faker.address.state();
      const cityName = faker.address.cityName();

      const input = `${countryCode}|${regionCode}|${cityName}`;

      const configService = createMockInstance(ConfigService);
      configService.countries.mockReturnValue({
        [countryName]: countryCode
      });
      configService.regions.mockReturnValue({
        [countryCode]: {
          [regionName]: regionCode
        }
      });

      const locationLabelFormatterService = new LocationLabelFormatterService(
        configService
      );

      expect(locationLabelFormatterService.cityWithoutCounty(input)).toEqual(
        `${cityName}, ${regionName}, ${countryName}`
      );
    });

    it("should use countryCode when expanded full name is not found", () => {
      const countryCode = faker.address.countryCode().toUpperCase();
      const regionCode = faker.address.stateAbbr();
      const cityName = faker.address.cityName();

      const input = `${countryCode}|${regionCode}|${cityName}`;

      const configService = createMockInstance(ConfigService);
      configService.countries.mockReturnValue({});
      configService.regions.mockReturnValue({
        [countryCode]: {}
      });

      const locationLabelFormatterService = new LocationLabelFormatterService(
        configService
      );

      expect(locationLabelFormatterService.cityWithoutCounty(input)).toEqual(
        `${cityName}, ${regionCode}, ${countryCode}`
      );
    });

    it("should not expand 'us' regions into to full names when code is found", () => {
      const countryCode = "us";
      const countryName = faker.address.country();
      const regionCode = faker.address.stateAbbr();
      const regionName = faker.address.state();
      const cityName = faker.address.cityName();

      const input = `${countryCode}|${regionCode}|${cityName}`;

      const configService = createMockInstance(ConfigService);
      configService.countries.mockReturnValue({
        [countryName]: countryCode
      });
      configService.regions.mockReturnValue({
        [countryCode]: {
          [regionName]: regionCode
        }
      });

      const locationLabelFormatterService = new LocationLabelFormatterService(
        configService
      );

      expect(locationLabelFormatterService.cityWithoutCounty(input)).toEqual(
        `${cityName}, ${regionCode}, ${countryName}`
      );
    });
  });

  describe("postal code", () => {
    it("should return value as-is when it does not contain at least 6 pipe-delimited fields or 6th field is zero-length", () => {
      const countryCode = faker.address.countryCode();
      const regionCode = faker.address.stateAbbr();
      const countyName = faker.address.county();
      const cityName = faker.address.cityName();
      const districtName = faker.word.noun();

      const values = [
        faker.address.zipCode(),
        `${countryCode}|${regionCode}`,
        `${countryCode}|${regionCode}|${cityName}`,
        `${countryCode}|${regionCode}|${cityName}|${countyName}`,
        `${countryCode}|${regionCode}|${cityName}|${countyName}|${districtName}`,
        `${countryCode}|${regionCode}|${cityName}|${countyName}|${districtName}|`
      ];

      const configService = createMockInstance(ConfigService);
      configService.countries.mockReturnValue({});
      configService.regions.mockReturnValue({});

      const locationLabelFormatterService = new LocationLabelFormatterService(
        configService
      );

      for (const value of values) {
        expect(locationLabelFormatterService.postalCode(value)).toEqual(value);
      }
    });

    it("should return 5th pipe-delimited field when input contains at least 6 pipe-delimited fields", () => {
      const countryCode = faker.address.countryCode();
      const regionCode = faker.address.stateAbbr();
      const countyName = faker.address.county();
      const cityName = faker.address.cityName();
      const districtName = faker.word.noun();
      const postalCode1 = faker.address.zipCode();
      const postalCode2 = faker.address.zipCode();

      const values = [
        `${countryCode}|${regionCode}|${cityName}|${countyName}|${districtName}|${postalCode1}`,
        `${countryCode}|${regionCode}|${cityName}|${countyName}|${districtName}|${postalCode1}|${postalCode2}`
      ];

      const configService = createMockInstance(ConfigService);
      configService.countries.mockReturnValue({});
      configService.regions.mockReturnValue({});

      const locationLabelFormatterService = new LocationLabelFormatterService(
        configService
      );

      for (const value of values) {
        expect(locationLabelFormatterService.postalCode(value)).toEqual(
          postalCode1
        );
      }
    });
  });
});
