import { RpcResourceService, RpcService, RpcMethod } from "@h1nyc/systems-rpc";
import {
  ClinicalTrialInput,
  RPC_NAMESPACE_CLINICAL_TRIALS,
  ClinicalTrialSearchResource,
  ClinicalTrialsSearchResponse,
  ClinicalTrialDocument,
  ClinicalTrialDocumentV2,
  ClinicalTrial,
  ClinicalTrialDocumentV3,
  ClinicalTrialDocumentV4,
  ClinicalTrialDocumentWithCTMS,
  ClinicalTrialDocumentBase,
  ClinicalTrialFilterAutocompleteInput,
  KeywordFilterAggregation,
  IndicationCountsAggregation,
  IndicationsType,
  ClinicalTrialHistogramResponse,
  TrialHistogramData,
  TrialHistogramMetric,
  ClinicalTrialHistogramInput,
  ClinicalTrialCountryBreakdownInput,
  ClinicalTrialCountryBreakdownResponse
} from "@h1nyc/search-sdk";

// Temporary types until SDK is rebuilt
interface ClinicalTrialRegionBreakdownInput {
  country: string;
  filters?: any[];
  ids?: string[];
  sort: any;
  projectId?: string;
  userId?: string;
}

interface ClinicalTrialRegionBreakdownResponse {
  regions: any[];
  total: number;
}
import { createLogger, Logger } from "../lib/Logger";
import { ConfigService } from "./ConfigService";
import { Trace } from "../Tracer";
import {
  createQuery,
  createQueryForAutocomplete,
  createQueryWithCTMS,
  createQueryWithCTMSForAutocomplete,
  QueryVersion
} from "../lib/ClinicalTrials/ClinicalTrialsQueryBuilder";
import { mapResults } from "../lib/ClinicalTrials/ClinicalTrialsMapper";
import {
  HitSource,
  HitsV3,
  HitsV4,
  RootObjectV1,
  RootObjectV2,
  RootObjectV3
} from "../lib/ClinicalTrials/types";
import { ElasticTrialSearchService } from "./ElasticTrialSearchService";
import { ElasticTrialSearchServiceV2 } from "./ElasticTrialSearchServiceV2";
import { ElasticTrialSearchServiceV3 } from "./ElasticTrialSearchServiceV3";
import { ElasticCommonSearchService } from "./ElasticCommonSearchService";
import { mapResultsV2 } from "../lib/ClinicalTrials/ClinicalTrialsMapperV2";
import { detailedDiff } from "deep-object-diff";
import * as _ from "lodash";
import {
  autocompleteFilterMap,
  trialsAggregationsV3,
  histogramAggregations,
  createTrialCountryBreakdownAggregation,
  createTrialRegionBreakdownAggregation
} from "../lib/ClinicalTrials/trials-index";
import { mapResultsV3 } from "../lib/ClinicalTrials/ClinicalTrialsMapperV3";
import { ElasticTrialSearchServiceV4 } from "./ElasticTrialSearchServiceV4";
import { mapResultsWithCTMS } from "../lib/ClinicalTrials/ClinicalTrialsMapperWithCTMS";
import { Service } from "typedi";
import { FeatureFlagsService } from "@h1nyc/systems-feature-flags";
import { estypes } from "@elastic/elasticsearch";
import { mapResultsV4 } from "../lib/ClinicalTrials/ClinicalTrialsMapperV4";
import { Entity, UserEntities } from "@h1nyc/account-sdk";
import { EntityTagResourceClient } from "@h1nyc/account-sdk";
import { EntityType } from "@h1nyc/account-user-entities";
import {
  AggregationsAggregationContainer,
  AggregationsTermsAggregateBase,
  QueryDslQueryContainer,
  SearchRequest
} from "@elastic/elasticsearch/lib/api/types";
import { buildTermsQuery } from "../util/QueryBuildingUtils";
import { QueryParserService } from "./QueryParserService";
import { ParsedQueryTreeToElasticsearchQueriesService } from "./ParsedQueryTreeToElasticsearchQueries";
import { QueryUnderstandingServiceClient } from "./QueryUnderstandingServiceClient";

const TRIALS_SEARCH_FEATURE_FLAG = "use-trials-gtp-search";
export enum TrialSearchFlagVariation {
  V1 = 1,
  V1_WITH_V2_SHADOW = 2,
  V2_WITH_V1_SHADOW = 3,
  V2 = 4,
  V3 = 5, // trials v3 index (includes deprecated trial documents)
  V4 = 6, // trials v4 index (includes EUDRA)
  CTMS = 7
}
const MATCH_ALL = {
  match_all: {}
};

type DocCountBucket = {
  key: string;
  doc_count: number;
};

type NonNestedFilteredMatchingAggregations = Record<
  "filtered_matching",
  Record<"matching", AggregationsTermsAggregateBase<DocCountBucket>>
>;
type NestedFilteredMatchingAggregations = Record<
  "nested",
  Record<
    "filtered_matching",
    Record<"matching", AggregationsTermsAggregateBase<DocCountBucket>>
  >
>;
type SupportedAggregations =
  | NonNestedFilteredMatchingAggregations
  | NestedFilteredMatchingAggregations;

const REGEXP_SPECIAL_CHARACTERS = new Set([
  ".",
  "+",
  "*",
  "?",
  "^",
  "$",
  "(",
  ")",
  "[",
  "]",
  "{",
  "}",
  "|",
  "\\"
]);
const REGEX_ONE_OR_TWO_CHARACTERS = ".{1,2}";

@Service()
@RpcService()
export class ClinicalTrialSearchResourceService
  extends RpcResourceService
  implements ClinicalTrialSearchResource
{
  private readonly logger = createLogger(this);
  private readonly config;

  constructor(
    config: ConfigService,
    private elasticService: ElasticTrialSearchService,
    private elasticTrialSearchServiceV2: ElasticTrialSearchServiceV2,
    private elasticTrialSearchServiceV3: ElasticTrialSearchServiceV3,
    private elasticTrialSearchServiceV4: ElasticTrialSearchServiceV4,
    private featureFlagsService: FeatureFlagsService,
    private entityResourceService: EntityTagResourceClient,
    private queryParserService: QueryParserService,
    private parsedQueryTreeToElasticsearchQueriesService: ParsedQueryTreeToElasticsearchQueriesService,
    private queryUnderstandingServiceClient: QueryUnderstandingServiceClient
  ) {
    super(
      config.searchRpcSigningKey,
      RPC_NAMESPACE_CLINICAL_TRIALS,
      config.searchRedisOptions
    );
    this.config = config;
  }

  @RpcMethod()
  async isReady(): Promise<boolean> {
    return true;
  }

  @RpcMethod()
  @Trace("h1-search.clinicialtrial.searchtrials")
  async searchTrials(
    input: ClinicalTrialInput
  ): Promise<ClinicalTrialsSearchResponse> {
    this.logger.debug(JSON.stringify(input, null, 2), "Clinical Trials Input");

    try {
      return this.getResults(input);
    } catch (err) {
      this.logger.error(
        JSON.stringify(err, null, 2),
        "Clinical Trial Search Query Failed Error"
      );
    }

    // no query or results, return empty result set
    return {
      from: 0,
      pageSize: 0,
      total: 0,
      results: [],
      resultIds: []
    };
  }

  @RpcMethod()
  @Trace("h1-search.clinicialtrial.searchBulktrials")
  async searchBulkTrials(input: ClinicalTrialInput): Promise<Array<Entity>> {
    this.logger.debug(JSON.stringify(input, null, 2), "Clinical Trials Input");

    try {
      const searchQuery = await createQueryWithCTMS(
        input,
        trialsAggregationsV3,
        true,
        this.queryParserService,
        this.parsedQueryTreeToElasticsearchQueriesService,
        this.queryUnderstandingServiceClient
      );

      const result = await this.getResultsWithCTMS(searchQuery);
      return result.hits.hits.map((hit) => {
        return {
          entityType: UserEntities.EntityType.TRIAL,
          entityId: hit._source?.h1_clinical_trial_id
        };
      });
    } catch (err) {
      this.logger.error(
        JSON.stringify(err, null, 2),
        "Clinical Trial Bulk Search Query Failed Error"
      );
    }

    // no query or results, return empty result array
    return [];
  }

  @RpcMethod()
  @Trace("h1-search.clinicialtrial.trialsAutocomplete")
  async autocompleteForTrialFilters(
    input: ClinicalTrialFilterAutocompleteInput
  ): Promise<Array<KeywordFilterAggregation>> {
    this.logger.debug(
      JSON.stringify(input, null, 2),
      "Clinical Trials Autocomplete Input"
    );

    try {
      return this.getResultsForAutocomplete(input);
    } catch (err) {
      this.logger.error(
        JSON.stringify(err, null, 2),
        "Clinical Trial Autocomplete Query Failed Error"
      );
    }

    // no query or results, return empty result set
    return [];
  }

  async getResults(
    input: ClinicalTrialInput
  ): Promise<ClinicalTrialsSearchResponse> {
    const flag = await this.getFeatureFlag(input);
    const queryV1 = await createQuery(input, QueryVersion.V1);
    const queryV2 = await createQuery(input, QueryVersion.V2);
    const queryV3 = await createQuery(
      input,
      QueryVersion.V3,
      trialsAggregationsV3,
      undefined,
      this.queryParserService,
      this.parsedQueryTreeToElasticsearchQueriesService,
      this.queryUnderstandingServiceClient
    );
    const queryV4 = await createQuery(
      input,
      QueryVersion.V4,
      trialsAggregationsV3,
      undefined,
      this.queryParserService,
      this.parsedQueryTreeToElasticsearchQueriesService,
      this.queryUnderstandingServiceClient
    );

    switch (flag) {
      case TrialSearchFlagVariation.V1: {
        return await this.getResultsV1(input, queryV1);
      }
      case TrialSearchFlagVariation.V1_WITH_V2_SHADOW: {
        const resultsV1 = await this.getResultsV1(input, queryV1);
        this.getV2AndLogDiff(input, queryV2, resultsV1);
        return resultsV1;
      }
      case TrialSearchFlagVariation.V2_WITH_V1_SHADOW: {
        const resultsV2 = await this.getResultsV2(input, queryV2);
        this.getV1AndLogDiff(input, queryV1, resultsV2);
        return resultsV2;
      }
      case TrialSearchFlagVariation.V2:
        return await this.getResultsV2(input, queryV2);
      case TrialSearchFlagVariation.V3: {
        return await this.getResultsV3(input, queryV3);
      }
      case TrialSearchFlagVariation.V4: {
        const result = await this.getResultsV4(queryV4);

        const mappedResult = mapResultsV4(result, input);
        this.logger.debug(mappedResult, "trial search query result");
        return mappedResult;
      }
      case TrialSearchFlagVariation.CTMS: {
        const query = await createQueryWithCTMS(
          input,
          trialsAggregationsV3,
          false,
          this.queryParserService,
          this.parsedQueryTreeToElasticsearchQueriesService,
          this.queryUnderstandingServiceClient
        );
        await this.updateTrialsSearchQueryToAddTagsFilters(input, query);

        this.logger.info({ query }, "trials CTMS search query");
        const result = await this.getResultsWithCTMS(query);
        return mapResultsWithCTMS(result, input);
      }
      default: {
        // By default return newest variation
        const result = await this.getResultsV4(queryV4);

        const mappedResult = mapResultsV4(result, input);
        this.logger.debug(mappedResult, "trial search query result");
        return mappedResult;
      }
    }
  }

  private async getFeatureFlag({
    userId,
    projectId
  }: ClinicalTrialInput): Promise<number> {
    try {
      const user = userId && projectId ? { userId, projectId } : undefined;
      return this.featureFlagsService.getFlag(
        TRIALS_SEARCH_FEATURE_FLAG,
        6,
        user
      );
    } catch (e) {
      this.logger.warn(
        `Failed to call FeatureFlagsService. Defaulting to flag 6. ${JSON.stringify(
          e
        )}`
      );
      return 6;
    }
  }

  async getResultsWithCTMS(
    query: estypes.SearchRequest
  ): Promise<
    estypes.SearchResponse<
      ClinicalTrialDocumentWithCTMS,
      Record<string, estypes.AggregationsAggregate>
    >
  > {
    const esService: ElasticCommonSearchService =
      this.elasticTrialSearchServiceV4;

    this.logger.debug(
      JSON.stringify(query, null, 2),
      "Clinical Trials Search Query"
    );

    this.logger.debug(
      `making request to index ${this.config.elasticTrialsIndexWithCTMS}`
    );
    const result = await esService.query<ClinicalTrialDocumentWithCTMS>({
      index: this.config.elasticTrialsIndexWithCTMS,
      ...query
    });

    const deprecatedReplacementTrials = await this.getSurvivorTrialsWithCTMS(
      result
    );

    if (!_.isEmpty(deprecatedReplacementTrials)) {
      result.hits = this.rebuildHitsWithCTMSWithReplacements(
        result,
        deprecatedReplacementTrials
      );
    }

    return result;
  }

  async getResultsV4(
    query: any
  ): Promise<RootObjectV3<ClinicalTrialDocumentV4>> {
    const esService: ElasticCommonSearchService =
      this.elasticTrialSearchServiceV4;

    this.logger.debug(
      JSON.stringify(query, null, 2),
      "Clinical Trials Search Query"
    );

    this.logger.debug(`making request to index ${esService.index}`);
    const result = await esService.getSignedElasticRequest<
      RootObjectV3<ClinicalTrialDocumentV4>
    >(query);

    const deprecatedReplacementTrials = await this.getSurvivorTrials(result);

    if (!_.isEmpty(deprecatedReplacementTrials)) {
      result.hits = this.rebuildHitsWithReplacements(
        result,
        deprecatedReplacementTrials
      ) as HitsV4;
    }

    return result;
  }

  async getResultsV3(
    input: ClinicalTrialInput,
    query: any
  ): Promise<ClinicalTrialsSearchResponse> {
    const esService: ElasticCommonSearchService =
      this.elasticTrialSearchServiceV3;

    this.logger.debug(
      JSON.stringify(query, null, 2),
      "Clinical Trials Search Query"
    );

    this.logger.debug(`making request to index ${esService.index}`);
    const result = await esService.getSignedElasticRequest<
      RootObjectV3<ClinicalTrialDocumentV3>
    >(query);

    const deprecatedReplacementTrials = await this.getSurvivorTrials(result);

    if (!_.isEmpty(deprecatedReplacementTrials)) {
      result.hits = this.rebuildHitsWithReplacements(
        result,
        deprecatedReplacementTrials
      ) as HitsV3;
    }

    return mapResultsV3(result, input);
  }

  async getResultsV2(
    input: ClinicalTrialInput,
    query: any
  ): Promise<ClinicalTrialsSearchResponse> {
    const esService: ElasticCommonSearchService =
      this.elasticTrialSearchServiceV2;

    this.logger.debug(
      JSON.stringify(query, null, 2),
      "Clinical Trials Search Query"
    );

    this.logger.debug(`making request to index ${esService.index}`);
    const result = await esService.getSignedElasticRequest<RootObjectV2>(query);
    const mappedResults = mapResultsV2(result, input);
    return mappedResults;
  }

  async getResultsV1(
    input: ClinicalTrialInput,
    query: any
  ): Promise<ClinicalTrialsSearchResponse> {
    const esService: ElasticCommonSearchService = this.elasticService;

    this.logger.debug(
      JSON.stringify(query, null, 2),
      "Clinical Trials Search Query"
    );

    this.logger.debug(`making request to index ${esService.index}`);
    const result = await esService.getSignedElasticRequest<RootObjectV1>(query);

    return mapResults(result, input);
  }

  private async buildTagFilterQuery(
    tags: string[],
    intersectTags: boolean
  ): Promise<QueryDslQueryContainer | null> {
    const filters: QueryDslQueryContainer[] = [];
    const entites = await this.entityResourceService.getByActiveTagIds(tags!, {
      entityType: EntityType.TRIAL
    });

    let trialIds: string[] = [];
    if (intersectTags && tags.length > 1) {
      const entityIdToTagIdMap = new Map<string, string[]>();
      entites.forEach((entity) => {
        if (entity?.entityId) {
          const tagIds = entityIdToTagIdMap.get(entity.entityId) ?? [];
          tagIds.push(entity.tagId);
          entityIdToTagIdMap.set(entity.entityId, tagIds);
        }
      });
      entityIdToTagIdMap.forEach((tagIds: string[], entityId: string) => {
        if (tagIds.length === tags.length) {
          trialIds.push(entityId);
        }
      });
    } else {
      trialIds = _.compact(entites.map((obj) => obj.entityId));
    }
    this.logger.info(
      { tags, entitesLength: entites.length, trialsIdLength: trialIds.length },
      "Trials tag filter info"
    );

    if (intersectTags && trialIds.length === 0) {
      trialIds.push("no-match-trial-id");
    }

    if (trialIds.length > 0) {
      filters.push(buildTermsQuery("h1_clinical_trial_id", trialIds));

      return {
        bool: {
          should: filters,
          minimum_should_match: 1
        }
      };
    } else {
      // no results found tag might be deleted
      return null;
    }
  }

  private async updateTrialsSearchQueryToAddTagsFilters(
    input: ClinicalTrialInput,
    searchQuery: estypes.SearchRequest
  ) {
    const tagsInclusionFilter = input?.filters?.find((val) => {
      return val.name === "TagsInclusion";
    });
    const tagsExclusionFilter = input?.filters?.find((val) => {
      return val.name === "TagsExclusion";
    });

    if (!_.isEmpty(tagsInclusionFilter || tagsExclusionFilter)) {
      const includedTags = JSON.parse(tagsInclusionFilter?.value ?? "[]");
      const excludedTags = JSON.parse(tagsExclusionFilter?.value ?? "[]");
      const intersectTagsFilter = input?.filters?.find(
        (val) => val.name === "TagsIntersection"
      )?.value;

      const intersectTags =
        intersectTagsFilter &&
        typeof intersectTagsFilter === "string" &&
        JSON.parse(intersectTagsFilter) === "AND"
          ? true
          : false;

      const tagFilterQuery = await this.buildFiltersForTags(
        includedTags,
        excludedTags,
        intersectTags
      );
      if (
        searchQuery.query!.bool &&
        Array.isArray(searchQuery.query!.bool!.filter!)
      ) {
        searchQuery.query!.bool!.filter!.push(tagFilterQuery);
      } else {
        searchQuery.query! = {
          bool: {
            filter: [tagFilterQuery]
          }
        };
      }
    }
  }

  private async buildFiltersForTags(
    inclusionTags: string[],
    exclusionTags: string[],
    intersectTags: boolean
  ): Promise<QueryDslQueryContainer> {
    let inclusionTagsFilterQuery: QueryDslQueryContainer | null = null;
    if (inclusionTags?.length > 0) {
      inclusionTagsFilterQuery = await this.buildTagFilterQuery(
        inclusionTags,
        intersectTags
      );
    }

    let exclusionTagsFilterQuery: QueryDslQueryContainer | null = null;
    if (exclusionTags?.length > 0) {
      exclusionTagsFilterQuery = await this.buildTagFilterQuery(
        exclusionTags,
        false
      );
    }

    const filterQuery: QueryDslQueryContainer = {
      bool: {
        filter: inclusionTagsFilterQuery ?? undefined,
        must_not: exclusionTagsFilterQuery ?? undefined
      }
    };
    return filterQuery;
  }

  @RpcMethod()
  @Trace("h1-search.clinicialtrial.trial")
  async trialDocument(trialId: string): Promise<ClinicalTrialDocument | null> {
    this.logger.debug(
      `making request to index ${this.elasticService.index} with trialId ${trialId}`
    );

    const result =
      await this.elasticService.getSignedElasticRequest<RootObjectV1>({
        query: { ids: { values: [trialId] } }
      });

    this.logger.debug(
      `trialDocument response: ${JSON.stringify(result, null, 2)}`
    );
    const hits = result.hits.hits;
    return hits?.length ? hits[0]._source : null;
  }

  @RpcMethod()
  @Trace("h1-search.clinicialtrial.trialv2")
  async trialDocumentV2(
    trialId: string
  ): Promise<ClinicalTrialDocumentV2 | null> {
    const result =
      await this.elasticTrialSearchServiceV2.getSignedElasticRequest<RootObjectV2>(
        { query: { match: { "identifiers.external_uuid": trialId } } }
      );

    const hits = result.hits.hits;
    return hits?.length ? hits[0]._source : null;
  }

  @RpcMethod()
  @Trace("h1-search.clinicialtrial.trialv3")
  async trialDocumentV3(
    trialId: string
  ): Promise<ClinicalTrialDocumentV3 | null> {
    const result =
      await this.elasticTrialSearchServiceV3.getSignedElasticRequest<
        RootObjectV3<ClinicalTrialDocumentV3>
      >({
        query: { match: { "identifiers.external_uuid": trialId } }
      });

    const hits = result.hits.hits;
    return hits?.length ? hits[0]._source : null;
  }

  @RpcMethod()
  @Trace("h1-search.clinicialtrial.trialv4")
  async trialDocumentV4(
    trialId: string
  ): Promise<ClinicalTrialDocumentV4 | null> {
    const result =
      await this.elasticTrialSearchServiceV4.getSignedElasticRequest<
        RootObjectV3<ClinicalTrialDocumentV4>
      >({
        query: { match: { "identifiers.external_uuid": trialId } }
      });

    const hits = result.hits.hits;
    return hits?.length ? hits[0]._source : null;
  }

  @RpcMethod()
  @Trace("h1-search.clinicaltrial.trial-with-ctms")
  async trialDocumentWithCTMS(
    trialId: string
  ): Promise<ClinicalTrialDocumentWithCTMS | null> {
    this.logger.debug(
      `retrieving document with id ${trialId} from index ${this.config.elasticTrialsIndexWithCTMS}`
    );

    const resultUsingH1Id =
      await this.elasticTrialSearchServiceV4.query<ClinicalTrialDocumentWithCTMS>(
        {
          index: this.config.elasticTrialsIndexWithCTMS,
          query: {
            bool: {
              should: [
                {
                  term: {
                    _id: trialId
                  }
                },
                {
                  term: {
                    h1dn_clinical_trial_id: trialId
                  }
                },
                {
                  term: {
                    ["identifiers.external_uuid.keyword"]: trialId
                  }
                }
              ]
            }
          }
        }
      );

    this.logger.debug(
      `trialDocument response: ${JSON.stringify(resultUsingH1Id, null, 2)}`
    );

    return this.getTrialFromElasticsearchResponse(resultUsingH1Id);
  }

  @RpcMethod()
  @Trace("h1-search.keyword.search.trialCountForIndications")
  async trialCountForIndications(
    searchInput: Readonly<ClinicalTrialInput>,
    indications: string[],
    type: IndicationsType
  ): Promise<_.Dictionary<number>> {
    const esService: ElasticCommonSearchService =
      this.elasticTrialSearchServiceV4;

    // Build out query with filters identical to what we have on the main search
    // query
    const query = await createQueryWithCTMS(
      { ...searchInput, limit: 0, offset: 0 },
      {},
      false,
      this.queryParserService,
      this.parsedQueryTreeToElasticsearchQueriesService,
      this.queryUnderstandingServiceClient
    );

    // Append indication filters and aggregations to correctly obtain bucket
    // counts of indications
    this.buildCountForIndicationsAggregation(query, indications, type);

    this.logger.debug(
      JSON.stringify(query, null, 2),
      "Clinical Trials Count for Indications Query"
    );

    this.logger.debug(
      {
        indications
      },
      `making request to index ${this.config.elasticTrialsIndexWithCTMS}`
    );

    const result = await esService.query<
      ClinicalTrialDocumentWithCTMS,
      IndicationCountsAggregation
    >({
      index: this.config.elasticTrialsIndexWithCTMS,
      ...query
    });

    // loop over the buckets and format them
    const resp: _.Dictionary<number> = {};
    result?.aggregations?.indication_counts?.buckets?.forEach(
      (agg) => (resp[agg.key] = agg.doc_count)
    );

    return resp;
  }

  buildCountForIndicationsAggregation(
    request: SearchRequest,
    indications: string[],
    type: IndicationsType
  ) {
    // Given the type of indications being requested, we need to change the
    // field we use to collect counts
    let field = "indications";
    if (type === IndicationsType.INCLUSION) {
      field = "indication_inclusions";
    }
    if (type === IndicationsType.EXCLUSION) {
      field = "indication_exclusions";
    }

    // Build aggs object if it doesn't exist on the passed query
    if (!request.aggs) {
      request.aggs = {};
    }
    if (!request.aggs.indication_counts) {
      request.aggs.indication_counts = { terms: {} };
    }

    request.aggs.indication_counts.terms = {
      field,
      size: 100,
      include: indications
    };
  }

  private getTrialFromElasticsearchResponse(
    response: estypes.SearchResponse<ClinicalTrialDocumentWithCTMS>
  ): ClinicalTrialDocumentWithCTMS | null {
    if (response.hits.hits.length) {
      const hits = response.hits.hits;
      return hits[0]._source ?? null;
    }

    return null;
  }

  async getSurvivorTrialsWithCTMS(
    root: estypes.SearchResponse<ClinicalTrialDocumentWithCTMS>
  ): Promise<ClinicalTrialDocumentBase[]> {
    const { hits } = root;
    const deprecatedH1TrialIds = hits.hits
      .filter(
        (source) =>
          source._source?.is_deprecated === true &&
          source._source?.survivor_h1_clinical_trial_id
      )
      .map((source) => source._source!.survivor_h1_clinical_trial_id!);

    if (_.isEmpty(deprecatedH1TrialIds)) {
      return Promise.resolve([]);
    }
    const trials =
      await this.elasticTrialSearchServiceV4.query<ClinicalTrialDocumentBase>({
        index: this.config.elasticTrialsIndexWithCTMS,
        query: { ids: { values: deprecatedH1TrialIds } }
      });
    return trials.hits.hits.map((source) => source._source!);
  }

  async getSurvivorTrials(
    root: RootObjectV3<ClinicalTrialDocumentBase>
  ): Promise<ClinicalTrialDocumentBase[]> {
    const { hits } = root;
    const deprecatedH1TrialIds = hits.hits
      .filter((source) => source._source.is_deprecated === true)
      .map((source) => source._source.survivor_h1_clinical_trial_id);
    if (_.isEmpty(deprecatedH1TrialIds)) {
      return Promise.resolve([]);
    }
    const trials: RootObjectV3<ClinicalTrialDocumentBase> =
      await this.elasticTrialSearchServiceV4.getSignedElasticRequest<
        RootObjectV3<ClinicalTrialDocumentBase>
      >({ query: { ids: { values: deprecatedH1TrialIds as string[] } } });
    return trials.hits.hits.map((source) => source._source);
  }

  async getV2AndLogDiff(
    input: ClinicalTrialInput,
    query: any,
    resultsV1: ClinicalTrialsSearchResponse
  ): Promise<void> {
    let resultsV2: ClinicalTrialsSearchResponse;

    try {
      resultsV2 = await this.getResultsV2(input, query);
      ClinicalTrialSearchResourceService.logDiff(
        resultsV1,
        resultsV2,
        this.logger
      );
    } catch (e) {
      this.logger.warn(`Failed to get TrialSearch v2 results: ${e}`);
    }
  }

  async getV1AndLogDiff(
    input: ClinicalTrialInput,
    query: any,
    resultsV2: ClinicalTrialsSearchResponse
  ): Promise<void> {
    let resultsV1: ClinicalTrialsSearchResponse;

    try {
      resultsV1 = await this.getResultsV1(input, query);
      ClinicalTrialSearchResourceService.logDiff(
        resultsV1,
        resultsV2,
        this.logger
      );
    } catch (e) {
      this.logger.warn(`Failed to get TrialSearch v1 results: ${e}`);
    }
  }

  // Logs diff for one random common trial in the responses.
  // This uses deep-object-diff library to identify which keys
  // in the objects have deleted, added, or updated values.
  static logDiff(
    v1Results: ClinicalTrialsSearchResponse,
    v2Results: ClinicalTrialsSearchResponse,
    logger: Logger
  ) {
    try {
      const chosenNCTId =
        ClinicalTrialSearchResourceService.getRandomCommonTrialId(
          v1Results,
          v2Results
        );
      const v1ResultsFiltered =
        _.find(v1Results.results, (r) => r.nctId === chosenNCTId) || {};
      const v2ResultsFiltered =
        _.find(v2Results.results, (r) => r.nctId === chosenNCTId) || {};
      const diff = detailedDiff(v1ResultsFiltered, v2ResultsFiltered);

      // @ts-ignore
      const addedFields = Object.keys(diff.added);
      // @ts-ignore
      const deletedFields = Object.keys(diff.deleted);
      // @ts-ignore
      const updatedFields = Object.keys(diff.updated);

      logger.info(`nct id ${chosenNCTId} added fields: ${addedFields}`);
      logger.info(`nct id ${chosenNCTId} deleted fields: ${deletedFields}`);
      logger.info(`nct id ${chosenNCTId} updated fields: ${updatedFields}`);

      logger.debug(`diff for ${chosenNCTId}: ${JSON.stringify(diff, null, 2)}`);

      logger.debug(
        `v1 response for ${chosenNCTId}: ${JSON.stringify(
          v1ResultsFiltered,
          null,
          2
        )}`
      );
      logger.debug(
        `v2 response for ${chosenNCTId}: ${JSON.stringify(
          v2ResultsFiltered,
          null,
          2
        )}`
      );
    } catch (e) {
      logger.warn("Unable to log Trial diff");
    }
  }

  private static getRandomCommonTrialId(
    v1Results: ClinicalTrialsSearchResponse,
    v2Results: ClinicalTrialsSearchResponse
  ): string {
    const v1NCTIds: string[] = v1Results?.results.map(
      (res: ClinicalTrial) => res.nctId || ""
    );
    const v2NCTIds: string[] = v2Results?.results.map(
      (res: ClinicalTrial) => res.nctId || ""
    );
    const intersectionIds = _.intersection(v1NCTIds || [], v2NCTIds);
    const chosenNCTId = _.sample(intersectionIds);
    return chosenNCTId || "";
  }

  private rebuildHitsWithReplacements(
    root: RootObjectV3<ClinicalTrialDocumentBase>,
    replacementTrials: ClinicalTrialDocumentBase[] | undefined
  ) {
    if (!replacementTrials) {
      return root.hits;
    }
    const trials = root.hits.hits
      .map((source) => {
        if (source._source.is_deprecated) {
          const replacement = replacementTrials.find(
            (replacementTrial) =>
              replacementTrial.h1_clinical_trial_id ===
              source._source.survivor_h1_clinical_trial_id
          );
          if (!replacement) {
            this.logger.warn(
              `No replacement found for ${source._source.h1_clinical_trial_id}`
            );
            return undefined;
          }
          return replacement;
        }
        return source._source;
      })
      .filter((trial) => trial);
    root.hits.hits = trials.map(
      (trial) => ({ _source: trial } as HitSource<ClinicalTrialDocumentBase>)
    ) as HitSource<ClinicalTrialDocumentBase>[];
    return root.hits;
  }

  private rebuildHitsWithCTMSWithReplacements(
    root: estypes.SearchResponse<ClinicalTrialDocumentBase>,
    replacementTrials: ClinicalTrialDocumentBase[] | undefined
  ) {
    if (!replacementTrials) {
      return root.hits;
    }
    const trials = root.hits.hits
      .map((source) => {
        if (source._source?.is_deprecated) {
          const replacement = replacementTrials.find(
            (replacementTrial) =>
              replacementTrial.h1_clinical_trial_id ===
              source._source?.survivor_h1_clinical_trial_id
          );
          if (!replacement) {
            this.logger.warn(
              `No replacement found for ${source._source.h1_clinical_trial_id}`
            );
            return undefined;
          }
          return replacement;
        }
        return source._source;
      })
      .filter((trial) => trial);
    root.hits.hits = trials.map(
      (trial) =>
        ({ _source: trial } as estypes.SearchHit<ClinicalTrialDocumentBase>)
    );
    return root.hits;
  }

  async getResultsForAutocomplete(
    input: ClinicalTrialFilterAutocompleteInput
  ): Promise<Array<KeywordFilterAggregation>> {
    const flag = await this.getFeatureFlag(input);
    if (input.filterField) {
      const aggregation = this.buildAggregationForAutocomplete(input);
      const queryV3 = await createQueryForAutocomplete(
        input,
        QueryVersion.V3,
        aggregation,
        undefined,
        this.queryParserService,
        this.parsedQueryTreeToElasticsearchQueriesService,
        this.queryUnderstandingServiceClient
      );
      const queryV4 = await createQueryForAutocomplete(
        input,
        QueryVersion.V4,
        aggregation,
        undefined,
        this.queryParserService,
        this.parsedQueryTreeToElasticsearchQueriesService,
        this.queryUnderstandingServiceClient
      );

      switch (flag) {
        case TrialSearchFlagVariation.V3: {
          return await this.getResultsV3ForAutocomplete(queryV3);
        }
        case TrialSearchFlagVariation.V4: {
          const result = await this.getResultsV4ForAutocomplete(queryV4);

          return result;
        }
        case TrialSearchFlagVariation.CTMS: {
          const query = await createQueryWithCTMSForAutocomplete(
            input,
            aggregation,
            false,
            this.queryParserService,
            this.parsedQueryTreeToElasticsearchQueriesService,
            this.queryUnderstandingServiceClient
          );
          await this.updateTrialsSearchQueryToAddTagsFilters(input, query);
          this.logger.info({ query }, "trials autocomplete search query");
          const result = await this.getResultsWithCTMSForAutocomplete(query);
          return result;
        }
        default: {
          // By default return newest variation
          const result = await this.getResultsV4ForAutocomplete(queryV4);

          return result;
        }
      }
    }
    return [];
  }
  buildAggregationForAutocomplete(
    input: ClinicalTrialFilterAutocompleteInput
  ): Record<string, AggregationsAggregationContainer> {
    let aggregations: Record<string, AggregationsAggregationContainer>;
    const aggmap = autocompleteFilterMap[input.filterField!];
    if (aggmap.nestedPath) {
      const multimatchQuery: QueryDslQueryContainer = input.filterValue
        ? {
            multi_match: {
              query: input.filterValue,
              type: "bool_prefix",
              operator: "AND",
              fields: aggmap.fieldsToSearch
            }
          }
        : MATCH_ALL;

      aggregations = {
        nested: {
          nested: {
            path: aggmap.nestedPath
          },
          aggs: {
            filtered_matching: {
              filter: multimatchQuery,
              aggs: {
                matching: {
                  terms: {
                    field: aggmap.fieldForAutocomplete,
                    size: 10
                  }
                }
              }
            }
          }
        }
      };
    } else {
      let filterValueExpandedForRegex;
      if (input.filterValue?.length) {
        filterValueExpandedForRegex = expandFilterValueToRegex(
          input.filterValue
        );
      }
      const multimatchQuery: QueryDslQueryContainer = input.filterValue
        ? {
            multi_match: {
              query: input.filterValue,
              type: "bool_prefix",
              operator: "AND",
              fields: aggmap.fieldsToSearch
            }
          }
        : MATCH_ALL;
      aggregations = {
        filtered_matching: {
          filter: multimatchQuery,
          aggs: {
            matching: {
              terms: {
                field: aggmap.fieldForAutocomplete,
                size: 10,
                include: input.filterValue?.length
                  ? `(.*[ \\-#~.]${filterValueExpandedForRegex}.*)|(${filterValueExpandedForRegex}.*)`
                  : undefined
              }
            }
          }
        }
      };
    }
    return aggregations;
  }

  private async getAutocompleteResultsBasedOnVersion(
    esService: ElasticCommonSearchService,
    query: estypes.SearchRequest,
    index?: string
  ): Promise<Array<KeywordFilterAggregation>> {
    this.logger.debug(
      JSON.stringify(query, null, 2),
      "Clinical Trials Search Query"
    );

    if (index) {
      this.logger.debug(`making request to index ${index}`);
    } else {
      this.logger.debug(`making request to index ${esService.index}`);
    }

    try {
      const { aggregations } = await esService.query({
        index: index || esService.index,
        ...query
      });

      this.logger.debug(
        JSON.stringify(aggregations, null, 2),
        "Clinical Trials Autocomplete response"
      );

      return this.adaptAggregationResponse(
        aggregations as SupportedAggregations
      );
    } catch (error) {
      this.logger.error("Error in getAutocompleteResultsBasedOnVersion", error);
      throw error;
    }
  }

  async getResultsWithCTMSForAutocomplete(
    query: estypes.SearchRequest
  ): Promise<Array<KeywordFilterAggregation>> {
    return this.getAutocompleteResultsBasedOnVersion(
      this.elasticTrialSearchServiceV4,
      query,
      this.config.elasticTrialsIndexWithCTMS
    );
  }

  async getResultsV4ForAutocomplete(
    query: estypes.SearchRequest
  ): Promise<Array<KeywordFilterAggregation>> {
    return this.getAutocompleteResultsBasedOnVersion(
      this.elasticTrialSearchServiceV4,
      query
    );
  }

  async getResultsV3ForAutocomplete(
    query: estypes.SearchRequest
  ): Promise<Array<KeywordFilterAggregation>> {
    return this.getAutocompleteResultsBasedOnVersion(
      this.elasticTrialSearchServiceV3,
      query
    );
  }

  adaptAggregationResponse(
    aggregations: SupportedAggregations
  ): Array<KeywordFilterAggregation> {
    let buckets: DocCountBucket[] = [];
    try {
      if (
        aggregations &&
        "nested" in aggregations &&
        aggregations.nested.filtered_matching?.matching?.buckets
      ) {
        buckets = aggregations.nested.filtered_matching.matching
          .buckets as DocCountBucket[];
      } else if (
        aggregations &&
        "filtered_matching" in aggregations &&
        aggregations.filtered_matching?.matching?.buckets
      ) {
        buckets = aggregations.filtered_matching.matching
          .buckets as DocCountBucket[];
      } else {
        throw new Error("Unexpected aggregation structure");
      }

      const results = buckets.map((bucket) => ({
        id: bucket.key,
        count: bucket.doc_count
      }));

      return results;
    } catch (error) {
      this.logger.error("Error in adaptAggregationResponse", error);
      throw error;
    }
  }

  @RpcMethod()
  @Trace("h1-search.clinicaltrial.generateHistogram")
  async generateTrialHistogram(
    input: ClinicalTrialHistogramInput
  ): Promise<ClinicalTrialHistogramResponse> {
    this.logger.debug(
      JSON.stringify(input, null, 2),
      "Clinical Trials Histogram Input"
    );

    try {
      const bucketCount = input.bucketCount || 10;

      // Build base query with filters using the same logic as main search
      const baseQuery = await createQueryWithCTMS(
        { ...input, limit: 1, offset: 0 },
        {},
        false,
        this.queryParserService,
        this.parsedQueryTreeToElasticsearchQueriesService,
        this.queryUnderstandingServiceClient
      );

      // First, get range statistics for all metrics to calculate appropriate intervals
      const rangeStatsQuery = this.buildRangeStatisticsQuery(baseQuery);
      const allMetrics = Object.values(TrialHistogramMetric);

      this.logger.debug(
        JSON.stringify(rangeStatsQuery, null, 2),
        "Clinical Trials Range Statistics Query"
      );

      // Execute range statistics query
      const rangeStatsResult = await this.elasticTrialSearchServiceV4.query({
        index: this.config.elasticTrialsIndexWithCTMS,
        ...rangeStatsQuery
      });

      this.logger.debug(
        JSON.stringify(rangeStatsResult, null, 2),
        "Clinical Trials Histogram range stats"
      );

      // Calculate intervals based on actual data ranges
      const { intervals, offsets, extendedBounds, hasOutliers } =
        this.buildHistogramIntervals(rangeStatsResult, allMetrics, bucketCount);

      this.logger.debug(
        JSON.stringify({ intervals, offsets, extendedBounds }, null, 2),
        "Clinical Trials Histogram Intervals and Offsets"
      );

      // Add histogram aggregations for all metrics in a single query
      const histogramQuery = { ...baseQuery };

      if (!histogramQuery.aggs) {
        histogramQuery.aggs = {};
      }

      // Add histogram aggregation for each metric
      for (const metric of allMetrics) {
        const metricConfig = (histogramAggregations as any)[metric];
        if (!metricConfig) {
          this.logger.warn(`No configuration found for metric: ${metric}`);
          continue;
        }

        const interval = intervals[metric];
        const offset = offsets[metric];
        const metricExtendedBounds = extendedBounds[metric];

        // Create a combined filter that includes both the metric filter and the range filter
        const combinedFilter = (() => {
          switch (metric) {
            case TrialHistogramMetric.TRIAL_DURATION:
              return {
                bool: {
                  must: [
                    metricConfig.filter,
                    {
                      script: {
                        script: {
                          // Same script as the one in the trials-index.ts file for Trial Duration but adjusted to remove outliers based on the IQR bounds
                          source: `ZonedDateTime d1 = doc['study.completion_date'].value; ZonedDateTime d2 = doc['study.start_date'].value; long differenceInMillis = ChronoUnit.MILLIS.between(d1, d2); def duration = Math.abs(differenceInMillis) / (1000 * 60 * 60 * 24 * 365.25); return duration >= ${metricExtendedBounds.min} && duration <= ${metricExtendedBounds.max};`,
                          lang: "painless"
                        }
                      }
                    }
                  ]
                }
              };
            default:
              return {
                bool: {
                  must: [
                    metricConfig.filter,
                    {
                      range: {
                        [metricConfig.field]: {
                          gte: metricExtendedBounds.min,
                          lte: metricExtendedBounds.max
                        }
                      }
                    }
                  ]
                }
              };
          }
        })();

        if (metricConfig.field === "script") {
          histogramQuery.aggs[`${metric}_histogram`] = {
            filter: combinedFilter,
            aggs: {
              histogram: {
                histogram: {
                  script: metricConfig.script,
                  interval,
                  extended_bounds: metricExtendedBounds,
                  offset
                }
              }
            }
          };
        } else {
          histogramQuery.aggs[`${metric}_histogram`] = {
            filter: combinedFilter,
            aggs: {
              histogram: {
                histogram: {
                  field: metricConfig.field,
                  interval,
                  extended_bounds: metricExtendedBounds,
                  offset
                }
              }
            }
          };
        }
      }

      this.logger.debug(
        JSON.stringify(histogramQuery, null, 2),
        "Clinical Trials Histogram Query with Multiple Aggregations"
      );

      // Execute single query with all histogram aggregations
      const result = await this.elasticTrialSearchServiceV4.query({
        index: this.config.elasticTrialsIndexWithCTMS,
        ...histogramQuery
      });

      // Process results for all metrics
      const histogramResults: TrialHistogramData = {};
      for (const metric of allMetrics) {
        try {
          const metricAggKey = `${metric}_histogram`;
          const metricResult = (result as any).aggregations?.[metricAggKey];

          if (metricResult) {
            const buckets = metricResult.histogram?.buckets || [];
            const total = metricResult.doc_count || 0;

            histogramResults[metric] = {
              buckets,
              hasOutliers: hasOutliers[metric],
              total
            };
          }
        } catch (metricError) {
          this.logger.warn(
            `Failed to process histogram for metric ${metric}: ${metricError}`
          );
          // Continue with other metrics even if one fails
        }
      }

      return {
        metrics: histogramResults,
        total: (result as any).hits?.total?.value || 0
      };
    } catch (err) {
      this.logger.error(
        JSON.stringify(err, null, 2),
        "Clinical Trial Histogram Query Failed Error"
      );
      throw err;
    }
  }

  /**
   * Builds a range statistics query to get min/max values for all histogram metrics.
   * This query is used to calculate appropriate intervals for histogram buckets.
   *
   * @param baseQuery - The base search query to extend with range statistics
   * @returns Search request with range statistics aggregations for all metrics
   */
  private buildRangeStatisticsQuery(
    baseQuery: estypes.SearchRequest
  ): estypes.SearchRequest {
    const rangeStatsQuery = { ...baseQuery };

    // Initialize aggregations if not present
    if (!rangeStatsQuery.aggs) {
      rangeStatsQuery.aggs = {};
    }

    const allMetrics = Object.values(TrialHistogramMetric);

    // Add range statistics aggregation for each metric
    for (const metric of allMetrics) {
      const metricConfig = histogramAggregations[metric] as any;
      if (!metricConfig) {
        this.logger.warn(`No configuration found for metric: ${metric}`);
        continue;
      }

      // Create a filter aggregation that applies the metric's filter conditions
      // and then calculates min/max/avg/count statistics
      rangeStatsQuery.aggs[`${metric}_range_stats`] = {
        filter: metricConfig.filter,
        aggs: {
          range_stats: {
            percentiles: {
              // Use field for regular numeric fields
              field:
                metricConfig.field === "script"
                  ? undefined
                  : metricConfig.field,
              // Use script for computed fields like trial duration
              script:
                metricConfig.field === "script"
                  ? metricConfig.script
                  : undefined,
              percents: [0, 25, 75, 100] // Q1 and Q3 for IQR calculation
            }
          }
        }
      };
    }

    return rangeStatsQuery;
  }

  /**
   * Calculates histogram intervals and offsets based on range statistics.
   * Intervals are calculated to create the specified number of buckets across the data range.
   *
   * @param rangeStatsResult - Elasticsearch response containing range statistics
   * @param allMetrics - Array of all histogram metrics to process
   * @param bucketCount - Number of buckets to create for each histogram
   * @returns Object containing intervals and offsets for each metric
   */
  private buildHistogramIntervals(
    rangeStatsResult: any,
    allMetrics: TrialHistogramMetric[],
    bucketCount: number
  ): {
    intervals: Record<string, number>;
    offsets: Record<string, number>;
    extendedBounds: Record<string, { min: number; max: number }>;
    hasOutliers: Record<string, boolean>;
  } {
    const intervals: Record<string, number> = {};
    const offsets: Record<string, number> = {};
    const extendedBounds: Record<string, { min: number; max: number }> = {};
    const hasOutliers: Record<string, boolean> = {};

    for (const metric of allMetrics) {
      const metricConfig = histogramAggregations[metric];
      if (!metricConfig) {
        this.logger.warn(`No configuration found for metric: ${metric}`);
        continue;
      }

      // Extract range statistics for this metric
      const rangeStats =
        rangeStatsResult.aggregations?.[`${metric}_range_stats`]?.range_stats;

      if (
        rangeStats &&
        rangeStats.values &&
        Object.keys(rangeStats.values).length > 0
      ) {
        // Extract percentiles
        const q0 = rangeStats.values["0.0"] ?? 0;
        const q1 = rangeStats.values["25.0"] ?? 0;
        const q3 = rangeStats.values["75.0"] ?? 0;
        const q100 = rangeStats.values["100.0"] ?? 0;

        // Calculate IQR (Interquartile Range)
        const iqr = q3 - q1;

        // Calculate bounds using IQR method: Q1 - 1.5*IQR to Q3 + 1.5*IQR
        const min = Math.max(q1 - 1.5 * iqr, q0);
        const max = Math.min(q3 + 1.5 * iqr, q100);
        hasOutliers[metric] = min !== q0 || max !== q100;

        // We can't have intervals of 0
        if (min === 0 && max === 0) {
          intervals[metric] = 1;
          offsets[metric] = 0;
          extendedBounds[metric] = {
            min: 0,
            max: 10
          };
          hasOutliers[metric] = false;
          // If all items share the same value, set the interval to the value and the extended bounds to 0 and 2x the value. This will show a nice bell curve.
        } else if (min === max) {
          intervals[metric] = min;
          extendedBounds[metric] = {
            min: 0,
            max: min * 2
          };
        } else {
          // Calculate interval size to create the desired number of buckets
          const rawInterval = (max - min) / bucketCount;

          intervals[metric] = rawInterval;

          // Set offset to minimum value so buckets start at the actual data minimum
          offsets[metric] = min;
          // This extended bounds is set in this code path specifically to help calculate the range filter to remove outliers for the histogram query (see generateTrialHistogram)
          extendedBounds[metric] = {
            min,
            max
          };
        }

        this.logger.debug(
          `Metric ${metric}: min=${min}, max=${max}, interval=${intervals[metric]}, offset=${offsets[metric]}, bucketCount=${bucketCount}`
        );
      } else {
        // Fallback values when no range data is available
        // This ensures the histogram query doesn't fail

        intervals[metric] = 1;
        offsets[metric] = 0;
        extendedBounds[metric] = {
          min: 0,
          max: 10
        };
        hasOutliers[metric] = false;
        this.logger.warn(
          `No range data for metric ${metric}, using default interval of 1, offset of 0, and bounds [0, 10]`
        );
      }
    }

    return { intervals, offsets, extendedBounds, hasOutliers };
  }

  @RpcMethod()
  @Trace("h1-search.clinicaltrial.generateCountryBreakdown")
  async generateTrialCountryBreakdown(
    input: ClinicalTrialCountryBreakdownInput
  ): Promise<ClinicalTrialCountryBreakdownResponse> {
    try {
      const start = performance.now();
      // Build base query with filters using the same logic as main search
      const countryBreakdownQuery = await createQueryWithCTMS(
        {
          ...input,
          sort: { direction: "Desc", sortBy: "StartDate" },
          limit: 0,
          offset: 0
        },
        { trial_country_breakdown: createTrialCountryBreakdownAggregation() },
        false,
        this.queryParserService,
        this.parsedQueryTreeToElasticsearchQueriesService,
        this.queryUnderstandingServiceClient
      );

      this.logger.debug(
        JSON.stringify(countryBreakdownQuery, null, 2),
        "Clinical Trials Country Breakdown Query"
      );

      // Execute the query
      const result = await this.elasticTrialSearchServiceV4.query({
        index: this.config.elasticTrialsIndexWithCTMS,
        ...countryBreakdownQuery
      });

      this.logger.info(
        {
          took: result.took,
          total:
            // @ts-ignore countries will exist on the aggregations at this step
            result.aggregations?.trial_country_breakdown?.countries?.buckets
              ?.length || 0
        },
        "Clinical Trials Country Breakdown execution time"
      );

      this.logger.debug(
        JSON.stringify(result, null, 2),
        "Clinical Trials Country Breakdown Result"
      );

      // Map the response to the expected format
      const countries =
        // @ts-ignore countries will exist on the aggregations at this step
        result.aggregations?.trial_country_breakdown?.countries?.buckets?.map(
          (bucket: any) => ({
            country: bucket.key,
            totalTrials: bucket.trials?.total_trials?.value || 0,
            activeTrials: bucket.trials?.active_trials?.doc_count || 0,
            recruitingTrials: bucket.trials?.recruiting_trials?.doc_count || 0,
            patientEnrollmentMedian:
              bucket.trials?.patient_enrollment_median?.median?.values?.[
                "50.0"
              ] || null,
            enrollmentDurationMedian:
              bucket.trials?.enrollment_duration_median?.median?.values?.[
                "50.0"
              ] || null,
            enrollmentRateMedian:
              bucket.trials?.enrollment_rate_median?.median?.values?.["50.0"] ||
              null,
            trialDurationMedian:
              bucket.trials?.trial_duration_median?.median?.values?.["50.0"] ||
              null,
            totalH1Sites: bucket.h1_sites_count?.count?.value || 0,
            totalH1Investigators:
              bucket.h1_investigators_count?.investigators_filter?.count
                ?.value || 0
          })
        ) || [];

      const end = performance.now();

      this.logger.info({
        msg: "Clinical Trials Country Breakdown",
        duration: end - start,
        input
      });

      return {
        countries,
        total:
          // @ts-ignore countries will exist on the aggregations at this step
          result.aggregations?.trial_country_breakdown?.countries?.buckets
            ?.length || 0
      };
    } catch (err) {
      this.logger.error(
        JSON.stringify(err, null, 2),
        "Clinical Trial Country Breakdown Query Failed Error"
      );
      throw err;
    }
  }

  @RpcMethod()
  @Trace("h1-search.clinicaltrial.generateRegionBreakdown")
  async generateTrialRegionBreakdown(
    input: ClinicalTrialRegionBreakdownInput
  ): Promise<ClinicalTrialRegionBreakdownResponse> {
    try {
      const start = performance.now();
      // Build base query with filters using the same logic as main search
      const regionBreakdownQuery = await createQueryWithCTMS(
        {
          ...input,
          sort: { direction: "Desc", sortBy: "StartDate" },
          limit: 0,
          offset: 0
        },
        {
          trial_region_breakdown: createTrialRegionBreakdownAggregation(
            input.country
          )
        },
        false,
        this.queryParserService,
        this.parsedQueryTreeToElasticsearchQueriesService,
        this.queryUnderstandingServiceClient
      );

      this.logger.debug(
        JSON.stringify(regionBreakdownQuery, null, 2),
        "Clinical Trials Region Breakdown Query"
      );

      // Execute the query
      const result = await this.elasticTrialSearchServiceV4.query({
        index: this.config.elasticTrialsIndexWithCTMS,
        ...regionBreakdownQuery
      });

      this.logger.debug(
        JSON.stringify(result, null, 2),
        "Clinical Trials Region Breakdown Result"
      );

      // Map the response to the expected format
      const regions =
        // @ts-ignore regions will exist on the aggregations at this step
        result.aggregations?.trial_region_breakdown?.country_filter?.regions?.buckets?.map(
          (bucket: any) => ({
            region: bucket.key,
            totalTrials: bucket.trials?.total_trials?.value || 0,
            activeTrials: bucket.trials?.active_trials?.doc_count || 0,
            recruitingTrials: bucket.trials?.recruiting_trials?.doc_count || 0,
            patientEnrollmentMedian:
              bucket.trials?.patient_enrollment_median?.median?.values?.[
                "50.0"
              ] || null,
            enrollmentDurationMedian:
              bucket.trials?.enrollment_duration_median?.median?.values?.[
                "50.0"
              ] || null,
            enrollmentRateMedian:
              bucket.trials?.enrollment_rate_median?.median?.values?.["50.0"] ||
              null,
            trialDurationMedian:
              bucket.trials?.trial_duration_median?.median?.values?.["50.0"] ||
              null,
            totalH1Sites: bucket.h1_sites_count?.count?.value || 0,
            totalH1Investigators:
              bucket.h1_investigators_count?.investigators_filter?.count
                ?.value || 0
          })
        ) || [];

      const end = performance.now();

      this.logger.info({
        msg: "Clinical Trials Region Breakdown",
        duration: end - start,
        input
      });

      return {
        regions,
        total:
          // @ts-ignore regions will exist on the aggregations at this step
          result.aggregations?.trial_region_breakdown?.country_filter?.regions
            ?.buckets?.length || 0
      };
    } catch (err) {
      this.logger.error(
        JSON.stringify(err, null, 2),
        "Clinical Trial Region Breakdown Query Failed Error"
      );
      throw err;
    }
  }
}
export function expandFilterValueToRegex(filterValue: string): string {
  return filterValue
    .trim()
    .split(" ")
    .map((word) => {
      return word
        .split("")
        .map((char) => {
          const lowerCased = char.toLowerCase();
          const upperCased = char.toUpperCase();
          const hasCaseDifference = lowerCased !== upperCased;

          if (hasCaseDifference) {
            return `[${lowerCased}${upperCased}]`;
          }

          if (REGEXP_SPECIAL_CHARACTERS.has(char)) {
            return `\\${char}`;
          }

          return char;
        })
        .join("");
    })
    .join(REGEX_ONE_OR_TWO_CHARACTERS);
}
