/* eslint-disable @typescript-eslint/no-non-null-assertion */
import {
  FilterInterface,
  KeywordSearchRewriteResource,
  RPC_NAMESPACE_KOL_SEARCH_REWRITE,
  KeywordSearchInput,
  PersonSearchResponse,
  WeightedSortBy,
  DiagnosesFields,
  ProceduresFields,
  HCPSearchScoringFunctions,
  GeoChoroplethLevelTypes,
  CompletionSuggesterInput,
  QueryIntentFilterValues,
  QueryIntent,
  SearchSliceOptionsEnum,
  HighlightCountPerAsset,
  PatientDiversityStatsResponse,
  PatientsDiversityRaceInfo,
  AffiliationSignal,
  NullableValueFilter,
  Apps
} from "@h1nyc/search-sdk";
import { RpcMethod, RpcResourceService, RpcService } from "@h1nyc/systems-rpc";
import { createLogger } from "../lib/Logger";
import { BothQueryParseResult, ITree } from "../lib/ParserTypes/types";
import { Trace } from "../Tracer";
import { ConfigService } from "./ConfigService";
import { ElasticSearchService } from "./ElasticSearchService";
import { QueryParserService } from "./QueryParserService";
import { QueryUnderstandingServiceClient } from "./QueryUnderstandingServiceClient";
// @ts-ignore
import franc from "franc-min";
import {
  AggregationsAggregationContainer,
  QueryDslFunctionBoostMode,
  QueryDslFunctionScoreContainer,
  SearchHighlightField,
  SearchHit,
  SearchInnerHits,
  QueryDslNestedQuery,
  QueryDslQueryContainer,
  SearchRequest,
  SearchResponse,
  AggregationsAggregateBase,
  AggregationsTermsAggregateBase,
  SortCombinations,
  AggregationsMultiBucketAggregateBase,
  QueryDslFunctionScoreMode,
  CountRequest,
  ScoreSort,
  SearchTotalHits,
  MsearchRequest,
  MsearchMultisearchHeader,
  MsearchMultisearchBody,
  SearchResponseBody,
  QueryDslScriptScoreFunction
} from "@elastic/elasticsearch/lib/api/types";
import { ParsedQueryTreeToElasticsearchQueriesService } from "./ParsedQueryTreeToElasticsearchQueries";
import { QueryUnderstandingServiceResponse } from "../proto/query_understanding_service_pb";
import { KeywordSearchResponseAdapterService } from "./KeywordSearchResponseAdapterService";
import { FeatureFlagsService } from "@h1nyc/systems-feature-flags";
import { SpellSuggesterService } from "./SpellSuggesterService";
import {
  getQuerySynonymList,
  getQueryIntents
} from "../lib/query_understanding/QueryUnderstandingResponseAnalyzer";
import { Service } from "typedi";
import { CHINESE, ENGLISH, JAPANESE, Language } from "./LanguageDetectService";
import {
  Entity,
  SavedTerritoryResourceClient,
  UserEntities
} from "@h1nyc/account-sdk";
import { SavedTerritory } from "@h1nyc/account-user-entities";
import { CompletionSuggesterService } from "./CompletionSuggesterService";
import { KeywordSearchMatchedCountService } from "./KeywordSearchMatchedCountService";
import {
  ConferenceQueryIntentFilterService,
  hasGoodConfidenceForConferenceIntent
} from "./ConferenceQueryIntentFilterService";
import _, { Dictionary, isEmpty, trim } from "lodash";
import { QueryIntent as QueryIntentProtoEnum } from "../proto/query_intent_pb";
import {
  OnboardingData,
  UserOnboardingDataService
} from "./UserOnboardingDataService";
import {
  getLocationQueryForOnboardingData,
  isLocationIntentPresent
} from "./queryBuilders/DefaultNameSearchBuilder";
import { AFFILIATION_HIGHLIGHT_NAME_PREFIX } from "./queryBuilders/DefaultNameSearchBuilder";
import { buildClaimsRegionFilterForPeopleSearch } from "../util/ClaimsRegionFilterUtils";
import { RulesParserService } from "./RulesParserService";
import {
  buildFieldExistsQuery,
  buildFunctionScoreQuery,
  buildFunctionScoreQueryWithOnlyFunctions,
  buildScriptScore,
  buildTermQuery,
  buildTermsQuery
} from "../util/QueryBuildingUtils";
import {
  findNestedClaimsFilters,
  findNestedFilters
} from "../util/QueryParsingUtils";
import { DocCountBucket } from "./InstitutionsResourceService";
import {
  extractClaimsCode,
  KeywordFilterClauseBuilderService
} from "./KeywordFilterClauseBuilderService";
import { HcpDiversityStatsInput } from "@h1nyc/search-sdk";
import { PatientDiversityStatsService } from "./PatientDiversityStatsService";
import { estypes } from "@elastic/elasticsearch";
import { SearchIndicationsByQueryInput } from "@h1nyc/search-sdk";
import { IndicationType } from "@h1nyc/search-sdk";
import { IndicationSource } from "@h1nyc/search-sdk";
import { IndicationSortBy } from "@h1nyc/search-sdk";
import { IndicationsTreeSearchService } from "./IndicationsTreeSearchService";
import { EntitySearchProcessorService } from "./EntitySearchProcessorService";
import {
  CcsrIcdMappingRepository,
  CcsrPxMappingRepository,
} from "@h1nyc/pipeline-repositories";
import {ClaimCodeService} from "./ClaimCodeService"

export const HAS_ADVANCED_OPERATORS = /\s(AND|OR|NOT)\s/;
export const ALL_ANDs = /\bAND\b/g;
export const ALL_ORs = /\bOR\b/g;
export const OR = "|";
export const EMPTY_STRING = "";
export const ALL_UNICODE_DOUBLE_QUOTES = /[“”]/g;
export const ASCII_DOUBLE_QUOTES = '"';
export const MAXIMUM_QUERY_TOKENS = 10;

const UNDETERMINED = "und";
const BOOST_MODE_REPLACE: QueryDslFunctionBoostMode = "replace";
const BOOST_MODE_DEFAULT: QueryDslFunctionBoostMode = "multiply";
const BOOST_MODE_SUM: QueryDslFunctionBoostMode = "sum";
const SCORE_MODE_SUM: QueryDslFunctionScoreMode = "sum";
const SCORE_MODE_AVG: QueryDslFunctionScoreMode = "avg";

const ALL_ASSETS_OPTIONAL = 0;
const AT_LEAST_ONE_ASSET = 1;

const DEFAULT_VALUE_NO_DIGITAL_RANK = 5000;
const DIGITAL_RELEVANCE_SCORE_SCRIPT = `doc['digitalRank'].value == 0 ?
                                        2 / ( ${DEFAULT_VALUE_NO_DIGITAL_RANK} + 1/_score ) :
                                        2 / ( doc['digitalRank'].value/300 + 1/_score )`;

const NUMBER_OF_HIGHLIGHTS = 2;
const NO_HIGHLIGHT_FIELD_PARAMETERS: Readonly<SearchHighlightField> = {};

// TODO: Uncomment once we have the data
// const CTMS_AVG_TIME_TO_FIRST_PATIENT_IN_FIELD =
//   "ctmsInvestigatorStats.averageTimeToFirstPatientIn";
const CTMS_TRIAL_COUNT_FIELD = "ctmsInvestigatorStats.trialCount";
const CTMS_AVG_PATIENTS_ENROLLED_FIELD =
  "ctmsInvestigatorStats.averagePatientsEnrolled";
const CTMS_AVG_PATIENT_RECRUITMENT_PERIOD_FIELD =
  "ctmsInvestigatorStats.averagePatientRecruitmentPeriod";
const CTMS_AVG_PATIENT_RETENTION_PERCENTAGE_FIELD =
  "ctmsInvestigatorStats.averagePatientRetentionPercentage";

const TRIAL_PERFORMANCE_SCORE_SCRIPTS = [
  `doc['${CTMS_AVG_PATIENT_RECRUITMENT_PERIOD_FIELD}'].size() == 0 ?
          0 : 1 / (doc['${CTMS_AVG_PATIENT_RECRUITMENT_PERIOD_FIELD}'].value + 1)`,
  `doc['${CTMS_AVG_PATIENT_RETENTION_PERCENTAGE_FIELD}'].size() == 0 ?
          0 : 3 * Math.tanh(doc['${CTMS_AVG_PATIENT_RETENTION_PERCENTAGE_FIELD}'].value / 100)`,
  `doc['${CTMS_AVG_PATIENTS_ENROLLED_FIELD}'].size() == 0 ?
          0 : saturation(doc['${CTMS_AVG_PATIENTS_ENROLLED_FIELD}'].value, 10)`
];

const CTMS_TRIAL_COUNT_WEIGHT_FOR_TRIAL_PERFORMANCE_SORT = 5;
const CTMS_TRIAL_COUNT_WEIGHT_FOR_HAS_CTMS_FILTER = 10;

const CONTRIBUTOR_RANKING_KEYNOTE_SPEAKER_BOOST = 10;
const CONTRIBUTOR_RANKING_RESEARCH_LEADER_BOOST = 5;

export type KeywordSearchFeatureFlags = {
  enableNewClaimsSearch: boolean;
  enableMinMaxResultCache: boolean;
  enableClaimsExports?: boolean;
  enableQueryContextualPaymentsFiltering: boolean;
  enableQueryIntent: boolean;
  enableSpellSuggestion: boolean;
  enableCompletionSuggestion: boolean;
  enableSearchHighlights: boolean;
  enableConferenceIntentFilter: boolean;
  enableLocationConfidenceRanking: boolean;
  enableCTMSV2: boolean;
  enableIntentBasedKeywordSearch: boolean;
  enableKWSearchPersonalisation: boolean;
  enableTagsInElasticsearch: boolean;
  enableUniquePatientCountForClaims: boolean;
  enableBrazilianClaims: boolean;
  disableUniquePatientCountForOnlyProcedures: boolean;
  enableEnhancedHasCTMSDataFilterBehavior: boolean;
  enableNestedIndicationFilter: boolean;
  enableAsianPacificIslanderDiversityBreakOut: boolean;
  enablePenaltyForZeroTrialsDiversityRanking: boolean;
  enableCcsrExclusionForMatchedCounts: boolean;
  enableLocationFilterRegionRollup: boolean;
  enableIndicationSynonymPrescriptionSearch: boolean;
  enableInternalUsersForRankingDebug: boolean;
  enableNewH1DefaultRanking: boolean;
  enableAiqData: boolean;
  enableBothOperatorForSearch: boolean;
  enableLlmQueryParserForSearch: boolean;
  enableNewGlobalLeaderTier: boolean;
};

export const keywordSearchFeatureFlagTypes = [
  "enableNewClaimsSearch",
  "enableMinMaxResultCache",
  "enableClaimsExports",
  "enableQueryContextualPaymentsFiltering",
  "enableQueryIntent",
  "enableSpellSuggestion",
  "enableCompletionSuggestion",
  "enableSearchHighlights",
  "enableConferenceIntentFilter",
  "enableLocationConfidenceRanking",
  "enableCTMSV2",
  "enableIntentBasedKeywordSearch",
  "enableKWSearchPersonalisation",
  "enableTagsInElasticsearch",
  "enableUniquePatientCountForClaims",
  "enableBrazilianClaims",
  "disableUniquePatientCountForOnlyProcedures",
  "enableEnhancedHasCTMSDataFilterBehavior",
  "enableNestedIndicationFilter",
  "enableAsianPacificIslanderDiversityBreakOut",
  "enablePenaltyForZeroTrialsDiversityRanking",
  "enableCcsrExclusionForMatchedCounts",
  "enableLocationFilterRegionRollup",
  "enableIndicationSynonymPrescriptionSearch",
  "enableInternalUsersForRankingDebug",
  "enableNewH1DefaultRanking",
  "enableAiqData",
  "enableBothOperatorForSearch",
  "enableLlmQueryParserForSearch",
  "enableNewGlobalLeaderTier"
] as const;

export type KeywordSearchFeatureFlag =
  (typeof keywordSearchFeatureFlagTypes)[number];

export const assetsRequiringAggregationForMatchedCount = [
  "microBlogging",
  "citations",
  "payments",
  "diagnoses_amount",
  "procedures_amount",
  "prescriptions_amount",
  "l1_indications",
  "l3_indications",
  "patient_claims_matching_count",
  "trial_ongoing_count",
  "trial_actively_recruiting_count",
  "ccsr_amount",
  "ccsr_px_amount"
] as const;

const locationFields = ["country", "state", "city", "zipCode"];
type LocationFields = (typeof locationFields)[number];

const locationFieldToIndexFieldMapping: Readonly<Record<locationType, string>> =
  {
    country: "location_country_confidence_mapping",
    state: "location_state_confidence_mapping",
    city: "location_token_confidence_mapping",
    zipCode: "location_zipcode5_confidence_mapping"
  };

type locationType = "country" | "state" | "city" | "zipCode";

enum RankingType {
  PATIENT_DIVERSITY_RANK = "patientDiversityRank",
  STANDARD_WEIGHT_RANK = "standardWeightRank",
  NEW_TL_H1_DEFAULT_RANK = "newTLH1DefaultRank"
}

export type ScoringValueRange = {
  min: number;
  mean: number;
  max: number;
};

export type ScaleScoreTargetRange = {
  targetMin: number;
  targetMax: number;
};

export type AssetsRequiringAggregationForMatchedCount =
  (typeof assetsRequiringAggregationForMatchedCount)[number];

export const assetAggregationValues: Readonly<
  Record<RootFieldsForScoring, ScoringValueRange>
> = {
  trialCount: { mean: 0.17, max: 665, min: 0 },
  publicationCount: { mean: 6.4, max: 5313, min: 0 },
  congressCount: { mean: 0.2, max: 595, min: 0 },
  paymentTotal: { mean: 9920.1, max: 385156955, min: 0 },
  citationTotal: { mean: 275.77, max: 564072, min: 0 },
  microBloggingTotal: { mean: 19.63, max: 1204740, min: 0 },
  referralsReceivedCount: { mean: 5.98, max: 181048, min: 0 },
  referralsSentCount: { mean: 6.02, max: 65738, min: 0 },
  DRG_proceduresCount: { mean: 2718.33, max: 16174720, min: 0 },
  DRG_diagnosesCount: { mean: 3059.54, max: 8331254, min: 0 },
  DRG_proceduresCount_1_year: { mean: 318.96, max: 11191600, min: 0 },
  DRG_proceduresCount_2_year: { mean: 637.59, max: 13155558, min: 0 },
  DRG_proceduresCount_5_year: { mean: 1569.57, max: 16133306, min: 0 },
  DRG_diagnosesCount_1_year: { mean: 373.86, max: 5259859, min: 0 },
  DRG_diagnosesCount_2_year: { mean: 749, max: 7681059, min: 0 },
  DRG_diagnosesCount_5_year: { mean: 1796.4, max: 8326004, min: 0 },
  twitterFollowersCount: { mean: 2078.34, max: 3749221, min: 0 },
  twitterTweetCount: { mean: 1793.19, max: 264144, min: 0 },
  diagnosesCollection: { mean: 0, max: 0, min: 0 },
  proceduresCollection: { mean: 0, max: 0, min: 0 },
  DRG_proceduresUniqueCount: { mean: 454.61, max: 1569676, min: 0 },
  DRG_diagnosesUniqueCount: { mean: 405.06, max: 954061, min: 0 },
  DRG_proceduresUniqueCount_1_year: { mean: 65.06, max: 546601, min: 0 },
  DRG_proceduresUniqueCount_2_year: { mean: 118.25, max: 636975, min: 0 },
  DRG_proceduresUniqueCount_5_year: { mean: 270.13, max: 1219539, min: 0 },
  DRG_diagnosesUniqueCount_1_year: { mean: 60.4, max: 636975, min: 0 },
  DRG_diagnosesUniqueCount_2_year: { mean: 108.75, max: 395311, min: 0 },
  DRG_diagnosesUniqueCount_5_year: { mean: 241.62, max: 954061, min: 0 },
  [CTMS_TRIAL_COUNT_FIELD]: { mean: 2.39, max: 86, min: 1 },
  num_prescriptions: { mean: 4294.32, max: 7415960, min: 1 },
  num_prescriptions_1_year: { mean: 95.18, max: 1062404, min: 0 },
  num_prescriptions_2_year: { mean: 208.57, max: 2030681, min: 0 },
  num_prescriptions_5_year: { mean: 530.49, max: 7023391, min: 0 },
  patientDiversity: { mean: 404.66, max: 837619, min: 0 },
  totalPatientDocs: { mean: 497.44, max: 837619, min: 1 },
  ccsr: { mean: 0, max: 0, min: 0 },
  congressSessionCount: { mean: 0, max: 0, min: 0 },
  ccsr_px: { mean: 0, max: 0, min: 0 }
};

export const assetMeanValues: Readonly<Record<RootFieldsForScoring, number>> = {
  trialCount: 0.17,
  publicationCount: 6.4,
  congressCount: 0.2,
  paymentTotal: 9920.1,
  citationTotal: 275.77,
  microBloggingTotal: 19.63,
  referralsReceivedCount: 5.98,
  referralsSentCount: 6.02,
  DRG_proceduresCount: 2718.33,
  DRG_diagnosesCount: 3059.54,
  DRG_proceduresCount_1_year: 318.96,
  DRG_proceduresCount_2_year: 637.59,
  DRG_proceduresCount_5_year: 1569.57,
  DRG_diagnosesCount_1_year: 373.86,
  DRG_diagnosesCount_2_year: 749,
  DRG_diagnosesCount_5_year: 1796.4,
  twitterFollowersCount: 2078.34,
  twitterTweetCount: 1793.19,
  diagnosesCollection: 0,
  proceduresCollection: 0,
  DRG_proceduresUniqueCount: 454.61,
  DRG_diagnosesUniqueCount: 405.06,
  DRG_proceduresUniqueCount_1_year: 65.06,
  DRG_proceduresUniqueCount_2_year: 118.25,
  DRG_proceduresUniqueCount_5_year: 270.13,
  DRG_diagnosesUniqueCount_1_year: 60.4,
  DRG_diagnosesUniqueCount_2_year: 108.75,
  DRG_diagnosesUniqueCount_5_year: 241.62,
  [CTMS_TRIAL_COUNT_FIELD]: 2.39,
  num_prescriptions: 4294.32,
  num_prescriptions_1_year: 95.18,
  num_prescriptions_2_year: 208.57,
  num_prescriptions_5_year: 530.49,
  patientDiversity: 837619,
  totalPatientDocs: 497.44,
  ccsr: 0,
  congressSessionCount: 0,
  ccsr_px: 0
};

export const priorityBasedScoreScaleRanges: Readonly<
  Record<number, ScaleScoreTargetRange>
> = {
  0: { targetMin: 1, targetMax: 5 },
  1: { targetMin: 10000, targetMax: 20000 },
  2: { targetMin: 1000, targetMax: 5000 },
  3: { targetMin: 100, targetMax: 500 },
  4: { targetMin: 10, targetMax: 50 }
};

export const fieldPriorityMapForNewTLH1DefaultRanking: Readonly<
  Record<RootFieldsForScoring, number>
> = {
  trialCount: 1,
  publicationCount: 4,
  congressCount: 0,
  paymentTotal: 2,
  citationTotal: 0,
  microBloggingTotal: 0,
  referralsReceivedCount: 0,
  referralsSentCount: 0,
  DRG_diagnosesCount: 3,
  DRG_diagnosesCount_1_year: 3,
  DRG_diagnosesCount_2_year: 3,
  DRG_diagnosesCount_5_year: 3,
  DRG_diagnosesUniqueCount: 3,
  DRG_diagnosesUniqueCount_1_year: 3,
  DRG_diagnosesUniqueCount_2_year: 3,
  DRG_diagnosesUniqueCount_5_year: 3,
  DRG_proceduresCount: 0,
  DRG_proceduresCount_1_year: 0,
  DRG_proceduresCount_2_year: 0,
  DRG_proceduresCount_5_year: 0,
  DRG_proceduresUniqueCount: 0,
  DRG_proceduresUniqueCount_1_year: 0,
  DRG_proceduresUniqueCount_2_year: 0,
  DRG_proceduresUniqueCount_5_year: 0,
  num_prescriptions: 0,
  num_prescriptions_1_year: 0,
  num_prescriptions_2_year: 0,
  num_prescriptions_5_year: 0,
  twitterFollowersCount: 0,
  twitterTweetCount: 0,
  diagnosesCollection: 0,
  proceduresCollection: 0,
  patientDiversity: 3,
  totalPatientDocs: 3,
  [CTMS_TRIAL_COUNT_FIELD]: 0,
  ccsr: 0,
  congressSessionCount: 0,
  ccsr_px: 0
};

/**
 * Maps country field values from ElasticSearch to their corresponding country names
 * as used in the pipeline database.
 */
export const elasticCountryFieldToPipelineDBCountryMap: Record<string, string> =
  {
    Austria: "Austria",
    Brazil: "Brazil",
    Denmark: "Denmark",
    France: "France",
    Germany: "Germany",
    "Germany,United Kingdom,Norway": "Germany",
    Ireland: "Ireland",
    Italy: "Italy",
    "Norway,Denmark,Sweden,Finland": "Norway",
    Spain: "Spain",
    "United Kingdom": "United Kingdom",
    UK: "United Kingdom",
    GBR: "United Kingdom",
    US: "US",
    USA: "US",
    "United States": "US",
    "United States of America": "US"
  };

export const featureFlagDefaults: Readonly<
  Record<keyof KeywordSearchFeatureFlags, { key: string; default: boolean }>
> = {
  enableNewClaimsSearch: {
    key: "search.enable-new-claims-search",
    default: false
  },
  enableMinMaxResultCache: {
    key: "enable-min-max-result-cache",
    default: false
  },
  enableClaimsExports: {
    key: "export-matched-claims",
    default: false
  },
  enableQueryContextualPaymentsFiltering: {
    key: "enable-query-contextual-payments-filtering",
    default: false
  },
  enableQueryIntent: {
    key: "search.enable-query-intent",
    default: false
  },
  enableSpellSuggestion: {
    key: "search.enable-spell-suggestion",
    default: false
  },
  enableSearchHighlights: {
    key: "search.enable-highlights",
    default: false
  },
  enableConferenceIntentFilter: {
    key: "search.enable-conference-intent-filter",
    default: false
  },
  enableLocationConfidenceRanking: {
    key: "search.enable-location-confidence-ranking",
    default: false
  },
  enableCompletionSuggestion: {
    key: "search.enable-completion-suggestion",
    default: false
  },
  enableCTMSV2: {
    key: "enable-ctms-v2",
    default: false
  },
  enableIntentBasedKeywordSearch: {
    key: "intent-based-keyword-search",
    default: false
  },
  enableKWSearchPersonalisation: {
    key: "search.enable-kw-search-personalisation",
    default: false
  },
  enableTagsInElasticsearch: {
    key: "search.enable-tags-in-elasticsearch",
    default: false
  },
  enableUniquePatientCountForClaims: {
    key: "search.enable-unique-patient-count-for-claims",
    default: false
  },
  enableBrazilianClaims: {
    key: "enable-brazilian-claims",
    default: false
  },
  disableUniquePatientCountForOnlyProcedures: {
    key: "search.disable-unique-patient-count-for-only-procedures",
    default: true
  },
  enableEnhancedHasCTMSDataFilterBehavior: {
    key: "enable-enhanced-has-ctms-data-filter-behavior-for-people-and-institution-search",
    default: false
  },
  enableNestedIndicationFilter: {
    key: "search.enable-nested-indication-filter",
    default: false
  },
  enableAsianPacificIslanderDiversityBreakOut: {
    key: "enable-asian-pacific-islander-diversity-break-out",
    default: false
  },
  enablePenaltyForZeroTrialsDiversityRanking: {
    key: "enable-penalty-for-zero-trials-diversity-ranking",
    default: false
  },
  enableCcsrExclusionForMatchedCounts: {
    key: "enable-ccsr-exclusion-for-matched-counts",
    default: false
  },
  enableIndicationSynonymPrescriptionSearch: {
    key: "enable-indication-synonym-prescription-search",
    default: false
  },
  enableLocationFilterRegionRollup: {
    key: "enable-location-filter-region-rollup",
    default: false
  },
  enableInternalUsersForRankingDebug: {
    key: "enable-internal-users-for-ranking-debug",
    default: false
  },
  enableNewH1DefaultRanking: {
    key: "enable-new-h-1-default-ranking",
    default: false
  },
  enableAiqData: {
    key: "enable-aiq-data",
    default: false
  },
  enableBothOperatorForSearch: {
    key: "enable-both-operator-for-hcpu-search",
    default: false
  },
  enableLlmQueryParserForSearch: {
    key: "enable-llm-query-parser-for-search",
    default: false
  },
  enableNewGlobalLeaderTier: {
    key: "enable-new-global-leader-tier",
    default: false
  }
};

const MATCH_ALL: Readonly<QueryDslQueryContainer> = {
  match_all: {}
};

export const PUBLICATIONS_TYPE_TRIAL: Readonly<Array<string>> = [
  "Clinical Trial",
  "Controlled Clinical Trial",
  "Clinical Trial, Phase II",
  "Clinical Trial, Phase I",
  "Clinical Trial, Phase III",
  "Clinical Trial, Phase IV",
  "Clinical Trial Protocol",
  "Pragmatic Clinical Trial",
  "Adaptive Clinical Trial"
];

export const TRIAL_STATUS_TERMS: Readonly<Array<string>> = [
  "Active, not recruiting",
  "Completed",
  "Active"
];

export const ELASTICSEARCH_CURRENT_DATE = "now";
export const ELASTICSEARCH_5_YEARS_BACK = "now-5y";

const ZERO_WEIGHT_FUNCTION_SCORE = toWeightFunctionScore(0);
const EMPTY_NESTED_FILTERS: Array<QueryDslQueryContainer> = [];
const EMPTY_PARSED_QUERY: ParsedQueryTree = undefined;
export const COUNTRY_PERSONALISATION_BOOST = 1;
export const STATE_PERSONALISATION_BOOST = 0.03;

const CONGRESS_INTENT_BOOST = 3.0;
const NO_BOOST = 1.0;
const KEYWORD_SEARCH_TOKEN_THRESHOLD = 0.8;
const LOCATION_INTENT_BOOST = 3.16;
const LOCATION_INTENT_SCORE_THRESHOLD = 0.3;
const PROJECT_ID_BOOST = 10;

const EMPTY_ONBOARDING_DATA: OnboardingData = {
  indications: [],
  countries: [],
  states: []
};

export type QueryIndicationInfo = {
  finalIndicationFilter: Array<string>;
  indicationsMatchedToQuery: Array<string>;
  isIndication: boolean;
};

export type SearchContext = "keyword" | "bulk";
export type ReferralType = "referralsReceived" | "referralsSent";
export type ClaimType =
  | "diagnoses"
  | "procedures"
  | "prescriptions"
  | "ccsr"
  | "ccsr_px";
export type TwitterType = "Followers" | "Tweet";
export type IndicationOperator = "Indications_OR" | "Indications_AND";

export type LanguageDetector = (val: string) => Language;
export type ParsedQueryTree = ITree | string | undefined;

export type Translation = {
  languageCode: Language;
};

type Department = string;
type Title = string;
type Name = string;

type DepartmentTranslation = Translation & {
  department: Department;
};

type TitlesTranslation = Translation & {
  titles: Array<Title>;
};

type NameTranslation = Translation & {
  name: Name;
};

type Address = ID & {
  city: string;
  country: string;
  country_code: string;
  postal_code: string;
  region: string;
  region_code: string;
  street1: string;
  street2: string;
  street3: string;
  country_level_regions?: string[];
  state_level_regions?: string[];
  city_level_regions?: string[];
};

type ID = {
  id: number;
};

export type AffiliationInstitutionNestedDocument = ID & {
  address: Address;
  addressTranslations?: Array<Address & Translation>;
  filters: {
    city?: string;
    country?: string;
    county?: string;
    district?: string;
    postal_code?: string;
    region?: string;
  };
  isIol: boolean;
  location: {
    lat: string;
    lon: string;
  };
  lat_long_precision?: number;
  masterOrganizationId: number;
  name: Name;
  nameTranslations: Array<NameTranslation>;
  ultimateParentId?: number | string;
  ultimateParentInstitution: UltimateParentInstitution;
  type: string;
  orgTypes: string;
  region: string;
};

export type UltimateParentInstitution = ID & {
  isIol: boolean;
  masterOrganizationId: string;
  name: string;
  type: string;
  nameTranslations: Array<NameTranslation>;
  region: string;
};

export type AffiliationNestedDocument = ID & {
  accuracyScore: number;
  tlAccuracyScore: number;
  otherSignals: Array<AffiliationSignal>;
  claimsNumbers: number;
  claimsStates: Array<string>;
  department?: Department;
  departmentTranslations: Array<DepartmentTranslation>;
  institution: AffiliationInstitutionNestedDocument;
  isCurrent: boolean;
  titleTranslations: Array<TitlesTranslation>;
  titles: Array<Title>;
  type: string;
};

export type CongressNestedDocument = {
  id: string;
  role: string;
  name_eng: string;
  title_eng: string;
  title_cmn?: string;
  title_jpn?: string;
};

export type PrescriptionNestedDocument = {
  generic_name: string;
  patient_count: number;
  patient_count_1_year?: number;
  patient_count_2_year?: number;
  patient_count_5_year?: number;
  num_prescriptions: number;
  num_prescriptions_1_year?: number;
  num_prescriptions_2_year?: number;
  num_prescriptions_5_year?: number;
};

export type Location = {
  city_eng: string;
  state_eng: string;
  zipCode5_eng: string;
  state_cmn: string;
  state_jpn: string;
  languageCode: string;
  token: string;
  zipCode5_cmn: string;
  zipCode5_jpn: string;
  country_eng: string;
  zipCodeFull: string;
  country_cmn: string;
  stateCode: string;
  city_cmn: string;
  country_jpn: string;
  city_jpn: string;
};

export type HCPDocument = {
  id: string;
  h1dn_id: string;
  orcidId?: string;
  name_eng: string;
  name_cmn: string;
  name_jpn: string;
  firstName_eng: string;
  firstName_cmn: string;
  firstName_jpn: string;
  middleName_eng: string;
  middleName_cmn: string;
  middleName_jpn: string;
  lastName_eng: string;
  lastName_cmn: string;
  lastName_jpn: string;
  isFacultyOpinionsMember?: boolean;
  hasCtms?: boolean;
  inCtmsNetwork?: boolean;
  affiliations?: Array<AffiliationNestedDocument>;
  prescriptions?: Array<PrescriptionNestedDocument>;
  num_prescriptions?: number;
  num_prescriptions_1_year?: number;
  num_prescriptions_2_year?: number;
  num_prescriptions_5_year?: number;
  locations?: Array<Location>;
  designations: Array<string>;
  emails: Array<string>;
  specialty_eng: Array<string>;
  specialty_cmn: Array<string>;
  specialty_jpn: Array<string>;
  citationTotal: number;
  congressCount: number;
  paymentTotal: number;
  referralsSentCount: number;
  referralsReceivedCount: number;
  microBloggingTotal: number;
  trialCount: number;
  trialEnrollmentRate: number;
  presentWorkInstitutionCount: number;
  publicationCount: number;
  totalWorks: number;
  DRG_diagnosesCount: number;
  DRG_proceduresCount: number;
  patientsDiversityCount: number | null;
  projectIds: Array<string>;
  patientsDiversity: Array<{
    sex: Array<{
      count: number;
      value: string;
      percent?: number;
    }>;
    races_eng: Array<{
      race: string;
      count: number;
      rank: number;
      percent?: number;
    }>;
    age: Array<{
      count: number;
      range: string;
      percent?: number;
    }>;
  }> | null;
  patientsDiversityRatio: {
    americanIndianOrAlaskaNative?: number;
    asianPacificIslander?: number;
    asian?: number;
    pacificIslander?: number;
    blackNonHispanic?: number;
    hispanic?: number;
    notIdentified?: number;
    whiteNonHispanic?: number;
    indigenousBrazil?: number;
    mixedBrazil?: number;
  };
  patientsDiversityPercentile: number;
  providerDiversity: Array<{
    sex: Array<string>;
    races_eng: Array<string>;
    languagesSpoken: Array<string>;
  }> | null;
  totalPatientCount: number | null;
  digitalRank?: number | null;
  top1PercentileDigitalRank?: boolean | null;
  top10PercentileDigitalRank?: boolean | null;
  twitterFollowersCount?: number | null;
  twitterTweetCount?: number | null;
  DRG_diagnosesCount_1_year?: number | null;
  DRG_diagnosesCount_2_year?: number | null;
  DRG_diagnosesCount_5_year?: number | null;
  DRG_proceduresCount_1_year?: number | null;
  DRG_proceduresCount_2_year?: number | null;
  DRG_proceduresCount_5_year?: number | null;
  isInactive?: boolean;
  isIndustry?: boolean;
  hasSocietyAffiliation?: boolean;
  totalPatientDocs?: number;
  overallRisingScore?: number;
  congress?: Array<CongressNestedDocument>;
} & DRG_diagnosesCount &
  DRG_proceduresCount;

export type DRG_diagnosesCount = {
  DRG_diagnosesCount: number;
  DRG_diagnosesCount_1_year?: number | null;
  DRG_diagnosesCount_2_year?: number | null;
  DRG_diagnosesCount_5_year?: number | null;
  DRG_diagnosesUniqueCount?: number;
  DRG_diagnosesUniqueCount_1_year?: number | null;
  DRG_diagnosesUniqueCount_2_year?: number | null;
  DRG_diagnosesUniqueCount_5_year?: number | null;
};

export type DRG_proceduresCount = {
  DRG_proceduresCount: number;
  DRG_proceduresCount_1_year?: number | null;
  DRG_proceduresCount_2_year?: number | null;
  DRG_proceduresCount_5_year?: number | null;
  DRG_proceduresUniqueCount?: number;
  DRG_proceduresUniqueCount_1_year?: number | null;
  DRG_proceduresUniqueCount_2_year?: number | null;
  DRG_proceduresUniqueCount_5_year?: number | null;
};

export type PrescriptionsCount = {
  num_prescriptions?: number;
  num_prescriptions_1_year?: number | null;
  num_prescriptions_2_year?: number | null;
  num_prescriptions_5_year?: number | null;
};

export type OnlyIdDocument = Pick<HCPDocument, "id" | "h1dn_id">;

export type RootFieldsForScoring =
  | "trialCount"
  | "publicationCount"
  | "congressCount"
  | "paymentTotal"
  | "citationTotal"
  | "microBloggingTotal"
  | "referralsReceivedCount"
  | "referralsSentCount"
  | keyof DRG_diagnosesCount
  | keyof DRG_proceduresCount
  | keyof PrescriptionsCount
  | "twitterFollowersCount"
  | "twitterTweetCount"
  | "diagnosesCollection"
  | "proceduresCollection"
  | "patientDiversity"
  | "totalPatientDocs"
  | typeof CTMS_TRIAL_COUNT_FIELD
  | "ccsr"
  | "congressSessionCount"
  | "ccsr_px";

type OptionalQueries = {
  needToAddParsedQueryToCongress: boolean;
  needToAddParsedQueryToPayments: boolean;
  needToAddParsedQueryToPublications: boolean;
  needToAddParsedQueryToTrials: boolean;
  needToAddHighlights: boolean;
};

const SOURCE_FIELDS: Readonly<Array<string>> = [
  "totalWorks",
  "id",
  "h1dn_id",
  "name_eng",
  "name_cmn",
  "name_jpn",
  "firstName_eng",
  "firstName_jpn",
  "firstName_cmn",
  "middleName_eng",
  "middleName_cmn",
  "middleName_jpn",
  "lastName_eng",
  "lastName_jpn",
  "lastName_cmn",
  "isFacultyOpinionsMember",
  "hasCtms",
  "inCtmsNetwork",
  "specialty_eng",
  "publicationCount",
  "citationTotal",
  "trialCount",
  "paymentTotal",
  "congressCount",
  "microBloggingTotal",
  "DRG_diagnosesCount",
  "DRG_diagnosesCount_1_year",
  "DRG_diagnosesCount_2_year",
  "DRG_diagnosesCount_5_year",
  "DRG_proceduresCount",
  "DRG_proceduresCount_1_year",
  "DRG_proceduresCount_2_year",
  "DRG_proceduresCount_5_year",
  "referralsSentCount",
  "referralsReceivedCount",
  "designations",
  "patientsDiversityCount",
  "patientsDiversity",
  "patientsDiversityRatio",
  "providerDiversity",
  "patientsDiversityPercentile",
  "totalPatientCount",
  "digitalRank",
  "top1PercentileDigitalRank",
  "top10PercentileDigitalRank",
  "twitterFollowersCount",
  "twitterTweetCount",
  "affiliations",
  "locations",
  "DRG_diagnosesUniqueCount",
  "DRG_diagnosesUniqueCount_1_year",
  "DRG_diagnosesUniqueCount_2_year",
  "DRG_diagnosesUniqueCount_5_year",
  "DRG_proceduresUniqueCount",
  "DRG_proceduresUniqueCount_1_year",
  "DRG_proceduresUniqueCount_2_year",
  "DRG_proceduresUniqueCount_5_year",
  "isIndustry",
  "isInactive",
  "hasSocietyAffiliation",
  "totalPatientDocs",
  "projectIds",
  "num_prescriptions",
  "num_prescriptions_1_year",
  "num_prescriptions_2_year",
  "num_prescriptions_5_year",
  "trialEnrollmentRate",
  "overallRisingScore"
];

export const SORT_TIEBREAKER: SortCombinations[] = [
  {
    _score: {
      order: "desc"
    }
  },
  {
    id: {
      order: "asc"
    }
  },
  {
    h1dn_id: {
      order: "asc"
    }
  }
];

const trialsQueryFields = [
  `trials.officialTitle_${ENGLISH}`,
  `trials.briefTitle_${ENGLISH}`,
  `trials.conditions_${ENGLISH}`,
  `trials.interventions_${ENGLISH}`,
  `trials.keywords_${ENGLISH}`,
  `trials.summary_${ENGLISH}`,
  `trials.indication`
];

const publicationsQueryFieldBaseNames = [
  "publications.keywords",
  "publications.publicationAbstract",
  "publications.title"
];
export const publicationsQueryFields = [
  ...publicationsQueryFieldBaseNames.map((field) => `${field}_eng`),
  ...publicationsQueryFieldBaseNames.map((field) => `${field}_cmn`),
  ...publicationsQueryFieldBaseNames.map((field) => `${field}_jpn`)
];

const congressSessionsQueryFields = [
  "congress.keywords_eng",
  "congress.title_eng",
  "congress.role.search"
];
const congressesQueryFields = [
  "congress.keywords_eng",
  "congress.title_eng",
  "congress.organizer_eng.search",
  "congress.name_eng.search",
  "congress.role.search"
];
const paymentsQueryFields = [
  "payments.associatedDrugOrDevice.autocomplete_search",
  "payments.payerCompany.autocomplete_search"
];

export const PATIENT_DIVERSITY_RATIO = [
  "(doc['patientsDiversityRatio.americanIndianOrAlaskaNative'].size() == 0 ? 0 : doc['patientsDiversityRatio.americanIndianOrAlaskaNative'].value)",
  "(doc['patientsDiversityRatio.asianPacificIslander'].size() == 0 ? 0 : doc['patientsDiversityRatio.asianPacificIslander'].value)",
  "(doc['patientsDiversityRatio.blackNonHispanic'].size() == 0 ? 0 : doc['patientsDiversityRatio.blackNonHispanic'].value)",
  "(doc['patientsDiversityRatio.hispanic'].size() == 0 ? 0 : doc['patientsDiversityRatio.hispanic'].value)"
];

// TODO: replace this to use LanguageDetectService.getLanguageDetector
export function getLanguageDetector(
  userPreferredLanguage: string = ENGLISH
): LanguageDetector {
  return (query: string): Language => {
    const detectedLanguage = franc(query, {
      minLength: 1,
      only: [ENGLISH, CHINESE, JAPANESE]
    });

    if (detectedLanguage === CHINESE && userPreferredLanguage === JAPANESE) {
      return JAPANESE;
    }

    return detectedLanguage === UNDETERMINED
      ? ENGLISH
      : (detectedLanguage as Language);
  };
}

function removeIllegalCharsForESFieldNames(field: string): string {
  const illegalFieldNameCharacters = /\\|\.|'|"|>|<|_|,|#/g;
  return field.replace(illegalFieldNameCharacters, "");
}

function toQueryWrappedFunctionScore(
  queryToWrap: QueryDslQueryContainer,
  field: RootFieldsForScoring
): QueryDslQueryContainer {
  return {
    function_score: {
      query: queryToWrap,
      boost_mode: BOOST_MODE_REPLACE,
      functions: [
        {
          script_score: toFunctionScriptScoreForH1DefaultRanking(field)
        }
      ]
    }
  };
}

function toFunctionScriptScoreForH1DefaultRanking(
  key: RootFieldsForScoring
): QueryDslScriptScoreFunction {
  const priority = fieldPriorityMapForNewTLH1DefaultRanking[key];
  const { targetMax, targetMin } = priorityBasedScoreScaleRanges[priority];
  const { max, mean, min } = assetAggregationValues[key];

  return {
    script: {
      source: `
            double score = _score;

            // Define the original minimum and maximum score
            double originalMin = params.min;
            double originalMax = params.max;

            // Define the target range
            double targetMin = params.targetMin;
            double targetMax = params.targetMax;

            // Scale the score to the range [targetMin, targetMax]
            double scaledScore = targetMin + ((score - originalMin) / (originalMax - originalMin)) * (targetMax - targetMin);

            // Return the scaled score
            return scaledScore;
          `,
      params: {
        mean,
        max,
        min,
        targetMax,
        targetMin
      }
    }
  };
}

function getNewH1DefaultAssetFunctionScore(
  parsedQueryTree: ParsedQueryTree,
  nestedFilters: Array<QueryDslQueryContainer>,
  field: RootFieldsForScoring,
  weight = 0,
  intentBoost?: number,
  hasPatientClaimsFilter?: boolean
): QueryDslFunctionScoreContainer {
  const fieldPriorityRank = fieldPriorityMapForNewTLH1DefaultRanking[field];
  const { targetMin, targetMax } =
    priorityBasedScoreScaleRanges[fieldPriorityRank];

  return !hasPatientClaimsFilter &&
    isEmptyClauseQuery(parsedQueryTree, nestedFilters)
    ? getScaledFunctionScoreForQueryWithoutMatchCriterion(
        assetAggregationValues[field].mean,
        assetAggregationValues[field].max,
        assetAggregationValues[field].min,
        targetMax,
        targetMin,
        field,
        weight,
        intentBoost
      )
    : getScaledFunctionScoreForQueryWithMatchCriterion(
        assetAggregationValues[field].mean,
        assetAggregationValues[field].max,
        assetAggregationValues[field].min,
        targetMax,
        targetMin,
        weight,
        intentBoost
      );
}

function getAssetFunctionScore(
  parsedQueryTree: ParsedQueryTree,
  nestedFilters: Array<QueryDslQueryContainer>,
  field: RootFieldsForScoring,
  weight = 0,
  intentBoost?: number,
  hasPatientClaimsFilter?: boolean,
  useAssetCountAsScore?: boolean
): QueryDslFunctionScoreContainer {
  if (useAssetCountAsScore) {
    return {
      script_score: {
        script: {
          source: "(_score)*params.weight",
          params: {
            weight
          }
        }
      },
      weight: intentBoost
    };
  }
  return !hasPatientClaimsFilter &&
    isEmptyClauseQuery(parsedQueryTree, nestedFilters)
    ? getFunctionScoreForQueryWithoutMatchCriterion(
        assetAggregationValues[field].mean,
        weight,
        field
      )
    : getFunctionScoreForQueryWithMatchCriterion(
        assetAggregationValues[field].mean,
        weight,
        intentBoost
      );
}

function getSaturationScriptFunction(
  saturationParam: number
): QueryDslFunctionScoreContainer {
  return {
    script_score: {
      script: {
        source: `saturation(_score, params['saturation_param'])`,
        params: {
          saturation_param: saturationParam
        }
      }
    }
  };
}

function getRankFeatureQuery(rankField: string): QueryDslQueryContainer {
  return {
    rank_feature: {
      field: rankField,
      linear: {}
    }
  };
}

function toWeightFunctionScore(weight: number): QueryDslFunctionScoreContainer {
  return {
    weight
  };
}
function getFunctionScoreForQueryWithMatchCriterion(
  mean: number,
  weight: number,
  intentBoost?: number
): QueryDslFunctionScoreContainer {
  return {
    script_score: {
      script: {
        source: "Math.log((_score/params.mean) + 1)*params.weight",
        params: {
          mean,
          weight
        }
      }
    },
    weight: intentBoost
  };
}

function getFunctionScoreForQueryWithoutMatchCriterion(
  mean: number,
  weight: number,
  field: RootFieldsForScoring
): QueryDslFunctionScoreContainer {
  return {
    script_score: {
      script: {
        source:
          "doc[params.field].size()==0 ? 0 : Math.log((doc[params.field].value/params.mean) + 1)*params.weight",
        params: {
          mean,
          weight,
          field
        }
      }
    }
  };
}

function getScaledFunctionScoreForQueryWithoutMatchCriterion(
  mean: number,
  max: number,
  min: number,
  targetMax: number,
  targetMin: number,
  field: RootFieldsForScoring,
  weight = 0,
  intentBoost?: number
): QueryDslFunctionScoreContainer {
  return {
    script_score: {
      script: {
        source: `
            // If weight is 0, return 0 score
            if (params.weight == 0) {
              return 0;
            }

            if(doc[params.field].size()==0){
              return 0;
            }

            double score = doc[params.field].value;

            // Define the original minimum and maximum score
            double originalMin = params.min;
            double originalMax = params.max;

            // Define the target range
            double targetMin = params.targetMin;
            double targetMax = params.targetMax;

            // Scale the score to the range [targetMin, targetMax]
            double scaledScore = targetMin + ((score - originalMin) / (originalMax - originalMin)) * (targetMax - targetMin);

            // Return the scaled score
            return scaledScore;
        `,
        params: {
          min,
          max,
          mean,
          targetMin,
          targetMax,
          field,
          weight
        }
      }
    },
    weight: intentBoost
  };
}

function getScaledFunctionScoreForQueryWithMatchCriterion(
  mean: number,
  max: number,
  min: number,
  targetMax: number,
  targetMin: number,
  weight = 0,
  intentBoost?: number
): QueryDslFunctionScoreContainer {
  return {
    script_score: {
      script: {
        source: `
            // If weight is 0, return 0 score
            if (params.weight == 0) {
              return 0;
            }

            double score = _score;

            // Define the original minimum and maximum score
            double originalMin = params.min;
            double originalMax = params.max;

            // Define the target range
            double targetMin = params.targetMin;
            double targetMax = params.targetMax;

            // Scale the score to the range [targetMin, targetMax]
            double scaledScore = targetMin + ((score - originalMin) / (originalMax - originalMin)) * (targetMax - targetMin);

            // Return the scaled score
            return scaledScore;
        `,
        params: {
          min,
          max,
          mean,
          targetMin,
          targetMax,
          weight
        }
      }
    },
    weight: intentBoost
  };
}

function isEmptyClauseQuery(
  parsedQueryTree: ParsedQueryTree,
  nestedFilters: QueryDslQueryContainer[]
) {
  return !parsedQueryTree && nestedFilters.length === 0;
}

function anyFiltersWereSupplied(filters: QueryDslQueryContainer[]) {
  return filters.length > 1;
}

function hasSource(doc: SearchHit<OnlyIdDocument>) {
  return !!doc._source;
}

class NoQueryOrFiltersError extends Error {}

export const KeywordSearchScoringFunctions: Readonly<
  Record<HCPSearchScoringFunctions, QueryDslFunctionScoreContainer[]>
> = {
  [HCPSearchScoringFunctions.DigitalRelevance]: [
    buildScriptScore(DIGITAL_RELEVANCE_SCORE_SCRIPT)
  ]
};

export function getKeywordSearchScoringFunction(
  scoringFunction?: HCPSearchScoringFunctions | null,
  isTrialPerformanceSort?: boolean
) {
  if (isTrialPerformanceSort) {
    return TRIAL_PERFORMANCE_SCORE_SCRIPTS.map((script) =>
      buildScriptScore(script)
    );
  }

  switch (scoringFunction) {
    case HCPSearchScoringFunctions.DigitalRelevance: {
      return KeywordSearchScoringFunctions.DigitalRelevance;
    }
  }

  return undefined;
}

export interface DocCountClaimsBucket {
  doc_count: number;
  filteredClaims: {
    doc_count: number;
    counts: {
      value: number;
    };
  };
}
export interface ClaimsLocationAggregationDocCountBucket {
  key: string;
  doc_count: number;
  allClaims: {
    procedures: DocCountClaimsBucket;
    diagnoses: DocCountClaimsBucket;
  };
}
export type MatchedClaimCountsFromIEForExports = {
  diagnosesCountMap: Dictionary<
    Dictionary<{
      count: number;
      scheme: string | undefined;
      description: string | undefined;
    }>
  >;
  proceduresCountMap: Dictionary<
    Dictionary<{
      count: number;
      scheme: string | undefined;
      description: string | undefined;
    }>
  >;
};

export function getLeaderScoreField(
  suppliedFilters: FilterInterface,
  shouldUseIndicationScore: boolean
) {
  const isRisingStar = !!suppliedFilters.isRisingStar?.value;
  const prefix = shouldUseIndicationScore
    ? "indications.indication"
    : isRisingStar
    ? "overall"
    : "";
  const risingSuffix = isRisingStar ? "Rising" : "";
  const leaderTypes: { key: keyof FilterInterface; field: string }[] = [
    { key: "isEducatorLeader", field: `ScoreForEducator` },
    { key: "isTrialLeader", field: `ScoreFor${risingSuffix}Trial` },
    { key: "isScholarLeader", field: `ScoreForScholar` },
    { key: "isPublicationLeader", field: `ScoreFor${risingSuffix}Pub` },
    { key: "isDOLeader", field: `ScoreForDigital` }
  ];

  for (const leaderType of leaderTypes) {
    if (
      leaderType.key in suppliedFilters &&
      (suppliedFilters[leaderType.key] as NullableValueFilter<boolean>).value
    ) {
      if (prefix.length > 0) {
        return `${prefix}${leaderType.field}`;
      }
    }
  }
  const hasIndications = !!suppliedFilters.indications?.values.length;
  const defaultIndicationScore = hasIndications
    ? "indications.indicationScore"
    : "";
  // Default scoring fields if no leader filter is applied
  if (shouldUseIndicationScore) {
    return isRisingStar
      ? "indications.indicationRisingScore"
      : defaultIndicationScore;
  } else {
    return isRisingStar ? "overallRisingScore" : "";
  }
}

@Service()
@RpcService()
export class KeywordSearchResourceServiceRewrite
  extends RpcResourceService
  implements KeywordSearchRewriteResource
{
  private readonly logger = createLogger(this);
  private peopleIndex: string;
  constructor(
    config: ConfigService,
    private elasticService: ElasticSearchService,
    private queryParserService: QueryParserService,
    private queryUnderstandingServiceClient: QueryUnderstandingServiceClient,
    private keywordFilterClauseBuilderService: KeywordFilterClauseBuilderService,
    private keywordSearchResponseAdapterService: KeywordSearchResponseAdapterService,
    private parsedQueryTreeToElasticsearchQueriesService: ParsedQueryTreeToElasticsearchQueriesService,
    private featureFlagsService: FeatureFlagsService,
    private spellSuggesterService: SpellSuggesterService,
    private conferenceFilterService: ConferenceQueryIntentFilterService,
    private completionSuggesterService: CompletionSuggesterService,
    private keywordSearchMatchedCountService: KeywordSearchMatchedCountService,
    private userOnboardingDataService: UserOnboardingDataService,
    private rulesParserService: RulesParserService,
    private savedTerritoryService: SavedTerritoryResourceClient,
    private patientDiversityService: PatientDiversityStatsService,
    private indicationsTreeSearchService: IndicationsTreeSearchService,
    private entitySearchProcessorService: EntitySearchProcessorService,
    private ccsrIcdMappingRepository: CcsrIcdMappingRepository,
    private ccsrPxMappingRepository: CcsrPxMappingRepository,
    private claimCodeService: ClaimCodeService
  ) {
    super(
      config.searchRpcSigningKey,
      RPC_NAMESPACE_KOL_SEARCH_REWRITE,
      config.searchRedisOptions
    );
    this.peopleIndex = config.elasticPeopleIndex;
  }

  @RpcMethod()
  @Trace("h1-search.keyword.isReady")
  async isReady(): Promise<boolean> {
    return true;
  }

  @RpcMethod()
  @Trace("h1-search.keyword.search")
  async keywordSearchRewrite(
    input: Readonly<KeywordSearchInput>
  ): Promise<PersonSearchResponse> {
    try {
      const keywordSearchFeatureFlags = await this.getFeatureFlagValues(input);

      // Process entities from the query first using LLM parser in QUS
      const entityProcessingResponse =
        await this.entitySearchProcessorService.processSearchWithEntityDetection(
          input,
          keywordSearchFeatureFlags
        );

      // Only continue with search if it should be executed
      if (
        entityProcessingResponse &&
        !entityProcessingResponse.shouldExecuteSearch
      ) {
        this.logger.info(
          `Search not executed. Detected entity searchFor: ${entityProcessingResponse.detectedEntities?.searchFor}`
        );
        return this.keywordSearchResponseAdapterService.emptyResponse(
          input.page
        );
      }
      //Added optional chaining to entityProcessingResponse since it is undefined in unit tests
      const modifiedInput = entityProcessingResponse?.modifiedInput ?? input;
      const isLLMGenerated =
        entityProcessingResponse?.shouldExecuteSearch &&
        entityProcessingResponse.detectedEntities?.searchFor.toLowerCase() ===
          "people";
      let onboardingData = EMPTY_ONBOARDING_DATA;
      if (modifiedInput.userId) {
        onboardingData = await this.userOnboardingDataService.getOnboardingData(
          modifiedInput.userId,
          modifiedInput.projectId
        );
      }

      const {
        response,
        synonyms,
        queryIntents,
        spellSuggesterResult,
        assetToClause,
        filters,
        suggestedFilterValues,
        indicationsSynonyms,
        indicationsIcdCodeSynonyms,
        keywordIcdCodeSynonyms,
        territories
      } = await this.search<HCPDocument>(
        "keyword",
        modifiedInput,
        keywordSearchFeatureFlags,
        onboardingData,
        isLLMGenerated
      );
      let matchedCountResults,
        l1IndicationsResponse,
        topL3IndicationsResponse,
        patientDiversityDistribution = {};
      this.logKeywordSearchResponse(response);
      if (
        response.hits.total &&
        ((response.hits.total as SearchTotalHits).value > 0 ||
          (response.hits.total as number) > 0)
      ) {
        [
          matchedCountResults,
          l1IndicationsResponse,
          topL3IndicationsResponse,
          patientDiversityDistribution
        ] =
          await this.keywordSearchMatchedCountService.getMatchedCountForAssets(
            modifiedInput,
            response.hits,
            filters,
            assetToClause,
            keywordSearchFeatureFlags
          );
      }
      const matchedClaimsCountFromIEForExport =
        await this.getMatchedClaimsCountForIEForExports(
          modifiedInput,
          response,
          keywordSearchFeatureFlags
        );
      // TODO : Need not be called for claims excel request.
      const areMatchedClaimCountsPresent: boolean =
        Object.keys(matchedClaimsCountFromIEForExport.diagnosesCountMap)
          .length > 0 ||
        Object.keys(matchedClaimsCountFromIEForExport.proceduresCountMap)
          .length > 0;
      const adaptedResponse =
        await this.keywordSearchResponseAdapterService.adapt(
          modifiedInput.page,
          modifiedInput.language || ENGLISH,
          response.hits,
          response.aggregations,
          synonyms,
          modifiedInput.suppliedFilters,
          queryIntents,
          indicationsSynonyms,
          keywordSearchFeatureFlags,
          keywordIcdCodeSynonyms,
          indicationsIcdCodeSynonyms,
          spellSuggesterResult,
          matchedCountResults,
          modifiedInput,
          patientDiversityDistribution,
          l1IndicationsResponse,
          topL3IndicationsResponse,
          territories,
          areMatchedClaimCountsPresent
            ? matchedClaimsCountFromIEForExport
            : undefined
        );

      return {
        ...adaptedResponse,
        aggregations: response.aggregations,
        suggestedFilters: suggestedFilterValues
      };
    } catch (err) {
      if (err instanceof NoQueryOrFiltersError) {
        this.logger.error("No query or filter error");
        return this.keywordSearchResponseAdapterService.emptyResponse(
          input.page
        );
      }
      if (err instanceof Error) {
        this.logger.error(err, err.message);
      }
      throw err;
    }
  }

  @RpcMethod()
  @Trace("h1-search.keyword.bulk")
  async bulkSearchRewrite(
    input: Readonly<KeywordSearchInput>
  ): Promise<Array<string>> {
    try {
      const keywordSearchFeatureFlags = await this.getFeatureFlagValues(input);
      let onboardingData = EMPTY_ONBOARDING_DATA;
      if (input.userId) {
        onboardingData = await this.userOnboardingDataService.getOnboardingData(
          input.userId,
          input.projectId
        );
      }
      const { response } = await this.search<OnlyIdDocument>(
        "bulk",
        input,
        keywordSearchFeatureFlags,
        onboardingData
      );

      this.logBulkSearchResponse(response);

      return response.hits.hits.filter(hasSource).map((hit) => hit._source!.id);
    } catch (err) {
      if (err instanceof NoQueryOrFiltersError) {
        return [];
      }
      if (err instanceof Error) {
        this.logger.error(err, err.message);
      }
      throw err;
    }
  }

  @RpcMethod()
  @Trace("h1-search.keyword.bulkEntitySearch")
  async bulkEntitySearch(
    input: Readonly<KeywordSearchInput>
  ): Promise<Array<Entity>> {
    try {
      const keywordSearchFeatureFlags = await this.getFeatureFlagValues(input);
      let onboardingData = EMPTY_ONBOARDING_DATA;
      if (input.userId) {
        onboardingData = await this.userOnboardingDataService.getOnboardingData(
          input.userId,
          input.projectId
        );
      }
      const { response } = await this.search<OnlyIdDocument>(
        "bulk",
        input,
        keywordSearchFeatureFlags,
        onboardingData
      );

      this.logBulkSearchResponse(response);

      return response.hits.hits.filter(hasSource).map((hit) => {
        if (hit._source!.id) {
          return {
            entityType: UserEntities.EntityType.PERSON,
            entityId: hit._source!.id
          };
        }

        return {
          entityType: UserEntities.EntityType.H1DN,
          entityId: hit._source!.h1dn_id
        };
      });
    } catch (err) {
      if (err instanceof NoQueryOrFiltersError) {
        return [];
      }
      if (err instanceof Error) {
        this.logger.error(err, err.message);
      }
      throw err;
    }
  }

  @RpcMethod()
  @Trace("h1-search.keyword.completion")
  async keywordCompletion(
    input: Readonly<CompletionSuggesterInput>
  ): Promise<string[]> {
    const keywordSearchFeatureFlags = await this.getFeatureFlagValues(input);

    if (keywordSearchFeatureFlags.enableCompletionSuggestion) {
      return await this.completionSuggesterService.getCompletionSuggestions(
        input.query
      );
    } else {
      return [];
    }
  }

  private async getHcpDocIdAndPatientDiversityRatio(hcpId: string) {
    const query: QueryDslQueryContainer = {
      term: {
        id: hcpId
      }
    };

    const request: estypes.SearchRequest = {
      index: this.peopleIndex,
      query,
      _source: ["patientsDiversityRatio"]
    };

    const data = await this.elasticService.query<{
      patientsDiversityRatio?: PatientsDiversityRaceInfo;
    }>(request);

    if (data.hits.hits.length) {
      return {
        docId: data.hits.hits[0]._id,
        patientsDiversityRatio:
          data.hits.hits[0]._source?.patientsDiversityRatio
      };
    }
    this.logger.error(`HCP ${hcpId} not found`);
    return { docId: undefined, patientsDiversityRatio: undefined };
  }

  @RpcMethod()
  @Trace("h1-search.hcp.diversity.stats")
  async hcpDiversityStats(
    input: Readonly<HcpDiversityStatsInput>
  ): Promise<PatientDiversityStatsResponse> {
    const { docId: hcpId, patientsDiversityRatio } =
      await this.getHcpDocIdAndPatientDiversityRatio(input.hcpId);
    return this.patientDiversityService.getPatientDiversityStats(
      input,
      hcpId!,
      this.peopleIndex,
      patientsDiversityRatio
    );
  }

  private truncateQuery(query?: string) {
    if (query && !HAS_ADVANCED_OPERATORS.test(query)) {
      return query.trim().split(/\s+/).slice(0, MAXIMUM_QUERY_TOKENS).join(" ");
    }
    return query;
  }

  private sanitizeQuery(query?: string) {
    return query?.replace(/[´]/g, "'");
  }

  private inputHasPatientClaimsFilter(
    suppliedFilters: FilterInterface,
    featureFlags: KeywordSearchFeatureFlags
  ) {
    const enabled =
      !!(
        suppliedFilters.patientClaimsFilter ??
        suppliedFilters.patientClaimsFilterV2
      ) ||
      ((suppliedFilters.patientsDiversity.race.values.length > 0 ||
        suppliedFilters.patientsDiversity.ageRange.values.length > 0 ||
        suppliedFilters.patientsDiversity.sex.values.length > 0) &&
        suppliedFilters.claims.showUniquePatients?.value === true);
    return enabled;
  }

  private inputHasClaimsFilterAsQualifyingConditionForPatients(
    suppliedFilters: FilterInterface,
    featureFlags: KeywordSearchFeatureFlags
  ) {
    const enabled =
      !!(
        suppliedFilters.claims.ccsr?.values.length ||
        suppliedFilters.claims.diagnosesICD.values.length ||
        suppliedFilters.claims.diagnosesICDMinCount.value ||
        suppliedFilters.claims.diagnosesICDMaxCount?.value
      ) && suppliedFilters.claims.showUniquePatients?.value === true;
    return enabled;
  }

  //This function checks whether we need score to be summed across icd and ccsr matches.
  private useMatchedCountAsAssetScoreForIcdOrCcsr(
    input: KeywordSearchInput,
    parsedQueryTree: ParsedQueryTree
  ) {
    const ccsrApplied = !!input.suppliedFilters.claims.ccsr?.values.length;
    const icdApplied: boolean =
      input.suppliedFilters.claims.diagnosesICD.values.length > 0;
    const toggleUniquePatients =
      !!input.suppliedFilters.claims.showUniquePatients?.value;
    const isAnyOtherSortAppled = this.hasPositiveValueExceptKey(
      input.sortBy,
      "diagnoses"
    );
    return (
      (icdApplied || !!parsedQueryTree) &&
      ccsrApplied &&
      !toggleUniquePatients &&
      input.sortBy.diagnoses == 1 &&
      !isAnyOtherSortAppled
    );
  }
  private useMatchedCountAsAssetScoreForProceduresOrCcsrPx(
    input: KeywordSearchInput,
    parsedQueryTree: ParsedQueryTree
  ) {
    const ccsrPxApplied = !!input.suppliedFilters.claims.ccsrPx?.values.length;
    const cptApplied: boolean =
      input.suppliedFilters.claims.proceduresCPT.values.length > 0;

    const isAnyOtherSortAppled = this.hasPositiveValueExceptKey(
      input.sortBy,
      "procedures"
    );
    return (
      (cptApplied || !!parsedQueryTree) &&
      ccsrPxApplied &&
      input.sortBy.procedures == 1 &&
      !isAnyOtherSortAppled
    );
  }

  private hasPositiveValueExceptKey(
    obj: WeightedSortBy,
    keyToCheck: keyof WeightedSortBy
  ): boolean {
    return Object.entries(obj).some(
      ([key, value]) =>
        key !== keyToCheck && typeof value === "number" && value > 0
    );
  }

  @Trace("h1-search.keyword.search.search")
  private async search<T>(
    searchContext: SearchContext,
    input: KeywordSearchInput,
    keywordSearchFeatureFlags: KeywordSearchFeatureFlags,
    onboardingData: OnboardingData,
    isLLMGenerated?: boolean
  ): Promise<{
    response: SearchResponse<T>;
    synonyms: Array<string>;
    queryIntents: QueryIntent[];
    spellSuggesterResult: string | undefined;
    assetToClause: Map<
      RootFieldsForScoring,
      {
        isEmptyClauseQuery: boolean;
        query: QueryDslQueryContainer;
      }
    >;
    filters: QueryDslQueryContainer[];
    suggestedFilterValues: QueryIntentFilterValues;
    indicationsSynonyms: Array<string>;
    indicationsIcdCodeSynonyms: Array<string>;
    keywordIcdCodeSynonyms: Array<string>;
    territories: Array<SavedTerritory>;
  }> {
    const {
      query,
      synonyms,
      indicationsIcdCodeSynonyms,
      indicationsSynonyms,
      queryUnderstandingServiceResponse,
      territories,
      assetToClause,
      filters,
      suggestedFilterValues
    } = await this.buildSearchQueryWithInput(
      searchContext,
      input,
      keywordSearchFeatureFlags,
      onboardingData,
      isLLMGenerated
    );
    this.logger.info({ data: query }, "keywordsearch elasticsearch query");
    let response: SearchResponse<T>;
    let spellSuggesterResult = undefined;
    if (keywordSearchFeatureFlags.enableSpellSuggestion) {
      const combinedResponse: [string | undefined, SearchResponse<T>] =
        await Promise.all([
          this.spellSuggesterService.getSpellCheckSuggestion(
            input,
            queryUnderstandingServiceResponse
          ),
          this.elasticService.query<T>(query)
        ]);
      spellSuggesterResult = combinedResponse[0];
      response = combinedResponse[1];
    } else {
      response = await this.elasticService.query<T>(query);
    }

    const queryIntents = keywordSearchFeatureFlags.enableQueryIntent
      ? getQueryIntents(input.query, queryUnderstandingServiceResponse)
      : [];

    if (
      input.suppliedFilters?.geoStatsRegionLevel?.value?.claims &&
      response.aggregations
    ) {
      const claimsAggregations = this.docCountNestedClaimsBucketsToAggregations(
        response.aggregations
      );
      response.aggregations.locationClaims = claimsAggregations.geo_claims;
    }
    const keywordIcdCodeSynonyms =
      queryUnderstandingServiceResponse?.getDiagnosisCodesList() ?? [];
    return {
      response,
      synonyms,
      queryIntents,
      spellSuggesterResult,
      assetToClause,
      filters,
      suggestedFilterValues,
      indicationsSynonyms,
      indicationsIcdCodeSynonyms,
      keywordIcdCodeSynonyms,
      territories
    };
  }

  // TODO: Refactor the common code with search function
  @RpcMethod()
  @Trace("h1-search.keyword.search.patientCount")
  async patientCount(input: KeywordSearchInput): Promise<number> {
    const keywordSearchFeatureFlags = await this.getFeatureFlagValues(input);
    let onboardingData = EMPTY_ONBOARDING_DATA;
    if (input.userId) {
      onboardingData = await this.userOnboardingDataService.getOnboardingData(
        input.userId,
        input.projectId
      );
    }

    const truncatedQuery = this.sanitizeQuery(this.truncateQuery(input.query));
    input.query = truncatedQuery;
    this.logger.info(
      { data: input, truncatedQuery },
      "keywordsearch patient count request"
    );

    // we need to add the indication filter to have matched counts for patient data
    if (input.app === Apps.TRIAL_LANDSCAPE) {
      const queryIndicationInfo: QueryIndicationInfo =
        await this.resolveInputQueryToIndications(input);
      if (queryIndicationInfo.finalIndicationFilter?.length) {
        input.suppliedFilters = {
          ...input.suppliedFilters,
          indications: {
            values: queryIndicationInfo.finalIndicationFilter
          }
        };
      }
    }

    const languageDetector = getLanguageDetector(input.language);

    const parsedQueryTreeInfo = await this.parseQueryToTree(
      input.projectFeatures.advancedOperators,
      keywordSearchFeatureFlags,
      languageDetector,
      input.query
    );
    let { parsedQueryTree } = parsedQueryTreeInfo;
    const { queryUnderstandingServiceResponse } = parsedQueryTreeInfo;

    let filters: QueryDslQueryContainer[] = [];
    const suggestedFilterValues: QueryIntentFilterValues = {};

    const inputWithoutClaimsFilter: KeywordSearchInput = {
      ...input,
      suppliedFilters: {
        ...input.suppliedFilters,
        patientClaimsFilter: undefined
      }
    };
    const territoryIds = input.suppliedFilters?.territory?.values;
    const territories = territoryIds?.length
      ? await this.savedTerritoryService.getByIds(territoryIds)
      : [];
    if (
      queryUnderstandingServiceResponse &&
      !input.disableQueryIntentFiltering &&
      keywordSearchFeatureFlags.enableConferenceIntentFilter
    ) {
      const { filtersWithParsedQuery, filtersWithoutParsedQuery } =
        await this.keywordFilterClauseBuilderService.buildFiltersForQueryIntentSearchRequest(
          inputWithoutClaimsFilter,
          languageDetector,
          keywordSearchFeatureFlags,
          parsedQueryTree,
          territories
        );
      const congressQueryIntentFilterResponse =
        await this.conferenceFilterService.getQueryIntentFilterResponse(
          queryUnderstandingServiceResponse,
          inputWithoutClaimsFilter,
          filtersWithoutParsedQuery
        );

      suggestedFilterValues.congresses =
        congressQueryIntentFilterResponse.suggestedFilterValues?.congresses;
      this.logger.info(
        { congressQueryIntentFilterResponse },
        "conference intent filters"
      );

      filters = filtersWithParsedQuery;
    } else {
      filters =
        await this.keywordFilterClauseBuilderService.buildForSearchRequest(
          inputWithoutClaimsFilter,
          languageDetector,
          keywordSearchFeatureFlags,
          parsedQueryTree,
          undefined,
          territories
        );
    }

    const specialQueryIntentFilters = this.getSpecialQueryIntentFilters(
      queryUnderstandingServiceResponse,
      input
    );

    if (specialQueryIntentFilters.length > 0) {
      filters.push(...specialQueryIntentFilters);
      parsedQueryTree = undefined;
    }

    if (
      !parsedQueryTree &&
      !anyFiltersWereSupplied(filters) &&
      !this.inputHasPatientClaimsFilter(
        input.suppliedFilters,
        keywordSearchFeatureFlags
      )
    ) {
      throw new NoQueryOrFiltersError();
    }

    const { query } = this.toElasticsearchQuery(
      inputWithoutClaimsFilter,
      "keyword",
      parsedQueryTree,
      filters,
      queryUnderstandingServiceResponse,
      keywordSearchFeatureFlags,
      {},
      onboardingData
    );

    const parentQuery: QueryDslQueryContainer = {
      has_parent: {
        parent_type: "hcp",
        query: {
          bool: {
            filter: query.query
          }
        }
      }
    };

    const claimsFilter =
      this.keywordFilterClauseBuilderService.buildPatientClaimsFilter(input);
    const countQuery: QueryDslQueryContainer = {
      bool: {
        filter: [parentQuery, ...claimsFilter]
      }
    };

    this.logger.info(
      { data: countQuery },
      "keywordsearch patient count elasticsearch query"
    );

    const countRequest: CountRequest = {
      index: this.peopleIndex,
      query: countQuery
    };

    const countResponse = await this.elasticService.count(countRequest);

    this.logger.info(
      { count: countResponse.count },
      "keywordsearch patient count"
    );

    return countResponse.count;
  }

  @RpcMethod()
  @Trace("h1-search.keyword.search.hcpCountForIndications")
  async hcpCountForIndications(
    searchInput: Readonly<KeywordSearchInput>,
    indications: string[]
  ): Promise<Dictionary<number>> {
    try {
      const keywordSearchFeatureFlags = await this.getFeatureFlagValues(
        searchInput
      );
      let onboardingData = EMPTY_ONBOARDING_DATA;
      if (searchInput.userId) {
        onboardingData = await this.userOnboardingDataService.getOnboardingData(
          searchInput.userId,
          searchInput.projectId
        );
      }
      const finalCountsForIndication: Dictionary<number> = {};
      const searchRequests = [];
      for (const indication of indications) {
        const input: KeywordSearchInput = {
          ...searchInput,
          suppliedFilters: {
            ...searchInput.suppliedFilters,
            indications: {
              values: [
                ...(searchInput.suppliedFilters.indications?.values ?? []),
                indication
              ]
            }
          }
        };
        searchRequests.push(
          this.buildSearchQueryWithInput(
            "keyword",
            input,
            keywordSearchFeatureFlags,
            onboardingData
          )
        );
      }
      const allValues = await Promise.all(searchRequests);
      const requests = allValues.map((response) => response.query);
      const msearches = [];
      const HEADER: Readonly<MsearchMultisearchHeader> = {};
      for (const request of requests) {
        msearches.push(HEADER);
        msearches.push(this.toMultiSearchBodyToFetchCount(request));
      }

      const multiSearchRequest: MsearchRequest = {
        index: this.peopleIndex,
        searches: msearches
      };
      const msearchResult = await this.elasticService.msearch(
        multiSearchRequest
      );
      msearchResult.responses.forEach((response, index) => {
        finalCountsForIndication[indications[index]] =
          ((response as SearchResponseBody).hits.total as SearchTotalHits)
            .value ?? 0;
      });

      return finalCountsForIndication;
    } catch (err) {
      if (err instanceof NoQueryOrFiltersError) {
        this.logger.error("No query or filter error");
      }
      if (err instanceof Error) {
        this.logger.error(err, err.message);
      }
      throw err;
    }
  }
  private toMultiSearchBodyToFetchCount(
    searchRequest: SearchRequest
  ): MsearchMultisearchBody {
    return {
      size: 0,
      _source: false,
      track_total_hits: searchRequest.track_total_hits,
      query: searchRequest.query
    };
  }

  private async buildSearchQueryWithInput(
    searchContext: SearchContext,
    input: KeywordSearchInput,
    keywordSearchFeatureFlags: KeywordSearchFeatureFlags,
    onboardingData: OnboardingData,
    isLLMGenerated?: boolean
  ): Promise<{
    query: SearchRequest;
    indicationsSynonyms: string[];
    indicationsIcdCodeSynonyms: string[];
    synonyms: string[];
    territories: UserEntities.SavedTerritory[];
    assetToClause: Map<
      RootFieldsForScoring,
      {
        isEmptyClauseQuery: boolean;
        query: QueryDslQueryContainer;
      }
    >;
    filters: QueryDslQueryContainer[];
    suggestedFilterValues: QueryIntentFilterValues;
    queryUnderstandingServiceResponse?: QueryUnderstandingServiceResponse;
  }> {
    const truncatedQuery = this.sanitizeQuery(this.truncateQuery(input.query));
    input.query = truncatedQuery;

    /**
     * The Idea behind the BOTH operator is that:-
     * We convert a query like BOTH( Cardiology WITH diabetes ) to Cardiology OR diabetes and add
     * corresponding should clauses based on the new query, but add filter clauses based on  BOTH( Cardiology WITH diabetes )
     * // Nadeem - 1 trial cardiology as well as diabetes:- Use Case A ✅
     * // Yash - 1 trial cardiology and 1 trial diabetes :- Use Case B ✅
     * // Abhishek - 1 trial Cardiology and 1 pub diabetes :- Use Case C ✅
     * // Shreenath - 1 trial Cardiology and 1 pub Cardiology:- ❌
     * // BMP - Has nothing:- Use Case D ❌
     * here essentially should would want to match Shreenath(using the OR query) but filter clause has removed Shreenath
     * from search space.
     */

    let bothParserResponse: BothQueryParseResult | undefined = undefined;

    //BOTH operator right now only availale for HCPU
    if (
      keywordSearchFeatureFlags.enableBothOperatorForSearch &&
      input.app === undefined
    ) {
      bothParserResponse = this.queryParserService.parseBothQuery(input.query);
      if (bothParserResponse.usedBoth) {
        input.query =
          this.queryParserService.rewriteBothQueryToOrExpression(
            bothParserResponse
          );
      }
    }

    // add 'extra' indication filters to the input, if the query resembles (or is) an indication. This is to ensure that the search results are the same if the user types in the indication in the search query or selects it from the indication dropdown
    const queryIndicationInfo: QueryIndicationInfo =
      await this.resolveInputQueryToIndications(input);

    if (
      input.app === Apps.TRIAL_LANDSCAPE &&
      queryIndicationInfo.finalIndicationFilter?.length
    ) {
      input.suppliedFilters = {
        ...input.suppliedFilters,
        indications: {
          values: queryIndicationInfo.finalIndicationFilter
        }
      };
    }
    this.logger.info({ data: input, truncatedQuery }, "keywordsearch request");

    const languageDetector = getLanguageDetector(input.language);

    const parsedQueryTreeInfo = await this.parseQueryToTree(
      input.projectFeatures.advancedOperators,
      keywordSearchFeatureFlags,
      languageDetector,
      input.query
    );

    let { parsedQueryTree } = parsedQueryTreeInfo;
    const { synonyms, queryUnderstandingServiceResponse } = parsedQueryTreeInfo;

    let filters: QueryDslQueryContainer[] = [];
    const suggestedFilterValues: QueryIntentFilterValues = {};
    const territoryIds = input.suppliedFilters?.territory?.values;
    const territories = territoryIds?.length
      ? await this.savedTerritoryService.getByIds(territoryIds)
      : [];
    if (
      queryUnderstandingServiceResponse &&
      !input.disableQueryIntentFiltering &&
      keywordSearchFeatureFlags.enableConferenceIntentFilter
    ) {
      const { filtersWithParsedQuery, filtersWithoutParsedQuery } =
        await this.keywordFilterClauseBuilderService.buildFiltersForQueryIntentSearchRequest(
          input,
          languageDetector,
          keywordSearchFeatureFlags,
          parsedQueryTree,
          territories,
          bothParserResponse,
          isLLMGenerated
        );
      const congressQueryIntentFilterResponse =
        await this.conferenceFilterService.getQueryIntentFilterResponse(
          queryUnderstandingServiceResponse,
          input,
          filtersWithoutParsedQuery
        );

      suggestedFilterValues.congresses =
        congressQueryIntentFilterResponse.suggestedFilterValues?.congresses;
      this.logger.info(
        { congressQueryIntentFilterResponse },
        "conference intent filters"
      );

      filters = filtersWithParsedQuery;
    } else {
      filters =
        await this.keywordFilterClauseBuilderService.buildForSearchRequest(
          input,
          languageDetector,
          keywordSearchFeatureFlags,
          parsedQueryTree,
          undefined,
          territories,
          bothParserResponse,
          isLLMGenerated
        );
    }

    const specialQueryIntentFilters = this.getSpecialQueryIntentFilters(
      queryUnderstandingServiceResponse,
      input
    );

    if (specialQueryIntentFilters.length > 0) {
      filters.push(...specialQueryIntentFilters);
      parsedQueryTree = undefined;
    }

    if (
      !parsedQueryTree &&
      !anyFiltersWereSupplied(filters) &&
      !this.inputHasPatientClaimsFilter(
        input.suppliedFilters,
        keywordSearchFeatureFlags
      )
    ) {
      throw new NoQueryOrFiltersError();
    }

    const aggregations = this.buildAggregations(
      input,
      parsedQueryTree,
      queryUnderstandingServiceResponse,
      keywordSearchFeatureFlags
    );

    //icd codes required for exports
    const icdCodesForCareClusterSelected =
      await this.getClaimCodesForCareCluster(input, "diagnoses");
    const procedureCodesForCareClusterSelected =
      await this.getClaimCodesForCareCluster(input, "procedures");

    const { query, assetToClause } = this.toElasticsearchQuery(
      input,
      searchContext,
      parsedQueryTree,
      filters,
      queryUnderstandingServiceResponse,
      keywordSearchFeatureFlags,
      aggregations,
      onboardingData,
      undefined,
      queryIndicationInfo.indicationsMatchedToQuery,
      icdCodesForCareClusterSelected,
      procedureCodesForCareClusterSelected
    );

    this.logger.info({ data: query }, "keywordsearch elasticsearch query");
    return {
      query,
      indicationsIcdCodeSynonyms: [],
      indicationsSynonyms: [],
      synonyms,
      territories,
      assetToClause,
      filters,
      suggestedFilterValues,
      queryUnderstandingServiceResponse
    };
  }

  private docCountNestedClaimsBucketsToAggregations(
    agg: Record<string, AggregationsAggregateBase> | undefined
  ): Record<string, AggregationsMultiBucketAggregateBase> {
    if (!agg?.locationClaims) {
      return {};
    }

    const geoClaimsAggregations = agg.locationClaims!.meta
      ?.meta as AggregationsTermsAggregateBase<ClaimsLocationAggregationDocCountBucket>;
    const geoclaimsBucket = (
      geoClaimsAggregations!
        .buckets as ClaimsLocationAggregationDocCountBucket[]
    ).map((bucket) => {
      const procedureCount =
        bucket.allClaims.procedures.filteredClaims.counts.value;
      const diagnosesCount =
        bucket.allClaims.diagnoses.filteredClaims.counts.value;
      return {
        key: bucket.key,
        doc_count: procedureCount + diagnosesCount
      };
    });
    return {
      geo_claims: {
        buckets: geoclaimsBucket?.length ? geoclaimsBucket : []
      }
    };
  }

  @Trace("h1-search.keyword.parseQueryToTree")
  private async parseQueryToTree(
    projectSupportsAdvancedOperators: boolean,
    keywordSearchFeatureFlags: KeywordSearchFeatureFlags,
    languageDetector: LanguageDetector,
    query?: string,
    indicationsParsedQuery?: string
  ): Promise<{
    parsedQueryTree: ParsedQueryTree;
    synonyms: Array<string>;
    queryUnderstandingServiceResponse?: QueryUnderstandingServiceResponse;
  }> {
    if (!query) {
      return {
        parsedQueryTree: indicationsParsedQuery,
        synonyms: []
      };
    }

    const languageCode = languageDetector(query);

    if (!projectSupportsAdvancedOperators) {
      const queryUnderstandingServiceResponse =
        await this.queryUnderstandingServiceClient.analyze(query, languageCode);

      return {
        parsedQueryTree: query,
        synonyms: [],
        queryUnderstandingServiceResponse
      };
    }

    if (!HAS_ADVANCED_OPERATORS.test(query)) {
      const queryUnderstandingServiceResponse =
        await this.queryUnderstandingServiceClient.analyze(query, languageCode);

      const parsedQueryTree = (
        keywordSearchFeatureFlags.enableIntentBasedKeywordSearch
          ? this.extractSearchTokens(queryUnderstandingServiceResponse)
          : queryUnderstandingServiceResponse.getAugmentedQuery()
      )
        .replace(ALL_ORs, OR)
        .replace(ALL_ANDs, EMPTY_STRING)
        .replace(ALL_UNICODE_DOUBLE_QUOTES, ASCII_DOUBLE_QUOTES)
        .concat(
          indicationsParsedQuery ? OR + indicationsParsedQuery : EMPTY_STRING
        );

      return {
        parsedQueryTree,
        synonyms: getQuerySynonymList(query, queryUnderstandingServiceResponse),
        queryUnderstandingServiceResponse
      };
    }
    const indicationQueryWithAdvancedOperators = query.concat(
      indicationsParsedQuery
        ? ` OR ${indicationsParsedQuery.replace(/\|/g, "OR")}`
        : EMPTY_STRING
    );
    return {
      parsedQueryTree: this.queryParserService.parseQuery(
        indicationQueryWithAdvancedOperators
      ),
      synonyms: []
    };
  }

  // TODO: Refactor once userId is required
  @Trace("h1-search.keyword.getFeatureFlagValues")
  private async getFeatureFlagValues({
    userId,
    projectId
  }: {
    userId?: string;
    projectId: string;
  }): Promise<KeywordSearchFeatureFlags> {
    const featureFlags: Partial<KeywordSearchFeatureFlags> = {};

    const user = userId ? { userId, projectId } : undefined;
    const flagsState = await this.featureFlagsService.getAllFlags(user);

    for (const flag of keywordSearchFeatureFlagTypes) {
      featureFlags[flag] =
        flagsState.getFlagValue(featureFlagDefaults[flag].key) ??
        featureFlagDefaults[flag].default;
    }

    return featureFlags as KeywordSearchFeatureFlags;
  }

  private finalizeSortByWithProjectFeatures({
    sortBy,
    projectFeatures: {
      claims: areClaimsEnabled,
      referrals: areReferralsEnabled
    }
  }: KeywordSearchInput): WeightedSortBy {
    const finalizedSortBy = { ...sortBy };

    if (!areClaimsEnabled) {
      finalizedSortBy.diagnoses = 0;
      finalizedSortBy.procedures = 0;
    }

    if (!areReferralsEnabled) {
      finalizedSortBy.referralsReceived = 0;
      finalizedSortBy.referralsSent = 0;
    }

    return finalizedSortBy;
  }

  private isLeaderboardSortApplied(
    filters: FilterInterface,
    indicationsMatchedToQuery?: string[]
  ): boolean {
    return (
      ((!_.isEmpty(filters.indications?.values) ||
        !!indicationsMatchedToQuery?.length) &&
        ((filters.isEducatorLeader?.value ||
          filters.isTrialLeader?.value ||
          filters.isScholarLeader?.value ||
          filters.isPublicationLeader?.value ||
          filters.isDOLeader?.value) ??
          false)) ||
      !!filters.isRisingStar?.value
    );
  }

  private calculateCongressBoostFromQueryIntent(
    queryUnderstandingServiceResponse:
      | QueryUnderstandingServiceResponse
      | undefined,
    sortBy: WeightedSortBy,
    keywordSearchFeatureFlags: KeywordSearchFeatureFlags
  ): number {
    if (
      keywordSearchFeatureFlags.enableConferenceIntentFilter &&
      this.checkIfAdvancedSortIsApplied(sortBy) &&
      queryUnderstandingServiceResponse &&
      hasGoodConfidenceForConferenceIntent(queryUnderstandingServiceResponse)
    ) {
      const intentScore = queryUnderstandingServiceResponse!
        .getQueryIntent()!
        .getConferenceIntent()!
        .getScore();
      return CONGRESS_INTENT_BOOST * intentScore;
    }
    return NO_BOOST;
  }

  /**
   * This function returns the script score that is used to score trials, procedures and diagnoses should clause for H1 Diversity Scoring
   * @param saturationParam constant in the saturation function score
   * @param raceFilterValues any patients diversity race filter selected by the user
   * @returns The Function Score Container containing the script score
   */
  private h1DiversityScoringFunctionForAssetShouldClause(
    saturationParam: number,
    suppliedFilters: FilterInterface,
    featureFlags: KeywordSearchFeatureFlags
  ): QueryDslFunctionScoreContainer {
    const raceFilterValues = suppliedFilters.patientsDiversity.race.values;
    const raceDiversityRatios: string[] = [];
    // aggregate the patientsDiversityRatio for every race selected in the filter by the user
    for (const raceFilterValue of raceFilterValues) {
      try {
        const raceField: string =
          this.keywordFilterClauseBuilderService.mapRaceFilterValueToPatientsDiversityRatioField(
            raceFilterValue
          );
        raceDiversityRatios.push(
          `(doc['patientsDiversityRatio.${raceField}'].empty ? 0.0: doc['patientsDiversityRatio.${raceField}'].value)`
        );
      } catch (err) {
        if (err instanceof Error) {
          this.logger.error(err, err.message);
        }
      }
    }

    let weight: string;
    if (raceDiversityRatios.length === 0) {
      // weight in case of no race filter
      weight = `(${PATIENT_DIVERSITY_RATIO.join(" + ")})`;
    } else {
      weight = "( " + raceDiversityRatios.join(" + ") + " )";
    }

    return {
      script_score: {
        script: {
          source: `saturation(_score, params['saturation_param']) * ${weight}`, // _score here is the number of matching fields (diag or proc or trials)
          params: {
            saturation_param: saturationParam
          }
        }
      }
    };
  }

  private scoreByPatientPopulationDiversity(
    input: KeywordSearchInput,
    featureFlags: KeywordSearchFeatureFlags
  ): boolean {
    return (
      (input.suppliedFilters?.claims?.diagnosesICD?.values?.length > 0 ||
        !!input.suppliedFilters?.claims?.ccsr?.values?.length ||
        (input.suppliedFilters.indications?.values ?? []).length > 0 ||
        (input.query ?? "").length > 0) &&
      !this.inputHasPatientClaimsFilter(input.suppliedFilters, featureFlags)
    );
  }

  private shouldPatientHasChildQueryAdded(
    { suppliedFilters }: KeywordSearchInput,
    featureFlags: KeywordSearchFeatureFlags
  ): boolean {
    const isClaimsFiltersApplied =
      this.inputHasClaimsFilterAsQualifyingConditionForPatients(
        suppliedFilters,
        featureFlags
      );

    const isPatientFiltersApplied = this.inputHasPatientClaimsFilter(
      suppliedFilters,
      featureFlags
    );

    return !(isClaimsFiltersApplied || isPatientFiltersApplied);
  }

  private isDiagnosisOrCcsrFilterApplied({
    suppliedFilters
  }: KeywordSearchInput): boolean {
    return (
      suppliedFilters.claims.diagnosesICD.values.length === 0 &&
      suppliedFilters.claims.ccsr?.values.length === 0
    );
  }

  private isProcedureOrCcsrPxFilterApplied({
    suppliedFilters
  }: KeywordSearchInput): boolean {
    return (
      suppliedFilters.claims.proceduresCPT.values.length === 0 &&
      suppliedFilters.claims.ccsrPx?.values.length === 0
    );
  }

  /**
   * Diversity Ranking for patient population is equal to number of non-white patients, if a race filter is applied
   * then its equal to the number of patients of that particular race.
   * Patient Sort for patient population is equal to total number of patients.
   * @param input
   * @param featureFlags
   * @returns
   */
  private patientPopulationSearchClause(
    input: KeywordSearchInput,
    featureFlags: KeywordSearchFeatureFlags
  ): QueryDslQueryContainer {
    const patientPopulationFilter =
      this.keywordFilterClauseBuilderService.buildPatientPopulationFilter(
        input,
        featureFlags
      );
    const raceFilterValues =
      input.suppliedFilters?.patientsDiversity?.race?.values ?? [];

    /**
     * true for patient sort or diversity sort
     * we need to have some weight for h1 default ranking too
     * this equates to if ranking is patient/diversity sort then wieght is 10 or else if sortby is h1 default then weight is 1 or else it is 0
     *
     * */
    const weight = this.sortByPatientCount(input)
      ? 10
      : +(
          this.getRankingType(input.sortBy, featureFlags, input.app) ===
          RankingType.NEW_TL_H1_DEFAULT_RANK
        ); //Type casting bool to either 0 or 1

    const defaultFilters: QueryDslFunctionScoreContainer[] =
      raceFilterValues.length || !input.sortBy?.patientsDiversityRank
        ? []
        : [
            {
              filter: {
                bool: {
                  must_not: {
                    exists: {
                      field: "patientClaims.diversity" // not all patients docs have a value for diversity field
                    }
                  }
                }
              },
              weight: 0
            },
            {
              filter: {
                term: {
                  "patientClaims.diversity": "White Non-Hispanic" // USA, Brazil: if the patient race is whiteNonHispanice weight is 0
                }
              },
              weight: 0
            },
            {
              filter: {
                term: {
                  "patientClaims.diversity": "Other" // USA: if the patient race is Other weight is 0
                }
              },
              weight: 0
            },
            {
              filter: {
                term: {
                  "patientClaims.diversity": "Not Disclosed" // Brazil: if the patient race is Not Disclosed weight is 0
                }
              },
              weight: 0
            }
          ];

    const patientPopulationQueryClause: QueryDslQueryContainer = {
      has_child: {
        type: "claim",
        score_mode: "sum",
        min_children: input.suppliedFilters?.claims?.diagnosesICDMinCount?.value
          ? input.suppliedFilters?.claims?.diagnosesICDMinCount?.value
          : undefined,
        max_children: input.suppliedFilters?.claims?.diagnosesICDMaxCount?.value
          ? input.suppliedFilters?.claims?.diagnosesICDMaxCount?.value
          : undefined,
        query: {
          function_score: {
            query: {
              bool: {
                filter: [...patientPopulationFilter]
              }
            },
            functions: [
              {
                weight
              },
              ...defaultFilters
            ],
            score_mode: "multiply",
            boost_mode: "replace"
          }
        },
        inner_hits: {
          name: "patient_claims_matching_count",
          size: 0,
          _source: false
        }
      }
    };

    return patientPopulationQueryClause;
  }

  private sortByPatientCount(input: KeywordSearchInput): boolean {
    return (
      (input?.suppliedFilters?.claims?.showUniquePatients?.value ?? false) &&
      (input.sortBy?.diagnoses === 1 || input.sortBy?.patientsDiversityRank > 0)
    );
  }

  private buildScoringFunctions(
    field: string,
    sortBy: WeightedSortBy,
    isLeaderboardSortApplied: boolean,
    hasIndications: boolean
  ): QueryDslFunctionScoreContainer[] {
    if (
      this.isH1Ranking(sortBy) &&
      (isLeaderboardSortApplied || hasIndications)
    ) {
      return [
        {
          field_value_factor: {
            field,
            missing: 0,
            factor: 100
          }
        }
      ];
    } else {
      return [{ weight: 0 }];
    }
  }

  private buildIndicationQuery(
    filters: QueryDslQueryContainer[],
    scoringFunctions: QueryDslFunctionScoreContainer[],
    innerHitsFields = ["indications.indicationScore"]
  ) {
    return {
      nested: {
        path: "indications",
        query: {
          function_score: {
            boost_mode: BOOST_MODE_REPLACE,
            score_mode: SCORE_MODE_AVG,
            query: { bool: { filter: filters } },
            functions: scoringFunctions
          }
        },
        inner_hits: {
          size: 1000,
          _source: false,
          docvalue_fields: innerHitsFields
        }
      }
    };
  }

  private buildNonNestedQueryForLeaderboards(
    filters: QueryDslQueryContainer[],
    scoringFunctions: QueryDslFunctionScoreContainer[],
    boostMode = BOOST_MODE_REPLACE,
    scoreMode = SCORE_MODE_AVG
  ) {
    return {
      function_score: {
        query: { bool: { filter: filters } },
        functions: scoringFunctions,
        boost_mode: boostMode,
        score_mode: scoreMode
      }
    };
  }

  private toElasticsearchQuery(
    input: KeywordSearchInput,
    searchContext: SearchContext,
    parsedQueryTree: ParsedQueryTree,
    filters: Array<QueryDslQueryContainer>,
    queryUnderstandingServiceResponse:
      | QueryUnderstandingServiceResponse
      | undefined,
    keywordSearchFeatureFlags: KeywordSearchFeatureFlags,
    aggregations: Record<string, AggregationsAggregationContainer>,
    onboardingData: OnboardingData,
    indicationsIcdCodesQuery?: string,
    indicationsMatchedToQuery?: string[],
    icdCodesForCareClusterSelected?: string[],
    procedureCodesForCareClusterSelected?: string[]
  ): {
    query: SearchRequest;
    assetToClause: Map<
      RootFieldsForScoring,
      {
        isEmptyClauseQuery: boolean;
        query: QueryDslQueryContainer;
      }
    >;
  } {
    const { page, suppliedFilters, scoringFunction, numberOfHighlights } =
      input;

    const sortBy = this.finalizeSortByWithProjectFeatures(input);

    const scoringFunctionType = getKeywordSearchScoringFunction(
      scoringFunction,
      !!input.sortBy.trialPerformance
    );
    const { assetToShouldClause, minimumMatchingAssets } =
      this.getShouldClausesBasedOnRanking(
        input,
        searchContext,
        parsedQueryTree,
        filters,
        queryUnderstandingServiceResponse,
        keywordSearchFeatureFlags,
        indicationsIcdCodesQuery,
        numberOfHighlights
      );

    const { mustClauses, assetToMustClause } = this.getMustClauses(
      input,
      searchContext,
      parsedQueryTree,
      filters,
      keywordSearchFeatureFlags,
      indicationsIcdCodesQuery,
      indicationsMatchedToQuery,
      icdCodesForCareClusterSelected,
      procedureCodesForCareClusterSelected
    );

    const shouldClauses = [...assetToShouldClause.values()].map(
      (shouldClauseDetail) => shouldClauseDetail.query
    );
    const intentShouldClauses =
      keywordSearchFeatureFlags.enableIntentBasedKeywordSearch &&
      this.checkIfAdvancedSortIsApplied(sortBy)
        ? [
            ...this.locationIntentClause(queryUnderstandingServiceResponse),
            ...this.specialtyIntentClause(queryUnderstandingServiceResponse)
          ]
        : [];
    const queryArgs = {
      shouldClauses,
      intentShouldClauses,
      mustClauses,
      minimumMatchingAssets,
      filters,
      scoringFunctionType,
      sortBy
    };

    let booleanQuery: QueryDslQueryContainer = sortBy.patientsDiversityRank
      ? this.getBooleanQueryForDiversityRank(
          queryArgs,
          keywordSearchFeatureFlags.enablePenaltyForZeroTrialsDiversityRanking
        )
      : this.getBooleanQueryForStandardWeighting(queryArgs);

    if (this.checkIfAdvancedSortIsApplied(sortBy)) {
      if (
        keywordSearchFeatureFlags.enableLocationConfidenceRanking &&
        this.isLocationFilterApplied(suppliedFilters)
      ) {
        booleanQuery = this.getQueryWithLocationConfidenceScoring(
          suppliedFilters,
          booleanQuery
        );
      } else if (
        keywordSearchFeatureFlags.enableKWSearchPersonalisation &&
        !this.isLocationFilterApplied(suppliedFilters) &&
        !isLocationIntentPresent(queryUnderstandingServiceResponse) &&
        (!_.isEmpty(onboardingData.countries) ||
          !_.isEmpty(onboardingData.states))
      ) {
        this.logger.info(
          {
            countries: onboardingData.countries,
            states: onboardingData.states
          },
          "Adding location personalisation"
        );

        booleanQuery = this.getQueryWithLocationPersonalisationBoost(
          onboardingData,
          booleanQuery
        );
      }
    }

    const query: SearchRequest = {
      index: this.peopleIndex,
      ...page,
      query: booleanQuery,
      sort: SORT_TIEBREAKER
    };

    if (query.query && !isEmpty(aggregations)) {
      query.aggs = aggregations;
    }

    if (searchContext === "keyword") {
      query.track_total_hits = true;
      query._source_includes = [...SOURCE_FIELDS];

      if (sortBy.congressContributerRank) {
        query._source_includes.push("congress");
      }
    } else {
      query._source_includes = ["id", "h1dn_id"];
    }

    this.logger.debug(
      { query: JSON.stringify(query) },
      "elasticsearch keyword search query"
    );

    return {
      query,
      assetToClause: new Map([
        ...Array.from(assetToShouldClause.entries()),
        ...Array.from(assetToMustClause.entries())
      ])
    };
  }

  private isLocationFilterApplied(suppliedFilters: FilterInterface): boolean {
    for (const field of locationFields) {
      if (
        suppliedFilters &&
        suppliedFilters[field as locationType]?.values.length
      ) {
        return true;
      }
    }

    return false;
  }

  private checkIfAdvancedSortIsApplied(sortBy: WeightedSortBy): boolean {
    let numAssetsWithNonZeroWeight = 0;
    for (const key in sortBy) {
      if (_.get(sortBy, key, 0) > 0)
        numAssetsWithNonZeroWeight = numAssetsWithNonZeroWeight + 1;
    }
    return sortBy.patientsDiversityRank || numAssetsWithNonZeroWeight > 1
      ? true
      : false;
  }

  private getQueryWithLocationPersonalisationBoost(
    onboardingData: OnboardingData,
    booleanQuery: QueryDslQueryContainer
  ): QueryDslQueryContainer {
    const locationQueries: QueryDslQueryContainer[] = [];

    if (!_.isEmpty(onboardingData.countries)) {
      locationQueries.push(
        getLocationQueryForOnboardingData(
          onboardingData.countries,
          "affiliations.institution.filters.country",
          COUNTRY_PERSONALISATION_BOOST
        )
      );
    }

    // we are giving a very small boost for state as user's might be interested in results for their preferred country.
    if (!_.isEmpty(onboardingData.states)) {
      locationQueries.push(
        getLocationQueryForOnboardingData(
          onboardingData.states,
          "affiliations.institution.filters.region",
          STATE_PERSONALISATION_BOOST
        )
      );
    }

    return {
      bool: {
        must: [this.getKeywordSearchQueryWithSaturationFunction(booleanQuery)],
        should: locationQueries
      }
    };
  }

  private getQueryWithLocationConfidenceScoring(
    suppliedFilters: FilterInterface,
    booleanQuery: QueryDslQueryContainer
  ): QueryDslQueryContainer {
    const locationFieldToRankQueries: Record<
      LocationFields,
      QueryDslQueryContainer[]
    > = {};

    locationFields.forEach((field) => {
      if (
        suppliedFilters &&
        suppliedFilters[field as locationType]?.values.length
      ) {
        const rankFeatureQueries: QueryDslQueryContainer[] = [];
        suppliedFilters[field as locationType].values.forEach(
          (locationFilterValue: string) => {
            const esFieldNameInIndex =
              locationFieldToIndexFieldMapping[field as locationType] +
              "." +
              removeIllegalCharsForESFieldNames(locationFilterValue);
            rankFeatureQueries.push(getRankFeatureQuery(esFieldNameInIndex));
          }
        );
        locationFieldToRankQueries[field] = rankFeatureQueries;
      }
    });

    const locationRankFeatureQueries = new Array<QueryDslQueryContainer>();
    for (const locationField in locationFieldToRankQueries) {
      locationRankFeatureQueries.push(
        this.getLocationFilterRankFeatureDisMaxQuery(
          locationFieldToRankQueries[locationField]
        )
      );
    }

    return {
      bool: {
        must: [this.getKeywordSearchQueryWithSaturationFunction(booleanQuery)],
        should: locationRankFeatureQueries
      }
    };
  }

  private getLocationFilterRankFeatureDisMaxQuery(
    rankFeatureQueries: QueryDslQueryContainer[]
  ): QueryDslQueryContainer {
    return {
      dis_max: {
        queries: rankFeatureQueries
      }
    };
  }

  private getKeywordSearchQueryWithSaturationFunction(
    booleanQuery: QueryDslQueryContainer
  ): QueryDslQueryContainer {
    return {
      function_score: {
        query: booleanQuery,
        // TODO : Saturation param will need to be tuned in future based on advance sort type.
        // Currently location confidence can be {0,1} and hence passing a value 1 should work fine.
        functions: [getSaturationScriptFunction(1)],
        boost_mode: "replace"
      }
    };
  }

  private getBooleanQueryForStandardWeighting({
    shouldClauses,
    intentShouldClauses,
    mustClauses,
    minimumMatchingAssets,
    filters,
    scoringFunctionType,
    sortBy
  }: {
    shouldClauses: QueryDslQueryContainer[];
    intentShouldClauses: QueryDslQueryContainer[];
    mustClauses: QueryDslQueryContainer[];
    minimumMatchingAssets: number;
    filters: QueryDslQueryContainer[];
    scoringFunctionType: QueryDslFunctionScoreContainer[] | undefined;
    sortBy: WeightedSortBy;
  }) {
    let booleanQuery = {
      bool: {
        should: shouldClauses,
        minimum_should_match: minimumMatchingAssets,
        must: mustClauses,
        filter: filters
      }
    };

    if (intentShouldClauses.length > 0) {
      booleanQuery = {
        bool: {
          must: [
            {
              bool: {
                should: shouldClauses,
                minimum_should_match: minimumMatchingAssets
              }
            },
            ...mustClauses
          ],
          should: intentShouldClauses,
          minimum_should_match: 0,
          filter: filters
        }
      };
    }

    const penalizeAutoCreatedHCPsFunction = {
      // penalize auto created HCPs by squashing their score between 0 and 1.
      filter: {
        term: {
          isAutoCreated: true
        }
      },
      weight: ZERO_WEIGHT_FUNCTION_SCORE.weight
    };

    const trialPerformanceScoringParameters = sortBy.trialPerformance
      ? {
          score_mode: SCORE_MODE_SUM,
          boost_mode: BOOST_MODE_SUM
        }
      : {};
    const scoringFunctionTypeFunctions = scoringFunctionType?.length
      ? scoringFunctionType
      : [];

    //construct the final query with function score
    const booleanQueryWithFunctionScore: QueryDslQueryContainer = {
      function_score: {
        query: booleanQuery,
        functions: [
          penalizeAutoCreatedHCPsFunction,
          ...scoringFunctionTypeFunctions
        ],
        ...trialPerformanceScoringParameters
      }
    };
    return booleanQueryWithFunctionScore;
  }

  private getBooleanQueryForDiversityRank(
    {
      shouldClauses,
      mustClauses,
      minimumMatchingAssets,
      filters
    }: {
      shouldClauses: QueryDslQueryContainer[];
      mustClauses: QueryDslQueryContainer[];
      minimumMatchingAssets: number;
      filters: QueryDslQueryContainer[];
    },
    enablePenaltyForZeroTrialsDiversityRanking: boolean
  ): QueryDslQueryContainer {
    const defaultScriptScore = enablePenaltyForZeroTrialsDiversityRanking
      ? {
          script_score: {
            script: {
              source:
                "_score>=10 && ((doc['trialCount'].size()!=0 ?doc['trialCount'].value:0)==0)?saturation(_score, params['saturation_param']):_score", //penalize HCPs who have patient doc matches but no trials
              params: {
                saturation_param: 10
              }
            }
          }
        }
      : {
          script_score: {
            script: "_score"
          }
        };

    const penalizeAutoCreatedHCPs = {
      // penalize auto created HCPs by squashing their score between 0 and 1.
      filter: {
        term: {
          isAutoCreated: true
        }
      },
      weight: ZERO_WEIGHT_FUNCTION_SCORE.weight
    };

    return {
      function_score: {
        query: {
          bool: {
            should: shouldClauses,
            minimum_should_match: minimumMatchingAssets,
            must: mustClauses,
            filter: filters
          }
        },
        functions: [
          {
            filter: {
              bool: {
                should: [
                  {
                    prefix: {
                      specialty_eng: {
                        value: "Radiology"
                      }
                    }
                  },
                  {
                    prefix: {
                      specialty_eng: {
                        value: "Pathology"
                      }
                    }
                  }
                ],
                minimum_should_match: 1
              }
            },
            script_score: getSaturationScriptFunction(100).script_score // penalize radiologist and pathologist by squashing their score between 0 and 1.
          },
          penalizeAutoCreatedHCPs,
          defaultScriptScore
        ],
        score_mode: "min", // if the speciality is radiology/pathology then take the sqashed score otherwise no change.
        boost_mode: BOOST_MODE_REPLACE
      }
    };
  }

  private hasPrescriptionsAssetFilter(input: KeywordSearchInput) {
    return (
      (input.suppliedFilters.claims.genericNames?.values?.length ?? 0) !== 0 ||
      (input.suppliedFilters.claims.brandNames?.values?.length ?? 0) !== 0 ||
      (input.suppliedFilters.claims.drugClasses?.values?.length ?? 0) !== 0
    );
  }

  private getShouldClausesForDiversitySortQuery(
    input: KeywordSearchInput,
    { claims }: { claims: boolean },
    searchContext: SearchContext,
    parsedQueryTree: ParsedQueryTree,
    filters: Array<QueryDslQueryContainer>,
    queryUnderstandingServiceResponse:
      | QueryUnderstandingServiceResponse
      | undefined,
    keywordSearchFeatureFlags: KeywordSearchFeatureFlags,
    appliedRanking: RankingType,
    indicationsIcdCodesQuery?: string,
    numberOfHighlights?: HighlightCountPerAsset
  ): {
    assetToShouldClause: Map<
      RootFieldsForScoring,
      {
        isEmptyClauseQuery: boolean;
        query: QueryDslQueryContainer;
      }
    >;
    minimumMatchingAssets: number;
  } {
    const assetToShouldClause: Map<
      RootFieldsForScoring,
      {
        isEmptyClauseQuery: boolean;
        query: QueryDslQueryContainer;
      }
    > = new Map();
    const {
      enableQueryContextualPaymentsFiltering,
      enableEnhancedHasCTMSDataFilterBehavior
    } = keywordSearchFeatureFlags;
    const { suppliedFilters } = input;
    const nestedTrialsFilters = findNestedFilters(filters, "trials");
    const nestedCongressesFilters = findNestedFilters(filters, "congress");
    const nestedPaymentsFilters = findNestedFilters(filters, "payments");
    const nestedPublicationsFilters = findNestedFilters(
      filters,
      "publications"
    );
    const nestedDiagnosesFilters = findNestedClaimsFilters(
      filters,
      "DRG_diagnoses",
      keywordSearchFeatureFlags
    );

    const nestedProceduresFilters = findNestedClaimsFilters(
      filters,
      "DRG_procedures",
      keywordSearchFeatureFlags
    );

    const nestedPrescriptionsFilters = findNestedFilters(
      filters,
      "prescriptions"
    );

    const parsedQueryAlreadyAddedToPaymentsFilters =
      nestedPaymentsFilters.length && enableQueryContextualPaymentsFiltering;
    const parsedQueryAlreadyAddedToCongressFilters =
      nestedCongressesFilters.length > 0;
    const parsedQueryAlreadyAddedToPublicationsFilters =
      nestedPublicationsFilters.length > 0;
    const parsedQueryAlreadyAddedToTrialsFilters =
      nestedTrialsFilters.length > 0;

    let minimumMatchingAssets = AT_LEAST_ONE_ASSET;

    if (
      parsedQueryAlreadyAddedToPaymentsFilters ||
      parsedQueryAlreadyAddedToCongressFilters ||
      parsedQueryAlreadyAddedToPublicationsFilters ||
      parsedQueryAlreadyAddedToTrialsFilters
    ) {
      minimumMatchingAssets = ALL_ASSETS_OPTIONAL;
    }
    const getMatchedClaimsDetails =
      keywordSearchFeatureFlags?.enableClaimsExports &&
      suppliedFilters.getMatchedClaims?.value;

    const needToAddHighlights =
      keywordSearchFeatureFlags?.enableSearchHighlights ? true : false;
    const numberOfTrialsHighlights = numberOfHighlights
      ? numberOfHighlights.trials
      : 0;
    const numberOfCongressesHighlights = numberOfHighlights
      ? numberOfHighlights.congresses
      : 0;
    const numberOfPublicationsHighlights = numberOfHighlights
      ? numberOfHighlights.publications
      : 0;

    const optionalQueries: OptionalQueries = {
      needToAddParsedQueryToCongress: !parsedQueryAlreadyAddedToCongressFilters,
      needToAddParsedQueryToPayments: !parsedQueryAlreadyAddedToPaymentsFilters,
      needToAddParsedQueryToPublications:
        !parsedQueryAlreadyAddedToPublicationsFilters,
      needToAddParsedQueryToTrials: !parsedQueryAlreadyAddedToTrialsFilters,
      needToAddHighlights
    };

    this.addNestedFilterWhenOnlyDateRangeFilterIsApplied(
      suppliedFilters,
      nestedCongressesFilters,
      nestedTrialsFilters,
      nestedPublicationsFilters
    );

    const isEmptyTrialsQuery = isEmptyClauseQuery(
      parsedQueryTree,
      nestedTrialsFilters
    );

    assetToShouldClause.set("trialCount", {
      isEmptyClauseQuery: isEmptyTrialsQuery,
      query: this.buildTrialsCountQuery(
        input,
        appliedRanking,
        searchContext,
        parsedQueryTree,
        nestedTrialsFilters,
        optionalQueries,
        this.h1DiversityScoringFunctionForAssetShouldClause(
          2,
          suppliedFilters,
          keywordSearchFeatureFlags
        ),
        numberOfTrialsHighlights,
        keywordSearchFeatureFlags,
        BOOST_MODE_REPLACE
      )
    });

    assetToShouldClause.set("publicationCount", {
      isEmptyClauseQuery: isEmptyClauseQuery(
        parsedQueryTree,
        nestedPublicationsFilters
      ),
      query: this.buildPublicationsCountQuery(
        input,
        appliedRanking,
        searchContext,
        parsedQueryTree,
        nestedPublicationsFilters,
        optionalQueries,
        keywordSearchFeatureFlags,
        ZERO_WEIGHT_FUNCTION_SCORE,
        BOOST_MODE_DEFAULT,
        numberOfPublicationsHighlights
      )
    });

    assetToShouldClause.set("microBloggingTotal", {
      isEmptyClauseQuery: isEmptyClauseQuery(
        parsedQueryTree,
        nestedPublicationsFilters
      ),
      query: this.buildPublicationsMicroBloggingSumQuery(
        input,
        searchContext,
        parsedQueryTree,
        nestedPublicationsFilters,
        !parsedQueryAlreadyAddedToPublicationsFilters,
        ZERO_WEIGHT_FUNCTION_SCORE,
        BOOST_MODE_DEFAULT
      )
    });
    assetToShouldClause.set("citationTotal", {
      isEmptyClauseQuery: isEmptyClauseQuery(
        parsedQueryTree,
        nestedPublicationsFilters
      ),
      query: this.buildPublicationsCitationSumQuery(
        input,
        searchContext,
        parsedQueryTree,
        nestedPublicationsFilters,
        !parsedQueryAlreadyAddedToPublicationsFilters,
        ZERO_WEIGHT_FUNCTION_SCORE,
        BOOST_MODE_DEFAULT
      )
    });
    assetToShouldClause.set("congressCount", {
      isEmptyClauseQuery: isEmptyClauseQuery(
        parsedQueryTree,
        nestedCongressesFilters
      ),
      query: this.buildCongressCountQuery(
        searchContext,
        parsedQueryTree,
        nestedCongressesFilters,
        optionalQueries,
        ZERO_WEIGHT_FUNCTION_SCORE,
        BOOST_MODE_DEFAULT,
        numberOfCongressesHighlights
      )
    });
    assetToShouldClause.set("paymentTotal", {
      isEmptyClauseQuery: isEmptyClauseQuery(
        parsedQueryTree,
        nestedPaymentsFilters
      ),
      query: this.buildPaymentsSumQuery(
        searchContext,
        parsedQueryTree,
        nestedPaymentsFilters,
        !parsedQueryAlreadyAddedToPaymentsFilters,
        keywordSearchFeatureFlags,
        ZERO_WEIGHT_FUNCTION_SCORE,
        BOOST_MODE_DEFAULT
      )
    });

    // rank based on the diversity of patient population
    if (input.suppliedFilters.claims.showUniquePatients?.value) {
      if (
        this.scoreByPatientPopulationDiversity(
          input,
          keywordSearchFeatureFlags
        ) &&
        !this.inputHasClaimsFilterAsQualifyingConditionForPatients(
          input.suppliedFilters,
          keywordSearchFeatureFlags
        )
      ) {
        assetToShouldClause.set("patientDiversity", {
          isEmptyClauseQuery: true,
          query: this.patientPopulationSearchClause(
            input,
            keywordSearchFeatureFlags
          )
        });
      } else {
        //In case of filters being present querying on child docs is expensive, so we use diagnoses nested docs
        if (
          !this.inputHasPatientClaimsFilter(
            input.suppliedFilters,
            keywordSearchFeatureFlags
          ) &&
          this.isDiagnosisOrCcsrFilterApplied(input)
        ) {
          assetToShouldClause.set("DRG_diagnosesCount", {
            isEmptyClauseQuery: isEmptyClauseQuery(
              parsedQueryTree,
              nestedDiagnosesFilters
            ),
            query: this.buildClaimsSumQuery(
              "diagnoses",
              searchContext,
              this.constructClaimsQueryString(
                parsedQueryTree,
                queryUnderstandingServiceResponse,
                keywordSearchFeatureFlags,
                suppliedFilters
              ),
              nestedDiagnosesFilters,
              this.h1DiversityScoringFunctionForAssetShouldClause(
                1000,
                suppliedFilters,
                keywordSearchFeatureFlags
              ),
              suppliedFilters,
              BOOST_MODE_REPLACE,
              keywordSearchFeatureFlags,
              indicationsIcdCodesQuery
            )
          });
        }
      }
    } else {
      if (claims) {
        if (this.isDiagnosisOrCcsrFilterApplied(input)) {
          assetToShouldClause.set("DRG_diagnosesCount", {
            isEmptyClauseQuery: isEmptyClauseQuery(
              parsedQueryTree,
              nestedDiagnosesFilters
            ),
            query: this.buildClaimsSumQuery(
              "diagnoses",
              searchContext,
              this.constructClaimsQueryString(
                parsedQueryTree,
                queryUnderstandingServiceResponse,
                keywordSearchFeatureFlags,
                suppliedFilters
              ),
              nestedDiagnosesFilters,
              this.h1DiversityScoringFunctionForAssetShouldClause(
                1000,
                suppliedFilters,
                keywordSearchFeatureFlags
              ),
              suppliedFilters,
              BOOST_MODE_REPLACE,
              keywordSearchFeatureFlags,
              indicationsIcdCodesQuery
            )
          });
        }
      }
    }

    if (claims) {
      if (this.isProcedureOrCcsrPxFilterApplied(input)) {
        assetToShouldClause.set("DRG_proceduresCount", {
          isEmptyClauseQuery: isEmptyClauseQuery(
            parsedQueryTree,
            nestedProceduresFilters
          ),
          query: this.buildClaimsSumQuery(
            "procedures",
            searchContext,
            suppliedFilters.claims.proceduresCPT.values.length > 0
              ? undefined
              : parsedQueryTree,
            nestedProceduresFilters,
            this.h1DiversityScoringFunctionForAssetShouldClause(
              1000,
              suppliedFilters,
              keywordSearchFeatureFlags
            ),
            suppliedFilters,
            BOOST_MODE_REPLACE,
            keywordSearchFeatureFlags
          )
        });
      }

      if (!this.hasPrescriptionsAssetFilter(input)) {
        assetToShouldClause.set("num_prescriptions", {
          isEmptyClauseQuery: isEmptyClauseQuery(
            parsedQueryTree,
            nestedPrescriptionsFilters
          ),
          query: this.buildClaimsSumQuery(
            "prescriptions",
            searchContext,
            suppliedFilters.claims.genericNames?.values?.length > 0
              ? undefined
              : parsedQueryTree,
            nestedPrescriptionsFilters,
            this.h1DiversityScoringFunctionForAssetShouldClause(
              1000,
              suppliedFilters,
              keywordSearchFeatureFlags
            ),
            suppliedFilters,
            BOOST_MODE_REPLACE,
            keywordSearchFeatureFlags
          )
        });
      }

      if (getMatchedClaimsDetails) {
        this.logger.info("keyword search is requested for excel export");
        if (
          !this.inputHasPatientClaimsFilter(
            suppliedFilters,
            keywordSearchFeatureFlags
          ) &&
          this.isDiagnosisOrCcsrFilterApplied(input)
        ) {
          assetToShouldClause.set("diagnosesCollection", {
            isEmptyClauseQuery: isEmptyClauseQuery(
              parsedQueryTree,
              nestedDiagnosesFilters
            ),
            query: this.buildClaimsQueryForExports(
              "diagnoses",
              searchContext,
              this.constructClaimsQueryString(
                parsedQueryTree,
                queryUnderstandingServiceResponse,
                keywordSearchFeatureFlags,
                suppliedFilters
              ),
              nestedDiagnosesFilters,
              suppliedFilters,
              keywordSearchFeatureFlags,
              BOOST_MODE_DEFAULT,
              indicationsIcdCodesQuery
            )
          });
        }
        if (this.isProcedureOrCcsrPxFilterApplied(input)) {
          assetToShouldClause.set("proceduresCollection", {
            isEmptyClauseQuery: isEmptyClauseQuery(
              parsedQueryTree,
              nestedProceduresFilters
            ),
            query: this.buildClaimsQueryForExports(
              "procedures",
              searchContext,
              parsedQueryTree,
              nestedProceduresFilters,
              suppliedFilters,
              keywordSearchFeatureFlags,
              BOOST_MODE_DEFAULT
            )
          });
        }
      }
    }

    if (
      enableEnhancedHasCTMSDataFilterBehavior &&
      suppliedFilters.hasCTMSData?.value &&
      !isEmptyTrialsQuery
    ) {
      assetToShouldClause.set(CTMS_TRIAL_COUNT_FIELD, {
        isEmptyClauseQuery: false,
        query: this.buildCTMSTrialsCountQuery(
          searchContext,
          parsedQueryTree,
          nestedTrialsFilters,
          optionalQueries,
          this.h1DiversityScoringFunctionForAssetShouldClause(
            1,
            suppliedFilters,
            keywordSearchFeatureFlags
          ),
          BOOST_MODE_REPLACE,
          CTMS_TRIAL_COUNT_WEIGHT_FOR_HAS_CTMS_FILTER
        )
      });
    }

    return { assetToShouldClause, minimumMatchingAssets };
  }

  private getMustClauses(
    input: KeywordSearchInput,
    searchContext: SearchContext,
    parsedQueryTree: ParsedQueryTree,
    filters: Array<QueryDslQueryContainer>,
    keywordSearchFeatureFlags: KeywordSearchFeatureFlags,
    indicationsIcdCodesQuery?: string,
    indicationsMatchedToQuery?: string[],
    icdCodesForCareClusterSelected?: string[],
    procedureCodesForCareClusterSelected?: string[]
  ): {
    mustClauses: QueryDslQueryContainer[];
    assetToMustClause: Map<
      RootFieldsForScoring,
      {
        isEmptyClauseQuery: boolean;
        query: QueryDslQueryContainer;
      }
    >;
  } {
    const musts: Array<QueryDslQueryContainer> = [];

    const sortBy = input.sortBy;
    if (sortBy.twitterFollowersCount && sortBy.twitterFollowersCount > 0) {
      musts.push({
        function_score: {
          functions: [
            getAssetFunctionScore(
              EMPTY_PARSED_QUERY,
              EMPTY_NESTED_FILTERS,
              "twitterFollowersCount",
              sortBy.twitterFollowersCount
            )
          ]
        }
      });
    }

    if (sortBy.twitterTweetCount && sortBy.twitterTweetCount > 0) {
      musts.push({
        function_score: {
          functions: [
            getAssetFunctionScore(
              EMPTY_PARSED_QUERY,
              EMPTY_NESTED_FILTERS,
              "twitterTweetCount",
              sortBy.twitterTweetCount
            )
          ]
        }
      });
    }

    if (input.projectFeatures.referrals) {
      if (sortBy.referralsReceived > 0) {
        musts.push({
          function_score: {
            functions: [
              getAssetFunctionScore(
                EMPTY_PARSED_QUERY,
                EMPTY_NESTED_FILTERS,
                "referralsReceivedCount",
                sortBy.referralsReceived
              )
            ]
          }
        });
      }

      if (sortBy.referralsSent > 0) {
        musts.push({
          function_score: {
            functions: [
              getAssetFunctionScore(
                EMPTY_PARSED_QUERY,
                EMPTY_NESTED_FILTERS,
                "referralsSentCount",
                sortBy.referralsSent
              )
            ]
          }
        });
      }
    }

    const ageSortWeight: number = sortBy.age ?? 0;
    const ageRanges = input.suppliedFilters.patientsDiversity?.ageRange.values;
    if (ageSortWeight > 0 && ageRanges?.length) {
      musts.push(this.buildAgeSortQuery(ageRanges, ageSortWeight));
    }

    if (
      this.inputHasPatientClaimsFilter(
        input.suppliedFilters,
        keywordSearchFeatureFlags
      )
    ) {
      musts.push(
        this.patientPopulationSearchClause(input, keywordSearchFeatureFlags)
      );
    }

    if (input.sliceOption === SearchSliceOptionsEnum.FullCorpus) {
      musts.push(this.projectIdBoostQuery(input.projectId));
    }

    const assetToMustClause: Map<
      RootFieldsForScoring,
      {
        isEmptyClauseQuery: boolean;
        query: QueryDslQueryContainer;
      }
    > = new Map();
    const mustClausesOfDiagnosesAndCcsr = [];
    const useMatchedAssetCountAsScore =
      this.useMatchedCountAsAssetScoreForIcdOrCcsr(input, parsedQueryTree);
    const isDiagnosisCodeApplied =
      !!input.suppliedFilters.claims.diagnosesICD.values.length;
    const isCcsrApplied = !!input.suppliedFilters.claims.ccsr?.values.length;
    if (!input.suppliedFilters.claims.showUniquePatients?.value) {
      if (isDiagnosisCodeApplied) {
        const nestedDiagnosesFilters = findNestedClaimsFilters(
          filters,
          "DRG_diagnoses",
          keywordSearchFeatureFlags
        );

        const scoringFunction = this.getScoringFunctionBasedOnRanking(
          parsedQueryTree,
          input.suppliedFilters,
          input.sortBy,
          nestedDiagnosesFilters,
          keywordSearchFeatureFlags,
          this.getDRGDiagnosisCountField(
            input.suppliedFilters,
            keywordSearchFeatureFlags
          ),
          input.sortBy.diagnoses,
          undefined,
          this.inputHasPatientClaimsFilter(
            input.suppliedFilters,
            keywordSearchFeatureFlags
          ),
          useMatchedAssetCountAsScore,
          input.app
        );

        const query = this.buildClaimsSumQuery(
          "diagnoses",
          searchContext,
          undefined,
          nestedDiagnosesFilters,
          scoringFunction,
          input.suppliedFilters,
          BOOST_MODE_REPLACE,
          keywordSearchFeatureFlags,
          indicationsIcdCodesQuery
        );
        mustClausesOfDiagnosesAndCcsr.push(query);
        assetToMustClause.set("DRG_diagnosesCount", {
          isEmptyClauseQuery: false,
          query
        });
      }

      if (isCcsrApplied) {
        const nestedCcsrFilters = findNestedClaimsFilters(
          filters,
          "ccsr",
          keywordSearchFeatureFlags
        );
        const scoringFunction = this.getScoringFunctionBasedOnRanking(
          parsedQueryTree,
          input.suppliedFilters,
          input.sortBy,
          nestedCcsrFilters,
          keywordSearchFeatureFlags,
          this.getDRGDiagnosisCountField(
            input.suppliedFilters,
            keywordSearchFeatureFlags
          ),
          input.sortBy.diagnoses,
          undefined,
          this.inputHasPatientClaimsFilter(
            input.suppliedFilters,
            keywordSearchFeatureFlags
          ),
          useMatchedAssetCountAsScore,
          input.app
        );

        const query = this.buildClaimsSumQuery(
          "ccsr",
          searchContext,
          undefined,
          nestedCcsrFilters,
          scoringFunction,
          input.suppliedFilters,
          BOOST_MODE_REPLACE,
          keywordSearchFeatureFlags,
          indicationsIcdCodesQuery
        );
        mustClausesOfDiagnosesAndCcsr.push(query);
        assetToMustClause.set("ccsr", {
          isEmptyClauseQuery: false,
          query
        });
      }
    }

    if (mustClausesOfDiagnosesAndCcsr.length == 2) {
      musts.push({
        bool: {
          should: mustClausesOfDiagnosesAndCcsr,
          minimum_should_match: 1
        }
      });
    } else {
      musts.push(...mustClausesOfDiagnosesAndCcsr);
    }
    //build matched claims tab in exports if care cluster or diagnosis code is applied
    if (
      input.suppliedFilters.getMatchedClaims?.value &&
      (isDiagnosisCodeApplied || isCcsrApplied)
    ) {
      this.logger.info("keyword search is requested for excel export");
      const nestedDiagnosesFilters = findNestedClaimsFilters(
        filters,
        "DRG_diagnoses",
        keywordSearchFeatureFlags
      );
      const exportMatchedClaimsQueryClause = this.buildClaimsQueryForExports(
        "diagnoses",
        searchContext,
        undefined,
        nestedDiagnosesFilters,
        input.suppliedFilters,
        keywordSearchFeatureFlags,
        BOOST_MODE_REPLACE,
        undefined,
        icdCodesForCareClusterSelected
      );
      musts.push(exportMatchedClaimsQueryClause);
    }
    const isProcedureCodeApplied =
      !!input.suppliedFilters.claims.proceduresCPT.values.length;
    const isCcsrPxApplied =
      !!input.suppliedFilters.claims.ccsrPx?.values.length;
    const mustClausesOfProceduresAndCcsrPx = [];
    const useMatchedAssetCountPxAsScore =
      this.useMatchedCountAsAssetScoreForProceduresOrCcsrPx(
        input,
        parsedQueryTree
      );
    if (isProcedureCodeApplied) {
      const nestedProcedureFilters = findNestedClaimsFilters(
        filters,
        "DRG_procedures",
        keywordSearchFeatureFlags
      );

      const scoringFunction = this.getScoringFunctionBasedOnRanking(
        parsedQueryTree,
        input.suppliedFilters,
        input.sortBy,
        nestedProcedureFilters,
        keywordSearchFeatureFlags,
        this.getDRGProceduresCountField(
          input.suppliedFilters,
          keywordSearchFeatureFlags
        ),
        input.sortBy.procedures,
        undefined,
        this.inputHasPatientClaimsFilter(
          input.suppliedFilters,
          keywordSearchFeatureFlags
        ),
        useMatchedAssetCountPxAsScore,
        input.app
      );

      const query = this.buildClaimsSumQuery(
        "procedures",
        searchContext,
        undefined,
        nestedProcedureFilters,
        scoringFunction,
        input.suppliedFilters,
        BOOST_MODE_REPLACE,
        keywordSearchFeatureFlags,
        indicationsIcdCodesQuery
      );
      mustClausesOfProceduresAndCcsrPx.push(query);
      assetToMustClause.set("DRG_proceduresCount", {
        isEmptyClauseQuery: false,
        query
      });
    }
    if (isCcsrPxApplied) {
      const nestedCcsrPxFilters = findNestedClaimsFilters(
        filters,
        "ccsr_px",
        keywordSearchFeatureFlags
      );
      const scoringFunction = this.getScoringFunctionBasedOnRanking(
        parsedQueryTree,
        input.suppliedFilters,
        input.sortBy,
        nestedCcsrPxFilters,
        keywordSearchFeatureFlags,
        this.getDRGProceduresCountField(
          input.suppliedFilters,
          keywordSearchFeatureFlags
        ),
        input.sortBy.procedures,
        undefined,
        this.inputHasPatientClaimsFilter(
          input.suppliedFilters,
          keywordSearchFeatureFlags
        ),
        useMatchedAssetCountAsScore,
        input.app
      );

      const query = this.buildClaimsSumQuery(
        "ccsr_px",
        searchContext,
        undefined,
        nestedCcsrPxFilters,
        scoringFunction,
        input.suppliedFilters,
        BOOST_MODE_REPLACE,
        keywordSearchFeatureFlags,
        undefined
      );
      mustClausesOfProceduresAndCcsrPx.push(query);
      assetToMustClause.set("ccsr_px", {
        isEmptyClauseQuery: false,
        query
      });
    }

    if (mustClausesOfProceduresAndCcsrPx.length == 2) {
      musts.push({
        bool: {
          should: mustClausesOfProceduresAndCcsrPx,
          minimum_should_match: 1
        }
      });
    } else {
      musts.push(...mustClausesOfProceduresAndCcsrPx);
    }

    if (
      input.suppliedFilters.getMatchedClaims?.value &&
      (isProcedureCodeApplied || isCcsrPxApplied)
    ) {
      const nestedProcedureFilters = findNestedClaimsFilters(
        filters,
        "DRG_procedures",
        keywordSearchFeatureFlags
      );
      const query = this.buildClaimsQueryForExports(
        "procedures",
        searchContext,
        parsedQueryTree,
        nestedProcedureFilters,
        input.suppliedFilters,
        keywordSearchFeatureFlags,
        BOOST_MODE_REPLACE,
        undefined,
        undefined,
        procedureCodesForCareClusterSelected
      );
      musts.push(query);
    }

    const hasIndications = !!input.suppliedFilters.indications?.values.length;
    //Use indication score when there is indication supplied or indications that match query
    const shouldUseIndicationScore =
      hasIndications || !!indicationsMatchedToQuery?.length;

    const leaderScoreField = getLeaderScoreField(
      input.suppliedFilters,
      shouldUseIndicationScore
    );

    const scoringFunctions = this.buildScoringFunctions(
      leaderScoreField,
      input.sortBy,
      this.isLeaderboardSortApplied(
        input.suppliedFilters,
        indicationsMatchedToQuery
      ),
      hasIndications
    );

    if (shouldUseIndicationScore && !!leaderScoreField.length) {
      const filters = [];
      const indicationValues = hasIndications
        ? input.suppliedFilters.indications!.values
        : indicationsMatchedToQuery;
      if (indicationValues?.length) {
        filters.push(
          buildTermsQuery("indications.indication.keyword", indicationValues)
        );
      }
      filters.push(buildFieldExistsQuery(leaderScoreField));
      const indicationQuery = this.buildIndicationQuery(
        filters,
        scoringFunctions,
        ["indications.indicationScore", "indications.indicationRisingScore"]
      );
      musts.push(indicationQuery);
    } else if (leaderScoreField.length) {
      const filters = [buildFieldExistsQuery(leaderScoreField)];
      const nonNestedQuery = this.buildNonNestedQueryForLeaderboards(
        filters,
        scoringFunctions
      );
      musts.push(nonNestedQuery);
    }

    if (this.hasPrescriptionsAssetFilter(input)) {
      const nestedPrescriptionsFilters = findNestedFilters(
        filters,
        "prescriptions"
      );

      const scoringFunction = this.getScoringFunctionBasedOnRanking(
        parsedQueryTree,
        input.suppliedFilters,
        input.sortBy,
        nestedPrescriptionsFilters,
        keywordSearchFeatureFlags,
        this.getPrescriptionsCountField(input.suppliedFilters),
        input.sortBy.prescriptions ?? input.sortBy.diagnoses,
        undefined,
        this.inputHasPatientClaimsFilter(
          input.suppliedFilters,
          keywordSearchFeatureFlags
        ),
        undefined,
        input.app
      );

      const query = this.buildClaimsSumQuery(
        "prescriptions",
        searchContext,
        parsedQueryTree,
        nestedPrescriptionsFilters,
        scoringFunction,
        input.suppliedFilters,
        BOOST_MODE_REPLACE,
        keywordSearchFeatureFlags
      );
      musts.push(query);
      assetToMustClause.set("num_prescriptions", {
        isEmptyClauseQuery: false,
        query
      });
    }

    if (
      this.scoreByPatientPopulationDiversity(
        input,
        keywordSearchFeatureFlags
      ) &&
      this.isPatientSort(input) &&
      this.inputHasClaimsFilterAsQualifyingConditionForPatients(
        input.suppliedFilters,
        keywordSearchFeatureFlags
      )
    ) {
      const query = this.patientPopulationSearchClause(
        input,
        keywordSearchFeatureFlags
      );
      musts.push(query);
      assetToMustClause.set("patientDiversity", {
        isEmptyClauseQuery: true,
        query
      });
    }

    return { mustClauses: musts, assetToMustClause };
  }

  private getShouldClausesForStandardWeightedQuery(
    input: KeywordSearchInput,
    { claims }: { claims: boolean },
    searchContext: SearchContext,
    parsedQueryTree: ParsedQueryTree,
    filters: Array<QueryDslQueryContainer>,
    queryUnderstandingServiceResponse:
      | QueryUnderstandingServiceResponse
      | undefined,
    keywordSearchFeatureFlags: KeywordSearchFeatureFlags,
    appliedRanking: RankingType,
    sortBy: WeightedSortBy,
    indicationsIcdCodesQuery?: string,
    numberOfHighlights?: HighlightCountPerAsset
  ): {
    assetToShouldClause: Map<
      RootFieldsForScoring,
      {
        isEmptyClauseQuery: boolean;
        query: QueryDslQueryContainer;
      }
    >;
    minimumMatchingAssets: number;
  } {
    const assetToShouldClause: Map<
      RootFieldsForScoring,
      {
        isEmptyClauseQuery: boolean;
        query: QueryDslQueryContainer;
      }
    > = new Map();

    const {
      enableQueryContextualPaymentsFiltering,
      enableSearchHighlights,
      enableClaimsExports,
      enableEnhancedHasCTMSDataFilterBehavior
    } = keywordSearchFeatureFlags;
    const nestedTrialsFilters = findNestedFilters(filters, "trials");
    const nestedCongressesFilters = findNestedFilters(filters, "congress");
    const nestedPaymentsFilters = findNestedFilters(filters, "payments");
    const nestedPublicationsFilters = findNestedFilters(
      filters,
      "publications"
    );
    const nestedDiagnosesFilters = findNestedClaimsFilters(
      filters,
      "DRG_diagnoses",
      keywordSearchFeatureFlags
    );
    const nestedProceduresFilters = findNestedClaimsFilters(
      filters,
      "DRG_procedures",
      keywordSearchFeatureFlags
    );
    const nestedPrescriptionsFilters = findNestedFilters(
      filters,
      "prescriptions"
    );
    const parsedQueryAlreadyAddedToPaymentsFilters =
      nestedPaymentsFilters.length > 0 &&
      enableQueryContextualPaymentsFiltering;
    const parsedQueryAlreadyAddedToCongressFilters =
      nestedCongressesFilters.length > 0;
    const parsedQueryAlreadyAddedToPublicationsFilters =
      nestedPublicationsFilters.length > 0;
    const parsedQueryAlreadyAddedToTrialsFilters =
      nestedTrialsFilters.length > 0;

    const needToAddHighlights = enableSearchHighlights ? true : false;
    let minimumMatchingAssets = parsedQueryTree
      ? AT_LEAST_ONE_ASSET
      : ALL_ASSETS_OPTIONAL;
    const numberOfTrialsHighlights = numberOfHighlights
      ? numberOfHighlights.trials
      : 0;
    const numberOfCongressesHighlights = numberOfHighlights
      ? numberOfHighlights.congresses
      : 0;
    const numberOfPublicationsHighlights = numberOfHighlights
      ? numberOfHighlights.publications
      : 0;
    const optionalQueries: OptionalQueries = {
      needToAddParsedQueryToCongress: !parsedQueryAlreadyAddedToCongressFilters,
      needToAddParsedQueryToPayments: !parsedQueryAlreadyAddedToPaymentsFilters,
      needToAddParsedQueryToPublications:
        !parsedQueryAlreadyAddedToPublicationsFilters,
      needToAddParsedQueryToTrials: !parsedQueryAlreadyAddedToTrialsFilters,
      needToAddHighlights
    };

    if (
      parsedQueryAlreadyAddedToPaymentsFilters ||
      parsedQueryAlreadyAddedToCongressFilters ||
      parsedQueryAlreadyAddedToPublicationsFilters ||
      parsedQueryAlreadyAddedToTrialsFilters
    ) {
      minimumMatchingAssets = ALL_ASSETS_OPTIONAL;
    }
    const { suppliedFilters } = input;
    const getMatchedClaimsDetails =
      enableClaimsExports && suppliedFilters.getMatchedClaims?.value;

    this.addNestedFilterWhenOnlyDateRangeFilterIsApplied(
      suppliedFilters,
      nestedCongressesFilters,
      nestedTrialsFilters,
      nestedPublicationsFilters
    );
    const isEmptyTrialsQuery = isEmptyClauseQuery(
      parsedQueryTree,
      nestedTrialsFilters
    );

    assetToShouldClause.set("trialCount", {
      isEmptyClauseQuery: isEmptyTrialsQuery,
      query: this.buildTrialsCountQuery(
        input,
        appliedRanking,
        searchContext,
        parsedQueryTree,
        nestedTrialsFilters,
        optionalQueries,
        getAssetFunctionScore(
          parsedQueryTree,
          nestedTrialsFilters,
          "trialCount",
          sortBy.trial
        ),
        numberOfTrialsHighlights,
        keywordSearchFeatureFlags,
        BOOST_MODE_REPLACE
      )
    });
    assetToShouldClause.set("publicationCount", {
      isEmptyClauseQuery: isEmptyClauseQuery(
        parsedQueryTree,
        nestedPublicationsFilters
      ),
      query: this.buildPublicationsCountQuery(
        input,
        appliedRanking,
        searchContext,
        parsedQueryTree,
        nestedPublicationsFilters,
        optionalQueries,
        keywordSearchFeatureFlags,
        getAssetFunctionScore(
          parsedQueryTree,
          nestedPublicationsFilters,
          "publicationCount",
          sortBy.publication
        ),
        BOOST_MODE_REPLACE,
        numberOfPublicationsHighlights
      )
    });
    assetToShouldClause.set("microBloggingTotal", {
      isEmptyClauseQuery: isEmptyClauseQuery(
        parsedQueryTree,
        nestedPublicationsFilters
      ),
      query: this.buildPublicationsMicroBloggingSumQuery(
        input,
        searchContext,
        parsedQueryTree,
        nestedPublicationsFilters,
        !parsedQueryAlreadyAddedToPublicationsFilters,

        getAssetFunctionScore(
          parsedQueryTree,
          nestedPublicationsFilters,
          "microBloggingTotal",
          sortBy.microBloggingCount
        ),
        BOOST_MODE_REPLACE
      )
    });
    assetToShouldClause.set("citationTotal", {
      isEmptyClauseQuery: isEmptyClauseQuery(
        parsedQueryTree,
        nestedPublicationsFilters
      ),
      query: this.buildPublicationsCitationSumQuery(
        input,
        searchContext,
        parsedQueryTree,
        nestedPublicationsFilters,
        !parsedQueryAlreadyAddedToPublicationsFilters,
        getAssetFunctionScore(
          parsedQueryTree,
          nestedPublicationsFilters,
          "citationTotal",
          sortBy.citation
        ),
        BOOST_MODE_REPLACE
      )
    });
    assetToShouldClause.set("congressCount", {
      isEmptyClauseQuery: isEmptyClauseQuery(
        parsedQueryTree,
        nestedCongressesFilters
      ),
      query: this.buildCongressCountQuery(
        searchContext,
        parsedQueryTree,
        nestedCongressesFilters,
        optionalQueries,
        getAssetFunctionScore(
          parsedQueryTree,
          nestedCongressesFilters,
          "congressCount",
          sortBy.congress,
          this.calculateCongressBoostFromQueryIntent(
            queryUnderstandingServiceResponse,
            sortBy,
            keywordSearchFeatureFlags
          )
        ),
        BOOST_MODE_REPLACE,
        numberOfCongressesHighlights
      )
    });
    assetToShouldClause.set("paymentTotal", {
      isEmptyClauseQuery: isEmptyClauseQuery(
        parsedQueryTree,
        nestedPaymentsFilters
      ),
      query: this.buildPaymentsSumQuery(
        searchContext,
        parsedQueryTree,
        nestedPaymentsFilters,
        !parsedQueryAlreadyAddedToPaymentsFilters,
        keywordSearchFeatureFlags,
        getAssetFunctionScore(
          parsedQueryTree,
          nestedPaymentsFilters,
          "paymentTotal",
          sortBy.payment
        ),
        BOOST_MODE_REPLACE
      )
    });

    // When the sortBy is patient then only we want to add this should clause
    if (input.suppliedFilters.claims.showUniquePatients?.value) {
      if (
        this.scoreByPatientPopulationDiversity(
          input,
          keywordSearchFeatureFlags
        ) &&
        this.isPatientSort(input) &&
        !this.inputHasClaimsFilterAsQualifyingConditionForPatients(
          input.suppliedFilters,
          keywordSearchFeatureFlags
        )
      ) {
        assetToShouldClause.set("patientDiversity", {
          isEmptyClauseQuery: true,
          query: this.patientPopulationSearchClause(
            input,
            keywordSearchFeatureFlags
          )
        });
      } else {
        if (
          !this.inputHasPatientClaimsFilter(
            input.suppliedFilters,
            keywordSearchFeatureFlags
          ) &&
          this.isDiagnosisOrCcsrFilterApplied(input)
        ) {
          const useMatchedAssetCountAsScore =
            this.useMatchedCountAsAssetScoreForIcdOrCcsr(
              input,
              parsedQueryTree
            );
          assetToShouldClause.set("DRG_diagnosesCount", {
            isEmptyClauseQuery: isEmptyClauseQuery(
              parsedQueryTree,
              nestedDiagnosesFilters
            ),
            query: this.buildClaimsSumQuery(
              "diagnoses",
              searchContext,
              this.constructClaimsQueryString(
                parsedQueryTree,
                queryUnderstandingServiceResponse,
                keywordSearchFeatureFlags,
                suppliedFilters
              ),
              nestedDiagnosesFilters,
              getAssetFunctionScore(
                parsedQueryTree,
                nestedDiagnosesFilters,
                this.getDRGDiagnosisCountField(
                  suppliedFilters,
                  keywordSearchFeatureFlags
                ),
                sortBy.diagnoses,
                undefined,
                this.inputHasPatientClaimsFilter(
                  suppliedFilters,
                  keywordSearchFeatureFlags
                ),
                useMatchedAssetCountAsScore
              ),
              suppliedFilters,
              BOOST_MODE_REPLACE,
              keywordSearchFeatureFlags,
              indicationsIcdCodesQuery
            )
          });
        }
      }
    } else {
      // if inclusion claims filter is supplied by the user then, diagnosis clause should
      // be in the 'must' clause and not in the 'should' clause.

      if (claims) {
        if (this.isDiagnosisOrCcsrFilterApplied(input)) {
          const useMatchedAssetCountAsScore =
            this.useMatchedCountAsAssetScoreForIcdOrCcsr(
              input,
              parsedQueryTree
            );
          assetToShouldClause.set("DRG_diagnosesCount", {
            isEmptyClauseQuery: isEmptyClauseQuery(
              parsedQueryTree,
              nestedDiagnosesFilters
            ),
            query: this.buildClaimsSumQuery(
              "diagnoses",
              searchContext,
              this.constructClaimsQueryString(
                parsedQueryTree,
                queryUnderstandingServiceResponse,
                keywordSearchFeatureFlags,
                suppliedFilters
              ),
              nestedDiagnosesFilters,
              getAssetFunctionScore(
                parsedQueryTree,
                nestedDiagnosesFilters,
                this.getDRGDiagnosisCountField(
                  suppliedFilters,
                  keywordSearchFeatureFlags
                ),
                sortBy.diagnoses,
                undefined,
                this.inputHasPatientClaimsFilter(
                  suppliedFilters,
                  keywordSearchFeatureFlags
                ),
                useMatchedAssetCountAsScore
              ),
              suppliedFilters,
              BOOST_MODE_REPLACE,
              keywordSearchFeatureFlags,
              indicationsIcdCodesQuery
            )
          });
        }
      }
    }

    if (claims) {
      if (this.isProcedureOrCcsrPxFilterApplied(input)) {
        assetToShouldClause.set("DRG_proceduresCount", {
          isEmptyClauseQuery: isEmptyClauseQuery(
            parsedQueryTree,
            nestedProceduresFilters
          ),
          query: this.buildClaimsSumQuery(
            "procedures",
            searchContext,
            suppliedFilters.claims.proceduresCPT.values.length > 0
              ? undefined
              : parsedQueryTree,
            nestedProceduresFilters,
            getAssetFunctionScore(
              parsedQueryTree,
              nestedProceduresFilters,
              this.getDRGProceduresCountField(
                suppliedFilters,
                keywordSearchFeatureFlags
              ),
              sortBy.procedures
            ),
            suppliedFilters,
            BOOST_MODE_REPLACE,
            keywordSearchFeatureFlags
          )
        });
      }

      if (!this.hasPrescriptionsAssetFilter(input)) {
        assetToShouldClause.set("num_prescriptions", {
          isEmptyClauseQuery: isEmptyClauseQuery(
            parsedQueryTree,
            nestedPrescriptionsFilters
          ),
          query: this.buildClaimsSumQuery(
            "prescriptions",
            searchContext,
            suppliedFilters.claims.genericNames?.values?.length > 0
              ? undefined
              : parsedQueryTree,
            nestedPrescriptionsFilters,
            getAssetFunctionScore(
              parsedQueryTree,
              nestedPrescriptionsFilters,
              this.getPrescriptionsCountField(suppliedFilters),
              sortBy.prescriptions
            ),
            suppliedFilters,
            BOOST_MODE_REPLACE,
            keywordSearchFeatureFlags
          )
        });
      }

      if (getMatchedClaimsDetails) {
        this.logger.info("keyword search is requested for excel export");
        if (
          !this.inputHasPatientClaimsFilter(
            suppliedFilters,
            keywordSearchFeatureFlags
          ) &&
          this.isDiagnosisOrCcsrFilterApplied(input)
        ) {
          assetToShouldClause.set("diagnosesCollection", {
            isEmptyClauseQuery: isEmptyClauseQuery(
              parsedQueryTree,
              nestedDiagnosesFilters
            ),
            query: this.buildClaimsQueryForExports(
              "diagnoses",
              searchContext,
              this.constructClaimsQueryString(
                parsedQueryTree,
                queryUnderstandingServiceResponse,
                keywordSearchFeatureFlags,
                suppliedFilters
              ),
              nestedDiagnosesFilters,
              suppliedFilters,
              keywordSearchFeatureFlags,
              BOOST_MODE_REPLACE,
              indicationsIcdCodesQuery
            )
          });
        }

        if (this.isProcedureOrCcsrPxFilterApplied(input)) {
          assetToShouldClause.set("proceduresCollection", {
            isEmptyClauseQuery: isEmptyClauseQuery(
              parsedQueryTree,
              nestedProceduresFilters
            ),
            query: this.buildClaimsQueryForExports(
              "procedures",
              searchContext,
              parsedQueryTree,
              nestedProceduresFilters,
              suppliedFilters,
              keywordSearchFeatureFlags,
              BOOST_MODE_REPLACE
            )
          });
        }
      }
    }

    if (sortBy.trialPerformance) {
      assetToShouldClause.set(CTMS_TRIAL_COUNT_FIELD, {
        isEmptyClauseQuery: isEmptyClauseQuery(
          parsedQueryTree,
          nestedTrialsFilters
        ),
        query: this.buildCTMSTrialsCountQuery(
          searchContext,
          parsedQueryTree,
          nestedTrialsFilters,
          optionalQueries,
          getAssetFunctionScore(
            parsedQueryTree,
            nestedTrialsFilters,
            CTMS_TRIAL_COUNT_FIELD,
            CTMS_TRIAL_COUNT_WEIGHT_FOR_TRIAL_PERFORMANCE_SORT
          ),
          BOOST_MODE_REPLACE
        )
      });
    } else if (
      enableEnhancedHasCTMSDataFilterBehavior &&
      suppliedFilters.hasCTMSData?.value &&
      !isEmptyTrialsQuery &&
      !this.isSortBySpecificAsset(sortBy)
    ) {
      assetToShouldClause.set(CTMS_TRIAL_COUNT_FIELD, {
        isEmptyClauseQuery: false,
        query: this.buildCTMSTrialsCountQuery(
          searchContext,
          parsedQueryTree,
          nestedTrialsFilters,
          optionalQueries,
          getAssetFunctionScore(
            parsedQueryTree,
            nestedTrialsFilters,
            CTMS_TRIAL_COUNT_FIELD,
            CTMS_TRIAL_COUNT_WEIGHT_FOR_HAS_CTMS_FILTER
          ),
          BOOST_MODE_REPLACE
        )
      });
    }

    if (sortBy.congressContributerRank) {
      assetToShouldClause.set("congressSessionCount", {
        isEmptyClauseQuery: isEmptyClauseQuery(
          parsedQueryTree,
          nestedCongressesFilters
        ),
        query: this.buildCongressCountQueryForContributorSort(
          searchContext,
          parsedQueryTree,
          nestedCongressesFilters,
          optionalQueries
        )
      });
    }

    return { assetToShouldClause, minimumMatchingAssets };
  }

  private getShouldClausesForNewH1DefaultSortQuery(
    input: KeywordSearchInput,
    { claims }: { claims: boolean },
    searchContext: SearchContext,
    parsedQueryTree: ParsedQueryTree,
    filters: Array<QueryDslQueryContainer>,
    queryUnderstandingServiceResponse:
      | QueryUnderstandingServiceResponse
      | undefined,
    keywordSearchFeatureFlags: KeywordSearchFeatureFlags,
    appliedRanking: RankingType,
    indicationsIcdCodesQuery?: string,
    numberOfHighlights?: HighlightCountPerAsset
  ): {
    assetToShouldClause: Map<
      RootFieldsForScoring,
      {
        isEmptyClauseQuery: boolean;
        query: QueryDslQueryContainer;
      }
    >;
    minimumMatchingAssets: number;
  } {
    const assetToShouldClause: Map<
      RootFieldsForScoring,
      {
        isEmptyClauseQuery: boolean;
        query: QueryDslQueryContainer;
      }
    > = new Map();

    const { enableQueryContextualPaymentsFiltering, enableSearchHighlights } =
      keywordSearchFeatureFlags;
    const nestedTrialsFilters = findNestedFilters(filters, "trials");
    const nestedCongressesFilters = findNestedFilters(filters, "congress");
    const nestedPaymentsFilters = findNestedFilters(filters, "payments");

    const nestedPublicationsFilters = findNestedFilters(
      filters,
      "publications"
    );
    const nestedDiagnosesFilters = findNestedClaimsFilters(
      filters,
      "DRG_diagnoses",
      keywordSearchFeatureFlags
    );
    const nestedProceduresFilters = findNestedClaimsFilters(
      filters,
      "DRG_procedures",
      keywordSearchFeatureFlags
    );

    const nestedPrescriptionsFilters = findNestedFilters(
      filters,
      "prescriptions"
    );

    const parsedQueryAlreadyAddedToPaymentsFilters =
      nestedPaymentsFilters.length > 0 &&
      enableQueryContextualPaymentsFiltering;
    const parsedQueryAlreadyAddedToCongressFilters =
      nestedCongressesFilters.length > 0;
    const parsedQueryAlreadyAddedToPublicationsFilters =
      nestedPublicationsFilters.length > 0;
    const parsedQueryAlreadyAddedToTrialsFilters =
      nestedTrialsFilters.length > 0;

    const needToAddHighlights = enableSearchHighlights ? true : false;
    let minimumMatchingAssets = parsedQueryTree
      ? AT_LEAST_ONE_ASSET
      : ALL_ASSETS_OPTIONAL;
    const numberOfTrialsHighlights = numberOfHighlights
      ? numberOfHighlights.trials
      : 0;
    const numberOfCongressesHighlights = numberOfHighlights
      ? numberOfHighlights.congresses
      : 0;
    const isShowUniquePatientsFlagOn =
      input.suppliedFilters.claims.showUniquePatients?.value === true;

    const numberOfPublicationsHighlights = numberOfHighlights
      ? numberOfHighlights.publications
      : 0;
    const optionalQueries: OptionalQueries = {
      needToAddParsedQueryToCongress: !parsedQueryAlreadyAddedToCongressFilters,
      needToAddParsedQueryToPayments: !parsedQueryAlreadyAddedToPaymentsFilters,
      needToAddParsedQueryToPublications:
        !parsedQueryAlreadyAddedToPublicationsFilters,
      needToAddParsedQueryToTrials: !parsedQueryAlreadyAddedToTrialsFilters,
      needToAddHighlights
    };

    if (
      parsedQueryAlreadyAddedToPaymentsFilters ||
      parsedQueryAlreadyAddedToCongressFilters ||
      parsedQueryAlreadyAddedToPublicationsFilters ||
      parsedQueryAlreadyAddedToTrialsFilters
    ) {
      minimumMatchingAssets = ALL_ASSETS_OPTIONAL;
    }
    const { suppliedFilters } = input;

    this.addNestedFilterWhenOnlyDateRangeFilterIsApplied(
      suppliedFilters,
      nestedCongressesFilters,
      nestedTrialsFilters,
      nestedPublicationsFilters
    );
    const isEmptyTrialsQuery = isEmptyClauseQuery(
      parsedQueryTree,
      nestedTrialsFilters
    );

    //Trials, Completed trials
    assetToShouldClause.set("trialCount", {
      isEmptyClauseQuery: isEmptyTrialsQuery,
      query: this.buildTrialsCountQuery(
        input,
        appliedRanking,
        searchContext,
        parsedQueryTree,
        nestedTrialsFilters,
        optionalQueries,
        getNewH1DefaultAssetFunctionScore(
          parsedQueryTree,
          nestedTrialsFilters,
          "trialCount",
          input.sortBy.trial
        ),
        numberOfTrialsHighlights,
        keywordSearchFeatureFlags,
        BOOST_MODE_REPLACE
      )
    });

    //Research payments
    assetToShouldClause.set("paymentTotal", {
      isEmptyClauseQuery: isEmptyClauseQuery(
        parsedQueryTree,
        nestedPaymentsFilters
      ),
      query: this.buildPaymentsSumQuery(
        searchContext,
        parsedQueryTree,
        nestedPaymentsFilters,
        !parsedQueryAlreadyAddedToPaymentsFilters,
        keywordSearchFeatureFlags,
        getNewH1DefaultAssetFunctionScore(
          parsedQueryTree,
          nestedPaymentsFilters,
          "paymentTotal",
          input.sortBy.payment
        ),
        BOOST_MODE_REPLACE
      )
    });

    /**
     * The Idea here is we are using patient has child query in should clause if showUniquePatients is true else we use DRG nested claims.
     */
    if (isShowUniquePatientsFlagOn) {
      if (
        this.shouldPatientHasChildQueryAdded(input, keywordSearchFeatureFlags) //check whether claims filters or patient filter(diversity, age, sex..) are not applied else we add to must clause
      ) {
        const patientHasChildQuery = this.patientPopulationSearchClause(
          input,
          keywordSearchFeatureFlags
        );

        assetToShouldClause.set("totalPatientDocs", {
          isEmptyClauseQuery: true,
          query: toQueryWrappedFunctionScore(
            patientHasChildQuery,
            "totalPatientDocs"
          )
        });
      }
    } else {
      if (claims) {
        if (
          this.isDiagnosisOrCcsrFilterApplied(input) //check whether ccsr and diagnosesICD values are not passed in filters
        ) {
          const useMatchedAssetCountAsScore =
            this.useMatchedCountAsAssetScoreForIcdOrCcsr(
              input,
              parsedQueryTree
            );
          assetToShouldClause.set("DRG_diagnosesCount", {
            isEmptyClauseQuery: isEmptyClauseQuery(
              parsedQueryTree,
              nestedDiagnosesFilters
            ),
            query: this.buildClaimsSumQuery(
              "diagnoses",
              searchContext,
              this.constructClaimsQueryString(
                parsedQueryTree,
                queryUnderstandingServiceResponse,
                keywordSearchFeatureFlags,
                suppliedFilters
              ),
              nestedDiagnosesFilters,
              getNewH1DefaultAssetFunctionScore(
                parsedQueryTree,
                nestedDiagnosesFilters,
                this.getDRGDiagnosisCountField(
                  suppliedFilters,
                  keywordSearchFeatureFlags
                ),
                input.sortBy.diagnoses,
                undefined,
                useMatchedAssetCountAsScore
              ),
              suppliedFilters,
              BOOST_MODE_REPLACE,
              keywordSearchFeatureFlags,
              indicationsIcdCodesQuery
            )
          });
        }
      }
    }

    //Publications where type = trial
    assetToShouldClause.set("publicationCount", {
      isEmptyClauseQuery: isEmptyClauseQuery(
        parsedQueryTree,
        nestedPublicationsFilters
      ),
      query: this.buildPublicationsCountQuery(
        input,
        appliedRanking,
        searchContext,
        parsedQueryTree,
        nestedPublicationsFilters,
        optionalQueries,
        keywordSearchFeatureFlags,
        getNewH1DefaultAssetFunctionScore(
          parsedQueryTree,
          nestedPublicationsFilters,
          "publicationCount",
          input.sortBy.publication
        ),
        BOOST_MODE_REPLACE,
        numberOfPublicationsHighlights
      )
    });

    //Below fields are added so that number of HCPs returned are uniform accross all sorts
    assetToShouldClause.set("congressCount", {
      isEmptyClauseQuery: isEmptyClauseQuery(
        parsedQueryTree,
        nestedCongressesFilters
      ),
      query: this.buildCongressCountQuery(
        searchContext,
        parsedQueryTree,
        nestedCongressesFilters,
        optionalQueries,
        ZERO_WEIGHT_FUNCTION_SCORE,
        BOOST_MODE_DEFAULT,
        numberOfCongressesHighlights
      )
    });

    if (claims) {
      if (this.isProcedureOrCcsrPxFilterApplied(input)) {
        assetToShouldClause.set("DRG_proceduresCount", {
          isEmptyClauseQuery: isEmptyClauseQuery(
            parsedQueryTree,
            nestedProceduresFilters
          ),
          query: this.buildClaimsSumQuery(
            "procedures",
            searchContext,
            parsedQueryTree,
            nestedProceduresFilters,
            ZERO_WEIGHT_FUNCTION_SCORE,
            suppliedFilters,
            BOOST_MODE_REPLACE,
            keywordSearchFeatureFlags
          )
        });
      }

      if (!this.hasPrescriptionsAssetFilter(input)) {
        assetToShouldClause.set("num_prescriptions", {
          isEmptyClauseQuery: isEmptyClauseQuery(
            parsedQueryTree,
            nestedPrescriptionsFilters
          ),
          query: this.buildClaimsSumQuery(
            "prescriptions",
            searchContext,
            parsedQueryTree,
            nestedPrescriptionsFilters,
            ZERO_WEIGHT_FUNCTION_SCORE,
            suppliedFilters,
            BOOST_MODE_REPLACE,
            keywordSearchFeatureFlags
          )
        });
      }
    }
    return { assetToShouldClause, minimumMatchingAssets };
  }

  private isSortBySpecificAsset(sortBy: WeightedSortBy): boolean {
    let numAssetsSortingBy = 0;

    for (const [key, value] of Object.entries(sortBy)) {
      if (
        (key === "trialPerformance" || key === "patientsDiversityRank") &&
        (value as number) > 0
      ) {
        return false;
      }

      if ((value as number) > 0) {
        numAssetsSortingBy++;
      }
    }

    return numAssetsSortingBy === 1;
  }

  private buildPublicationsCountQuery(
    input: KeywordSearchInput,
    appliedRanking: RankingType,
    searchContext: SearchContext,
    parsedQueryTree: ParsedQueryTree,
    nestedPublicationsFilters: Array<QueryDslQueryContainer>,
    optionalQueries: OptionalQueries,
    keywordSearchFeatureFlags: KeywordSearchFeatureFlags,
    scoringFunction: QueryDslFunctionScoreContainer = ZERO_WEIGHT_FUNCTION_SCORE,
    boostMode: QueryDslFunctionBoostMode = BOOST_MODE_DEFAULT,
    numberOfHighlights: number
  ): QueryDslQueryContainer {
    if (
      input.suppliedFilters?.indications?.values?.length === 0 &&
      isEmptyClauseQuery(parsedQueryTree, nestedPublicationsFilters)
    ) {
      return buildFunctionScoreQueryWithOnlyFunctions([scoringFunction]);
    }

    const must: QueryDslQueryContainer = MATCH_ALL;
    const should: QueryDslQueryContainer[] = [];

    if (appliedRanking === RankingType.NEW_TL_H1_DEFAULT_RANK) {
      //Consider only trials whose type = trial for new H1 default ranking
      should.push({
        terms: {
          "publications.type_eng": [...PUBLICATIONS_TYPE_TRIAL],
          boost: 5.0
        }
      });
    }

    let innerHits: SearchInnerHits = {
      name: "publications",
      size: 0
    };

    if (optionalQueries.needToAddHighlights) {
      innerHits = {
        sort: [
          {
            "publications.datePublished": {
              order: "desc" as const
            }
          },
          {
            "publications.citationCount": {
              order: "desc" as const
            }
          }
        ],
        highlight: {
          order: "score",
          fields: {
            "publications.publicationAbstract_eng":
              NO_HIGHLIGHT_FIELD_PARAMETERS,
            "publications.keywords_eng": NO_HIGHLIGHT_FIELD_PARAMETERS,
            "publications.title_eng": NO_HIGHLIGHT_FIELD_PARAMETERS
          }
        },
        size: numberOfHighlights,
        _source: false,
        docvalue_fields: ["publications.id", "publications.citationCount"],
        name: "publications"
      };
    }

    const nestedQuery = this.buildNestedPublicationsQuery(
      input,
      parsedQueryTree,
      nestedPublicationsFilters,
      searchContext === "keyword" ? innerHits : null,
      optionalQueries.needToAddParsedQueryToPublications,
      must,
      should
    );

    return {
      function_score: {
        query: nestedQuery,
        boost_mode: boostMode,
        functions: [scoringFunction]
      }
    };
  }

  private buildPublicationsMicroBloggingSumQuery(
    input: KeywordSearchInput,
    searchContext: SearchContext,
    parsedQueryTree: ParsedQueryTree,
    nestedPublicationsFilters: Array<QueryDslQueryContainer>,
    needToAddParsedQuery: boolean,
    scoringFunction: QueryDslFunctionScoreContainer = ZERO_WEIGHT_FUNCTION_SCORE,
    boostMode: QueryDslFunctionBoostMode = BOOST_MODE_DEFAULT
  ): QueryDslQueryContainer {
    if (isEmptyClauseQuery(parsedQueryTree, nestedPublicationsFilters)) {
      return buildFunctionScoreQueryWithOnlyFunctions([scoringFunction]);
    }

    const innerHits: SearchInnerHits = {
      name: "microBlogging",
      size: 0,
      _source: false,
      docvalue_fields: ["publications.microBloggingCount"]
    };

    const must: QueryDslQueryContainer = {
      function_score: {
        functions: [
          {
            field_value_factor: {
              field: "publications.microBloggingCount",
              missing: 0
            }
          }
        ]
      }
    };

    const nestedQuery = this.buildNestedPublicationsQuery(
      input,
      parsedQueryTree,
      nestedPublicationsFilters,
      searchContext === "keyword" ? innerHits : null,
      needToAddParsedQuery,
      must
    );

    return {
      function_score: {
        query: nestedQuery,
        boost_mode: boostMode,
        functions: [scoringFunction]
      }
    };
  }

  private buildPublicationsCitationSumQuery(
    input: KeywordSearchInput,
    searchContext: SearchContext,
    parsedQueryTree: ParsedQueryTree,
    nestedPublicationsFilters: Array<QueryDslQueryContainer>,
    needToAddParsedQuery: boolean,
    scoringFunction: QueryDslFunctionScoreContainer = ZERO_WEIGHT_FUNCTION_SCORE,
    boostMode: QueryDslFunctionBoostMode = BOOST_MODE_DEFAULT
  ): QueryDslQueryContainer {
    if (isEmptyClauseQuery(parsedQueryTree, nestedPublicationsFilters)) {
      return buildFunctionScoreQueryWithOnlyFunctions([scoringFunction]);
    }

    const innerHits = {
      name: "citations",
      size: 0,
      _source: false,
      docvalue_fields: ["publications.citationCount"]
    };

    const must: QueryDslQueryContainer = {
      function_score: {
        functions: [
          {
            field_value_factor: {
              field: "publications.citationCount",
              missing: 0
            }
          }
        ]
      }
    };

    const nestedQuery = this.buildNestedPublicationsQuery(
      input,
      parsedQueryTree,
      nestedPublicationsFilters,
      searchContext === "keyword" ? innerHits : null,
      needToAddParsedQuery,
      must
    );

    return {
      function_score: {
        query: nestedQuery,
        boost_mode: boostMode,
        functions: [scoringFunction]
      }
    };
  }

  private isQueryAnIndication(
    parsedQueryTree: ParsedQueryTree,
    input: KeywordSearchInput
  ) {
    const indicationValues = input.suppliedFilters.indications?.values;
    return (
      parsedQueryTree &&
      typeof input.query === "string" &&
      typeof parsedQueryTree === "string" &&
      indicationValues &&
      indicationValues.includes(input.query.trim().toLowerCase())
    );
  }

  private buildNestedPublicationsQuery(
    input: KeywordSearchInput,
    parsedQueryTree: ParsedQueryTree,
    nestedPublicationsFilters: Array<QueryDslQueryContainer>,
    innerHits: SearchInnerHits | null,
    needToAddParsedQuery: boolean,
    must: QueryDslQueryContainer = MATCH_ALL,
    should: QueryDslQueryContainer[] = []
  ): QueryDslQueryContainer {
    const filters: Array<QueryDslQueryContainer> = [
      ...nestedPublicationsFilters
    ];

    if (needToAddParsedQuery) {
      // filter the nested publications based on the indication filters applied by the user or implictily applied by the query. This uses KG indication-publication tags.
      const indicationValues = input.suppliedFilters.indications?.values;
      if (indicationValues?.length) {
        filters.push(
          buildTermsQuery("publications.kgIndications", indicationValues)
        );
      }

      // if the user query does not have any matching indications and is not an advanced operator query then we need to add it as a filter.
      const queryIsIndication = this.isQueryAnIndication(
        parsedQueryTree,
        input
      );

      if (parsedQueryTree && !queryIsIndication) {
        filters.push(
          this.parsedQueryTreeToElasticsearchQueriesService.parse(
            parsedQueryTree,
            publicationsQueryFields
          )
        );
      }
    }

    const nestedPublicationsQuery: QueryDslNestedQuery = {
      path: "publications",
      score_mode: "sum",
      query: {
        bool: {
          should,
          must,
          filter: filters
        }
      }
    };

    if (innerHits) {
      nestedPublicationsQuery.inner_hits = innerHits;
    }

    return {
      nested: nestedPublicationsQuery
    };
  }

  private buildTrialsCountQuery(
    input: KeywordSearchInput,
    appliedRanking: RankingType,
    searchContext: SearchContext,
    parsedQueryTree: ParsedQueryTree,
    nestedTrialsFilters: Array<QueryDslQueryContainer>,
    optionalQueries: OptionalQueries,
    scoringFunction: QueryDslFunctionScoreContainer,
    numberOfHighlights: number,
    keywordSearchFeatureFlags: KeywordSearchFeatureFlags,
    boostMode?: QueryDslFunctionBoostMode
  ): QueryDslQueryContainer {
    if (
      input.suppliedFilters?.indications?.values?.length === 0 &&
      isEmptyClauseQuery(parsedQueryTree, nestedTrialsFilters)
    ) {
      return buildFunctionScoreQueryWithOnlyFunctions([scoringFunction]);
    }

    const nestedQuery = this.buildNestedTrialsQuery(
      input,
      appliedRanking,
      searchContext,
      parsedQueryTree,
      nestedTrialsFilters,
      optionalQueries,
      numberOfHighlights,
      keywordSearchFeatureFlags
    );

    return buildFunctionScoreQuery(nestedQuery, [scoringFunction], boostMode);
  }

  private buildAgeSortQuery(
    ageRanges: string[],
    weight: number
  ): QueryDslQueryContainer {
    const nestedPathForAge = "patientsDiversity.age";
    const ageRangeFunction: QueryDslNestedQuery = {
      path: nestedPathForAge,
      score_mode: "sum",
      query: {
        function_score: {
          query: {
            bool: {
              filter: [
                {
                  terms: {
                    [`${nestedPathForAge}.range`]: ageRanges
                  }
                }
              ]
            }
          },
          boost_mode: "replace",
          functions: [
            {
              field_value_factor: {
                field: `${nestedPathForAge}.count`,
                modifier: "none",
                missing: 0
              },
              weight
            }
          ]
        }
      }
    };
    return { nested: ageRangeFunction };
  }

  private buildNestedTrialsQuery(
    input: KeywordSearchInput,
    appliedRanking: RankingType,
    searchContext: SearchContext,
    parsedQueryTree: ParsedQueryTree,
    nestedTrialsFilters: Array<QueryDslQueryContainer>,
    optionalQueries: OptionalQueries,
    numberOfHighlights: number,
    keywordSearchFeatureFlags: KeywordSearchFeatureFlags
  ): QueryDslQueryContainer {
    const filters: Array<QueryDslQueryContainer> = [...nestedTrialsFilters];

    const should: Array<QueryDslQueryContainer> = [];
    if (appliedRanking === RankingType.NEW_TL_H1_DEFAULT_RANK) {
      //Score trials with status "Completed, Active, Active, not recruiting" higher.
      should.push({
        terms: {
          "trials.status_eng": [...TRIAL_STATUS_TERMS]
        }
      });

      //trials that are withing 5 years from now based on start date
      should.push({
        range: {
          "trials.startDate": {
            gte: ELASTICSEARCH_5_YEARS_BACK,
            lte: ELASTICSEARCH_CURRENT_DATE
          }
        }
      });
    }

    if (optionalQueries.needToAddParsedQueryToTrials) {
      // filter the nested trials based on the indication filters applied by the user or implictily applied by the query. This uses KG indication-trials tags.
      const indicationValues = input.suppliedFilters.indications?.values;
      if (indicationValues?.length) {
        filters.push(buildTermsQuery("trials.kgIndications", indicationValues));
      }

      // if the user query does not have any matching indications and is not an advanced operator query then we need to add it as a filter.
      const queryIsIndication = this.isQueryAnIndication(
        parsedQueryTree,
        input
      );
      if (parsedQueryTree && !queryIsIndication) {
        filters.push(
          this.parsedQueryTreeToElasticsearchQueriesService.parse(
            parsedQueryTree,
            trialsQueryFields
          )
        );
      }
    }

    let innerHits: SearchInnerHits = {
      size: 0
    };
    if (optionalQueries.needToAddHighlights) {
      innerHits = {
        sort: [
          { _score: "desc" as ScoreSort },
          {
            "trials.startDate": {
              order: "desc" as const
            }
          }
        ],
        highlight: {
          order: "score",
          fields: {
            "trials.officialTitle_eng": NO_HIGHLIGHT_FIELD_PARAMETERS,
            "trials.briefTitle_eng": NO_HIGHLIGHT_FIELD_PARAMETERS,
            "trials.conditions_eng": NO_HIGHLIGHT_FIELD_PARAMETERS,
            "trials.interventions_eng": NO_HIGHLIGHT_FIELD_PARAMETERS,
            "trials.keywords_eng": NO_HIGHLIGHT_FIELD_PARAMETERS,
            "trials.summary_eng": NO_HIGHLIGHT_FIELD_PARAMETERS
          }
        },
        size: numberOfHighlights,
        _source: false,
        docvalue_fields: ["trials.id"]
      };
    }

    const nestedTrialsQuery: QueryDslNestedQuery = {
      path: "trials",
      score_mode: "sum",
      query: {
        bool: {
          must: {
            match_all: {}
          },
          filter: filters,
          should: should
        }
      },
      inner_hits: innerHits
    };
    if (searchContext === "bulk") {
      nestedTrialsQuery.inner_hits = undefined;
    }

    return {
      nested: nestedTrialsQuery
    };
  }

  private buildCTMSTrialsCountQuery(
    searchContext: SearchContext,
    parsedQueryTree: ParsedQueryTree,
    nestedTrialsFilters: Array<QueryDslQueryContainer>,
    optionalQueries: OptionalQueries,
    scoringFunction: QueryDslFunctionScoreContainer,
    boostMode?: QueryDslFunctionBoostMode,
    weight?: number
  ): QueryDslQueryContainer {
    if (isEmptyClauseQuery(parsedQueryTree, nestedTrialsFilters)) {
      return buildFunctionScoreQueryWithOnlyFunctions([scoringFunction]);
    }

    const nestedQuery = this.buildNestedCTMSTrialsQuery(
      searchContext,
      parsedQueryTree,
      nestedTrialsFilters,
      optionalQueries
    );

    const scoringFunctions = [scoringFunction];

    if (weight) {
      scoringFunctions.unshift({
        weight
      });
    }

    return buildFunctionScoreQuery(nestedQuery, scoringFunctions, boostMode);
  }

  private buildNestedCTMSTrialsQuery(
    searchContext: SearchContext,
    parsedQueryTree: ParsedQueryTree,
    nestedTrialsFilters: Array<QueryDslQueryContainer>,
    optionalQueries: OptionalQueries
  ): QueryDslQueryContainer {
    const filters: Array<QueryDslQueryContainer> = [...nestedTrialsFilters];

    if (optionalQueries.needToAddParsedQueryToTrials && parsedQueryTree) {
      const queryFilter =
        this.parsedQueryTreeToElasticsearchQueriesService.parse(
          parsedQueryTree,
          trialsQueryFields
        );

      filters.push(queryFilter);
    }

    const nestedCTMSTrialsQuery: QueryDslNestedQuery = {
      path: "trials",
      score_mode: "sum",
      query: {
        bool: {
          must: {
            match_all: {}
          },
          must_not: buildTermQuery("trials.source", "h1"),
          filter: filters
        }
      },
      inner_hits: {
        name: "CTMSTrials",
        size: 0
      }
    };
    if (searchContext === "bulk") {
      nestedCTMSTrialsQuery.inner_hits = undefined;
    }
    return {
      nested: nestedCTMSTrialsQuery
    };
  }

  private buildCongressCountQuery(
    searchContext: SearchContext,
    parsedQueryTree: ParsedQueryTree,
    nestedCongressesFilters: Array<QueryDslQueryContainer>,
    optionalQueries: OptionalQueries,
    scoringFunction: QueryDslFunctionScoreContainer = ZERO_WEIGHT_FUNCTION_SCORE,
    boostMode: QueryDslFunctionBoostMode = BOOST_MODE_DEFAULT,
    numberOfHighlights: number
  ): QueryDslQueryContainer {
    if (isEmptyClauseQuery(parsedQueryTree, nestedCongressesFilters)) {
      return buildFunctionScoreQueryWithOnlyFunctions([scoringFunction]);
    }

    const nestedQuery = this.buildNestedCongressQuery(
      searchContext,
      parsedQueryTree,
      nestedCongressesFilters,
      optionalQueries,
      numberOfHighlights,
      MATCH_ALL
    );

    return buildFunctionScoreQuery(nestedQuery, [scoringFunction], boostMode);
  }

  private buildNestedCongressQuery(
    searchContext: SearchContext,
    parsedQueryTree: ParsedQueryTree,
    nestedCongressesFilters: Array<QueryDslQueryContainer>,
    optionalQueries: OptionalQueries,
    numberOfHighlights: number,
    must: QueryDslQueryContainer = MATCH_ALL
  ): QueryDslQueryContainer {
    const filters: Array<QueryDslQueryContainer> = [...nestedCongressesFilters];

    if (optionalQueries.needToAddParsedQueryToCongress && parsedQueryTree) {
      const queryFilter =
        this.parsedQueryTreeToElasticsearchQueriesService.parse(
          parsedQueryTree,
          congressesQueryFields
        );

      filters.push(queryFilter);
    }

    let innerHits: SearchInnerHits = {
      size: 0
    };
    if (optionalQueries.needToAddHighlights) {
      innerHits = {
        sort: [
          { _score: "desc" as ScoreSort },
          { "congress.masterInitialDate": { order: "desc" } }
        ],
        highlight: {
          order: "score",
          fields: {
            "congress.keywords_eng": NO_HIGHLIGHT_FIELD_PARAMETERS,
            "congress.title_eng": NO_HIGHLIGHT_FIELD_PARAMETERS
          }
        },
        size: numberOfHighlights,
        _source: false,
        docvalue_fields: ["congress.id"]
      };
    }
    const nestedCongressQuery: QueryDslNestedQuery = {
      path: "congress",
      score_mode: "sum",
      query: {
        bool: {
          must,
          filter: filters
        }
      },
      inner_hits: innerHits
    };
    if (searchContext === "bulk") {
      nestedCongressQuery.inner_hits = undefined;
    }
    return { nested: nestedCongressQuery };
  }

  private buildNestedCongressQueryForContributorSort(
    searchContext: SearchContext,
    parsedQueryTree: ParsedQueryTree,
    nestedCongressesFilters: Array<QueryDslQueryContainer>,
    optionalQueries: OptionalQueries
  ): QueryDslQueryContainer {
    const filters: Array<QueryDslQueryContainer> = [...nestedCongressesFilters];

    if (optionalQueries.needToAddParsedQueryToCongress && parsedQueryTree) {
      const queryFilter =
        this.parsedQueryTreeToElasticsearchQueriesService.parse(
          parsedQueryTree,
          congressSessionsQueryFields
        );

      filters.push(queryFilter);
    }

    const nestedCongressQuery: QueryDslNestedQuery = {
      path: "congress",
      score_mode: "sum",
      query: {
        function_score: {
          query: {
            bool: {
              filter: filters
            }
          },
          functions: [
            {
              script_score: {
                script: {
                  source: `doc['congress.role'].value === 'Keynote' ? ${CONTRIBUTOR_RANKING_KEYNOTE_SPEAKER_BOOST} : 1`
                }
              }
            }
          ]
        }
      }
    };

    if (searchContext === "bulk") {
      nestedCongressQuery.inner_hits = undefined;
    }

    return {
      function_score: {
        query: {
          nested: nestedCongressQuery
        },
        functions: [
          {
            filter: {
              term: {
                isScholarLeader: true
              }
            },
            weight: CONTRIBUTOR_RANKING_RESEARCH_LEADER_BOOST
          }
        ],
        boost_mode: "sum"
      }
    };
  }

  private buildCongressCountQueryForContributorSort(
    searchContext: SearchContext,
    parsedQueryTree: ParsedQueryTree,
    nestedCongressesFilters: Array<QueryDslQueryContainer>,
    optionalQueries: OptionalQueries
  ): QueryDslQueryContainer {
    return this.buildNestedCongressQueryForContributorSort(
      searchContext,
      parsedQueryTree,
      nestedCongressesFilters,
      optionalQueries
    );
  }

  private buildPaymentsSumQuery(
    searchContext: SearchContext,
    parsedQueryTree: ParsedQueryTree,
    nestedPaymentsFilters: Array<QueryDslQueryContainer>,
    needToAddParsedQuery: boolean,
    keywordSearchFeatureFlags: KeywordSearchFeatureFlags,
    scoringFunction: QueryDslFunctionScoreContainer = ZERO_WEIGHT_FUNCTION_SCORE,
    boostMode: QueryDslFunctionBoostMode = BOOST_MODE_DEFAULT
  ): QueryDslQueryContainer {
    if (isEmptyClauseQuery(parsedQueryTree, nestedPaymentsFilters)) {
      return buildFunctionScoreQueryWithOnlyFunctions([scoringFunction]);
    }

    const must: Array<QueryDslQueryContainer> = [
      {
        function_score: {
          functions: [
            {
              field_value_factor: {
                field: "payments.amount",
                missing: 0
              }
            }
          ]
        }
      }
    ];

    //consider only Research payments for new H1 default ranking
    if (keywordSearchFeatureFlags.enableNewH1DefaultRanking) {
      must.push({
        term: {
          "payments.category": "Research"
        }
      });
    }

    const nestedQuery = this.buildNestedPaymentsQuery(
      searchContext,
      parsedQueryTree,
      nestedPaymentsFilters,
      must,
      needToAddParsedQuery
    );

    return {
      function_score: {
        query: nestedQuery,
        boost_mode: boostMode,
        functions: [scoringFunction]
      }
    };
  }

  private buildNestedPaymentsQuery(
    searchContext: SearchContext,
    parsedQueryTree: ParsedQueryTree,
    nestedPaymentsFilters: Array<QueryDslQueryContainer>,
    must: Array<QueryDslQueryContainer>,
    needToAddParsedQuery: boolean
  ): QueryDslQueryContainer {
    const innerHits: SearchInnerHits = {
      name: "payments",
      size: 0,
      _source: false,
      docvalue_fields: ["payments.amount"]
    };

    const filters: Array<QueryDslQueryContainer> = [...nestedPaymentsFilters];

    if (needToAddParsedQuery && parsedQueryTree) {
      const queryFilter =
        this.parsedQueryTreeToElasticsearchQueriesService.parse(
          parsedQueryTree,
          paymentsQueryFields
        );
      filters.push(queryFilter);
    }

    const nestedPaymentsQuery: QueryDslNestedQuery = {
      path: "payments",
      score_mode: "sum",
      query: {
        bool: {
          must,
          filter: filters
        }
      }
    };

    if (searchContext === "keyword") {
      nestedPaymentsQuery.inner_hits = innerHits;
    }

    return {
      nested: nestedPaymentsQuery
    };
  }

  private buildClaimsSumQuery(
    claimType: ClaimType,
    searchContext: SearchContext,
    parsedQueryTree: ParsedQueryTree,
    nestedClaimsFilters: Array<QueryDslQueryContainer>,
    scoringFunction: QueryDslFunctionScoreContainer = ZERO_WEIGHT_FUNCTION_SCORE,
    suppliedFilters: FilterInterface,
    boostMode: QueryDslFunctionBoostMode = BOOST_MODE_DEFAULT,
    keywordSearchFeatureFlags: KeywordSearchFeatureFlags,
    indicationsIcdCodesQuery?: string
  ): QueryDslQueryContainer {
    if (isEmptyClauseQuery(parsedQueryTree, nestedClaimsFilters)) {
      return buildFunctionScoreQueryWithOnlyFunctions([scoringFunction]);
    }

    let claimsCountField = this.getInternalCountFieldForClaims(
      claimType,
      keywordSearchFeatureFlags,
      suppliedFilters
    );

    if (claimType === "prescriptions") {
      if (suppliedFilters.claims.prescriptionsTimeFrame?.value) {
        claimsCountField =
          claimsCountField +
          `_${suppliedFilters.claims.prescriptionsTimeFrame.value}_year`;
      }
    } else if (suppliedFilters.claims.timeFrame?.value) {
      claimsCountField =
        claimsCountField + `_${suppliedFilters.claims.timeFrame.value}_year`;
    }

    let claimFieldPrefix = "DRG_";

    if (
      claimType === "prescriptions" ||
      claimType === "ccsr" ||
      claimType === "ccsr_px"
    ) {
      claimFieldPrefix = "";
    }

    const innerHits: SearchInnerHits = {
      name: `${claimType}_amount`,
      size: 0,
      _source: false,
      docvalue_fields: [`${claimFieldPrefix}${claimType}.${claimsCountField}`]
    };

    const must: QueryDslQueryContainer = {
      function_score: {
        functions: [
          {
            field_value_factor: {
              field: `${claimFieldPrefix}${claimType}.${claimsCountField}`,
              missing: 0
            }
          }
        ]
      }
    };

    let nestedQuery;

    if (claimType === "prescriptions") {
      nestedQuery = this.buildNestedPrescriptionsQuery(
        searchContext,
        parsedQueryTree,
        nestedClaimsFilters,
        innerHits,
        must
      );
    } else if (claimType === "ccsr" || claimType === "ccsr_px") {
      nestedQuery = this.buildNestedCcsrQuery(
        claimType,
        searchContext,
        nestedClaimsFilters,
        innerHits,
        must
      );
    } else {
      nestedQuery = this.buildNestedClaimsQuery(
        claimType,
        searchContext,
        parsedQueryTree,
        nestedClaimsFilters,
        innerHits,
        must,
        indicationsIcdCodesQuery
      );
    }

    const functionScoreQuery = buildFunctionScoreQuery(
      nestedQuery,
      [scoringFunction],
      boostMode
    );

    return this.buildClaimsQueryWithRegionFilter(
      functionScoreQuery,
      keywordSearchFeatureFlags
    );
  }

  private buildClaimsQueryForExports(
    claimType: ClaimType,
    searchContext: SearchContext,
    parsedQueryTree: ParsedQueryTree,
    nestedClaimsFilters: Array<QueryDslQueryContainer>,
    suppliedFilters: FilterInterface,
    keywordSearchFeatureFlags: KeywordSearchFeatureFlags,
    boostMode?: QueryDslFunctionBoostMode,
    indicationsIcdCodesQuery?: string,
    icdCodesForCareClusterSelected?: string[],
    procedureCodesForCareClusterSelected?: string[]
  ): QueryDslQueryContainer {
    const {
      enableUniquePatientCountForClaims,
      disableUniquePatientCountForOnlyProcedures
    } = keywordSearchFeatureFlags;
    let claimsCountField = this.getInternalCountFieldForClaims(
      claimType,
      keywordSearchFeatureFlags,
      suppliedFilters
    );

    if (suppliedFilters.claims.timeFrame?.value) {
      claimsCountField = `${claimsCountField}_${suppliedFilters.claims.timeFrame.value}_year`;
    }
    let timeFrame = "";
    if (suppliedFilters.claims.timeFrame?.value) {
      timeFrame = `_${suppliedFilters.claims.timeFrame.value}_year`;
    }
    const uniquePatientToggleFromInput: boolean =
      suppliedFilters.claims?.showUniquePatients?.value ?? false;
    const innerHits: SearchInnerHits = {
      name: `${claimType}_collection`,
      size: 10,
      _source: false
    };
    const docValueToBeUsedForDiagnosesClaims =
      enableUniquePatientCountForClaims && uniquePatientToggleFromInput
        ? `${DiagnosesFields.InternalUniqueCount}${timeFrame}`
        : `${DiagnosesFields.InternalCount}${timeFrame}`;
    const docValueToBeUsedForProceduresClaims =
      !disableUniquePatientCountForOnlyProcedures &&
      enableUniquePatientCountForClaims &&
      uniquePatientToggleFromInput
        ? `${ProceduresFields.InternalUniqueCount}${timeFrame}`
        : `${ProceduresFields.InternalCount}${timeFrame}`;

    const docValueForPercentDiagnoses =
      enableUniquePatientCountForClaims && uniquePatientToggleFromInput
        ? `${DiagnosesFields.PercentOfUniqueClaims}${timeFrame}`
        : `${DiagnosesFields.PercentOfClaims}${timeFrame}`;

    const docValueForPercentProcedures =
      !disableUniquePatientCountForOnlyProcedures &&
      enableUniquePatientCountForClaims &&
      uniquePatientToggleFromInput
        ? `${ProceduresFields.PercentOfUniqueClaims}${timeFrame}`
        : `${ProceduresFields.PercentOfClaims}${timeFrame}`;

    if (claimType === "diagnoses") {
      innerHits.docvalue_fields = [
        DiagnosesFields.Description,
        DiagnosesFields.CodeScheme,
        DiagnosesFields.DiagnosisCode,
        docValueForPercentDiagnoses,
        docValueToBeUsedForDiagnosesClaims
      ];
    } else {
      innerHits.docvalue_fields = [
        ProceduresFields.Description,
        ProceduresFields.CodeScheme,
        ProceduresFields.ProcedureCode,
        docValueForPercentProcedures,
        docValueToBeUsedForProceduresClaims
      ];
    }

    const must: QueryDslQueryContainer = {
      function_score: {
        functions: [
          {
            field_value_factor: {
              field: `DRG_${claimType}.${claimsCountField}`,
              missing: 0
            }
          }
        ]
      }
    };

    const nestedQuery = this.buildNestedClaimsQuery(
      claimType,
      searchContext,
      parsedQueryTree,
      nestedClaimsFilters,
      innerHits,
      must,
      indicationsIcdCodesQuery,
      icdCodesForCareClusterSelected,
      procedureCodesForCareClusterSelected
    );

    const functionScoreQuery = {
      function_score: {
        query: nestedQuery,
        boost_mode: boostMode,
        functions: [
          {
            weight: 0
          }
        ]
      }
    };

    return this.buildClaimsQueryWithRegionFilter(
      functionScoreQuery,
      keywordSearchFeatureFlags
    );
  }

  private buildClaimsQueryWithRegionFilter(
    functionScoreQuery: QueryDslQueryContainer,
    featureFlags: KeywordSearchFeatureFlags
  ) {
    const claimsRegionFilter =
      buildClaimsRegionFilterForPeopleSearch(featureFlags);

    if (claimsRegionFilter) {
      return {
        bool: {
          must_not: [claimsRegionFilter],
          must: [functionScoreQuery]
        }
      };
    }

    return functionScoreQuery;
  }

  private buildNestedClaimsQuery(
    claimType: ClaimType,
    searchContext: SearchContext,
    parsedQueryTree: ParsedQueryTree,
    nestedClaimsFilters: Array<QueryDslQueryContainer>,
    innerHits: SearchInnerHits,
    must: QueryDslQueryContainer = MATCH_ALL,
    indicationsIcdCodesQuery?: string,
    icdCodesForCareClusterSelected?: string[],
    procedureCodesForCareClusterSelected?: string[]
  ): QueryDslQueryContainer {
    const filters: Array<QueryDslQueryContainer> = [...nestedClaimsFilters];
    const shoulds: Array<QueryDslQueryContainer> = [];
    const shouldsForCareClusterOrClaim: Array<QueryDslQueryContainer> = [];

    if (parsedQueryTree) {
      const queryFilter =
        this.parsedQueryTreeToElasticsearchQueriesService.parse(
          parsedQueryTree,
          [`DRG_${claimType}.codeAndDescription`],
          ENGLISH
        );

      shoulds.push(queryFilter);
    }

    if (icdCodesForCareClusterSelected?.length) {
      const termsFilter = buildTermsQuery(
        `DRG_diagnoses.diagnosisCode_eng`,
        icdCodesForCareClusterSelected
      );
      shouldsForCareClusterOrClaim.push(termsFilter);
      shouldsForCareClusterOrClaim.push({
        bool: {
          filter: filters
        }
      });
    }
    if (procedureCodesForCareClusterSelected?.length) {
      const termsFilter = buildTermsQuery(
        `DRG_procedures.procedureCode_eng`,
        procedureCodesForCareClusterSelected
      );
      shouldsForCareClusterOrClaim.push(termsFilter);
      shouldsForCareClusterOrClaim.push({
        bool: {
          filter: filters
        }
      });
    }
    // if there are claims filter present then do not add indication filter in nested diagnosis clause
    if (nestedClaimsFilters.length === 0 && indicationsIcdCodesQuery) {
      const queryFilter = {
        terms: {
          [`DRG_${claimType}.diagnosisCode_eng`]: indicationsIcdCodesQuery
            .replace(/\(|\)/g, "")
            .toUpperCase()
            .split("|")
            .map(trim)
        }
      };

      shoulds.push(queryFilter);
    }
    const filterToUse = shouldsForCareClusterOrClaim.length
      ? [
          {
            bool: {
              should: shouldsForCareClusterOrClaim,
              minimum_should_match: 1
            }
          }
        ]
      : filters;

    const nestedClaimsQuery: QueryDslNestedQuery = {
      path: `DRG_${claimType}`,
      score_mode: "sum",
      query: {
        bool: {
          must,
          filter: [
            ...filterToUse,
            {
              bool: {
                should: shoulds,
                minimum_should_match:
                  shoulds.length > 0 ? AT_LEAST_ONE_ASSET : ALL_ASSETS_OPTIONAL
              }
            }
          ]
        }
      }
    };

    if (searchContext === "keyword") {
      nestedClaimsQuery.inner_hits = innerHits;
    }

    return {
      nested: nestedClaimsQuery
    };
  }

  private buildNestedPrescriptionsQuery(
    searchContext: SearchContext,
    parsedQueryTree: ParsedQueryTree,
    nestedClaimsFilters: Array<QueryDslQueryContainer>,
    innerHits: SearchInnerHits,
    must: QueryDslQueryContainer = MATCH_ALL
  ): QueryDslQueryContainer {
    const shoulds: Array<QueryDslQueryContainer> = [];

    if (parsedQueryTree) {
      const queryFilter =
        this.parsedQueryTreeToElasticsearchQueriesService.parse(
          parsedQueryTree,
          [`prescriptions.generic_name.text`, `prescriptions.indications.text`]
        );
      shoulds.push(queryFilter);
    }

    const nestedClaimsQuery: QueryDslNestedQuery = {
      path: `prescriptions`,
      score_mode: "sum",
      query: {
        bool: {
          must,
          filter: [
            ...nestedClaimsFilters,
            {
              bool: {
                should: shoulds.length ? shoulds : undefined,
                minimum_should_match: shoulds.length
                  ? AT_LEAST_ONE_ASSET
                  : undefined
              }
            }
          ]
        }
      }
    };

    if (searchContext === "keyword") {
      nestedClaimsQuery.inner_hits = innerHits;
    }

    return {
      nested: nestedClaimsQuery
    };
  }

  private buildNestedCcsrQuery(
    claimType: ClaimType,
    searchContext: SearchContext,
    nestedClaimsFilters: Array<QueryDslQueryContainer>,
    innerHits: SearchInnerHits,
    must: QueryDslQueryContainer = MATCH_ALL
  ): QueryDslQueryContainer {
    const filters: Array<QueryDslQueryContainer> = [...nestedClaimsFilters];

    const nestedClaimsQuery: QueryDslNestedQuery = {
      path: `${claimType}`,
      score_mode: "sum",
      query: {
        bool: {
          must,
          filter: filters
        }
      }
    };

    if (searchContext === "keyword") {
      nestedClaimsQuery.inner_hits = innerHits;
    }

    return {
      nested: nestedClaimsQuery
    };
  }

  private buildSpecialistFilter(rawQuery: string): QueryDslQueryContainer {
    return {
      simple_query_string: {
        query: rawQuery,
        default_operator: "AND",
        fields: ["specialty_eng.search"],
        flags: "AND|ESCAPE|FUZZY|NEAR|NOT|OR|PHRASE|PRECEDENCE|PREFIX|SLOP"
      }
    };
  }

  private buildAggregations(
    input: Readonly<KeywordSearchInput>,
    parsedQueryTree: ParsedQueryTree,
    queryUnderstandingServiceResponse:
      | QueryUnderstandingServiceResponse
      | undefined,
    keywordSearchFeatureFlags: KeywordSearchFeatureFlags
  ): Record<string, AggregationsAggregationContainer> {
    let aggregations: Record<string, AggregationsAggregationContainer> = {};
    const { precision, geoBoundingBox, zoom, geoStatsRegionLevel } =
      input.suppliedFilters;

    // Deprecated. Remove when it's safe.
    // The new aggregation uses the geo_point_clustering agg
    if (precision?.value) {
      aggregations.location = {
        nested: {
          path: "affiliations"
        },
        aggs: {
          current_affiliations: {
            filter: buildTermQuery("affiliations.isCurrent", true),
            aggs: {
              geo_hash: {
                geohash_grid: {
                  field: "affiliations.institution.location",
                  precision: precision.value,
                  bounds: geoBoundingBox?.value || undefined
                }
              }
            }
          }
        }
      };
    }

    if (
      zoom?.value &&
      geoBoundingBox?.value &&
      input.suppliedFilters?.computeResultGeoClusters?.value === true
    ) {
      aggregations.geo_clustering_location = {
        nested: {
          path: "affiliations"
        },
        aggs: {
          current_affiliations: {
            filter: {
              bool: {
                filter: [
                  {
                    term: {
                      "affiliations.isCurrent": true
                    }
                  },
                  {
                    geo_bounding_box: {
                      // @ts-ignore
                      "affiliations.institution.location": {
                        ...geoBoundingBox.value
                      }
                    }
                  }
                ]
              }
            },
            aggs: {
              geo_clustering: {
                // geo_point_clustering is a custom plugin and we are missing the types
                // https://github.com/opendatasoft/elasticsearch-aggregation-geoclustering
                // @ts-ignore-line
                geo_point_clustering: {
                  field: "affiliations.institution.location",
                  zoom: zoom.value
                }
              }
            }
          }
        }
      };
    }

    if (geoBoundingBox?.value && geoStatsRegionLevel?.value?.claims) {
      const zoomLevel = geoStatsRegionLevel.value.claims.zoomLevel;
      const suppliedFilters = input.suppliedFilters;

      aggregations = {
        ...aggregations,
        locationClaims: {
          nested: {
            path: "affiliations"
          },
          aggs: {
            meta: {
              filter: {
                bool: {
                  filter: [
                    {
                      term: {
                        "affiliations.isCurrent": true
                      }
                    },
                    {
                      geo_bounding_box: {
                        // @ts-ignore
                        "affiliations.institution.location": {
                          ...geoBoundingBox.value
                        }
                      }
                    }
                  ]
                }
              },
              aggs: {
                meta: {
                  terms: {
                    field: `affiliations.institution.filters.${zoomLevel}`,
                    size:
                      zoomLevel === GeoChoroplethLevelTypes.Country ? 200 : 3500
                  },
                  aggs: {
                    allClaims: {
                      reverse_nested: {},
                      aggs: {
                        diagnoses: this.buildGeoClaimsAggregation(
                          "diagnoses",
                          suppliedFilters,
                          keywordSearchFeatureFlags,
                          this.constructClaimsQueryString(
                            parsedQueryTree,
                            queryUnderstandingServiceResponse,
                            keywordSearchFeatureFlags,
                            suppliedFilters
                          )
                        ),
                        procedures: this.buildGeoClaimsAggregation(
                          "procedures",
                          suppliedFilters,
                          keywordSearchFeatureFlags,
                          suppliedFilters.claims.proceduresCPT.values.length > 0
                            ? undefined
                            : parsedQueryTree
                        )
                      }
                    }
                  }
                }
              }
            }
          }
        }
      };
    }

    if (input.sliceOption === SearchSliceOptionsEnum.FullCorpus) {
      aggregations = {
        ...aggregations,
        user_slice_result_count: {
          filter: {
            term: {
              projectIds: input.projectId
            }
          }
        }
      };
    }

    return aggregations;
  }

  private buildGeoClaimsAggregation(
    claimType: ClaimType,
    suppliedFilters: FilterInterface,
    keywordSearchFeatureFlags: KeywordSearchFeatureFlags,
    parsedQuery?: ParsedQueryTree | string
  ): AggregationsAggregationContainer {
    let queryFilter: QueryDslQueryContainer | undefined;
    if (
      claimType === "diagnoses" &&
      suppliedFilters.claims.diagnosesICD.values.length > 0
    ) {
      queryFilter = {
        terms: {
          [`DRG_${claimType}.codeAndDescription_eng.keyword`]:
            suppliedFilters.claims.diagnosesICD.values
        }
      };
    } else if (claimType === "diagnoses" && parsedQuery) {
      queryFilter = this.parsedQueryTreeToElasticsearchQueriesService.parse(
        parsedQuery,
        [`DRG_${claimType}.codeAndDescription`],
        ENGLISH
      );
    }

    if (
      claimType === "procedures" &&
      suppliedFilters.claims.proceduresCPT.values.length > 0
    ) {
      queryFilter = {
        terms: {
          [`DRG_${claimType}.codeAndDescription_eng.keyword`]:
            suppliedFilters.claims.proceduresCPT.values
        }
      };
    } else if (claimType === "procedures" && parsedQuery) {
      queryFilter = this.parsedQueryTreeToElasticsearchQueriesService.parse(
        parsedQuery,
        [`DRG_${claimType}.codeAndDescription`],
        ENGLISH
      );
    }

    let claimsCountField = this.getInternalCountFieldForClaims(
      claimType,
      keywordSearchFeatureFlags,
      suppliedFilters
    );
    if (suppliedFilters.claims.timeFrame?.value) {
      claimsCountField = `${claimsCountField}_${suppliedFilters.claims.timeFrame.value}_year`;
    }

    return {
      nested: {
        path: `DRG_${claimType}`
      },
      aggs: {
        filteredClaims: {
          filter: {
            bool: {
              filter: queryFilter ? [queryFilter] : [{ match_all: {} }]
            }
          },
          aggs: {
            counts: {
              sum: {
                field: `DRG_${claimType}.${claimsCountField}`
              }
            }
          }
        }
      }
    };
  }

  private hasSpecialistIntent(
    queryUnderstandingServiceResponse:
      | QueryUnderstandingServiceResponse
      | undefined
  ): boolean {
    return !!(
      queryUnderstandingServiceResponse?.hasQueryIntent() &&
      queryUnderstandingServiceResponse
        .getQueryIntent()
        ?.hasSpecialistIntent() &&
      queryUnderstandingServiceResponse
        .getQueryIntent()
        ?.getSpecialistIntent()
        ?.getScore() === 1.0
    );
  }

  private hasNPIIntent(
    queryUnderstandingServiceResponse:
      | QueryUnderstandingServiceResponse
      | undefined
  ): boolean {
    return !!(
      queryUnderstandingServiceResponse?.hasQueryIntent() &&
      queryUnderstandingServiceResponse.getQueryIntent()?.hasNpiIntent() &&
      queryUnderstandingServiceResponse
        .getQueryIntent()
        ?.getNpiIntent()
        ?.getScore() === 1.0
    );
  }

  private logKeywordSearchResponse({
    took,
    hits,
    _shards,
    timed_out,
    aggregations
  }: SearchResponse<HCPDocument>) {
    const data: Record<string, any> = {};
    data.took = took;
    data.total = hits.total;
    data.results = hits.hits.map((hit) => {
      const name = [
        hit._source?.firstName_eng,
        hit._source?.middleName_eng,
        hit._source?.lastName_eng
      ]
        .filter((part) => !!part)
        .join(" ");

      return {
        _id: hit._id,
        id: hit._source?.id,
        score: hit._score,
        name
      };
    });
    data.timed_out = timed_out;
    data.shardStatistics = _shards;
    data.aggregations = aggregations;
    this.logger.info({ data });
  }

  private logBulkSearchResponse({
    took,
    hits,
    _shards,
    timed_out
  }: SearchResponse<OnlyIdDocument>) {
    const data: Record<string, any> = {};
    data.took = took;
    data.total = hits.total;
    data.timed_out = timed_out;
    data.shardStatistics = _shards;
    this.logger.info({ data }, "search response");
  }

  private constructClaimsQueryString(
    parsedQueryTree: ParsedQueryTree,
    queryUnderstandingServiceResponse:
      | QueryUnderstandingServiceResponse
      | undefined,
    keywordSearchFeatureFlags: KeywordSearchFeatureFlags,
    suppliedFilters: FilterInterface
  ): ParsedQueryTree {
    if (
      suppliedFilters.claims.diagnosesICD.values.length > 0 ||
      !!suppliedFilters.claims.ccsr?.values.length
    ) {
      return undefined;
    }
    if (
      queryUnderstandingServiceResponse?.getDiagnosisCodesList().length &&
      typeof parsedQueryTree === "string" &&
      keywordSearchFeatureFlags.enableNewClaimsSearch
    ) {
      return queryUnderstandingServiceResponse!
        .getDiagnosisCodesList()
        .map((diagnosisCode) => `(${diagnosisCode}*)`)
        .join(OR);
    } else {
      return parsedQueryTree;
    }
  }

  private getSpecialQueryIntentFilters(
    queryUnderstandingServiceResponse:
      | QueryUnderstandingServiceResponse
      | undefined,
    input: Readonly<KeywordSearchInput>
  ): Array<QueryDslQueryContainer> {
    const filters = [];

    if (this.hasSpecialistIntent(queryUnderstandingServiceResponse)) {
      filters.push(this.buildSpecialistFilter(input.query!));
    }

    if (this.hasNPIIntent(queryUnderstandingServiceResponse)) {
      filters.push({
        bool: {
          should: [
            buildTermQuery("npiNumber", input.query!.trim()),
            buildTermQuery("drCode", input.query!.trim())
          ],
          minimum_should_match: 1
        }
      });
    }
    return filters;
  }

  /*
TODO for keyword intent work:
  1. Remove specialty filter logic
  2. For location intent clause, only consider present work affiliations
  3. Add default indication clause search
 */
  private locationIntentClause(
    queryUnderstandingServiceResponse:
      | QueryUnderstandingServiceResponse
      | undefined
  ): QueryDslQueryContainer[] {
    if (
      !queryUnderstandingServiceResponse?.getQueryIntent()?.getEntitiesList()
    ) {
      return [];
    }
    const shouldClauses: QueryDslQueryContainer[] = [];
    let innerHitCount = 0;
    for (const entity of queryUnderstandingServiceResponse
      .getQueryIntent()!
      .getEntitiesList()) {
      const label = entity
        .getLabelsList()
        .find(
          (e) => e.getIntentType() === QueryIntentProtoEnum.IntentType.LOCATION
        );
      if (
        label &&
        entity.getEntity().length > 0 &&
        label.getScore() > LOCATION_INTENT_SCORE_THRESHOLD
      ) {
        shouldClauses.push({
          nested: {
            path: "affiliations",
            query: {
              function_score: {
                query: {
                  constant_score: {
                    filter: {
                      multi_match: {
                        query: entity.getEntity(),
                        fields: [
                          "affiliations.institution.address.city",
                          "affiliations.institution.address.region",
                          "affiliations.institution.address.country"
                        ],
                        operator: "and"
                      }
                    }
                  }
                },
                functions: [
                  {
                    field_value_factor: {
                      field: "affiliations.accuracyScore",
                      factor: label.getScore() * LOCATION_INTENT_BOOST,
                      modifier: "square",
                      missing: 0
                    }
                  }
                ]
              }
            },
            inner_hits: {
              highlight: {
                order: "score",
                fields: {
                  "affiliations.institution.address.city":
                    NO_HIGHLIGHT_FIELD_PARAMETERS,
                  "affiliations.institution.address.region":
                    NO_HIGHLIGHT_FIELD_PARAMETERS,
                  "affiliations.institution.address.country":
                    NO_HIGHLIGHT_FIELD_PARAMETERS
                }
              },
              _source: false,
              size: NUMBER_OF_HIGHLIGHTS,
              docvalue_fields: [
                "affiliations.id",
                "affiliations.institution.id"
              ],
              name: `${AFFILIATION_HIGHLIGHT_NAME_PREFIX}${innerHitCount}`
            }
          }
        });
        innerHitCount++;
      }
    }
    return shouldClauses;
  }

  private specialtyIntentClause(
    queryUnderstandingServiceResponse:
      | QueryUnderstandingServiceResponse
      | undefined
  ): QueryDslQueryContainer[] {
    if (
      !queryUnderstandingServiceResponse?.getQueryIntent()?.getEntitiesList()
    ) {
      return [];
    }
    const shouldClauses: QueryDslQueryContainer[] = [];
    for (const entity of queryUnderstandingServiceResponse
      .getQueryIntent()!
      .getEntitiesList()) {
      const label = entity
        .getLabelsList()
        .find(
          (e) =>
            e.getIntentType() === QueryIntentProtoEnum.IntentType.SPECIALIST
        );
      if (label && entity.getEntity().length > 0 && label.getScore() > 0) {
        shouldClauses.push({
          match: {
            specialty_eng: {
              query: entity.getEntity(),
              operator: "and",
              boost: label.getScore()
            }
          }
        });
      }
    }
    return shouldClauses;
  }

  private extractSearchTokens(
    queryUnderstandingServiceResponse: QueryUnderstandingServiceResponse
  ): string {
    if (!queryUnderstandingServiceResponse.hasQueryIntent()) {
      return queryUnderstandingServiceResponse.getAugmentedQuery();
    }
    // search tokens are tokens that we want to search and count in assets.
    let queryHasNonSearchTokens = false;
    const searchTokenList: string[] = [];
    for (const entity of queryUnderstandingServiceResponse
      .getQueryIntent()!
      .getEntitiesList()) {
      const labelList = entity.getLabelsList();
      if (labelList.length == 1) {
        const label = labelList[0];
        let addLabelToSearchTokenList = false;
        if (
          label.getIntentType() == QueryIntentProtoEnum.IntentType.LOCATION ||
          label.getIntentType() == QueryIntentProtoEnum.IntentType.SPECIALIST
        ) {
          if (label.getScore() >= KEYWORD_SEARCH_TOKEN_THRESHOLD) {
            queryHasNonSearchTokens = true;
          } else {
            addLabelToSearchTokenList = true;
          }
        } else {
          // label here can be Indication or Unknown
          addLabelToSearchTokenList = true;
        }
        if (addLabelToSearchTokenList) {
          searchTokenList.push(
            label.getSynonyms().length > 0
              ? label.getSynonyms()
              : entity.getEntity()
          );
        }
      } else {
        // if a token has more than one label then the intent is ambiguous and we must search for it
        searchTokenList.push(entity.getEntity());
      }
    }
    this.logger.info(
      {
        queryHasNonSearchTokens,
        augmentedQuery: queryUnderstandingServiceResponse.getAugmentedQuery(),
        searchTokenList
      },
      "Intent based search token extraction"
    );
    const searchTokens: string =
      searchTokenList.length > 0 && queryHasNonSearchTokens
        ? searchTokenList.join(" AND ")
        : queryUnderstandingServiceResponse.getAugmentedQuery();
    return searchTokens;
  }

  private projectIdBoostQuery(projectId: string): QueryDslQueryContainer {
    return {
      bool: {
        minimum_should_match: 1,
        should: [
          {
            constant_score: {
              // this clause is always true, it exists to make the must clause true
              filter: {
                term: {
                  projectIds: "1"
                }
              },
              boost: 0
            }
          },
          {
            constant_score: {
              filter: {
                term: {
                  projectIds: projectId
                }
              },
              boost: PROJECT_ID_BOOST
            }
          }
        ]
      }
    };
  }

  private getInternalCountFieldForClaims(
    claimType: ClaimType,
    featureFlags: KeywordSearchFeatureFlags,
    suppliedFilters: FilterInterface
  ) {
    if (claimType === "prescriptions") {
      return "num_prescriptions";
    }

    const {
      enableUniquePatientCountForClaims,
      disableUniquePatientCountForOnlyProcedures
    } = featureFlags;
    const uniquePatientToggleFromInput: boolean =
      suppliedFilters.claims?.showUniquePatients?.value ?? false;
    const isProcedureWithUniqueCount =
      (claimType === "procedures" || claimType === "ccsr_px") &&
      !disableUniquePatientCountForOnlyProcedures;
    const isDiagnosis = claimType === "diagnoses" || claimType === "ccsr";

    const shouldUseUniquePatientCount =
      uniquePatientToggleFromInput &&
      (isProcedureWithUniqueCount || isDiagnosis) &&
      enableUniquePatientCountForClaims;

    const claimsCountField = shouldUseUniquePatientCount
      ? "internalUniqueCount"
      : "internalCount";
    return claimsCountField;
  }
  private async getMatchedClaimsCountForIEForExports(
    input: Readonly<KeywordSearchInput>,
    data: SearchResponse<HCPDocument>,
    featureFlags: KeywordSearchFeatureFlags
  ): Promise<MatchedClaimCountsFromIEForExports> {
    const diagnosesCountMap: Dictionary<
      Dictionary<{
        count: number;
        scheme: string | undefined;
        description: string | undefined;
      }>
    > = {};
    const proceduresCountMap: Dictionary<
      Dictionary<{
        count: number;
        scheme: string | undefined;
        description: string | undefined;
      }>
    > = {};
    if (
      this.inputHasPatientClaimsFilter(input.suppliedFilters, featureFlags) &&
      input.suppliedFilters.getMatchedClaims?.value
    ) {
      const peopleDocIds = data.hits.hits.map((hit) => hit._id);

      //I/E Claims filters
      const {
        diagnosesCodes: diagnosesCodesFromIE,
        proceduresCodes: proceduresCodesFromIE,
        ccsrDxDescriptions: ccsrDxDescriptionsFromIE,
        ccsrPxDescriptions: ccsrPxDescriptionsFromIE
      } = this.extractInclusionClaimsCodesFromIE(input);

      //Side panel filters
      const sidePanelDiagnosesCodes =
        input.suppliedFilters.claims?.diagnosesICD?.values ?? [];
      const sidePanelProceduresCodes =
        input.suppliedFilters.claims?.proceduresCPT?.values ?? [];
      const sidePanelCcsrDxDescriptions =
        input.suppliedFilters.claims?.ccsr?.values ?? [];
      //NOTE: YASH we don't have ccsrPx in side panel we need to address this later

      //We collect codes from various sources-
      //1. side panel filters- codes, codes in ccsr
      //2. I/E filters - codes, codes in ccsr
      let allCombinedDiagnosesCodes: Array<string> = _.union(
        diagnosesCodesFromIE,
        sidePanelDiagnosesCodes.map(extractClaimsCode)
      );
      let allCombinedProceduresCodes: Array<string> = _.union(
        proceduresCodesFromIE,
        sidePanelProceduresCodes.map(extractClaimsCode)
      );

      //add codes from ccsr
      const ccsrDxDescriptions = _.union(
        ccsrDxDescriptionsFromIE,
        sidePanelCcsrDxDescriptions
      );
      const ccsrPxDescriptions = ccsrPxDescriptionsFromIE;
      if (!_.isEmpty(ccsrDxDescriptions)) {
        const ccsrDxCodes = await this.getClaimCodesForCareClusters(
          ccsrDxDescriptions,
          "ccsr"
        );
        allCombinedDiagnosesCodes = _.union(
          allCombinedDiagnosesCodes,
          ccsrDxCodes
        );
      }
      if (!_.isEmpty(ccsrPxDescriptions)) {
        const ccsrPxCodes = await this.getClaimCodesForCareClusters(
          ccsrPxDescriptions,
          "ccsr_px"
        );
        allCombinedProceduresCodes = _.union(
          allCombinedProceduresCodes,
          ccsrPxCodes
        );
      }

      if (
        peopleDocIds.length &&
        (allCombinedDiagnosesCodes.length || allCombinedProceduresCodes.length)
      ) {
        const searchRequests: Promise<SearchResponse>[] = [];
        //fetch claim code metadata(code, scheme, schemeType, description) from pipeline DB
        const diagnosesClaimCodeMetadataMap =
          await this.claimCodeService.getDiagnosesClaimCodeMetadataMap(
            allCombinedDiagnosesCodes
          );
        const procedureClaimCodeMetadataMap =
          await this.claimCodeService.getProcedureClaimCodeMetadataMap(
            allCombinedProceduresCodes
          );

        peopleDocIds.forEach((peopleDocId) => {
          const shouldClauses = [];
          if (allCombinedDiagnosesCodes.length) {
            shouldClauses.push(
              buildTermsQuery(
                "patientClaims.diagnosisIcdCode",
                allCombinedDiagnosesCodes
              )
            );
          }
          if (allCombinedProceduresCodes.length) {
            shouldClauses.push(
              buildTermsQuery(
                "patientClaims.procedureCode",
                allCombinedProceduresCodes
              )
            );
          }

          const searchQuery: QueryDslQueryContainer = {
            bool: {
              should: shouldClauses,
              minimum_should_match: 1,
              filter: [
                {
                  term: {
                    _routing: peopleDocId
                  }
                }
              ]
            }
          };
          const aggregations = {
            by_icdcode: {
              terms: {
                field: "patientClaims.diagnosisIcdCode",
                include: allCombinedDiagnosesCodes,
                size: 50
              }
            },
            by_procedure_code: {
              terms: {
                field: "patientClaims.procedureCode",
                include: allCombinedProceduresCodes,
                size: 50
              }
            }
          };
          const searchRequest: SearchRequest = {
            index: this.peopleIndex,
            query: searchQuery,
            _source: false,
            size: 0,
            aggs: aggregations
          };
          searchRequests.push(this.elasticService.query(searchRequest));
        });

        const responses = await Promise.all(searchRequests);
        const diagnosesAggregations = responses.map(
          (response) =>
            response.aggregations
              ?.by_icdcode as AggregationsTermsAggregateBase<DocCountBucket>
        );
        const proceduresAggregations = responses.map(
          (response) =>
            response.aggregations
              ?.by_procedure_code as AggregationsTermsAggregateBase<DocCountBucket>
        );

        diagnosesAggregations.forEach((diagnosesBucket, index) => {
          const hcpCountryLocation =
            data.hits.hits[index]?._source?.locations?.at(0)?.country_eng;
          const hcpNormalizedCountryName =
            this.normalizeCountryName(hcpCountryLocation);
          diagnosesCountMap[peopleDocIds[index]] =
            this.buildClaimsCountDictionaryForHCP(
              diagnosesBucket,
              diagnosesClaimCodeMetadataMap,
              hcpNormalizedCountryName
            );
        });
        proceduresAggregations.forEach((proceduresBucket, index) => {
          const hcpCountryLocation =
            data.hits.hits[index]?._source?.locations?.at(0)?.country_eng;
          const hcpNormalizedCountryName =
            this.normalizeCountryName(hcpCountryLocation);
          proceduresCountMap[peopleDocIds[index]] =
            this.buildClaimsCountDictionaryForHCP(
              proceduresBucket,
              procedureClaimCodeMetadataMap,
              hcpNormalizedCountryName
            );
        });

        this.logger.debug(
          {
            diagnosesCountMap,
            proceduresCountMap
          },
          "Individual claim codes matched patient counts for patientClaimsFilter for exports"
        );
      }
    }

    return { diagnosesCountMap, proceduresCountMap };
  }

  buildClaimsCountDictionaryForHCP(
    claimsBucket: AggregationsTermsAggregateBase<DocCountBucket>,
    codeToMetadataMap: Map<string, { scheme: string; description: string }>,
    hcpCountry: string | null
  ) {
    const codesDict: Dictionary<{
      count: number;
      scheme: string | undefined;
      description: string | undefined;
    }> = {};
    const buckets = claimsBucket?.buckets
      ? (claimsBucket.buckets as DocCountBucket[])
      : [];
    buckets.forEach((bucket: DocCountBucket) => {
      // ✅ Initialize object if not already
      if (!codesDict[bucket.key]) {
        codesDict[bucket.key] = {
          count: 0,
          scheme: undefined,
          description: undefined
        };
      }
      codesDict[bucket.key].count = bucket.doc_count;
      if (hcpCountry) {
        const key = this.claimCodeService.claimCodeMetadataMapKeyGenerator(bucket.key, hcpCountry);
        const metadata = codeToMetadataMap.get(key);
        codesDict[bucket.key].description = metadata?.description;
        codesDict[bucket.key].scheme = metadata?.scheme;
      }
    });
    return codesDict;
  }

  /**
   * Normalizes the given country name from the ElasticSearch format to the corresponding
   * country name used in the pipeline database.
   *
   * @param elasticCountry - The country name as returned by ElasticSearch, or `undefined`.
   * @returns The normalized country name as used in the pipeline database, or `null` if the input is `undefined` or not found in the mapping.
   */
  normalizeCountryName(elasticCountry: string | undefined): string | null {
    if (!elasticCountry) return null;
    return (
      elasticCountryFieldToPipelineDBCountryMap[elasticCountry.trim()] ?? null
    );
  }

  extractInclusionClaimsCodesFromIE(input: KeywordSearchInput) {
    const {
      diagnosesCodes,
      proceduresCodes,
      ccsrDxDescriptions,
      ccsrPxDescriptions
    } = this.rulesParserService.extractClaimsAndProcedureCodesFromRules(
      input.suppliedFilters
    );
    return {
      diagnosesCodes,
      proceduresCodes,
      ccsrDxDescriptions,
      ccsrPxDescriptions
    };
  }

  getDRGProceduresCountField(
    suppliedFilters: FilterInterface,
    featureFlags: KeywordSearchFeatureFlags
  ): keyof DRG_proceduresCount {
    const claimsCountFieldSuffix = suppliedFilters.claims.timeFrame?.value
      ? `_${suppliedFilters.claims.timeFrame.value}_year`
      : "";
    const uniquePatientToggleFromInput: boolean =
      suppliedFilters.claims?.showUniquePatients?.value ?? false;

    const claimsCountTotalField =
      !featureFlags.disableUniquePatientCountForOnlyProcedures &&
      featureFlags.enableUniquePatientCountForClaims &&
      uniquePatientToggleFromInput
        ? "DRG_proceduresUniqueCount"
        : "DRG_proceduresCount";

    return `${claimsCountTotalField}${claimsCountFieldSuffix}` as keyof DRG_proceduresCount;
  }

  getDRGDiagnosisCountField(
    suppliedFilters: FilterInterface,
    featureFlags: KeywordSearchFeatureFlags
  ): keyof DRG_diagnosesCount {
    const claimsCountFieldSuffix = suppliedFilters.claims.timeFrame?.value
      ? `_${suppliedFilters.claims.timeFrame.value}_year`
      : "";
    const uniquePatientToggleFromInput: boolean =
      suppliedFilters.claims?.showUniquePatients?.value ?? false;
    const claimsCountTotalField =
      featureFlags.enableUniquePatientCountForClaims &&
      uniquePatientToggleFromInput
        ? "DRG_diagnosesUniqueCount"
        : "DRG_diagnosesCount";
    return `${claimsCountTotalField}${claimsCountFieldSuffix}` as keyof DRG_diagnosesCount;
  }

  getPrescriptionsCountField(
    suppliedFilters: FilterInterface
  ): keyof PrescriptionsCount {
    const claimsCountFieldSuffix = suppliedFilters.claims.prescriptionsTimeFrame
      ?.value
      ? `_${suppliedFilters.claims.prescriptionsTimeFrame.value}_year`
      : "";
    const claimsCountTotalField = "num_prescriptions";
    return `${claimsCountTotalField}${claimsCountFieldSuffix}` as keyof PrescriptionsCount;
  }

  private isH1Ranking(sortBy: WeightedSortBy): boolean {
    return (
      sortBy.trial +
        sortBy.publication +
        sortBy.procedures +
        sortBy.diagnoses +
        sortBy.microBloggingCount +
        sortBy.congress +
        sortBy.grant >
      1
    );
  }

  private isPatientSort(input: KeywordSearchInput): boolean {
    const sortBy = input.sortBy;
    return (
      (input?.suppliedFilters?.claims?.showUniquePatients?.value ?? false) &&
      (sortBy.patientsDiversityRank ?? 0) +
        sortBy.trial +
        sortBy.publication +
        sortBy.procedures +
        sortBy.diagnoses +
        sortBy.microBloggingCount +
        sortBy.congress +
        sortBy.grant >
        0
    );
  }

  private addNestedFilterWhenOnlyDateRangeFilterIsApplied(
    suppliedFilters: FilterInterface,
    nestedCongressesFilters: QueryDslQueryContainer[],
    nestedTrialsFilters: QueryDslQueryContainer[],
    nestedPublicationsFilters: QueryDslQueryContainer[]
  ) {
    // we want to compute matched count when the only filter applied within pubs, trials and congresses context is
    // date range filter. This needs to be handled separately because its a special case where any one of the filter needs
    // satisfy and not all pubs, trials and congress should contain at least one asset within the date range
    if (
      this.keywordFilterClauseBuilderService.eitherMinOrMaxDateRangeValuesWereSupplied(
        suppliedFilters
      )
    ) {
      const [
        congressDateRangeFilter,
        trialsDateRangeFilter,
        publicationsDateRangeFilter
      ] =
        this.keywordFilterClauseBuilderService.addDateRangeFilterInNestedClause(
          suppliedFilters
        );
      if (nestedCongressesFilters.length === 0) {
        nestedCongressesFilters.push(congressDateRangeFilter);
      }
      if (nestedTrialsFilters.length === 0) {
        nestedTrialsFilters.push(trialsDateRangeFilter);
      }
      if (nestedPublicationsFilters.length === 0) {
        nestedPublicationsFilters.push(publicationsDateRangeFilter);
      }
    }
  }

  private async resolveInputQueryToIndications(
    input: Readonly<KeywordSearchInput>
  ): Promise<QueryIndicationInfo> {
    // contains the final list of indication filters that need to be applied to the query
    const finalIndicationFilter: string[] = [];
    // contains the list of indications that are close to the query, the user may not have typed an exact indication
    let indicationsMatchedToQuery: string[] = [];
    let queryIsIndication = false;
    if (!_.isEmpty(input.suppliedFilters?.indications?.values)) {
      // add indication filters added by the user to the list of final indications.
      finalIndicationFilter.push(...input.suppliedFilters!.indications!.values);
    }

    if (input.query && !HAS_ADVANCED_OPERATORS.test(input.query)) {
      const indicationSearchInput: SearchIndicationsByQueryInput = {
        query: input.query,
        indicationType: [IndicationType.L3],
        size: 5,
        indicationSource: [IndicationSource.ALL],
        sortBy: IndicationSortBy.HCP_COMMUNITY_SIZE,
        projectId: input.projectId
      };
      const indicationNodes =
        await this.indicationsTreeSearchService.searchIndicationsByQuery(
          indicationSearchInput
        );
      if (indicationNodes) {
        indicationsMatchedToQuery = indicationNodes.map((node) =>
          node.indicationName.trim().toLowerCase()
        );
      }

      // if the query itself is an indication then we need to ignore other indication matches
      const inputUserQuery = input.query.trim().toLowerCase();
      if (indicationsMatchedToQuery?.includes(inputUserQuery)) {
        indicationsMatchedToQuery = [inputUserQuery];
        queryIsIndication = true;
      }
    }
    // add the indications matched to the query to the final indication filter
    if (indicationsMatchedToQuery.length > 0) {
      finalIndicationFilter.push(...indicationsMatchedToQuery);
    }

    const queryIndicationInfo: QueryIndicationInfo = {
      finalIndicationFilter: [...new Set(finalIndicationFilter)], // remove duplicates between user applied indication filter and query resolved indication fitler
      indicationsMatchedToQuery,
      isIndication: queryIsIndication
    };

    this.logger.info(queryIndicationInfo, "resolved indications from query");

    return queryIndicationInfo;
  }

  private async getClaimCodesForCareClusters(
    careClusters: string[],
    claimType: ClaimType
  ): Promise<string[]> {
    if (claimType === "ccsr") {
      const codes = await this.ccsrIcdMappingRepository.getIcdCodesForCcsr(
        careClusters
      );
      return codes.map((code) => code.icdCode.toUpperCase());
    } else if (claimType === "ccsr_px") {
      const codes = await this.ccsrPxMappingRepository.getProcedureCodesForCcsr(
        careClusters
      );
      return codes.map((code) => code.procedureCode.toUpperCase());
    }
    return [];
  }

  private getRankingType(
    sortBy: WeightedSortBy,
    keywordSearchFeatureFlags: KeywordSearchFeatureFlags,
    app?: Apps
  ): RankingType {
    let ranking: RankingType = RankingType.STANDARD_WEIGHT_RANK;

    if (sortBy.patientsDiversityRank) {
      ranking = RankingType.PATIENT_DIVERSITY_RANK;
    }

    if (
      keywordSearchFeatureFlags.enableNewH1DefaultRanking &&
      sortBy.h1DefaultRank &&
      app === Apps.TRIAL_LANDSCAPE
    ) {
      ranking = RankingType.NEW_TL_H1_DEFAULT_RANK;
    }

    this.logger.info({ ranking, app, sortBy }, "Keyword search ranking type");
    return ranking;
  }

  private getShouldClausesBasedOnRanking(
    input: KeywordSearchInput,
    searchContext: SearchContext,
    parsedQueryTree: ParsedQueryTree,
    filters: Array<QueryDslQueryContainer>,
    queryUnderstandingServiceResponse:
      | QueryUnderstandingServiceResponse
      | undefined,
    keywordSearchFeatureFlags: KeywordSearchFeatureFlags,
    indicationsIcdCodesQuery?: string,
    numberOfHighlights?: HighlightCountPerAsset
  ): {
    assetToShouldClause: Map<
      RootFieldsForScoring,
      {
        isEmptyClauseQuery: boolean;
        query: QueryDslQueryContainer;
      }
    >;
    minimumMatchingAssets: number;
  } {
    const { projectFeatures, sortBy, app } = input;

    const rankingApplied = this.getRankingType(
      sortBy,
      keywordSearchFeatureFlags,
      app
    );

    switch (rankingApplied) {
      case RankingType.PATIENT_DIVERSITY_RANK:
        return this.getShouldClausesForDiversitySortQuery(
          input,
          projectFeatures,
          searchContext,
          parsedQueryTree,
          filters,
          queryUnderstandingServiceResponse,
          keywordSearchFeatureFlags,
          rankingApplied,
          indicationsIcdCodesQuery,
          numberOfHighlights
        );
      case RankingType.NEW_TL_H1_DEFAULT_RANK:
        return this.getShouldClausesForNewH1DefaultSortQuery(
          input,
          projectFeatures,
          searchContext,
          parsedQueryTree,
          filters,
          queryUnderstandingServiceResponse,
          keywordSearchFeatureFlags,
          rankingApplied,
          indicationsIcdCodesQuery,
          numberOfHighlights
        );
      case RankingType.STANDARD_WEIGHT_RANK:
        return this.getShouldClausesForStandardWeightedQuery(
          input,
          projectFeatures,
          searchContext,
          parsedQueryTree,
          filters,
          queryUnderstandingServiceResponse,
          keywordSearchFeatureFlags,
          rankingApplied,
          sortBy,
          indicationsIcdCodesQuery,
          numberOfHighlights
        );
      default:
        return this.getShouldClausesForStandardWeightedQuery(
          input,
          projectFeatures,
          searchContext,
          parsedQueryTree,
          filters,
          queryUnderstandingServiceResponse,
          keywordSearchFeatureFlags,
          rankingApplied,
          sortBy,
          indicationsIcdCodesQuery,
          numberOfHighlights
        );
    }
  }

  private getScoringFunctionBasedOnRanking(
    parsedQueryTree: ParsedQueryTree,
    suppliedFilters: FilterInterface,
    sortBy: WeightedSortBy,
    nestedFilters: Array<QueryDslQueryContainer>,
    keywordSearchFeatureFlags: KeywordSearchFeatureFlags,
    field: RootFieldsForScoring,
    weight = 0,
    intentBoost?: number,
    hasPatientClaimsFilter?: boolean,
    useAssetCountAsScore?: boolean,
    app?: Apps
  ) {
    const rankingApplied = this.getRankingType(
      sortBy,
      keywordSearchFeatureFlags,
      app
    );

    switch (rankingApplied) {
      case RankingType.PATIENT_DIVERSITY_RANK:
        return this.h1DiversityScoringFunctionForAssetShouldClause(
          1000,
          suppliedFilters,
          keywordSearchFeatureFlags
        );
      case RankingType.NEW_TL_H1_DEFAULT_RANK:
        return getNewH1DefaultAssetFunctionScore(
          parsedQueryTree,
          nestedFilters,
          field,
          weight,
          intentBoost,
          hasPatientClaimsFilter
        );
      case RankingType.STANDARD_WEIGHT_RANK:
        return getAssetFunctionScore(
          parsedQueryTree,
          nestedFilters,
          field,
          weight,
          intentBoost,
          hasPatientClaimsFilter,
          useAssetCountAsScore
        );

      default:
        return getAssetFunctionScore(
          parsedQueryTree,
          nestedFilters,
          field,
          weight,
          intentBoost,
          hasPatientClaimsFilter,
          useAssetCountAsScore
        );
    }
  }

  private async getClaimCodesForCareCluster(
    input: KeywordSearchInput,
    claimType: ClaimType
  ): Promise<string[]> {
    const isExport = !!input.suppliedFilters.getMatchedClaims?.value;
    if (claimType == "diagnoses") {
      const diagnosesCareClusterApplied =
        input.suppliedFilters.claims.ccsr?.values;
      if (!!diagnosesCareClusterApplied?.length && isExport) {
        const codes = await this.ccsrIcdMappingRepository.getIcdCodesForCcsr(
          diagnosesCareClusterApplied
        );
        if (!codes?.length) return [];
        return codes.map((code) => code.icdCode.toUpperCase());
      }
    } else {
      const proceduresCareClusterApplied =
        input.suppliedFilters.claims.ccsrPx?.values;
      if (!!proceduresCareClusterApplied?.length && isExport) {
        const codes =
          await this.ccsrPxMappingRepository.getProcedureCodesForCcsr(
            proceduresCareClusterApplied
          );
        if (!codes?.length) return [];
        return codes.map((code) => code.procedureCode.toUpperCase());
      }
    }
    return [];
  }
}
