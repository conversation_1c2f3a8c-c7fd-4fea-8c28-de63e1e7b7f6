{"bool": {"should": [{"function_score": {"query": {"nested": {"path": "diagnoses", "score_mode": "sum", "query": {"function_score": {"boost_mode": "replace", "query": {"simple_query_string": {"_name": "diagnoses", "query": "o9+d-SZDg_", "fields": ["diagnoses.description_eng", "diagnoses.code_eng"], "default_operator": "and", "analyzer": "main_analyzer"}}, "functions": [{"field_value_factor": {"field": "diagnoses.count", "missing": 0}}]}}, "inner_hits": {"_source": false, "docvalue_fields": ["diagnoses.count"], "size": 1000}}}, "boost_mode": "replace", "functions": [{"script_score": {"script": {"source": "\n            double score = _score;\n\n            // Define the original minimum and maximum score\n            double originalMin = params.min; \n            double originalMax = params.max;\n            \n            // Define the target range\n            double targetMin = params.targetMin;\n            double targetMax = params.targetMax;\n\n            // Scale the score to the range [targetMin, targetMax]\n            double scaledScore = targetMin + ((score - originalMin) / (originalMax - originalMin)) * (targetMax - targetMin);\n            \n            // Return the scaled score\n            return scaledScore;\n        ", "params": {"mean": 156874.57, "max": 239001410, "min": 0, "targetMax": 50, "targetMin": 10}}}}]}}, {"function_score": {"query": {"nested": {"path": "trials", "score_mode": "sum", "query": {"function_score": {"boost_mode": "replace", "query": {"bool": {"must": [{"simple_query_string": {"_name": "trials", "query": "o9+d-SZDg_", "fields": ["trials.trials_info"], "default_operator": "and"}}], "should": [{"term": {"trials.status": "Completed"}}]}}}}, "inner_hits": {"_source": false}}}, "boost_mode": "replace", "functions": [{"script_score": {"script": {"source": "\n            double score = _score;\n\n            // Define the original minimum and maximum score\n            double originalMin = params.min; \n            double originalMax = params.max;\n            \n            // Define the target range\n            double targetMin = params.targetMin;\n            double targetMax = params.targetMax;\n\n            // Scale the score to the range [targetMin, targetMax]\n            double scaledScore = targetMin + ((score - originalMin) / (originalMax - originalMin)) * (targetMax - targetMin);\n            \n            // Return the scaled score\n            return scaledScore;\n        ", "params": {"mean": 31.5, "max": 8178, "min": 1, "targetMax": 20000, "targetMin": 10000}}}}]}}, {"function_score": {"query": {"nested": {"path": "payments", "score_mode": "sum", "query": {"function_score": {"boost_mode": "replace", "query": {"bool": {"filter": [{"simple_query_string": {"_name": "payments", "query": "o9+d-SZDg_", "fields": ["payments.payment_info"], "default_operator": "and"}}]}}, "functions": [{"filter": {"term": {"payments.payment_type": "research"}}, "field_value_factor": {"field": "payments.amount", "missing": 0}}]}}, "inner_hits": {"_source": false, "docvalue_fields": ["payments.amount"], "size": 10000}}}, "boost_mode": "replace", "functions": [{"script_score": {"script": {"source": "\n            double score = _score;\n\n            // Define the original minimum and maximum score\n            double originalMin = params.min; \n            double originalMax = params.max;\n            \n            // Define the target range\n            double targetMin = params.targetMin;\n            double targetMax = params.targetMax;\n\n            // Scale the score to the range [targetMin, targetMax]\n            double scaledScore = targetMin + ((score - originalMin) / (originalMax - originalMin)) * (targetMax - targetMin);\n            \n            // Return the scaled score\n            return scaledScore;\n        ", "params": {"mean": 1549991.39, "max": 358108759, "min": 0, "targetMax": 5000, "targetMin": 1000}}}}]}}, {"function_score": {"query": {"nested": {"path": "trials", "score_mode": "sum", "inner_hits": {"name": "trialInvestigatorCount", "_source": false}, "query": {"function_score": {"boost_mode": "replace", "query": {"bool": {"filter": [{"simple_query_string": {"_name": "trials", "query": "o9+d-SZDg_", "fields": ["trials.trials_info"], "default_operator": "and"}}]}}, "functions": [{"script_score": {"script": "(doc['trials.investigator_count'].size()==0 || doc['trials.investigator_count'].value<=0) ? 0 : doc['trials.investigator_count'].value"}}]}}}}, "boost_mode": "replace", "functions": [{"script_score": {"script": {"source": "\n            double score = _score;\n\n            // Define the original minimum and maximum score\n            double originalMin = params.min; \n            double originalMax = params.max;\n            \n            // Define the target range\n            double targetMin = params.targetMin;\n            double targetMax = params.targetMax;\n\n            // Scale the score to the range [targetMin, targetMax]\n            double scaledScore = targetMin + ((score - originalMin) / (originalMax - originalMin)) * (targetMax - targetMin);\n            \n            // Return the scaled score\n            return scaledScore;\n        ", "params": {"mean": 24.57, "max": 1596, "min": 1, "targetMax": 5000, "targetMin": 1000}}}}]}}, {"nested": {"path": "procedures", "query": {"constant_score": {"filter": {"simple_query_string": {"_name": "procedures", "query": "o9+d-SZDg_", "fields": ["procedures.description_eng", "procedures.code_eng"], "default_operator": "and", "analyzer": "main_analyzer"}}, "boost": 0}}, "inner_hits": {"_source": false, "docvalue_fields": ["procedures.count"], "size": 1000}}}, {"nested": {"path": "prescriptions", "query": {"constant_score": {"filter": {"simple_query_string": {"_name": "prescriptions", "query": "o9+d-SZDg_", "fields": ["prescriptions.generic_name.text"], "default_operator": "and", "analyzer": "main_analyzer"}}, "boost": 0}}, "inner_hits": {"_source": false, "docvalue_fields": ["prescriptions.num_prescriptions"], "size": 1000}}}, {"nested": {"path": "congresses", "query": {"constant_score": {"filter": {"simple_query_string": {"_name": "congresses", "query": "o9+d-SZDg_", "fields": ["congresses.congress_info_eng"], "default_operator": "and"}}, "boost": 0}}, "inner_hits": {"_source": false}}}], "minimum_should_match": 1, "filter": [{"term": {"isIol": true}}, {"bool": {"should": [{"bool": {"must": [{"exists": {"field": "projectIds"}}, {"terms": {"projectIds": ["fbndE{:Um#"]}}]}}, {"bool": {"must_not": [{"exists": {"field": "projectIds"}}]}}]}}, {"term": {"join_field": "iol"}}]}}