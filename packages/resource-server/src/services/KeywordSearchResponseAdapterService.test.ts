/* eslint-disable @typescript-eslint/no-non-null-assertion */
import {
  SearchHit,
  SearchHitsMetadata,
  SearchInnerHitsResult,
  SearchTotalHits
} from "@elastic/elasticsearch/lib/api/types";
import {
  FilterInterface,
  KeywordSearchInput,
  QueryIntent
} from "@h1nyc/search-sdk";
import { faker } from "@faker-js/faker";
import {
  createMockInstance,
  generateKeywordSearchFeatureFlags,
  getEmptyKeywordSearchFilters
} from "../util/TestUtils";
import {
  AffiliationInstitutionNestedDocument,
  AffiliationNestedDocument,
  CongressNestedDocument,
  HCPDocument,
  MatchedClaimCountsFromIEForExports,
  Translation
} from "./KeywordSearchResourceServiceRewrite";
import {
  InstitutionDocument,
  KeywordSearchResponseAdapterService
} from "./KeywordSearchResponseAdapterService";
import {
  ENGLISH,
  CHINESE,
  JAPANES<PERSON>,
  Language,
  ALTERNATE_CHINESE_PREFIX,
  ALTERNATE_JAPANESE_PREFIX,
  ALTERNATE_ENGLISH_PREFIX
} from "./LanguageDetectService";
import _, { Dictionary } from "lodash";
import { AffiliationAdapterService } from "./AffiliationAdapterService";
import {
  generateKeywordSearchInput,
  generateStandardWeightedSortBy
} from "./KeywordSearchResourceServiceRewrite.test";
import { PersonSearchResponse } from "@h1nyc/search-sdk";
import { AssetToMatchedCount } from "./KeywordSearchMatchedCountService";
import { DocCountBucket } from "./KeywordAutocompleteResponseAdapterService";

const CHINESE_SIMPLIFIED = `${ALTERNATE_CHINESE_PREFIX}-cn`;
const CHINESE_TRADITIONAL = `${ALTERNATE_CHINESE_PREFIX}-tw`;
const JAPANESE_KANJI = `${ALTERNATE_JAPANESE_PREFIX}-kanji`;
const USER_LANGUAGES = [CHINESE, JAPANESE, ENGLISH];
const ALL_LANGUAGES: Readonly<Array<string>> = [
  CHINESE,
  JAPANESE,
  ENGLISH,
  ALTERNATE_ENGLISH_PREFIX,
  CHINESE_SIMPLIFIED,
  CHINESE_TRADITIONAL,
  JAPANESE_KANJI
];
const NON_ENGLISH_LANGUAGES: Readonly<Array<string>> = _.without(
  ALL_LANGUAGES,
  ENGLISH,
  ALTERNATE_ENGLISH_PREFIX
);
const NON_ENGLISH_USER_LANGUAGES: Readonly<Array<string>> = _.without(
  USER_LANGUAGES,
  ENGLISH
);

function generateMockHit(
  overrides: Partial<HCPDocument> = {},
  overrideInnerHits?: Record<string, SearchInnerHitsResult>,
  isUniquePatientCount?: boolean,
  timeFrame?: number,
  prescriptionsTimeFrame?: number,
  disableUniquePatientCountForProcedures?: boolean
): SearchHit<HCPDocument> {
  let finalTimeFrame = "";
  let finalPrescriptionsTimeFrame = "";
  if (timeFrame) {
    finalTimeFrame = `_${timeFrame}_year`;
  }
  if (prescriptionsTimeFrame) {
    finalPrescriptionsTimeFrame = `_${prescriptionsTimeFrame}_year`;
  }
  const source: HCPDocument = {
    id: faker.datatype.string(),
    h1dn_id: faker.datatype.string(),
    name_eng: `${faker.name.firstName()} ${faker.name.lastName()}`,
    name_cmn: `${faker.name.firstName()} ${faker.name.lastName()}`,
    name_jpn: `${faker.name.firstName()} ${faker.name.lastName()}`,
    firstName_eng: faker.name.firstName(),
    firstName_cmn: faker.name.firstName(),
    firstName_jpn: faker.name.firstName(),
    middleName_eng: faker.name.middleName(),
    middleName_cmn: faker.name.middleName(),
    middleName_jpn: faker.name.middleName(),
    lastName_eng: faker.name.lastName(),
    lastName_cmn: faker.name.lastName(),
    lastName_jpn: faker.name.lastName(),
    isFacultyOpinionsMember: faker.datatype.boolean(),
    hasCtms: faker.datatype.boolean(),
    inCtmsNetwork: faker.datatype.boolean(),
    affiliations: [generateMockAffiliation(), generateMockAffiliation()],
    designations: [],
    emails: [faker.datatype.string(), faker.datatype.string()],
    specialty_eng: [],
    specialty_cmn: [],
    specialty_jpn: [],
    citationTotal: faker.datatype.number(),
    congressCount: faker.datatype.number(),
    paymentTotal: faker.datatype.number(),
    referralsSentCount: faker.datatype.number(),
    referralsReceivedCount: faker.datatype.number(),
    microBloggingTotal: faker.datatype.number(),
    trialCount: faker.datatype.number(),
    trialEnrollmentRate: faker.datatype.number(),
    presentWorkInstitutionCount: faker.datatype.number(),
    publicationCount: faker.datatype.number(),
    totalWorks: faker.datatype.number(),
    DRG_diagnosesCount: faker.datatype.number(),
    DRG_proceduresCount: faker.datatype.number(),
    num_prescriptions: faker.datatype.number(),
    patientsDiversityCount: faker.datatype.number(),
    patientsDiversity: [],
    patientsDiversityPercentile: faker.datatype.number(),
    patientsDiversityRatio: {
      asian: faker.datatype.number(),
      pacificIslander: faker.datatype.number(),
      asianPacificIslander: faker.datatype.number(),
      blackNonHispanic: faker.datatype.number(),
      hispanic: faker.datatype.number(),
      whiteNonHispanic: faker.datatype.number(),
      americanIndianOrAlaskaNative: faker.datatype.number(),
      mixedBrazil: faker.datatype.number(),
      indigenousBrazil: faker.datatype.number(),
      notIdentified: faker.datatype.number()
    },
    providerDiversity: [],
    totalPatientCount: faker.datatype.number(),
    digitalRank: faker.datatype.number(),
    top1PercentileDigitalRank: faker.datatype.boolean(),
    top10PercentileDigitalRank: faker.datatype.boolean(),
    twitterTweetCount: faker.datatype.number(),
    twitterFollowersCount: faker.datatype.number(),
    projectIds: [faker.datatype.string()],
    locations: [],
    overallRisingScore: faker.datatype.number(),
    [`DRG_diagnosesCount${finalTimeFrame}`]: faker.datatype.number(),
    [`DRG_proceduresCount${finalTimeFrame}`]: faker.datatype.number(),
    [`num_prescriptions${finalPrescriptionsTimeFrame}`]:
      faker.datatype.number(),
    totalPatientDocs: faker.datatype.number(),
    congress: [generateMockCongress()]
  };
  const claimCountFieldForDiagnoses = isUniquePatientCount
    ? "internalUniqueCount"
    : "internalCount";
  const claimCountFieldForProcedures =
    isUniquePatientCount && !disableUniquePatientCountForProcedures
      ? "internalUniqueCount"
      : "internalCount";
  const innerHits: Record<string, SearchInnerHitsResult> = {
    publications: {
      hits: {
        total: {
          value: faker.datatype.number(),
          relation: "eq"
        },
        hits: [
          {
            _id: faker.datatype.string(),
            _index: faker.datatype.string(),
            fields: {
              "publications.citationCount": [faker.datatype.number()],
              "publications.id": [faker.datatype.string()]
            },
            highlight: {
              "publications.keywords_eng": [faker.datatype.string()],
              "publications.publicationAbstract_eng": [faker.datatype.string()],
              "publications.title_eng": [faker.datatype.string()]
            },
            sort: [faker.datatype.number()]
          }
        ]
      }
    },
    citations: {
      hits: {
        total: {
          value: faker.datatype.number(),
          relation: "eq"
        },
        hits: [
          {
            _id: faker.datatype.string(),
            _index: faker.datatype.string(),
            fields: {
              "publications.citationCount": [faker.datatype.number()]
            }
          },
          {
            _id: faker.datatype.string(),
            _index: faker.datatype.string(),
            fields: {
              "publications.citationCount": [faker.datatype.number()]
            }
          }
        ]
      }
    },
    microBlogging: {
      hits: {
        total: {
          value: faker.datatype.number(),
          relation: "eq"
        },
        hits: [
          {
            _id: faker.datatype.string(),
            _index: faker.datatype.string(),
            fields: {
              "publications.microBloggingCount": [faker.datatype.number()]
            }
          },
          {
            _id: faker.datatype.string(),
            _index: faker.datatype.string(),
            fields: {
              "publications.microBloggingCount": [faker.datatype.number()]
            }
          }
        ]
      }
    },
    trials: {
      hits: {
        total: {
          value: faker.datatype.number(),
          relation: "eq"
        },
        hits: [
          {
            _index: faker.datatype.string(),
            _id: faker.datatype.string(),
            _nested: {
              field: faker.datatype.string(),
              offset: faker.datatype.number()
            },
            _score: faker.datatype.number(),
            fields: {
              "trials.id": [faker.datatype.string()]
            },
            highlight: {
              "trials.briefTitle_eng": [faker.datatype.string()],
              "trials.conditions_eng": [faker.datatype.string()],
              "trials.officialTitle_eng": [faker.datatype.string()],
              "trials.summary_eng": [faker.datatype.string()],
              "trials.interventions_eng": [faker.datatype.string()],
              "trials.keywords_eng": [faker.datatype.string()]
            }
          }
        ]
      }
    },
    congress: {
      hits: {
        total: {
          value: faker.datatype.number(),
          relation: "eq"
        },
        hits: [
          {
            _index: faker.datatype.string(),
            _id: faker.datatype.string(),
            _nested: {
              field: faker.datatype.string(),
              offset: faker.datatype.number()
            },
            _score: faker.datatype.number(),
            fields: {
              "congress.id": [faker.datatype.string()]
            },
            highlight: {
              "congress.title_eng": [faker.datatype.string()],
              "congress.keywords_eng": [faker.datatype.string()]
            }
          }
        ]
      }
    },
    diagnoses_amount: {
      hits: {
        total: {
          value: faker.datatype.number(),
          relation: "eq"
        },
        hits: [
          {
            _id: faker.datatype.string(),
            _index: faker.datatype.string(),
            fields: {
              [`DRG_diagnoses.${claimCountFieldForDiagnoses}${finalTimeFrame}`]:
                [faker.datatype.number()]
            }
          },
          {
            _id: faker.datatype.string(),
            _index: faker.datatype.string(),
            fields: {
              [`DRG_diagnoses.${claimCountFieldForDiagnoses}${finalTimeFrame}`]:
                [faker.datatype.number()]
            }
          }
        ]
      }
    },
    procedures_amount: {
      hits: {
        total: {
          value: faker.datatype.number(),
          relation: "eq"
        },
        hits: [
          {
            _id: faker.datatype.string(),
            _index: faker.datatype.string(),
            fields: {
              [`DRG_procedures.${claimCountFieldForProcedures}${finalTimeFrame}`]:
                [faker.datatype.number()]
            }
          },
          {
            _id: faker.datatype.string(),
            _index: faker.datatype.string(),
            fields: {
              [`DRG_procedures.${claimCountFieldForProcedures}${finalTimeFrame}`]:
                [faker.datatype.number()]
            }
          }
        ]
      }
    },
    prescriptions_amount: {
      hits: {
        total: {
          value: faker.datatype.number(),
          relation: "eq"
        },
        hits: [
          {
            _id: faker.datatype.string(),
            _index: faker.datatype.string(),
            fields: {
              [`prescriptions.num_prescriptions${finalPrescriptionsTimeFrame}`]:
                [faker.datatype.number()]
            }
          },
          {
            _id: faker.datatype.string(),
            _index: faker.datatype.string(),
            fields: {
              [`prescriptions.num_prescriptions${finalPrescriptionsTimeFrame}`]:
                [faker.datatype.number()]
            }
          }
        ]
      }
    },
    diagnoses_collection: {
      hits: {
        total: {
          value: faker.datatype.number(),
          relation: "eq"
        },
        hits: [
          {
            _id: faker.datatype.string(),
            _index: faker.datatype.string(),
            fields: {
              "DRG_diagnoses.description_eng.keyword": [
                faker.datatype.string()
              ],
              "DRG_diagnoses.codeScheme.keyword": [faker.datatype.string()],
              [`DRG_diagnoses.${claimCountFieldForDiagnoses}${finalTimeFrame}`]:
                [faker.datatype.number()],
              "DRG_diagnoses.pctOfClaims": [faker.datatype.number()],
              "DRG_diagnoses.diagnosisCode_eng": [faker.datatype.string()]
            }
          },
          {
            _id: faker.datatype.string(),
            _index: faker.datatype.string(),
            fields: {
              "DRG_diagnoses.description_eng.keyword": [
                faker.datatype.string()
              ],
              "DRG_diagnoses.codeScheme.keyword": [faker.datatype.string()],
              [`DRG_diagnoses.${claimCountFieldForDiagnoses}${finalTimeFrame}`]:
                [faker.datatype.number()],
              "DRG_diagnoses.pctOfClaims": [faker.datatype.number()],
              "DRG_diagnoses.diagnosisCode_eng": [faker.datatype.string()]
            }
          }
        ]
      }
    },
    procedures_collection: {
      hits: {
        total: {
          value: faker.datatype.number(),
          relation: "eq"
        },
        hits: [
          {
            _id: faker.datatype.string(),
            _index: faker.datatype.string(),
            fields: {
              "DRG_procedures.description_eng.keyword": [
                faker.datatype.string()
              ],
              "DRG_procedures.codeScheme": [faker.datatype.string()],
              [`DRG_procedures.${claimCountFieldForProcedures}${finalTimeFrame}`]:
                [faker.datatype.number()],
              "DRG_procedures.percentage": [faker.datatype.number()],
              "DRG_procedures.procedureCode_eng.keyword": [
                faker.datatype.string()
              ]
            }
          },
          {
            _id: faker.datatype.string(),
            _index: faker.datatype.string(),
            fields: {
              "DRG_procedures.description_eng.keyword": [
                faker.datatype.string()
              ],
              "DRG_procedures.codeScheme": [faker.datatype.string()],
              [`DRG_procedures.${claimCountFieldForProcedures}${finalTimeFrame}`]:
                [faker.datatype.number()],
              "DRG_procedures.percentage": [faker.datatype.number()],
              "DRG_procedures.procedureCode_eng.keyword": [
                faker.datatype.string()
              ]
            }
          }
        ]
      }
    },
    referralsReceived_count: {
      hits: {
        total: {
          value: faker.datatype.number(),
          relation: "eq"
        },
        hits: []
      }
    },
    referralsSent_count: {
      hits: {
        total: {
          value: faker.datatype.number(),
          relation: "eq"
        },
        hits: []
      }
    },
    DRG_diagnoses: {
      hits: {
        total: {
          value: faker.datatype.number(),
          relation: "eq"
        },
        hits: [
          {
            _index: faker.datatype.string(),
            _id: faker.datatype.string(),
            _nested: {
              field: faker.datatype.string(),
              offset: faker.datatype.number()
            },
            _score: faker.datatype.number(),
            highlight: {
              "DRG_diagnoses.codeAndDescription_eng": [faker.datatype.string()]
            }
          }
        ]
      }
    },
    DRG_procedures: {
      hits: {
        total: {
          value: faker.datatype.number(),
          relation: "eq"
        },
        hits: [
          {
            _index: faker.datatype.string(),
            _id: faker.datatype.string(),
            _nested: {
              field: faker.datatype.string(),
              offset: faker.datatype.number()
            },
            _score: faker.datatype.number(),
            highlight: {
              "DRG_procedures.codeAndDescription_eng": [faker.datatype.string()]
            }
          }
        ]
      }
    },
    payments: {
      hits: {
        total: {
          value: faker.datatype.number(),
          relation: "eq"
        },
        hits: [
          {
            _id: faker.datatype.string(),
            _index: faker.datatype.string(),
            fields: {
              "payments.amount": [faker.datatype.number()]
            }
          },
          {
            _id: faker.datatype.string(),
            _index: faker.datatype.string(),
            fields: {
              "payments.amount": [faker.datatype.number()]
            }
          }
        ]
      }
    },
    indications: {
      hits: {
        total: {
          value: faker.datatype.number(),
          relation: "eq"
        },
        hits: [
          {
            _id: faker.datatype.string(),
            _index: faker.datatype.string(),
            fields: {
              [`indications.indicationScore`]: [faker.datatype.number()],
              [`indications.indicationRisingScore`]: [faker.datatype.number()]
            }
          },
          {
            _id: faker.datatype.string(),
            _index: faker.datatype.string(),
            fields: {
              [`indications.indicationScore`]: [faker.datatype.number()],
              [`indications.indicationRisingScore`]: [faker.datatype.number()]
            }
          }
        ]
      }
    }
  };

  return {
    _id: faker.datatype.hexadecimal({ length: 10 }),
    _index: faker.datatype.string(),
    _score: faker.datatype.number(),
    _source: { ...source, ...overrides },
    inner_hits: overrideInnerHits
      ? { ...innerHits, ...overrideInnerHits }
      : innerHits
  };
}

function generateMockSynonyms() {
  return [
    faker.datatype.string(),
    faker.datatype.string(),
    faker.datatype.string()
  ];
}

function generateMockAffiliation(
  overrides: Partial<AffiliationNestedDocument> = {}
): AffiliationNestedDocument {
  const affiliation = {
    id: faker.datatype.number(),
    departmentTranslations: [
      {
        department: faker.commerce.department(),
        languageCode: faker.helpers.arrayElement(ALL_LANGUAGES) as Language
      },
      {
        department: faker.commerce.department(),
        languageCode: faker.helpers.arrayElement(ALL_LANGUAGES) as Language
      }
    ],
    titleTranslations: [
      {
        titles: [faker.name.jobTitle(), faker.name.jobTitle()],
        languageCode: faker.helpers.arrayElement(ALL_LANGUAGES) as Language
      },
      {
        titles: [faker.name.jobTitle(), faker.name.jobTitle()],
        languageCode: faker.helpers.arrayElement(ALL_LANGUAGES) as Language
      }
    ],
    type: faker.datatype.string(),
    isCurrent: true,
    titles: [faker.datatype.string(), faker.datatype.string()],
    accuracyScore: faker.datatype.number(),
    tlAccuracyScore: faker.datatype.number(),
    claimsNumbers: faker.datatype.number(),
    claimsStates: [faker.datatype.string(), faker.datatype.string()],
    institution: generateMockInstitution(),
    otherSignals: []
  };

  return { ...affiliation, ...overrides };
}

function generateMockCongress(
  overrides: Partial<CongressNestedDocument> = {}
): CongressNestedDocument {
  const congress = {
    id: faker.datatype.string(),
    role: faker.datatype.string(),
    name_eng: faker.datatype.string(),
    title_eng: faker.datatype.string(),
    title_cmn: faker.datatype.string(),
    title_jpn: faker.datatype.string()
  };

  return { ...congress, ...overrides };
}

function generateMockInstitution(
  overrides: Partial<AffiliationInstitutionNestedDocument> = {}
): AffiliationInstitutionNestedDocument {
  const ultimateParentId = faker.datatype.number();
  const institution = {
    id: faker.datatype.number(),
    address: {
      id: faker.datatype.number(),
      city: faker.datatype.string(),
      country: faker.datatype.string(),
      country_code: faker.datatype.string(),
      postal_code: faker.datatype.string(),
      region: faker.datatype.string(),
      region_code: faker.datatype.string(),
      street1: faker.datatype.string(),
      street2: faker.datatype.string(),
      street3: faker.datatype.string()
    },
    addressTranslations: [],
    filters: {
      city: faker.datatype.string(),
      country: faker.datatype.string(),
      county: faker.datatype.string(),
      district: faker.datatype.string(),
      postal_code: faker.datatype.string(),
      region: faker.datatype.string()
    },
    isIol: faker.datatype.boolean(),
    masterOrganizationId: faker.datatype.number(),
    name: faker.datatype.string(),
    nameTranslations: [
      {
        languageCode: faker.helpers.arrayElement(ALL_LANGUAGES) as Language,
        name: faker.datatype.string()
      },
      {
        languageCode: faker.helpers.arrayElement(ALL_LANGUAGES) as Language,
        name: faker.datatype.string()
      }
    ],
    ultimateParentId,
    location: {
      lat: faker.address.latitude(),
      lon: faker.address.longitude()
    },
    ultimateParentInstitution: {
      id: ultimateParentId,
      isIol: faker.datatype.boolean(),
      masterOrganizationId: faker.datatype.string(),
      name: faker.datatype.string(),
      nameTranslations: [],
      region: faker.datatype.string(),
      type: faker.datatype.string()
    },
    type: faker.datatype.string(),
    orgTypes: faker.datatype.string(),
    region: faker.datatype.string(),
    diagnosisCount: faker.datatype.number(),
    procedureCount: faker.datatype.number()
  };

  return { ...institution, ...overrides };
}

function generateMockInstitutionDocument(
  overrides: Partial<InstitutionDocument> = {}
) {
  const institution: InstitutionDocument = {
    id: faker.datatype.number(),
    institutionId: faker.datatype.number(),
    masterOrganizationId: faker.datatype.number(),
    isIol: faker.datatype.boolean(),
    name: faker.datatype.string(),
    nameTranslations: [
      {
        name: faker.datatype.string(),
        languageCode: faker.helpers.arrayElement(ALL_LANGUAGES) as Language
      },
      {
        name: faker.datatype.string(),
        languageCode: faker.helpers.arrayElement(ALL_LANGUAGES) as Language
      }
    ],
    orgTypes: [faker.datatype.string(), faker.datatype.string()],
    region: faker.datatype.string(),
    type: faker.datatype.string()
  };

  return { ...institution, ...overrides };
}

function generateMockQueryIntents(): QueryIntent[] {
  return [faker.helpers.arrayElement(Object.values(QueryIntent))];
}

function generateFilters(
  overrides: Partial<FilterInterface> = {}
): FilterInterface {
  const baseFilters: FilterInterface = getEmptyKeywordSearchFilters();

  return { ...baseFilters, ...overrides };
}

function generateNMockDepartmentTranslations(languageCode: string) {
  const count = faker.datatype.number({ min: 1, max: 5 });

  return _.range(0, count).map(() => ({
    department: faker.commerce.department(),
    languageCode: languageCode as Language
  }));
}

function generateNMockTitlesTranslations(languageCode: string) {
  const count = faker.datatype.number({ min: 1, max: 5 });

  return _.range(0, count).map(() => ({
    titles: _.range(0, faker.datatype.number({ min: 1, max: 5 })).map(() =>
      faker.name.jobTitle()
    ),
    languageCode: languageCode as Language
  }));
}

function generateNMockAddressTranslations(languageCode: string) {
  const count = faker.datatype.number({ min: 1, max: 5 });

  return _.range(0, count).map(() => ({
    id: faker.datatype.number(),
    languageCode: languageCode as Language,
    city: faker.datatype.string(),
    country: faker.datatype.string(),
    country_code: faker.datatype.string(),
    postal_code: faker.datatype.string(),
    region: faker.datatype.string(),
    region_code: faker.datatype.string(),
    street1: faker.datatype.string(),
    street2: faker.datatype.string(),
    street3: faker.datatype.string()
  }));
}

function generateNMockInstitutionNameTranslations(languageCode: string) {
  const count = faker.datatype.number({ min: 1, max: 5 });

  return _.range(0, count).map(() => ({
    languageCode: languageCode as Language,
    name: faker.datatype.string()
  }));
}

function getRequestedTranslation<T extends Translation>(
  translations: Readonly<Array<T>> = [],
  languageCode: Language
): T | undefined {
  let requestedTranslation;

  if (languageCode === CHINESE) {
    requestedTranslation = getChineseTranslation(translations);
  } else if (languageCode === JAPANESE) {
    requestedTranslation = getJapaneseTranslation(translations);
  } else {
    requestedTranslation = getEnglishTranslation(translations);
  }

  if (requestedTranslation) {
    return requestedTranslation;
  }

  return getEnglishTranslation(translations);
}

function getChineseTranslation<T extends Translation>(
  translations: Readonly<Array<T>> = []
): T | undefined {
  return translations.find(
    (translation) =>
      translation.languageCode === CHINESE ||
      translation.languageCode.startsWith(ALTERNATE_CHINESE_PREFIX)
  );
}

function getJapaneseTranslation<T extends Translation>(
  translations: Readonly<Array<T>> = []
): T | undefined {
  return translations.find(
    (translation) =>
      translation.languageCode === JAPANESE ||
      translation.languageCode.startsWith(ALTERNATE_JAPANESE_PREFIX)
  );
}

function getEnglishTranslation<T extends Translation>(
  translations: Readonly<Array<T>> = []
): T | undefined {
  return translations.find(
    (translation) =>
      translation.languageCode === ENGLISH ||
      translation.languageCode.startsWith(ALTERNATE_ENGLISH_PREFIX)
  );
}

function getEquivalentLanguageCodes(languageCode: string): Array<string> {
  if (languageCode === CHINESE) {
    return [CHINESE, CHINESE_SIMPLIFIED, CHINESE_TRADITIONAL];
  } else if (languageCode === JAPANESE) {
    return [JAPANESE, JAPANESE_KANJI];
  }

  return [ENGLISH];
}

describe("KeywordSearchResponseAdapterService", () => {
  it("full response parse", async () => {
    const page = {
      from: faker.datatype.number(),
      size: faker.datatype.number()
    };

    const hit1 = generateMockHit({
      affiliations: [generateMockAffiliation(), generateMockAffiliation()]
    });
    const hit2 = generateMockHit({
      affiliations: [generateMockAffiliation(), generateMockAffiliation()]
    });

    const hits: SearchHitsMetadata<HCPDocument> = {
      total: {
        value: faker.datatype.number()
      } as SearchTotalHits,
      hits: [hit1, hit2]
    };

    const synonyms = generateMockSynonyms();
    const suppliedFilters = generateFilters();
    const queryIntent = generateMockQueryIntents();

    const affiliationAdapterService = createMockInstance(
      AffiliationAdapterService
    );

    const keywordSearchResponseAdapterService =
      new KeywordSearchResponseAdapterService(affiliationAdapterService);

    const adaptedResponse = await keywordSearchResponseAdapterService.adapt(
      page,
      ENGLISH,
      hits,
      undefined,
      synonyms,
      suppliedFilters,
      queryIntent,
      [],
      generateKeywordSearchFeatureFlags(),
      [],
      []
    );

    const hit1CitationCount =
      hit1.inner_hits!.citations.hits.hits[0].fields![
        "publications.citationCount"
      ][0] +
      hit1.inner_hits!.citations.hits.hits[1].fields![
        "publications.citationCount"
      ][0];
    const hit1SocialMediaCount =
      hit1.inner_hits!.microBlogging.hits.hits[0].fields![
        "publications.microBloggingCount"
      ][0] +
      hit1.inner_hits!.microBlogging.hits.hits[1].fields![
        "publications.microBloggingCount"
      ][0];
    const hit1PaymentAmountsTotal =
      hit1.inner_hits!.payments.hits.hits[0].fields!["payments.amount"][0] +
      hit1.inner_hits!.payments.hits.hits[1].fields!["payments.amount"][0];
    const hit1DiagnosesAmountsTotal =
      hit1.inner_hits!.diagnoses_amount.hits.hits[0].fields![
        "DRG_diagnoses.internalCount"
      ][0] +
      hit1.inner_hits!.diagnoses_amount.hits.hits[1].fields![
        "DRG_diagnoses.internalCount"
      ][0];
    const hit1PrescriptionsAmountsTotal =
      hit1.inner_hits!.prescriptions_amount.hits.hits[0].fields![
        "prescriptions.num_prescriptions"
      ][0] +
      hit1.inner_hits!.prescriptions_amount.hits.hits[1].fields![
        "prescriptions.num_prescriptions"
      ][0];
    const hit1ProceduresAmountsTotal =
      hit1.inner_hits!.procedures_amount.hits.hits[0].fields![
        "DRG_procedures.internalCount"
      ][0] +
      hit1.inner_hits!.procedures_amount.hits.hits[1].fields![
        "DRG_procedures.internalCount"
      ][0];

    const hit2CitationCount =
      hit2.inner_hits!.citations.hits.hits[0].fields![
        "publications.citationCount"
      ][0] +
      hit2.inner_hits!.citations.hits.hits[1].fields![
        "publications.citationCount"
      ][0];
    const hit2SocialMediaCount =
      hit2.inner_hits!.microBlogging.hits.hits[0].fields![
        "publications.microBloggingCount"
      ][0] +
      hit2.inner_hits!.microBlogging.hits.hits[1].fields![
        "publications.microBloggingCount"
      ][0];
    const hit2PaymentAmountsTotal =
      hit2.inner_hits!.payments.hits.hits[0].fields!["payments.amount"][0] +
      hit2.inner_hits!.payments.hits.hits[1].fields!["payments.amount"][0];
    const hit2DiagnosesAmountsTotal =
      hit2.inner_hits!.diagnoses_amount.hits.hits[0].fields![
        "DRG_diagnoses.internalCount"
      ][0] +
      hit2.inner_hits!.diagnoses_amount.hits.hits[1].fields![
        "DRG_diagnoses.internalCount"
      ][0];
    const hit2PrescriptionsAmountsTotal =
      hit2.inner_hits!.prescriptions_amount.hits.hits[0].fields![
        "prescriptions.num_prescriptions"
      ][0] +
      hit2.inner_hits!.prescriptions_amount.hits.hits[1].fields![
        "prescriptions.num_prescriptions"
      ][0];
    const hit2ProceduresAmountsTotal =
      hit2.inner_hits!.procedures_amount.hits.hits[0].fields![
        "DRG_procedures.internalCount"
      ][0] +
      hit2.inner_hits!.procedures_amount.hits.hits[1].fields![
        "DRG_procedures.internalCount"
      ][0];

    expect(adaptedResponse).toEqual({
      from: page.from,
      pageSize: page.size,
      total: (hits.total as SearchTotalHits).value,
      results: [
        {
          personId: hit1._source?.id,
          h1dnId: hit1._source?.h1dn_id,
          name: hit1._source?.name_eng,
          firstName: hit1._source?.firstName_eng,
          middleName: hit1._source?.middleName_eng,
          lastName: hit1._source?.lastName_eng,
          nameEng: hit1._source?.name_eng,
          firstNameEng: hit1._source?.firstName_eng,
          lastNameEng: hit1._source?.lastName_eng,
          isFacultyOpinionsMember: hit1._source!.isFacultyOpinionsMember,
          personTranslationEng: {
            fullName: hit1._source!.name_eng,
            firstName: hit1._source!.firstName_eng,
            middleName: hit1._source!.middleName_eng,
            lastName: hit1._source!.lastName_eng,
            languageCode: ENGLISH
          },
          personTranslation: {
            fullName: hit1._source!.name_jpn,
            firstName: hit1._source!.firstName_jpn,
            middleName: hit1._source!.middleName_jpn,
            lastName: hit1._source!.lastName_jpn,
            languageCode: JAPANESE
          },
          affiliations: [
            expect.objectContaining({
              id: hit1._source!.affiliations![0].id
            }),
            expect.objectContaining({
              id: hit1._source!.affiliations![1].id
            })
          ],
          score: hit1._score!,
          scores: {
            normalizedRange: {
              min: 0,
              max: 0
            },
            personId: hit1._source?.id,
            h1Score: hit1._score!,
            publications: {
              minValue: 0,
              maxValue: 0,
              normalizedValue: 0,
              percentile: 0,
              value: (
                hit1.inner_hits!.publications.hits.total as SearchTotalHits
              ).value
            },
            citations: {
              minValue: 0,
              maxValue: 0,
              normalizedValue: 0,
              percentile: 0,
              value: hit1CitationCount
            },
            trials: {
              minValue: 0,
              maxValue: 0,
              normalizedValue: 0,
              percentile: 0,
              value: (hit1.inner_hits!.trials.hits.total as SearchTotalHits)
                .value
            },
            payments: {
              minValue: 0,
              maxValue: 0,
              normalizedValue: 0,
              percentile: 0,
              value: hit1PaymentAmountsTotal
            },
            paymentsCount: {
              minValue: 0,
              maxValue: 0,
              normalizedValue: 0,
              percentile: 0,
              value: (hit1.inner_hits!.payments.hits.total as SearchTotalHits)
                .value
            },
            grants: {
              minValue: 0,
              maxValue: 0,
              normalizedValue: 0,
              percentile: 0,
              value: 0
            },
            grantsCount: {
              minValue: 0,
              maxValue: 0,
              normalizedValue: 0,
              percentile: 0,
              value: 0
            },
            congresses: {
              minValue: 0,
              maxValue: 0,
              normalizedValue: 0,
              percentile: 0,
              value: (hit1.inner_hits!.congress.hits.total as SearchTotalHits)
                .value
            },
            collaborators: {
              minValue: 0,
              maxValue: 0,
              normalizedValue: 0,
              percentile: 0,
              value: 0
            },
            socialMediaMentions: {
              minValue: 0,
              maxValue: 0,
              normalizedValue: 0,
              percentile: 0,
              value: hit1SocialMediaCount
            },
            procedures: {
              minValue: 0,
              maxValue: 0,
              normalizedValue: 0,
              percentile: 0,
              value: hit1ProceduresAmountsTotal
            },
            diagnoses: {
              minValue: 0,
              maxValue: 0,
              normalizedValue: 0,
              percentile: 0,
              value: hit1DiagnosesAmountsTotal
            },
            prescriptions: {
              minValue: 0,
              maxValue: 0,
              normalizedValue: 0,
              percentile: 0,
              value: hit1PrescriptionsAmountsTotal
            },
            referralsReceived: {
              minValue: 0,
              maxValue: 0,
              normalizedValue: 0,
              percentile: 0,
              value: hit1._source?.referralsReceivedCount
            },
            referralsSent: {
              minValue: 0,
              maxValue: 0,
              normalizedValue: 0,
              percentile: 0,
              value: hit1._source?.referralsSentCount
            },
            twitterFollowersCount: {
              minValue: 0,
              maxValue: 0,
              normalizedValue: 0,
              percentile: 0,
              value: hit1._source?.twitterFollowersCount
            },
            twitterTweetCount: {
              minValue: 0,
              maxValue: 0,
              normalizedValue: 0,
              percentile: 0,
              value: hit1._source?.twitterTweetCount
            }
          },
          totalWorks: hit1._source?.totalWorks,
          countPublications: hit1._source?.publicationCount,
          countPresentWorkAffiliations:
            hit1._source?.presentWorkInstitutionCount,
          publicationsHighlights: [
            {
              publicationsId:
                hit1.inner_hits?.publications.hits.hits[0].fields![
                  "publications.id"
                ][0],
              highlight: {
                keywords:
                  hit1.inner_hits?.publications.hits.hits[0].highlight![
                    "publications.keywords_eng"
                  ][0],
                publicationAbstract:
                  hit1.inner_hits?.publications.hits.hits[0].highlight![
                    "publications.publicationAbstract_eng"
                  ][0],
                title:
                  hit1.inner_hits?.publications.hits.hits[0].highlight![
                    "publications.title_eng"
                  ][0]
              }
            }
          ],
          countClinicalTrials: hit1._source?.trialCount,
          trialsHighlights: [
            {
              highlight: {
                briefTitle:
                  hit1.inner_hits?.trials.hits.hits[0].highlight![
                    "trials.briefTitle_eng"
                  ][0],
                conditions:
                  hit1.inner_hits?.trials.hits.hits[0].highlight![
                    "trials.conditions_eng"
                  ][0],
                interventions:
                  hit1.inner_hits?.trials.hits.hits[0].highlight![
                    "trials.interventions_eng"
                  ][0],
                keywords:
                  hit1.inner_hits?.trials.hits.hits[0].highlight![
                    "trials.keywords_eng"
                  ][0],
                officialTitle:
                  hit1.inner_hits?.trials.hits.hits[0].highlight![
                    "trials.officialTitle_eng"
                  ][0],
                summary:
                  hit1.inner_hits?.trials.hits.hits[0].highlight![
                    "trials.summary_eng"
                  ][0]
              },
              trialsId:
                hit1.inner_hits?.trials.hits.hits[0].fields!["trials.id"][0]
            }
          ],
          claimsDiagnosesHighlights: [
            {
              highlight: {
                codeAndDescription:
                  hit1.inner_hits?.DRG_diagnoses.hits.hits[0].highlight![
                    "DRG_diagnoses.codeAndDescription_eng"
                  ][0]
              }
            }
          ],
          claimsProceduresHighlights: [
            {
              highlight: {
                codeAndDescription:
                  hit1.inner_hits?.DRG_procedures.hits.hits[0].highlight![
                    "DRG_procedures.codeAndDescription_eng"
                  ][0]
              }
            }
          ],
          congressHighlights: [
            {
              congressId:
                hit1.inner_hits?.congress.hits.hits[0].fields![
                  "congress.id"
                ][0],
              highlight: {
                keywords:
                  hit1.inner_hits?.congress.hits.hits[0].highlight![
                    "congress.keywords_eng"
                  ][0],
                title:
                  hit1.inner_hits?.congress.hits.hits[0].highlight![
                    "congress.title_eng"
                  ][0]
              }
            }
          ],
          affiliationHighlights: [],
          socialMediaMentionsTotal: hit1._source?.microBloggingTotal || 0,
          referralsReceivedCount: hit1._source?.referralsReceivedCount,
          referralsSentCount: hit1._source?.referralsSentCount,
          sumPayments: hit1._source?.paymentTotal,
          diagnosesCount: hit1._source?.DRG_diagnosesCount,
          proceduresCount: hit1._source?.DRG_proceduresCount,
          prescriptionsCount: hit1._source?.num_prescriptions,
          trialEnrollmentRate: hit1._source?.trialEnrollmentRate,
          trialOngoingCount: 0,
          trialActivelyRecruitingCount: 0,
          congresses: hit1._source?.congressCount,
          grants: 0, // hard-coded to 0 since there's no data
          sumGrants: 0, // hard-coded to 0 since there's no data
          specialty: hit1._source?.specialty_eng,
          specialtyCmn: hit1._source?.specialty_cmn,
          specialtyJpn: hit1._source?.specialty_jpn,
          congressesDates: [], // from inner_hits
          paymentDates: [], // from inner_hits
          publicationDates: [], // from inner_hits
          trialDates: [], // from inner_hits
          citationCount: hit1._source?.citationTotal,
          citationCountAvg: 0,
          infoRequestsResolved: null,
          tags: [],
          languageCode: ENGLISH,
          designations: hit1._source?.designations,
          patientsDiversity: {
            age: [],
            races: [],
            sex: []
          },
          providerDiversity: {
            languagesSpoken: [],
            races: [],
            sex: []
          },
          diversityPercentile: hit1._source?.patientsDiversityPercentile,
          digitalRank: hit1._source?.digitalRank,
          top1PercentileDigitalRank: hit1._source?.top1PercentileDigitalRank,
          top10PercentileDigitalRank: hit1._source?.top10PercentileDigitalRank,
          twitterFollowersCount: hit1._source?.twitterFollowersCount,
          twitterTweetCount: hit1._source?.twitterTweetCount,
          locations: [],
          isOutsideUsersSlice: true,
          diversityRanking: null,
          debugData: null,
          congressSessions: []
        },
        {
          personId: hit2._source?.id,
          h1dnId: hit2._source?.h1dn_id,
          name: hit2._source?.name_eng,
          firstName: hit2._source?.firstName_eng,
          middleName: hit2._source?.middleName_eng,
          lastName: hit2._source?.lastName_eng,
          nameEng: hit2._source?.name_eng,
          firstNameEng: hit2._source?.firstName_eng,
          lastNameEng: hit2._source?.lastName_eng,
          isFacultyOpinionsMember: hit2._source!.isFacultyOpinionsMember,
          personTranslationEng: {
            fullName: hit2._source!.name_eng,
            firstName: hit2._source!.firstName_eng,
            middleName: hit2._source!.middleName_eng,
            lastName: hit2._source!.lastName_eng,
            languageCode: ENGLISH
          },
          personTranslation: {
            fullName: hit2._source!.name_jpn,
            firstName: hit2._source!.firstName_jpn,
            middleName: hit2._source!.middleName_jpn,
            lastName: hit2._source!.lastName_jpn,
            languageCode: JAPANESE
          },
          affiliations: [
            expect.objectContaining({
              id: hit2._source!.affiliations![0].id
            }),
            expect.objectContaining({
              id: hit2._source!.affiliations![1].id
            })
          ],
          score: hit2._score!,
          scores: {
            normalizedRange: {
              min: 0,
              max: 0
            },
            personId: hit2._source?.id,
            h1Score: hit2._score!,
            publications: {
              minValue: 0,
              maxValue: 0,
              normalizedValue: 0,
              percentile: 0,
              value: (
                hit2.inner_hits!.publications.hits.total as SearchTotalHits
              ).value
            },
            citations: {
              minValue: 0,
              maxValue: 0,
              normalizedValue: 0,
              percentile: 0,
              value: hit2CitationCount
            },
            trials: {
              minValue: 0,
              maxValue: 0,
              normalizedValue: 0,
              percentile: 0,
              value: (hit2.inner_hits!.trials.hits.total as SearchTotalHits)
                .value
            },
            payments: {
              minValue: 0,
              maxValue: 0,
              normalizedValue: 0,
              percentile: 0,
              value: hit2PaymentAmountsTotal
            },
            paymentsCount: {
              minValue: 0,
              maxValue: 0,
              normalizedValue: 0,
              percentile: 0,
              value: (hit2.inner_hits!.payments.hits.total as SearchTotalHits)
                .value
            },
            grants: {
              minValue: 0,
              maxValue: 0,
              normalizedValue: 0,
              percentile: 0,
              value: 0
            },
            grantsCount: {
              minValue: 0,
              maxValue: 0,
              normalizedValue: 0,
              percentile: 0,
              value: 0
            },
            congresses: {
              minValue: 0,
              maxValue: 0,
              normalizedValue: 0,
              percentile: 0,
              value: (hit2.inner_hits!.congress.hits.total as SearchTotalHits)
                .value
            },
            collaborators: {
              minValue: 0,
              maxValue: 0,
              normalizedValue: 0,
              percentile: 0,
              value: 0
            },
            socialMediaMentions: {
              minValue: 0,
              maxValue: 0,
              normalizedValue: 0,
              percentile: 0,
              value: hit2SocialMediaCount
            },
            procedures: {
              minValue: 0,
              maxValue: 0,
              normalizedValue: 0,
              percentile: 0,
              value: hit2ProceduresAmountsTotal
            },
            diagnoses: {
              minValue: 0,
              maxValue: 0,
              normalizedValue: 0,
              percentile: 0,
              value: hit2DiagnosesAmountsTotal
            },
            prescriptions: {
              minValue: 0,
              maxValue: 0,
              normalizedValue: 0,
              percentile: 0,
              value: hit2PrescriptionsAmountsTotal
            },
            referralsReceived: {
              minValue: 0,
              maxValue: 0,
              normalizedValue: 0,
              percentile: 0,
              value: hit2._source?.referralsReceivedCount
            },
            referralsSent: {
              minValue: 0,
              maxValue: 0,
              normalizedValue: 0,
              percentile: 0,
              value: hit2._source?.referralsSentCount
            },
            twitterFollowersCount: {
              minValue: 0,
              maxValue: 0,
              normalizedValue: 0,
              percentile: 0,
              value: hit2._source?.twitterFollowersCount
            },
            twitterTweetCount: {
              minValue: 0,
              maxValue: 0,
              normalizedValue: 0,
              percentile: 0,
              value: hit2._source?.twitterTweetCount
            }
          },
          totalWorks: hit2._source?.totalWorks,
          countPublications: hit2._source?.publicationCount,
          countPresentWorkAffiliations:
            hit2._source?.presentWorkInstitutionCount,
          publicationsHighlights: [
            {
              publicationsId:
                hit2.inner_hits?.publications.hits.hits[0].fields![
                  "publications.id"
                ][0],
              highlight: {
                keywords:
                  hit2.inner_hits?.publications.hits.hits[0].highlight![
                    "publications.keywords_eng"
                  ][0],
                publicationAbstract:
                  hit2.inner_hits?.publications.hits.hits[0].highlight![
                    "publications.publicationAbstract_eng"
                  ][0],
                title:
                  hit2.inner_hits?.publications.hits.hits[0].highlight![
                    "publications.title_eng"
                  ][0]
              }
            }
          ],
          countClinicalTrials: hit2._source?.trialCount,
          trialsHighlights: [
            {
              highlight: {
                briefTitle:
                  hit2.inner_hits?.trials.hits.hits[0].highlight![
                    "trials.briefTitle_eng"
                  ][0],
                conditions:
                  hit2.inner_hits?.trials.hits.hits[0].highlight![
                    "trials.conditions_eng"
                  ][0],
                interventions:
                  hit2.inner_hits?.trials.hits.hits[0].highlight![
                    "trials.interventions_eng"
                  ][0],
                keywords:
                  hit2.inner_hits?.trials.hits.hits[0].highlight![
                    "trials.keywords_eng"
                  ][0],
                officialTitle:
                  hit2.inner_hits?.trials.hits.hits[0].highlight![
                    "trials.officialTitle_eng"
                  ][0],
                summary:
                  hit2.inner_hits?.trials.hits.hits[0].highlight![
                    "trials.summary_eng"
                  ][0]
              },
              trialsId:
                hit2.inner_hits?.trials.hits.hits[0].fields!["trials.id"][0]
            }
          ],
          claimsDiagnosesHighlights: [
            {
              highlight: {
                codeAndDescription:
                  hit2.inner_hits?.DRG_diagnoses.hits.hits[0].highlight![
                    "DRG_diagnoses.codeAndDescription_eng"
                  ][0]
              }
            }
          ],
          claimsProceduresHighlights: [
            {
              highlight: {
                codeAndDescription:
                  hit2.inner_hits?.DRG_procedures.hits.hits[0].highlight![
                    "DRG_procedures.codeAndDescription_eng"
                  ][0]
              }
            }
          ],
          congressHighlights: [
            {
              congressId:
                hit2.inner_hits?.congress.hits.hits[0].fields![
                  "congress.id"
                ][0],
              highlight: {
                keywords:
                  hit2.inner_hits?.congress.hits.hits[0].highlight![
                    "congress.keywords_eng"
                  ][0],
                title:
                  hit2.inner_hits?.congress.hits.hits[0].highlight![
                    "congress.title_eng"
                  ][0]
              }
            }
          ],
          affiliationHighlights: [],
          socialMediaMentionsTotal: hit2._source?.microBloggingTotal || 0,
          referralsReceivedCount: hit2._source?.referralsReceivedCount,
          referralsSentCount: hit2._source?.referralsSentCount,
          sumPayments: hit2._source?.paymentTotal,
          diagnosesCount: hit2._source?.DRG_diagnosesCount,
          proceduresCount: hit2._source?.DRG_proceduresCount,
          prescriptionsCount: hit2._source?.num_prescriptions,
          trialEnrollmentRate: hit2._source?.trialEnrollmentRate,
          trialOngoingCount: 0,
          trialActivelyRecruitingCount: 0,
          congresses: hit2._source?.congressCount,
          grants: 0, // hard-coded to 0 since there's no data
          sumGrants: 0, // hard-coded to 0 since there's no data
          specialty: hit2._source?.specialty_eng,
          specialtyCmn: hit2._source?.specialty_cmn,
          specialtyJpn: hit2._source?.specialty_jpn,
          congressesDates: [], // from inner_hits
          paymentDates: [], // from inner_hits
          publicationDates: [], // from inner_hits
          trialDates: [], // from inner_hits
          citationCount: hit2._source?.citationTotal,
          citationCountAvg: 0,
          infoRequestsResolved: null,
          tags: [],
          languageCode: ENGLISH,
          designations: hit2._source?.designations,
          patientsDiversity: {
            age: [],
            races: [],
            sex: []
          },
          providerDiversity: {
            languagesSpoken: [],
            races: [],
            sex: []
          },
          diversityPercentile: hit2._source?.patientsDiversityPercentile,
          digitalRank: hit2._source?.digitalRank,
          top1PercentileDigitalRank: hit2._source?.top1PercentileDigitalRank,
          top10PercentileDigitalRank: hit2._source?.top10PercentileDigitalRank,
          twitterFollowersCount: hit2._source?.twitterFollowersCount,
          twitterTweetCount: hit2._source?.twitterTweetCount,
          locations: [],
          isOutsideUsersSlice: true,
          diversityRanking: null,
          debugData: null,
          congressSessions: []
        }
      ],
      ranges: {
        publicationCount: { min: 0, max: 0 },
        citationCount: { min: 0, max: 0 },
        trialCount: { min: 0, max: 0 },
        paymentCount: { min: 0, max: 0 },
        paymentSum: { min: 0, max: 0 },
        grantCount: { min: 0, max: 0 },
        grantSum: { min: 0, max: 0 },
        congressCount: { min: 0, max: 0 },
        totalCollaborators: { min: 0, max: 0 },
        socialMediaMentions: { min: 0, max: 0 },
        procedures: { min: 0, max: 0 },
        diagnoses: { min: 0, max: 0 },
        referralsReceived: { min: 0, max: 0 },
        referralsSent: { min: 0, max: 0 }
      },
      normalizedRange: { min: 0, max: 0 },
      filterCounts: [
        {
          field: "institutions",
          buckets: []
        },
        {
          field: "affiliations",
          buckets: []
        },
        {
          field: "journals",
          buckets: []
        },
        {
          field: "zipCode",
          buckets: []
        },
        {
          field: "country",
          buckets: []
        },
        {
          field: "specialty",
          buckets: []
        },
        {
          field: "city",
          buckets: []
        },
        {
          field: "procedureCodes",
          buckets: []
        },
        {
          field: "payerCompanies",
          buckets: []
        },
        {
          field: "trialPhases",
          buckets: []
        },
        {
          field: "publicationTypes",
          buckets: []
        },
        {
          field: "congressConferenceNames",
          buckets: []
        },
        {
          field: "medSchool",
          buckets: []
        },
        {
          field: "diagnosisCodes",
          buckets: []
        },
        {
          field: "natureOfPayments",
          buckets: []
        },
        {
          field: "designations",
          buckets: []
        },
        {
          field: "paymentAssociatedDrugs",
          buckets: []
        },
        {
          field: "congressOrganizerNames",
          buckets: []
        },
        {
          field: "referralsServiceLine",
          buckets: []
        },
        {
          field: "trialStatuses",
          buckets: []
        },
        {
          field: "state",
          buckets: []
        },
        {
          field: "trialTypes",
          buckets: []
        }
      ],
      synonyms,
      queryIntent,
      icdCodeSynonyms: [],
      totalResultsOutsideUserSlice: 0
    });
  });

  it("should populate null into h1dnId when not present in elasticsearch", async () => {
    const page = {
      from: faker.datatype.number(),
      size: faker.datatype.number()
    };

    const hit1 = generateMockHit({
      h1dn_id: undefined
    });

    const hits: SearchHitsMetadata<HCPDocument> = {
      total: {
        value: faker.datatype.number()
      } as SearchTotalHits,
      hits: [hit1]
    };

    const synonyms = generateMockSynonyms();
    const suppliedFilters = generateFilters();
    const queryIntent = generateMockQueryIntents();

    const affiliationAdapterService = createMockInstance(
      AffiliationAdapterService
    );

    const keywordSearchResponseAdapterService =
      new KeywordSearchResponseAdapterService(affiliationAdapterService);

    const adaptedResponse = await keywordSearchResponseAdapterService.adapt(
      page,
      ENGLISH,
      hits,
      undefined,
      synonyms,
      suppliedFilters,
      queryIntent,
      [],
      generateKeywordSearchFeatureFlags(),

      [],
      []
    );

    expect(adaptedResponse).toEqual(
      expect.objectContaining({
        results: expect.arrayContaining([
          expect.objectContaining({
            h1dnId: null
          })
        ])
      })
    );
  });

  it("should treat hit counts", async () => {
    const page = {
      from: faker.datatype.number(),
      size: faker.datatype.number()
    };

    const hit = generateMockHit();
    hit.inner_hits!.publications.hits.total = faker.datatype.number();
    hit.inner_hits!.congress.hits.total = faker.datatype.number();
    hit.inner_hits!.trials.hits.total = faker.datatype.number();
    hit.inner_hits!.payments.hits.total = faker.datatype.number();

    const hits: SearchHitsMetadata<HCPDocument> = {
      total: faker.datatype.number(),
      hits: [hit]
    };

    const synonyms = generateMockSynonyms();
    const suppliedFilters = generateFilters();
    const queryIntent = generateMockQueryIntents();

    const affiliationAdapterService = createMockInstance(
      AffiliationAdapterService
    );

    const keywordSearchResponseAdapterService =
      new KeywordSearchResponseAdapterService(affiliationAdapterService);

    const adaptedResponse = await keywordSearchResponseAdapterService.adapt(
      page,
      ENGLISH,
      hits,
      undefined,
      synonyms,
      suppliedFilters,
      queryIntent,
      [],
      generateKeywordSearchFeatureFlags(),

      [],
      []
    );

    expect(adaptedResponse).toEqual(
      expect.objectContaining({
        total: hits.total,
        results: expect.arrayContaining([
          expect.objectContaining({
            scores: expect.objectContaining({
              publications: expect.objectContaining({
                value: hit.inner_hits!.publications.hits.total
              }),
              trials: expect.objectContaining({
                value: hit.inner_hits!.trials.hits.total
              }),
              paymentsCount: expect.objectContaining({
                value: hit.inner_hits!.payments.hits.total
              }),
              congresses: expect.objectContaining({
                value: hit.inner_hits!.congress.hits.total
              })
            })
          })
        ])
      })
    );
  });

  it("should skip hits w/o _source property", async () => {
    const page = {
      from: faker.datatype.number(),
      size: faker.datatype.number()
    };

    const hit1 = generateMockHit();
    const hitWithoutSource = generateMockHit();
    delete hitWithoutSource._source;
    const hit2 = generateMockHit();

    const hits: SearchHitsMetadata<HCPDocument> = {
      total: {
        value: faker.datatype.number()
      } as SearchTotalHits,
      hits: [hit1, hitWithoutSource, hit2]
    };

    const synonyms = generateMockSynonyms();
    const suppliedFilters = generateFilters();
    const queryIntent = generateMockQueryIntents();

    const affiliationAdapterService = createMockInstance(
      AffiliationAdapterService
    );

    const keywordSearchResponseAdapterService =
      new KeywordSearchResponseAdapterService(affiliationAdapterService);

    const adaptedResponse = await keywordSearchResponseAdapterService.adapt(
      page,
      ENGLISH,
      hits,
      undefined,
      synonyms,
      suppliedFilters,
      queryIntent,
      [],
      generateKeywordSearchFeatureFlags(),

      [],
      []
    );

    expect(adaptedResponse).toEqual(
      expect.objectContaining({
        results: expect.arrayContaining([
          expect.objectContaining({
            personId: hit1._source?.id
          }),
          expect.objectContaining({
            personId: hit2._source?.id
          })
        ])
      })
    );
  });

  it("should return a completely empty response when calling emptyResponse", () => {
    const page = {
      from: faker.datatype.number(),
      size: faker.datatype.number()
    };

    const affiliationAdapterService = createMockInstance(
      AffiliationAdapterService
    );

    const keywordSearchResponseAdapterService =
      new KeywordSearchResponseAdapterService(affiliationAdapterService);

    const adaptedResponse =
      keywordSearchResponseAdapterService.emptyResponse(page);

    expect(adaptedResponse).toEqual({
      from: page.from,
      pageSize: page.size,
      total: 0,
      results: [],
      ranges: {
        publicationCount: { min: 0, max: 0 },
        citationCount: { min: 0, max: 0 },
        trialCount: { min: 0, max: 0 },
        paymentCount: { min: 0, max: 0 },
        paymentSum: { min: 0, max: 0 },
        grantCount: { min: 0, max: 0 },
        grantSum: { min: 0, max: 0 },
        congressCount: { min: 0, max: 0 },
        totalCollaborators: { min: 0, max: 0 },
        socialMediaMentions: { min: 0, max: 0 },
        procedures: { min: 0, max: 0 },
        diagnoses: { min: 0, max: 0 },
        referralsReceived: { min: 0, max: 0 },
        referralsSent: { min: 0, max: 0 }
      },
      normalizedRange: { min: 0, max: 0 },
      filterCounts: [
        {
          field: "institutions",
          buckets: []
        },
        {
          field: "affiliations",
          buckets: []
        },
        {
          field: "journals",
          buckets: []
        },
        {
          field: "zipCode",
          buckets: []
        },
        {
          field: "country",
          buckets: []
        },
        {
          field: "specialty",
          buckets: []
        },
        {
          field: "city",
          buckets: []
        },
        {
          field: "procedureCodes",
          buckets: []
        },
        {
          field: "payerCompanies",
          buckets: []
        },
        {
          field: "trialPhases",
          buckets: []
        },
        {
          field: "publicationTypes",
          buckets: []
        },
        {
          field: "congressConferenceNames",
          buckets: []
        },
        {
          field: "medSchool",
          buckets: []
        },
        {
          field: "diagnosisCodes",
          buckets: []
        },
        {
          field: "natureOfPayments",
          buckets: []
        },
        {
          field: "designations",
          buckets: []
        },
        {
          field: "paymentAssociatedDrugs",
          buckets: []
        },
        {
          field: "congressOrganizerNames",
          buckets: []
        },
        {
          field: "referralsServiceLine",
          buckets: []
        },
        {
          field: "trialStatuses",
          buckets: []
        },
        {
          field: "state",
          buckets: []
        },
        {
          field: "trialTypes",
          buckets: []
        }
      ],
      synonyms: []
    });
  });

  it("should return hasCTMSData in the response, using the value of inCtmsNetwork, when the enable-ctms-v2 flag is enabled", async () => {
    const page = {
      from: faker.datatype.number(),
      size: faker.datatype.number()
    };

    const mockHit = generateMockHit();

    const hits: SearchHitsMetadata<HCPDocument> = {
      total: {
        value: faker.datatype.number()
      } as SearchTotalHits,
      hits: [mockHit]
    };

    const synonyms = generateMockSynonyms();
    const suppliedFilters = generateFilters();
    const queryIntent = generateMockQueryIntents();

    const affiliationAdapterService = createMockInstance(
      AffiliationAdapterService
    );

    const keywordSearchResponseAdapterService =
      new KeywordSearchResponseAdapterService(affiliationAdapterService);

    const adaptedResponse = await keywordSearchResponseAdapterService.adapt(
      page,
      ENGLISH,
      hits,
      undefined,
      synonyms,
      suppliedFilters,
      queryIntent,
      [],
      generateKeywordSearchFeatureFlags({
        enableCTMSV2: true
      }),

      [],
      []
    );

    expect(adaptedResponse).toEqual(
      expect.objectContaining({
        results: expect.arrayContaining([
          expect.objectContaining({
            hasCTMSData:
              mockHit._source?.inCtmsNetwork ?? mockHit._source?.hasCtms
          })
        ])
      })
    );
  });

  describe("negative conditions", () => {
    it("missing inner_hits.publications should output 0 for scores.publications.value", async () => {
      const page = {
        from: faker.datatype.number(),
        size: faker.datatype.number()
      };

      const hit = generateMockHit();
      delete hit.inner_hits!.publications;

      const hits: SearchHitsMetadata<HCPDocument> = {
        total: {
          value: faker.datatype.number()
        } as SearchTotalHits,
        hits: [hit]
      };

      const synonyms = generateMockSynonyms();
      const suppliedFilters = generateFilters();
      const queryIntent = generateMockQueryIntents();

      const affiliationAdapterService = createMockInstance(
        AffiliationAdapterService
      );

      const keywordSearchResponseAdapterService =
        new KeywordSearchResponseAdapterService(affiliationAdapterService);

      const adaptedResponse = await keywordSearchResponseAdapterService.adapt(
        page,
        ENGLISH,
        hits,
        undefined,
        synonyms,
        suppliedFilters,
        queryIntent,
        [],
        generateKeywordSearchFeatureFlags(),

        [],
        []
      );

      expect(adaptedResponse).toEqual(
        expect.objectContaining({
          results: expect.arrayContaining([
            expect.objectContaining({
              personId: hit._source?.id,
              scores: expect.objectContaining({
                publications: {
                  minValue: 0,
                  maxValue: 0,
                  normalizedValue: 0,
                  percentile: 0,
                  value: 0
                }
              })
            })
          ])
        })
      );
    });

    it("missing inner_hits.congress should output 0 for scores.congress.value", async () => {
      const page = {
        from: faker.datatype.number(),
        size: faker.datatype.number()
      };

      const hit = generateMockHit();
      delete hit.inner_hits!.congress;

      const hits: SearchHitsMetadata<HCPDocument> = {
        total: {
          value: faker.datatype.number()
        } as SearchTotalHits,
        hits: [hit]
      };

      const synonyms = generateMockSynonyms();
      const suppliedFilters = generateFilters();
      const queryIntent = generateMockQueryIntents();

      const affiliationAdapterService = createMockInstance(
        AffiliationAdapterService
      );

      const keywordSearchResponseAdapterService =
        new KeywordSearchResponseAdapterService(affiliationAdapterService);

      const adaptedResponse = await keywordSearchResponseAdapterService.adapt(
        page,
        ENGLISH,
        hits,
        undefined,
        synonyms,
        suppliedFilters,
        queryIntent,
        [],
        generateKeywordSearchFeatureFlags(),

        [],
        []
      );

      expect(adaptedResponse).toEqual(
        expect.objectContaining({
          results: expect.arrayContaining([
            expect.objectContaining({
              personId: hit._source?.id,
              scores: expect.objectContaining({
                congresses: {
                  minValue: 0,
                  maxValue: 0,
                  normalizedValue: 0,
                  percentile: 0,
                  value: 0
                }
              })
            })
          ])
        })
      );
    });

    it("missing inner_hits.trials should output 0 for scores.trials.value", async () => {
      const page = {
        from: faker.datatype.number(),
        size: faker.datatype.number()
      };

      const hit = generateMockHit();
      delete hit.inner_hits!.trials;

      const hits: SearchHitsMetadata<HCPDocument> = {
        total: {
          value: faker.datatype.number()
        } as SearchTotalHits,
        hits: [hit]
      };

      const synonyms = generateMockSynonyms();
      const suppliedFilters = generateFilters();
      const queryIntent = generateMockQueryIntents();

      const affiliationAdapterService = createMockInstance(
        AffiliationAdapterService
      );

      const keywordSearchResponseAdapterService =
        new KeywordSearchResponseAdapterService(affiliationAdapterService);

      const adaptedResponse = await keywordSearchResponseAdapterService.adapt(
        page,
        ENGLISH,
        hits,
        undefined,
        synonyms,
        suppliedFilters,
        queryIntent,
        [],
        generateKeywordSearchFeatureFlags(),

        [],
        []
      );

      expect(adaptedResponse).toEqual(
        expect.objectContaining({
          results: expect.arrayContaining([
            expect.objectContaining({
              personId: hit._source?.id,
              scores: expect.objectContaining({
                trials: {
                  minValue: 0,
                  maxValue: 0,
                  normalizedValue: 0,
                  percentile: 0,
                  value: 0
                }
              })
            })
          ])
        })
      );
    });

    it("missing inner_hits.payments should output 0 for scores.payments.value/scores.paymentsCount.value", async () => {
      const page = {
        from: faker.datatype.number(),
        size: faker.datatype.number()
      };

      const hit = generateMockHit();
      delete hit.inner_hits!.payments;

      const hits: SearchHitsMetadata<HCPDocument> = {
        total: {
          value: faker.datatype.number()
        } as SearchTotalHits,
        hits: [hit]
      };

      const synonyms = generateMockSynonyms();
      const suppliedFilters = generateFilters();
      const queryIntent = generateMockQueryIntents();

      const affiliationAdapterService = createMockInstance(
        AffiliationAdapterService
      );

      const keywordSearchResponseAdapterService =
        new KeywordSearchResponseAdapterService(affiliationAdapterService);

      const adaptedResponse = await keywordSearchResponseAdapterService.adapt(
        page,
        ENGLISH,
        hits,
        undefined,
        synonyms,
        suppliedFilters,
        queryIntent,
        [],
        generateKeywordSearchFeatureFlags(),

        [],
        []
      );

      expect(adaptedResponse).toEqual(
        expect.objectContaining({
          results: expect.arrayContaining([
            expect.objectContaining({
              personId: hit._source?.id,
              scores: expect.objectContaining({
                payments: {
                  minValue: 0,
                  maxValue: 0,
                  normalizedValue: 0,
                  percentile: 0,
                  value: 0
                }
              })
            }),
            expect.objectContaining({
              personId: hit._source?.id,
              scores: expect.objectContaining({
                paymentsCount: {
                  minValue: 0,
                  maxValue: 0,
                  normalizedValue: 0,
                  percentile: 0,
                  value: 0
                }
              })
            })
          ])
        })
      );
    });

    it("missing inner_hits.citations should output 0 for scores.citations.value", async () => {
      const page = {
        from: faker.datatype.number(),
        size: faker.datatype.number()
      };

      const hit = generateMockHit();
      delete hit.inner_hits!.citations;

      const hits: SearchHitsMetadata<HCPDocument> = {
        total: {
          value: faker.datatype.number()
        } as SearchTotalHits,
        hits: [hit]
      };

      const synonyms = generateMockSynonyms();
      const suppliedFilters = generateFilters();
      const queryIntent = generateMockQueryIntents();

      const affiliationAdapterService = createMockInstance(
        AffiliationAdapterService
      );

      const keywordSearchResponseAdapterService =
        new KeywordSearchResponseAdapterService(affiliationAdapterService);

      const adaptedResponse = await keywordSearchResponseAdapterService.adapt(
        page,
        ENGLISH,
        hits,
        undefined,
        synonyms,
        suppliedFilters,
        queryIntent,
        [],
        generateKeywordSearchFeatureFlags(),

        [],
        []
      );

      expect(adaptedResponse).toEqual(
        expect.objectContaining({
          results: expect.arrayContaining([
            expect.objectContaining({
              personId: hit._source?.id,
              scores: expect.objectContaining({
                citations: {
                  minValue: 0,
                  maxValue: 0,
                  normalizedValue: 0,
                  percentile: 0,
                  value: 0
                }
              })
            })
          ])
        })
      );
    });

    it("missing inner_hits.microBlogging should output 0 for scores.socialMediaMentions.value", async () => {
      const page = {
        from: faker.datatype.number(),
        size: faker.datatype.number()
      };

      const hit = generateMockHit();
      delete hit.inner_hits!.microBlogging;

      const hits: SearchHitsMetadata<HCPDocument> = {
        total: {
          value: faker.datatype.number()
        } as SearchTotalHits,
        hits: [hit]
      };

      const synonyms = generateMockSynonyms();
      const suppliedFilters = generateFilters();
      const queryIntent = generateMockQueryIntents();

      const affiliationAdapterService = createMockInstance(
        AffiliationAdapterService
      );

      const keywordSearchResponseAdapterService =
        new KeywordSearchResponseAdapterService(affiliationAdapterService);

      const adaptedResponse = await keywordSearchResponseAdapterService.adapt(
        page,
        ENGLISH,
        hits,
        undefined,
        synonyms,
        suppliedFilters,
        queryIntent,
        [],
        generateKeywordSearchFeatureFlags(),

        [],
        []
      );

      expect(adaptedResponse).toEqual(
        expect.objectContaining({
          results: expect.arrayContaining([
            expect.objectContaining({
              personId: hit._source?.id,
              scores: expect.objectContaining({
                socialMediaMentions: {
                  minValue: 0,
                  maxValue: 0,
                  normalizedValue: 0,
                  percentile: 0,
                  value: 0
                }
              })
            })
          ])
        })
      );
    });

    it("missing specialty should output empty array", async () => {
      const page = {
        from: faker.datatype.number(),
        size: faker.datatype.number()
      };

      const noSpecialtyEng = {
        specialty_eng: undefined
      };
      const hit = generateMockHit(noSpecialtyEng);

      const hits: SearchHitsMetadata<HCPDocument> = {
        total: {
          value: faker.datatype.number()
        } as SearchTotalHits,
        hits: [hit]
      };

      const synonyms = generateMockSynonyms();
      const suppliedFilters = generateFilters();
      const queryIntent = generateMockQueryIntents();

      const affiliationAdapterService = createMockInstance(
        AffiliationAdapterService
      );

      const keywordSearchResponseAdapterService =
        new KeywordSearchResponseAdapterService(affiliationAdapterService);

      const adaptedResponse = await keywordSearchResponseAdapterService.adapt(
        page,
        ENGLISH,
        hits,
        undefined,
        synonyms,
        suppliedFilters,
        queryIntent,
        [],
        generateKeywordSearchFeatureFlags(),

        [],
        []
      );

      expect(adaptedResponse).toEqual(
        expect.objectContaining({
          results: expect.arrayContaining([
            expect.objectContaining({
              personId: hit._source?.id,
              specialty: []
            })
          ])
        })
      );
    });

    it("missing isFacultyOpinionsMember should not be set into response result", async () => {
      const page = {
        from: faker.datatype.number(),
        size: faker.datatype.number()
      };

      const hit = generateMockHit({ isFacultyOpinionsMember: undefined });

      const hits: SearchHitsMetadata<HCPDocument> = {
        total: {
          value: faker.datatype.number()
        } as SearchTotalHits,
        hits: [hit]
      };

      const synonyms = generateMockSynonyms();
      const suppliedFilters = generateFilters();
      const queryIntent = generateMockQueryIntents();

      const affiliationAdapterService = createMockInstance(
        AffiliationAdapterService
      );

      const keywordSearchResponseAdapterService =
        new KeywordSearchResponseAdapterService(affiliationAdapterService);

      const adaptedResponse = await keywordSearchResponseAdapterService.adapt(
        page,
        ENGLISH,
        hits,
        undefined,
        synonyms,
        suppliedFilters,
        queryIntent,
        [],
        generateKeywordSearchFeatureFlags(),

        [],
        []
      );

      expect(adaptedResponse).toEqual(
        expect.objectContaining({
          results: [
            expect.not.objectContaining({
              isFacultyOpinionsMember: expect.anything()
            })
          ]
        })
      );
    });

    it("missing hasCtms should not be set into response result", async () => {
      const page = {
        from: faker.datatype.number(),
        size: faker.datatype.number()
      };

      const hit = generateMockHit({ hasCtms: undefined });

      const hits: SearchHitsMetadata<HCPDocument> = {
        total: {
          value: faker.datatype.number()
        } as SearchTotalHits,
        hits: [hit]
      };

      const synonyms = generateMockSynonyms();
      const suppliedFilters = generateFilters();
      const queryIntent = generateMockQueryIntents();

      const affiliationAdapterService = createMockInstance(
        AffiliationAdapterService
      );

      const keywordSearchResponseAdapterService =
        new KeywordSearchResponseAdapterService(affiliationAdapterService);

      const adaptedResponse = await keywordSearchResponseAdapterService.adapt(
        page,
        ENGLISH,
        hits,
        undefined,
        synonyms,
        suppliedFilters,
        queryIntent,
        [],
        generateKeywordSearchFeatureFlags(),

        [],
        []
      );

      expect(adaptedResponse).toEqual(
        expect.objectContaining({
          results: [
            expect.not.objectContaining({
              hasCTMSData: expect.anything()
            })
          ]
        })
      );
    });
  });

  describe("project features", () => {
    describe("claims disabled", () => {
      it("inner_hits missing DRG_diagnoses should output 0 for scores.diagnoses.value", async () => {
        const page = {
          from: faker.datatype.number(),
          size: faker.datatype.number()
        };

        const hit = generateMockHit();
        delete hit.inner_hits!.diagnoses_amount;

        const hits: SearchHitsMetadata<HCPDocument> = {
          total: {
            value: faker.datatype.number()
          } as SearchTotalHits,
          hits: [hit]
        };

        const synonyms = generateMockSynonyms();
        const suppliedFilters = generateFilters();
        const queryIntent = generateMockQueryIntents();

        const affiliationAdapterService = createMockInstance(
          AffiliationAdapterService
        );

        const keywordSearchResponseAdapterService =
          new KeywordSearchResponseAdapterService(affiliationAdapterService);

        const adaptedResponse = await keywordSearchResponseAdapterService.adapt(
          page,
          ENGLISH,
          hits,
          undefined,
          synonyms,
          suppliedFilters,
          queryIntent,
          [],
          generateKeywordSearchFeatureFlags(),

          [],
          []
        );

        expect(adaptedResponse).toEqual(
          expect.objectContaining({
            results: expect.arrayContaining([
              expect.objectContaining({
                personId: hit._source?.id,
                scores: expect.objectContaining({
                  diagnoses: {
                    minValue: 0,
                    maxValue: 0,
                    normalizedValue: 0,
                    percentile: 0,
                    value: 0
                  }
                })
              })
            ])
          })
        );
      });

      it("inner_hits missing DRG_procedures should output 0 for scores.procedures.value", async () => {
        const page = {
          from: faker.datatype.number(),
          size: faker.datatype.number()
        };

        const hit = generateMockHit();
        delete hit.inner_hits!.procedures_amount;

        const hits: SearchHitsMetadata<HCPDocument> = {
          total: {
            value: faker.datatype.number()
          } as SearchTotalHits,
          hits: [hit]
        };

        const synonyms = generateMockSynonyms();
        const suppliedFilters = generateFilters();
        const queryIntent = generateMockQueryIntents();

        const affiliationAdapterService = createMockInstance(
          AffiliationAdapterService
        );

        const keywordSearchResponseAdapterService =
          new KeywordSearchResponseAdapterService(affiliationAdapterService);

        const adaptedResponse = await keywordSearchResponseAdapterService.adapt(
          page,
          ENGLISH,
          hits,
          undefined,
          synonyms,
          suppliedFilters,
          queryIntent,
          [],
          generateKeywordSearchFeatureFlags(),

          [],
          []
        );

        expect(adaptedResponse).toEqual(
          expect.objectContaining({
            results: expect.arrayContaining([
              expect.objectContaining({
                personId: hit._source?.id,
                scores: expect.objectContaining({
                  procedures: {
                    minValue: 0,
                    maxValue: 0,
                    normalizedValue: 0,
                    percentile: 0,
                    value: 0
                  }
                })
              })
            ])
          })
        );
      });
    });

    describe("referrals", () => {
      it("referralsReceived_count should output _source.referralsReceivedCount for scores.referralsReceived.value", async () => {
        const page = {
          from: faker.datatype.number(),
          size: faker.datatype.number()
        };

        const hit = generateMockHit();
        delete hit.inner_hits!.referralsReceived_count;

        const hits: SearchHitsMetadata<HCPDocument> = {
          total: {
            value: faker.datatype.number()
          } as SearchTotalHits,
          hits: [hit]
        };

        const synonyms = generateMockSynonyms();
        const suppliedFilters = generateFilters();
        const queryIntent = generateMockQueryIntents();

        const affiliationAdapterService = createMockInstance(
          AffiliationAdapterService
        );

        const keywordSearchResponseAdapterService =
          new KeywordSearchResponseAdapterService(affiliationAdapterService);

        const adaptedResponse = await keywordSearchResponseAdapterService.adapt(
          page,
          ENGLISH,
          hits,
          undefined,
          synonyms,
          suppliedFilters,
          queryIntent,
          [],
          generateKeywordSearchFeatureFlags(),

          [],
          []
        );

        expect(adaptedResponse).toEqual(
          expect.objectContaining({
            results: expect.arrayContaining([
              expect.objectContaining({
                personId: hit._source?.id,
                scores: expect.objectContaining({
                  referralsReceived: {
                    minValue: 0,
                    maxValue: 0,
                    normalizedValue: 0,
                    percentile: 0,
                    value: hit._source?.referralsReceivedCount
                  }
                })
              })
            ])
          })
        );
      });

      it("referralsSent_count should output _source.referralsSentCount for scores.referralsSent.value", async () => {
        const page = {
          from: faker.datatype.number(),
          size: faker.datatype.number()
        };

        const hit = generateMockHit();
        delete hit.inner_hits!.referralsSent_count;

        const hits: SearchHitsMetadata<HCPDocument> = {
          total: {
            value: faker.datatype.number()
          } as SearchTotalHits,
          hits: [hit]
        };

        const synonyms = generateMockSynonyms();
        const suppliedFilters = generateFilters();
        const queryIntent = generateMockQueryIntents();

        const affiliationAdapterService = createMockInstance(
          AffiliationAdapterService
        );

        const keywordSearchResponseAdapterService =
          new KeywordSearchResponseAdapterService(affiliationAdapterService);

        const adaptedResponse = await keywordSearchResponseAdapterService.adapt(
          page,
          ENGLISH,
          hits,
          undefined,
          synonyms,
          suppliedFilters,
          queryIntent,
          [],
          generateKeywordSearchFeatureFlags(),

          [],
          []
        );

        expect(adaptedResponse).toEqual(
          expect.objectContaining({
            results: expect.arrayContaining([
              expect.objectContaining({
                personId: hit._source?.id,
                scores: expect.objectContaining({
                  referralsSent: {
                    minValue: 0,
                    maxValue: 0,
                    normalizedValue: 0,
                    percentile: 0,
                    value: hit._source?.referralsSentCount
                  }
                })
              })
            ])
          })
        );
      });
    });
  });

  describe("translations", () => {
    describe("name translations", () => {
      it('undefined "name_eng" field should not set personTranslationEng', async () => {
        const page = {
          from: faker.datatype.number(),
          size: faker.datatype.number()
        };

        const hit = generateMockHit({
          name_eng: undefined
        });

        const hits: SearchHitsMetadata<HCPDocument> = {
          total: {
            value: faker.datatype.number()
          } as SearchTotalHits,
          hits: [hit]
        };

        const synonyms = generateMockSynonyms();
        const suppliedFilters = generateFilters();
        const queryIntent = generateMockQueryIntents();

        const affiliationAdapterService = createMockInstance(
          AffiliationAdapterService
        );

        const keywordSearchResponseAdapterService =
          new KeywordSearchResponseAdapterService(affiliationAdapterService);

        const adaptedResponse = await keywordSearchResponseAdapterService.adapt(
          page,
          ENGLISH,
          hits,
          undefined,
          synonyms,
          suppliedFilters,
          queryIntent,
          [],
          generateKeywordSearchFeatureFlags(),

          [],
          []
        );

        expect(adaptedResponse).toEqual(
          expect.objectContaining({
            results: [
              expect.not.objectContaining({
                personTranslationEng: expect.anything()
              })
            ]
          })
        );

        expect(adaptedResponse).toEqual(
          expect.objectContaining({
            results: [
              expect.objectContaining({
                personTranslation: {
                  fullName: hit._source!.name_jpn,
                  lastName: hit._source!.lastName_jpn,
                  middleName: hit._source!.middleName_jpn,
                  firstName: hit._source!.firstName_jpn,
                  languageCode: JAPANESE
                }
              })
            ]
          })
        );
      });

      it('only "name_eng" field available should set personTranslationEng but not set personTranslation', async () => {
        const page = {
          from: faker.datatype.number(),
          size: faker.datatype.number()
        };

        const hit = generateMockHit({
          name_jpn: undefined,
          name_cmn: undefined
        });

        const hits: SearchHitsMetadata<HCPDocument> = {
          total: {
            value: faker.datatype.number()
          } as SearchTotalHits,
          hits: [hit]
        };

        const synonyms = generateMockSynonyms();
        const suppliedFilters = generateFilters();
        const queryIntent = generateMockQueryIntents();

        const affiliationAdapterService = createMockInstance(
          AffiliationAdapterService
        );

        const keywordSearchResponseAdapterService =
          new KeywordSearchResponseAdapterService(affiliationAdapterService);

        const adaptedResponse = await keywordSearchResponseAdapterService.adapt(
          page,
          ENGLISH,
          hits,
          undefined,
          synonyms,
          suppliedFilters,
          queryIntent,
          [],
          generateKeywordSearchFeatureFlags(),

          [],
          []
        );

        expect(adaptedResponse).toEqual(
          expect.objectContaining({
            results: [
              expect.objectContaining({
                personTranslationEng: {
                  fullName: hit._source!.name_eng,
                  lastName: hit._source!.lastName_eng,
                  middleName: hit._source!.middleName_eng,
                  firstName: hit._source!.firstName_eng,
                  languageCode: ENGLISH
                }
              })
            ]
          })
        );

        expect(adaptedResponse).toEqual(
          expect.objectContaining({
            results: [
              expect.not.objectContaining({
                personTranslation: expect.anything()
              })
            ]
          })
        );
      });

      it("existence of both name_jpn and name_cmn should set personTranslation with _jpn fields", async () => {
        const page = {
          from: faker.datatype.number(),
          size: faker.datatype.number()
        };

        const hit = generateMockHit();

        const hits: SearchHitsMetadata<HCPDocument> = {
          total: {
            value: faker.datatype.number()
          } as SearchTotalHits,
          hits: [hit]
        };

        const synonyms = generateMockSynonyms();
        const suppliedFilters = generateFilters();
        const queryIntent = generateMockQueryIntents();

        const affiliationAdapterService = createMockInstance(
          AffiliationAdapterService
        );

        const keywordSearchResponseAdapterService =
          new KeywordSearchResponseAdapterService(affiliationAdapterService);

        const adaptedResponse = await keywordSearchResponseAdapterService.adapt(
          page,
          ENGLISH,
          hits,
          undefined,
          synonyms,
          suppliedFilters,
          queryIntent,
          [],
          generateKeywordSearchFeatureFlags(),

          [],
          []
        );

        expect(adaptedResponse).toEqual(
          expect.objectContaining({
            results: [
              expect.objectContaining({
                personTranslationEng: expect.anything(),
                personTranslation: {
                  fullName: hit._source!.name_jpn,
                  lastName: hit._source!.lastName_jpn,
                  middleName: hit._source!.middleName_jpn,
                  firstName: hit._source!.firstName_jpn,
                  languageCode: JAPANESE
                }
              })
            ]
          })
        );
      });

      it("should fallback to _cmn name fields if name_jpn is undefined", async () => {
        const page = {
          from: faker.datatype.number(),
          size: faker.datatype.number()
        };

        const hit = generateMockHit({
          name_jpn: undefined
        });

        const hits: SearchHitsMetadata<HCPDocument> = {
          total: {
            value: faker.datatype.number()
          } as SearchTotalHits,
          hits: [hit]
        };

        const synonyms = generateMockSynonyms();
        const suppliedFilters = generateFilters();
        const queryIntent = generateMockQueryIntents();

        const affiliationAdapterService = createMockInstance(
          AffiliationAdapterService
        );

        const keywordSearchResponseAdapterService =
          new KeywordSearchResponseAdapterService(affiliationAdapterService);

        const adaptedResponse = await keywordSearchResponseAdapterService.adapt(
          page,
          ENGLISH,
          hits,
          undefined,
          synonyms,
          suppliedFilters,
          queryIntent,
          [],
          generateKeywordSearchFeatureFlags(),

          [],
          []
        );

        expect(adaptedResponse).toEqual(
          expect.objectContaining({
            results: [
              expect.objectContaining({
                personTranslationEng: expect.anything(),
                personTranslation: {
                  fullName: hit._source!.name_cmn,
                  lastName: hit._source!.lastName_cmn,
                  middleName: hit._source!.middleName_cmn,
                  firstName: hit._source!.firstName_cmn,
                  languageCode: CHINESE
                }
              })
            ]
          })
        );
      });

      it("should not set any name translations when none of name_eng, name_jpn, or name_cmn exist", async () => {
        const page = {
          from: faker.datatype.number(),
          size: faker.datatype.number()
        };

        const hit = generateMockHit({
          name_eng: undefined,
          name_jpn: undefined,
          name_cmn: undefined
        });

        const hits: SearchHitsMetadata<HCPDocument> = {
          total: {
            value: faker.datatype.number()
          } as SearchTotalHits,
          hits: [hit]
        };

        const synonyms = generateMockSynonyms();
        const suppliedFilters = generateFilters();
        const queryIntent = generateMockQueryIntents();

        const affiliationAdapterService = createMockInstance(
          AffiliationAdapterService
        );

        const keywordSearchResponseAdapterService =
          new KeywordSearchResponseAdapterService(affiliationAdapterService);

        const adaptedResponse = await keywordSearchResponseAdapterService.adapt(
          page,
          ENGLISH,
          hits,
          undefined,
          synonyms,
          suppliedFilters,
          queryIntent,
          [],
          generateKeywordSearchFeatureFlags(),

          [],
          []
        );

        expect(adaptedResponse).toEqual(
          expect.objectContaining({
            results: [
              expect.not.objectContaining({
                personTranslationEng: expect.anything(),
                personTranslation: expect.anything()
              })
            ]
          })
        );
      });
    });

    describe("department translations", () => {
      it("should return first language-specific department translation when found in that language", async () => {
        const page = {
          from: faker.datatype.number(),
          size: faker.datatype.number()
        };

        const requestedLanguageCode =
          faker.helpers.arrayElement(USER_LANGUAGES);

        const departmentTranslations = _(ALL_LANGUAGES)
          .map(generateNMockDepartmentTranslations)
          .flatten()
          .shuffle()
          .value();

        const firstDepartmentInRequestedLanguage = getRequestedTranslation(
          departmentTranslations,
          requestedLanguageCode as Language
        )!;

        const hit = generateMockHit({
          affiliations: [generateMockAffiliation({ departmentTranslations })]
        });

        const hits: SearchHitsMetadata<HCPDocument> = {
          total: {
            value: faker.datatype.number()
          } as SearchTotalHits,
          hits: [hit]
        };

        const synonyms = generateMockSynonyms();
        const suppliedFilters = generateFilters();
        const queryIntent = generateMockQueryIntents();

        const affiliationAdapterService = createMockInstance(
          AffiliationAdapterService
        );

        const keywordSearchResponseAdapterService =
          new KeywordSearchResponseAdapterService(affiliationAdapterService);

        const adaptedResponse = await keywordSearchResponseAdapterService.adapt(
          page,
          requestedLanguageCode,
          hits,
          undefined,
          synonyms,
          suppliedFilters,
          queryIntent,
          [],
          generateKeywordSearchFeatureFlags(),

          [],
          []
        );

        expect(adaptedResponse).toEqual(
          expect.objectContaining({
            results: [
              expect.objectContaining({
                affiliations: [
                  expect.objectContaining({
                    department: firstDepartmentInRequestedLanguage.department
                  })
                ]
              })
            ]
          })
        );
      });

      it("should return first english department translation when not found in the requested language", async () => {
        const page = {
          from: faker.datatype.number(),
          size: faker.datatype.number()
        };

        const requestedNonEnglishLanguageCode = faker.helpers.arrayElement(
          NON_ENGLISH_USER_LANGUAGES
        );

        const departmentTranslations = _(ALL_LANGUAGES)
          .without(
            ...getEquivalentLanguageCodes(requestedNonEnglishLanguageCode)
          )
          .map(generateNMockDepartmentTranslations)
          .flatten()
          .shuffle()
          .value();

        const firstEnglishDepartment = getRequestedTranslation(
          departmentTranslations,
          ENGLISH
        )!;

        const hit = generateMockHit({
          affiliations: [generateMockAffiliation({ departmentTranslations })]
        });

        const hits: SearchHitsMetadata<HCPDocument> = {
          total: {
            value: faker.datatype.number()
          } as SearchTotalHits,
          hits: [hit]
        };

        const synonyms = generateMockSynonyms();
        const suppliedFilters = generateFilters();
        const queryIntent = generateMockQueryIntents();

        const affiliationAdapterService = createMockInstance(
          AffiliationAdapterService
        );

        const keywordSearchResponseAdapterService =
          new KeywordSearchResponseAdapterService(affiliationAdapterService);

        const adaptedResponse = await keywordSearchResponseAdapterService.adapt(
          page,
          requestedNonEnglishLanguageCode,
          hits,
          undefined,
          synonyms,
          suppliedFilters,
          queryIntent,
          [],
          generateKeywordSearchFeatureFlags(),

          [],
          []
        );

        expect(adaptedResponse).toEqual(
          expect.objectContaining({
            results: [
              expect.objectContaining({
                affiliations: [
                  expect.objectContaining({
                    department: firstEnglishDepartment.department
                  })
                ]
              })
            ]
          })
        );
      });

      it("should return undefined when english translation is requested but not available", async () => {
        const page = {
          from: faker.datatype.number(),
          size: faker.datatype.number()
        };

        const departmentTranslations = _(NON_ENGLISH_LANGUAGES)
          .map(generateNMockDepartmentTranslations)
          .flatten()
          .shuffle()
          .value();

        const hit = generateMockHit({
          affiliations: [generateMockAffiliation({ departmentTranslations })]
        });

        const hits: SearchHitsMetadata<HCPDocument> = {
          total: {
            value: faker.datatype.number()
          } as SearchTotalHits,
          hits: [hit]
        };

        const synonyms = generateMockSynonyms();
        const suppliedFilters = generateFilters();
        const queryIntent = generateMockQueryIntents();

        const affiliationAdapterService = createMockInstance(
          AffiliationAdapterService
        );

        const keywordSearchResponseAdapterService =
          new KeywordSearchResponseAdapterService(affiliationAdapterService);

        const adaptedResponse = await keywordSearchResponseAdapterService.adapt(
          page,
          ENGLISH,
          hits,
          undefined,
          synonyms,
          suppliedFilters,
          queryIntent,
          [],
          generateKeywordSearchFeatureFlags(),

          [],
          []
        );

        expect(adaptedResponse).toEqual(
          expect.objectContaining({
            results: [
              expect.objectContaining({
                affiliations: [
                  expect.objectContaining({
                    department: undefined
                  })
                ]
              })
            ]
          })
        );
      });

      it("should return undefined when neither requested language nor English translation is unavailable", async () => {
        const page = {
          from: faker.datatype.number(),
          size: faker.datatype.number()
        };

        const requestedNonEnglishLanguageCode = faker.helpers.arrayElement(
          NON_ENGLISH_USER_LANGUAGES
        );

        const departmentTranslations = _(NON_ENGLISH_LANGUAGES)
          .without(
            ...getEquivalentLanguageCodes(requestedNonEnglishLanguageCode)
          )
          .map(generateNMockDepartmentTranslations)
          .flatten()
          .shuffle()
          .value();

        const hit = generateMockHit({
          affiliations: [generateMockAffiliation({ departmentTranslations })]
        });

        const hits: SearchHitsMetadata<HCPDocument> = {
          total: {
            value: faker.datatype.number()
          } as SearchTotalHits,
          hits: [hit]
        };

        const synonyms = generateMockSynonyms();
        const suppliedFilters = generateFilters();
        const queryIntent = generateMockQueryIntents();

        const affiliationAdapterService = createMockInstance(
          AffiliationAdapterService
        );

        const keywordSearchResponseAdapterService =
          new KeywordSearchResponseAdapterService(affiliationAdapterService);

        const adaptedResponse = await keywordSearchResponseAdapterService.adapt(
          page,
          requestedNonEnglishLanguageCode,
          hits,
          undefined,
          synonyms,
          suppliedFilters,
          queryIntent,
          [],
          generateKeywordSearchFeatureFlags(),

          [],
          []
        );

        expect(adaptedResponse).toEqual(
          expect.objectContaining({
            results: [
              expect.objectContaining({
                affiliations: [
                  expect.objectContaining({
                    department: undefined
                  })
                ]
              })
            ]
          })
        );
      });
    });

    describe("title translations", () => {
      it("should return first language-specific titles translation when found in that language", async () => {
        const page = {
          from: faker.datatype.number(),
          size: faker.datatype.number()
        };

        const requestedLanguageCode =
          faker.helpers.arrayElement(USER_LANGUAGES);

        const titleTranslations = _(ALL_LANGUAGES)
          .map(generateNMockTitlesTranslations)
          .flatten()
          .shuffle()
          .value();

        const firstTitlesInRequestedLanguage = getRequestedTranslation(
          titleTranslations,
          requestedLanguageCode as Language
        )!;

        const hit = generateMockHit({
          affiliations: [generateMockAffiliation({ titleTranslations })]
        });

        const hits: SearchHitsMetadata<HCPDocument> = {
          total: {
            value: faker.datatype.number()
          } as SearchTotalHits,
          hits: [hit]
        };

        const synonyms = generateMockSynonyms();
        const suppliedFilters = generateFilters();
        const queryIntent = generateMockQueryIntents();

        const affiliationAdapterService = createMockInstance(
          AffiliationAdapterService
        );

        const keywordSearchResponseAdapterService =
          new KeywordSearchResponseAdapterService(affiliationAdapterService);

        const adaptedResponse = await keywordSearchResponseAdapterService.adapt(
          page,
          requestedLanguageCode,
          hits,
          undefined,
          synonyms,
          suppliedFilters,
          queryIntent,
          [],
          generateKeywordSearchFeatureFlags(),

          [],
          []
        );

        expect(adaptedResponse.results[0].affiliations![0].titles).toEqual(
          firstTitlesInRequestedLanguage.titles
        );
      });

      it("should return first english titles translation when not found in the requested language", async () => {
        const page = {
          from: faker.datatype.number(),
          size: faker.datatype.number()
        };

        const requestedNonEnglishLanguageCode = faker.helpers.arrayElement(
          NON_ENGLISH_USER_LANGUAGES
        );

        const titleTranslations = _(ALL_LANGUAGES)
          .without(
            ...getEquivalentLanguageCodes(requestedNonEnglishLanguageCode)
          )
          .map(generateNMockTitlesTranslations)
          .flatten()
          .shuffle()
          .value();

        const firstEnglishTitles = getRequestedTranslation(
          titleTranslations,
          ENGLISH
        )!;

        const hit = generateMockHit({
          affiliations: [generateMockAffiliation({ titleTranslations })]
        });

        const hits: SearchHitsMetadata<HCPDocument> = {
          total: {
            value: faker.datatype.number()
          } as SearchTotalHits,
          hits: [hit]
        };

        const synonyms = generateMockSynonyms();
        const suppliedFilters = generateFilters();
        const queryIntent = generateMockQueryIntents();

        const affiliationAdapterService = createMockInstance(
          AffiliationAdapterService
        );

        const keywordSearchResponseAdapterService =
          new KeywordSearchResponseAdapterService(affiliationAdapterService);

        const adaptedResponse = await keywordSearchResponseAdapterService.adapt(
          page,
          requestedNonEnglishLanguageCode,
          hits,
          undefined,
          synonyms,
          suppliedFilters,
          queryIntent,
          [],
          generateKeywordSearchFeatureFlags(),

          [],
          []
        );

        expect(adaptedResponse.results[0].affiliations![0].titles).toEqual(
          firstEnglishTitles.titles
        );
      });

      it("should return non-translated titles when english translation is requested but not available", async () => {
        const page = {
          from: faker.datatype.number(),
          size: faker.datatype.number()
        };

        const titleTranslations = _(NON_ENGLISH_LANGUAGES)
          .map(generateNMockTitlesTranslations)
          .flatten()
          .shuffle()
          .value();

        const hit = generateMockHit({
          affiliations: [generateMockAffiliation({ titleTranslations })]
        });

        const hits: SearchHitsMetadata<HCPDocument> = {
          total: {
            value: faker.datatype.number()
          } as SearchTotalHits,
          hits: [hit]
        };

        const synonyms = generateMockSynonyms();
        const suppliedFilters = generateFilters();
        const queryIntent = generateMockQueryIntents();

        const affiliationAdapterService = createMockInstance(
          AffiliationAdapterService
        );

        const keywordSearchResponseAdapterService =
          new KeywordSearchResponseAdapterService(affiliationAdapterService);

        const adaptedResponse = await keywordSearchResponseAdapterService.adapt(
          page,
          ENGLISH,
          hits,
          undefined,
          synonyms,
          suppliedFilters,
          queryIntent,
          [],
          generateKeywordSearchFeatureFlags(),

          [],
          []
        );

        expect(adaptedResponse.results[0].affiliations![0].titles).toEqual(
          hit._source!.affiliations![0].titles
        );
      });

      it("should return an empty array when neither requested language nor English translation is unavailable and non-translated titles is unavailable", async () => {
        const page = {
          from: faker.datatype.number(),
          size: faker.datatype.number()
        };

        const requestedNonEnglishLanguageCode = faker.helpers.arrayElement(
          NON_ENGLISH_USER_LANGUAGES
        );

        const titleTranslations = _(NON_ENGLISH_LANGUAGES)
          .without(
            ...getEquivalentLanguageCodes(requestedNonEnglishLanguageCode)
          )
          .map(generateNMockTitlesTranslations)
          .flatten()
          .shuffle()
          .value();

        console.log(requestedNonEnglishLanguageCode);
        console.log(titleTranslations);

        const hit = generateMockHit({
          affiliations: [
            generateMockAffiliation({ titleTranslations, titles: [] })
          ]
        });

        const hits: SearchHitsMetadata<HCPDocument> = {
          total: {
            value: faker.datatype.number()
          } as SearchTotalHits,
          hits: [hit]
        };

        const synonyms = generateMockSynonyms();
        const suppliedFilters = generateFilters();
        const queryIntent = generateMockQueryIntents();

        const affiliationAdapterService = createMockInstance(
          AffiliationAdapterService
        );

        const keywordSearchResponseAdapterService =
          new KeywordSearchResponseAdapterService(affiliationAdapterService);

        const adaptedResponse = await keywordSearchResponseAdapterService.adapt(
          page,
          requestedNonEnglishLanguageCode,
          hits,
          undefined,
          synonyms,
          suppliedFilters,
          queryIntent,
          [],
          generateKeywordSearchFeatureFlags(),

          [],
          []
        );

        expect(adaptedResponse.results[0].affiliations![0].titles).toEqual([]);
      });
    });

    describe("address translations", () => {
      it("should return first language-specific address translation when found in that language", async () => {
        const page = {
          from: faker.datatype.number(),
          size: faker.datatype.number()
        };

        const requestedLanguageCode =
          faker.helpers.arrayElement(USER_LANGUAGES);

        const addressTranslations = _(ALL_LANGUAGES)
          .map(generateNMockAddressTranslations)
          .flatten()
          .shuffle()
          .value();

        const firstAddressInRequestedLanguage = getRequestedTranslation(
          addressTranslations,
          requestedLanguageCode as Language
        )!;

        const institution = generateMockInstitution({ addressTranslations });

        const hit = generateMockHit({
          affiliations: [generateMockAffiliation({ institution })]
        });

        const hits: SearchHitsMetadata<HCPDocument> = {
          total: {
            value: faker.datatype.number()
          } as SearchTotalHits,
          hits: [hit]
        };

        const synonyms = generateMockSynonyms();
        const suppliedFilters = generateFilters();
        const queryIntent = generateMockQueryIntents();

        const affiliationAdapterService = createMockInstance(
          AffiliationAdapterService
        );

        const keywordSearchResponseAdapterService =
          new KeywordSearchResponseAdapterService(affiliationAdapterService);

        const adaptedResponse = await keywordSearchResponseAdapterService.adapt(
          page,
          requestedLanguageCode,
          hits,
          undefined,
          synonyms,
          suppliedFilters,
          queryIntent,
          [],
          generateKeywordSearchFeatureFlags(),

          [],
          []
        );

        expect(
          adaptedResponse.results[0].affiliations![0].institution!.address
        ).toEqual({
          id: firstAddressInRequestedLanguage.id,
          street1: firstAddressInRequestedLanguage.street1,
          street2: firstAddressInRequestedLanguage.street2,
          street3: firstAddressInRequestedLanguage.street3,
          city: firstAddressInRequestedLanguage.city,
          region: firstAddressInRequestedLanguage.region,
          regionCode: firstAddressInRequestedLanguage.region_code,
          postalCode: firstAddressInRequestedLanguage.postal_code,
          country: firstAddressInRequestedLanguage.country,
          countryCode: firstAddressInRequestedLanguage.country_code,
          latitude: institution.location.lat,
          longitude: institution.location.lon
        });
      });

      it("should return first english address translation when not found in the requested language", async () => {
        const page = {
          from: faker.datatype.number(),
          size: faker.datatype.number()
        };

        const requestedNonEnglishLanguageCode = faker.helpers.arrayElement(
          NON_ENGLISH_LANGUAGES
        );

        const addressTranslations = _(ALL_LANGUAGES)
          .without(
            ...getEquivalentLanguageCodes(requestedNonEnglishLanguageCode)
          )
          .map(generateNMockAddressTranslations)
          .flatten()
          .shuffle()
          .value();

        const firstEnglishAddressTranslation = getRequestedTranslation(
          addressTranslations,
          ENGLISH
        )!;

        const institution = generateMockInstitution({ addressTranslations });

        const hit = generateMockHit({
          affiliations: [generateMockAffiliation({ institution })]
        });

        const hits: SearchHitsMetadata<HCPDocument> = {
          total: {
            value: faker.datatype.number()
          } as SearchTotalHits,
          hits: [hit]
        };

        const synonyms = generateMockSynonyms();
        const suppliedFilters = generateFilters();
        const queryIntent = generateMockQueryIntents();

        const affiliationAdapterService = createMockInstance(
          AffiliationAdapterService
        );

        const keywordSearchResponseAdapterService =
          new KeywordSearchResponseAdapterService(affiliationAdapterService);

        const adaptedResponse = await keywordSearchResponseAdapterService.adapt(
          page,
          requestedNonEnglishLanguageCode,
          hits,
          undefined,
          synonyms,
          suppliedFilters,
          queryIntent,
          [],
          generateKeywordSearchFeatureFlags(),

          [],
          []
        );

        expect(
          adaptedResponse.results[0].affiliations![0].institution!.address
        ).toEqual({
          id: firstEnglishAddressTranslation.id,
          street1: firstEnglishAddressTranslation.street1,
          street2: firstEnglishAddressTranslation.street2,
          street3: firstEnglishAddressTranslation.street3,
          city: firstEnglishAddressTranslation.city,
          region: firstEnglishAddressTranslation.region,
          regionCode: firstEnglishAddressTranslation.region_code,
          postalCode: firstEnglishAddressTranslation.postal_code,
          country: firstEnglishAddressTranslation.country,
          countryCode: firstEnglishAddressTranslation.country_code,
          latitude: institution.location.lat,
          longitude: institution.location.lon
        });
      });

      it("should return non-translated titles when english translation is requested but not available", async () => {
        const page = {
          from: faker.datatype.number(),
          size: faker.datatype.number()
        };

        const addressTranslations = _(NON_ENGLISH_LANGUAGES)
          .map(generateNMockAddressTranslations)
          .flatten()
          .shuffle()
          .value();

        const institution = generateMockInstitution({ addressTranslations });

        const hit = generateMockHit({
          affiliations: [generateMockAffiliation({ institution })]
        });

        const hits: SearchHitsMetadata<HCPDocument> = {
          total: {
            value: faker.datatype.number()
          } as SearchTotalHits,
          hits: [hit]
        };

        const synonyms = generateMockSynonyms();
        const suppliedFilters = generateFilters();
        const queryIntent = generateMockQueryIntents();

        const affiliationAdapterService = createMockInstance(
          AffiliationAdapterService
        );

        const keywordSearchResponseAdapterService =
          new KeywordSearchResponseAdapterService(affiliationAdapterService);

        const adaptedResponse = await keywordSearchResponseAdapterService.adapt(
          page,
          ENGLISH,
          hits,
          undefined,
          synonyms,
          suppliedFilters,
          queryIntent,
          [],
          generateKeywordSearchFeatureFlags(),

          [],
          []
        );

        expect(
          adaptedResponse.results[0].affiliations![0].institution!.address
        ).toEqual({
          id: hit._source!.affiliations![0].institution.address.id,
          street1: hit._source!.affiliations![0].institution.address.street1,
          street2: hit._source!.affiliations![0].institution.address.street2,
          street3: hit._source!.affiliations![0].institution.address.street3,
          city: hit._source!.affiliations![0].institution.address.city,
          region: hit._source!.affiliations![0].institution.address.region,
          regionCode:
            hit._source!.affiliations![0].institution.address.region_code,
          postalCode:
            hit._source!.affiliations![0].institution.address.postal_code,
          country: hit._source!.affiliations![0].institution.address.country,
          countryCode:
            hit._source!.affiliations![0].institution.address.country_code,
          latitude: institution.location.lat,
          longitude: institution.location.lon
        });
      });

      it("should return undefined when neither requested language nor English translation is unavailable and non-translated address is unavailable", async () => {
        const page = {
          from: faker.datatype.number(),
          size: faker.datatype.number()
        };

        const addressTranslations = _(NON_ENGLISH_LANGUAGES)
          .map(generateNMockAddressTranslations)
          .flatten()
          .shuffle()
          .value();

        const institution = generateMockInstitution({
          addressTranslations,
          address: undefined
        });

        const hit = generateMockHit({
          affiliations: [generateMockAffiliation({ institution })]
        });

        const hits: SearchHitsMetadata<HCPDocument> = {
          total: {
            value: faker.datatype.number()
          } as SearchTotalHits,
          hits: [hit]
        };

        const synonyms = generateMockSynonyms();
        const suppliedFilters = generateFilters();
        const queryIntent = generateMockQueryIntents();

        const affiliationAdapterService = createMockInstance(
          AffiliationAdapterService
        );

        const keywordSearchResponseAdapterService =
          new KeywordSearchResponseAdapterService(affiliationAdapterService);

        const adaptedResponse = await keywordSearchResponseAdapterService.adapt(
          page,
          ENGLISH,
          hits,
          undefined,
          synonyms,
          suppliedFilters,
          queryIntent,
          [],
          generateKeywordSearchFeatureFlags(),

          [],
          []
        );

        expect(
          adaptedResponse.results[0].affiliations![0].institution!.address
        ).toBeUndefined();
      });
    });
  });

  describe("institutions", () => {
    it("should parse combine institutions from lookup service with affiliation institutions", async () => {
      const page = {
        from: faker.datatype.number(),
        size: faker.datatype.number()
      };

      const ultimateParentInstitutionDocument1 =
        generateMockInstitutionDocument({
          isIol: true,
          nameTranslations: []
        });
      const ultimateParentInstitutionDocument3 =
        generateMockInstitutionDocument({
          isIol: false,
          nameTranslations: []
        });

      const affiliation1 = generateMockAffiliation({
        institution: generateMockInstitution({
          nameTranslations: [],
          isIol: true,
          ultimateParentId: ultimateParentInstitutionDocument1.id,
          ultimateParentInstitution: {
            ...ultimateParentInstitutionDocument1,
            masterOrganizationId:
              ultimateParentInstitutionDocument1.masterOrganizationId!.toString(),
            nameTranslations: [
              ...ultimateParentInstitutionDocument1.nameTranslations
            ]
          }
        })
      });
      const affiliation2 = generateMockAffiliation({
        institution: generateMockInstitution({
          nameTranslations: [],
          isIol: true,
          ultimateParentId: undefined
        })
      });
      const affiliation3 = generateMockAffiliation({
        institution: generateMockInstitution({
          nameTranslations: [],
          isIol: false,
          ultimateParentId: ultimateParentInstitutionDocument3.id,
          ultimateParentInstitution: {
            ...ultimateParentInstitutionDocument3,
            masterOrganizationId:
              ultimateParentInstitutionDocument3.masterOrganizationId!.toString(),
            nameTranslations: [
              ...ultimateParentInstitutionDocument3.nameTranslations
            ]
          }
        })
      });

      const hit = generateMockHit({
        affiliations: [affiliation1, affiliation2, affiliation3]
      });

      const hits: SearchHitsMetadata<HCPDocument> = {
        total: {
          value: faker.datatype.number()
        } as SearchTotalHits,
        hits: [hit]
      };

      const affiliationAdapterService = createMockInstance(
        AffiliationAdapterService
      );

      const keywordSearchResponseAdapterService =
        new KeywordSearchResponseAdapterService(affiliationAdapterService);

      const adaptedResponse = await keywordSearchResponseAdapterService.adapt(
        page,
        ENGLISH,
        hits,
        undefined,
        generateMockSynonyms(),
        generateFilters(),
        generateMockQueryIntents(),
        [],
        generateKeywordSearchFeatureFlags(),

        [],
        []
      );

      expect(adaptedResponse.results[0].affiliations).toEqual([
        expect.objectContaining({
          institution: expect.objectContaining({
            id: affiliation1.institution.id,
            orgTypes: [affiliation1.institution.orgTypes],
            iol: {
              id: affiliation1.institution.masterOrganizationId,
              region: affiliation1.institution.region
            },
            name: affiliation1.institution.name,
            type: affiliation1.institution.type,
            ultimateParent: {
              id: ultimateParentInstitutionDocument1.id,
              orgTypes: [],
              iol: {
                id: ultimateParentInstitutionDocument1.masterOrganizationId,
                region: ultimateParentInstitutionDocument1.region
              },
              name: ultimateParentInstitutionDocument1.name,
              type: ultimateParentInstitutionDocument1.type
            }
          })
        }),
        expect.objectContaining({
          institution: expect.objectContaining({
            id: affiliation2.institution.id,
            orgTypes: [affiliation2.institution.orgTypes],
            iol: {
              id: affiliation2.institution.masterOrganizationId,
              region: affiliation2.institution.region
            },
            name: affiliation2.institution.name,
            type: affiliation2.institution.type,
            ultimateParent: undefined
          })
        }),
        expect.objectContaining({
          institution: expect.objectContaining({
            id: affiliation3.institution.id,
            orgTypes: [affiliation3.institution.orgTypes],
            iol: undefined,
            name: affiliation3.institution.name,
            type: affiliation3.institution.type,
            ultimateParent: {
              id: ultimateParentInstitutionDocument3.id,
              orgTypes: [],
              iol: undefined,
              name: ultimateParentInstitutionDocument3.name,
              type: ultimateParentInstitutionDocument3.type
            }
          })
        })
      ]);
    });

    it("should return first language-specific institution name translation when found in that language", async () => {
      const page = {
        from: faker.datatype.number(),
        size: faker.datatype.number()
      };

      const requestedLanguageCode = faker.helpers.arrayElement(USER_LANGUAGES);

      const institutionNameTranslations = _(ALL_LANGUAGES)
        .map(generateNMockInstitutionNameTranslations)
        .flatten()
        .shuffle()
        .value();

      const firstInstitutionNameInRequestedLanguage = getRequestedTranslation(
        institutionNameTranslations,
        requestedLanguageCode as Language
      )!;

      const ultimateParentInstitutionDocument = generateMockInstitutionDocument(
        {
          nameTranslations: institutionNameTranslations
        }
      );

      const affiliation = generateMockAffiliation({
        institution: generateMockInstitution({
          nameTranslations: [],
          ultimateParentId: ultimateParentInstitutionDocument.id,
          ultimateParentInstitution: {
            ...ultimateParentInstitutionDocument,
            masterOrganizationId:
              ultimateParentInstitutionDocument.masterOrganizationId!.toString(),
            nameTranslations: [
              ...ultimateParentInstitutionDocument.nameTranslations
            ]
          }
        })
      });

      const hit = generateMockHit({
        affiliations: [affiliation]
      });

      const hits: SearchHitsMetadata<HCPDocument> = {
        total: {
          value: faker.datatype.number()
        } as SearchTotalHits,
        hits: [hit]
      };

      const affiliationAdapterService = createMockInstance(
        AffiliationAdapterService
      );

      const keywordSearchResponseAdapterService =
        new KeywordSearchResponseAdapterService(affiliationAdapterService);

      const adaptedResponse = await keywordSearchResponseAdapterService.adapt(
        page,
        requestedLanguageCode,
        hits,
        undefined,
        generateMockSynonyms(),
        generateFilters(),
        generateMockQueryIntents(),
        [],
        generateKeywordSearchFeatureFlags(),

        [],
        []
      );

      expect(
        adaptedResponse.results[0].affiliations![0].institution!.ultimateParent!
          .name
      ).toEqual(firstInstitutionNameInRequestedLanguage.name);
    });

    it("should return first english institution name translation when not found in the requested language", async () => {
      const page = {
        from: faker.datatype.number(),
        size: faker.datatype.number()
      };

      const requestedNonEnglishLanguageCode = faker.helpers.arrayElement(
        NON_ENGLISH_USER_LANGUAGES
      );

      const institutionNameTranslations = _(ALL_LANGUAGES)
        .without(...getEquivalentLanguageCodes(requestedNonEnglishLanguageCode))
        .map(generateNMockInstitutionNameTranslations)
        .flatten()
        .shuffle()
        .value();

      const firstEnglishInstitutionName = getRequestedTranslation(
        institutionNameTranslations,
        ENGLISH
      )!;

      const ultimateParentInstitutionDocument = generateMockInstitutionDocument(
        {
          nameTranslations: institutionNameTranslations
        }
      );

      const affiliation = generateMockAffiliation({
        institution: generateMockInstitution({
          nameTranslations: institutionNameTranslations,
          ultimateParentId: ultimateParentInstitutionDocument.id,
          ultimateParentInstitution: {
            ...ultimateParentInstitutionDocument,
            masterOrganizationId:
              ultimateParentInstitutionDocument.masterOrganizationId!.toString(),
            nameTranslations: [
              ...ultimateParentInstitutionDocument.nameTranslations
            ]
          }
        })
      });

      const hit = generateMockHit({
        affiliations: [affiliation]
      });

      const hits: SearchHitsMetadata<HCPDocument> = {
        total: {
          value: faker.datatype.number()
        } as SearchTotalHits,
        hits: [hit]
      };

      const affiliationAdapterService = createMockInstance(
        AffiliationAdapterService
      );

      const keywordSearchResponseAdapterService =
        new KeywordSearchResponseAdapterService(affiliationAdapterService);

      const adaptedResponse = await keywordSearchResponseAdapterService.adapt(
        page,
        requestedNonEnglishLanguageCode,
        hits,
        undefined,
        generateMockSynonyms(),
        generateFilters(),
        generateMockQueryIntents(),
        [],
        generateKeywordSearchFeatureFlags(),

        [],
        []
      );

      expect(
        adaptedResponse.results[0].affiliations![0].institution!.ultimateParent!
          .name
      ).toEqual(firstEnglishInstitutionName.name);
    });

    it("should return untranslated institution name when requested language is unavailable", async () => {
      const page = {
        from: faker.datatype.number(),
        size: faker.datatype.number()
      };

      const requestedLanguageCode = faker.helpers.arrayElement(USER_LANGUAGES);

      const ultimateParentInstitutionDocument = generateMockInstitutionDocument(
        {
          nameTranslations: []
        }
      );

      const affiliation = generateMockAffiliation({
        institution: generateMockInstitution({
          ultimateParentId: ultimateParentInstitutionDocument.id,
          ultimateParentInstitution: {
            ...ultimateParentInstitutionDocument,
            masterOrganizationId:
              ultimateParentInstitutionDocument.masterOrganizationId!.toString(),
            nameTranslations: [
              ...ultimateParentInstitutionDocument.nameTranslations
            ]
          }
        })
      });

      const hit = generateMockHit({
        affiliations: [affiliation]
      });

      const hits: SearchHitsMetadata<HCPDocument> = {
        total: {
          value: faker.datatype.number()
        } as SearchTotalHits,
        hits: [hit]
      };

      const affiliationAdapterService = createMockInstance(
        AffiliationAdapterService
      );

      const keywordSearchResponseAdapterService =
        new KeywordSearchResponseAdapterService(affiliationAdapterService);

      const adaptedResponse = await keywordSearchResponseAdapterService.adapt(
        page,
        requestedLanguageCode,
        hits,
        undefined,
        generateMockSynonyms(),
        generateFilters(),
        generateMockQueryIntents(),
        [],
        generateKeywordSearchFeatureFlags(),

        [],
        []
      );

      expect(
        adaptedResponse.results[0].affiliations![0].institution!.ultimateParent!
          .name
      ).toEqual(ultimateParentInstitutionDocument.name);
    });
  });

  describe("name parsing", () => {
    describe("preferred language is chinese", () => {
      it("should use _cmn names when user's preferred language is Chinese and populate _eng names from _eng fields", async () => {
        const page = {
          from: faker.datatype.number(),
          size: faker.datatype.number()
        };

        const hit = generateMockHit();

        const hits: SearchHitsMetadata<HCPDocument> = {
          total: {
            value: faker.datatype.number()
          } as SearchTotalHits,
          hits: [hit]
        };

        const synonyms = generateMockSynonyms();
        const suppliedFilters = generateFilters();
        const queryIntent = generateMockQueryIntents();

        const affiliationAdapterService = createMockInstance(
          AffiliationAdapterService
        );

        const keywordSearchResponseAdapterService =
          new KeywordSearchResponseAdapterService(affiliationAdapterService);

        const {
          results: [result]
        } = await keywordSearchResponseAdapterService.adapt(
          page,
          CHINESE,
          hits,
          undefined,
          synonyms,
          suppliedFilters,
          queryIntent,
          [],
          generateKeywordSearchFeatureFlags(),

          [],
          []
        );

        expect(result.firstName).toEqual(hit._source!.firstName_cmn);
        expect(result.middleName).toBeNull();
        expect(result.lastName).toEqual(hit._source!.lastName_cmn);
        expect(result.firstNameEng).toEqual(hit._source!.firstName_eng);
        expect(result.lastNameEng).toEqual(hit._source!.lastName_eng);
      });

      it("should assign name_eng to firstName when _cmn fields are not available", async () => {
        const page = {
          from: faker.datatype.number(),
          size: faker.datatype.number()
        };

        const hit = generateMockHit({
          firstName_cmn: undefined,
          lastName_cmn: undefined,
          middleName_eng: undefined
        });

        const hits: SearchHitsMetadata<HCPDocument> = {
          total: {
            value: faker.datatype.number()
          } as SearchTotalHits,
          hits: [hit]
        };

        const synonyms = generateMockSynonyms();
        const suppliedFilters = generateFilters();
        const queryIntent = generateMockQueryIntents();

        const affiliationAdapterService = createMockInstance(
          AffiliationAdapterService
        );

        const keywordSearchResponseAdapterService =
          new KeywordSearchResponseAdapterService(affiliationAdapterService);

        const {
          results: [result]
        } = await keywordSearchResponseAdapterService.adapt(
          page,
          CHINESE,
          hits,
          undefined,
          synonyms,
          suppliedFilters,
          queryIntent,
          [],
          generateKeywordSearchFeatureFlags(),

          [],
          []
        );

        expect(result.firstName).toEqual(hit._source!.name_eng);
        expect(result.middleName).toBeNull();
        expect(result.lastName).toEqual("");
        expect(result.firstNameEng).toEqual("");
        expect(result.lastNameEng).toEqual("");
      });
    });

    describe("preferred language is japanese", () => {
      it("should use _jpn names when user's preferred language is Japanese and populate _eng names from _eng fields", async () => {
        const page = {
          from: faker.datatype.number(),
          size: faker.datatype.number()
        };

        const hit = generateMockHit();

        const hits: SearchHitsMetadata<HCPDocument> = {
          total: {
            value: faker.datatype.number()
          } as SearchTotalHits,
          hits: [hit]
        };

        const synonyms = generateMockSynonyms();
        const suppliedFilters = generateFilters();
        const queryIntent = generateMockQueryIntents();

        const affiliationAdapterService = createMockInstance(
          AffiliationAdapterService
        );

        const keywordSearchResponseAdapterService =
          new KeywordSearchResponseAdapterService(affiliationAdapterService);

        const {
          results: [result]
        } = await keywordSearchResponseAdapterService.adapt(
          page,
          JAPANESE,
          hits,
          undefined,
          synonyms,
          suppliedFilters,
          queryIntent,
          [],
          generateKeywordSearchFeatureFlags(),

          [],
          []
        );

        expect(result.firstName).toEqual(hit._source!.firstName_jpn);
        expect(result.middleName).toBeNull();
        expect(result.lastName).toEqual(hit._source!.lastName_jpn);
        expect(result.firstNameEng).toEqual(hit._source!.firstName_eng);
        expect(result.lastNameEng).toEqual(hit._source!.lastName_eng);
      });

      it("should assign name_eng to firstName when _jpn fields are not available", async () => {
        const page = {
          from: faker.datatype.number(),
          size: faker.datatype.number()
        };

        const hit = generateMockHit({
          firstName_jpn: undefined,
          lastName_jpn: undefined,
          middleName_eng: undefined
        });

        const hits: SearchHitsMetadata<HCPDocument> = {
          total: {
            value: faker.datatype.number()
          } as SearchTotalHits,
          hits: [hit]
        };

        const synonyms = generateMockSynonyms();
        const suppliedFilters = generateFilters();
        const queryIntent = generateMockQueryIntents();

        const affiliationAdapterService = createMockInstance(
          AffiliationAdapterService
        );

        const keywordSearchResponseAdapterService =
          new KeywordSearchResponseAdapterService(affiliationAdapterService);

        const {
          results: [result]
        } = await keywordSearchResponseAdapterService.adapt(
          page,
          JAPANESE,
          hits,
          undefined,
          synonyms,
          suppliedFilters,
          queryIntent,
          [],
          generateKeywordSearchFeatureFlags(),

          [],
          []
        );

        expect(result.firstName).toEqual(hit._source!.name_eng);
        expect(result.middleName).toBeNull();
        expect(result.lastName).toEqual("");
        expect(result.firstNameEng).toEqual("");
        expect(result.lastNameEng).toEqual("");
      });
    });

    it("falsey firstName_eng and lastName not equal to name_eng should use name_eng as firstName", async () => {
      const page = {
        from: faker.datatype.number(),
        size: faker.datatype.number()
      };

      const overrides: Partial<HCPDocument> = {
        firstName_eng: ""
      };

      const hit = generateMockHit(overrides);

      const hits: SearchHitsMetadata<HCPDocument> = {
        total: {
          value: faker.datatype.number()
        } as SearchTotalHits,
        hits: [hit]
      };

      const synonyms = generateMockSynonyms();
      const suppliedFilters = generateFilters();
      const queryIntent = generateMockQueryIntents();

      const affiliationAdapterService = createMockInstance(
        AffiliationAdapterService
      );

      const keywordSearchResponseAdapterService =
        new KeywordSearchResponseAdapterService(affiliationAdapterService);

      const {
        results: [result]
      } = await keywordSearchResponseAdapterService.adapt(
        page,
        ENGLISH,
        hits,
        undefined,
        synonyms,
        suppliedFilters,
        queryIntent,
        [],
        generateKeywordSearchFeatureFlags(),

        [],
        []
      );

      expect(result.firstName).toEqual(hit._source!.name_eng);
      expect(result.middleName).toEqual(hit._source!.middleName_eng);
      expect(result.lastName).toEqual(hit._source!.lastName_eng);
      expect(result.firstNameEng).toEqual(hit._source!.firstName_eng);
      expect(result.lastNameEng).toEqual(hit._source!.middleName_eng);
    });

    it("should populate firstNameEng and lastNameEng when lastName is populated", async () => {
      const page = {
        from: faker.datatype.number(),
        size: faker.datatype.number()
      };

      const overrides: Partial<HCPDocument> = {
        firstName_eng: ""
      };

      const hit = generateMockHit(overrides);

      const hits: SearchHitsMetadata<HCPDocument> = {
        total: {
          value: faker.datatype.number()
        } as SearchTotalHits,
        hits: [hit]
      };

      const synonyms = generateMockSynonyms();
      const suppliedFilters = generateFilters();
      const queryIntent = generateMockQueryIntents();

      const affiliationAdapterService = createMockInstance(
        AffiliationAdapterService
      );

      const keywordSearchResponseAdapterService =
        new KeywordSearchResponseAdapterService(affiliationAdapterService);

      const {
        results: [result]
      } = await keywordSearchResponseAdapterService.adapt(
        page,
        ENGLISH,
        hits,
        undefined,
        synonyms,
        suppliedFilters,
        queryIntent,
        [],
        generateKeywordSearchFeatureFlags(),

        [],
        []
      );

      expect(result.lastNameEng).toEqual(hit._source!.middleName_eng);
    });

    it("should return undefined for middleName when middleName_eng is an empty string", async () => {
      const page = {
        from: faker.datatype.number(),
        size: faker.datatype.number()
      };

      const overrides: Partial<HCPDocument> = {
        middleName_eng: ""
      };

      const hit = generateMockHit(overrides);

      const hits: SearchHitsMetadata<HCPDocument> = {
        total: {
          value: faker.datatype.number()
        } as SearchTotalHits,
        hits: [hit]
      };

      const synonyms = generateMockSynonyms();
      const suppliedFilters = generateFilters();
      const queryIntent = generateMockQueryIntents();

      const affiliationAdapterService = createMockInstance(
        AffiliationAdapterService
      );

      const keywordSearchResponseAdapterService =
        new KeywordSearchResponseAdapterService(affiliationAdapterService);

      const {
        results: [result]
      } = await keywordSearchResponseAdapterService.adapt(
        page,
        ENGLISH,
        hits,
        undefined,
        synonyms,
        suppliedFilters,
        queryIntent,
        [],
        generateKeywordSearchFeatureFlags(),

        [],
        []
      );

      expect(result.middleName).toBeUndefined();
    });
  });

  describe("patient diversity", () => {
    it("should return empty arrays when patientsDiversity is null", async () => {
      const page = {
        from: faker.datatype.number(),
        size: faker.datatype.number()
      };

      const hit = generateMockHit({ patientsDiversity: null });

      const hits: SearchHitsMetadata<HCPDocument> = {
        total: {
          value: faker.datatype.number()
        } as SearchTotalHits,
        hits: [hit]
      };

      const synonyms = generateMockSynonyms();
      const suppliedFilters = generateFilters();
      const queryIntent = generateMockQueryIntents();

      const affiliationAdapterService = createMockInstance(
        AffiliationAdapterService
      );

      const keywordSearchResponseAdapterService =
        new KeywordSearchResponseAdapterService(affiliationAdapterService);

      const adaptedResponse = await keywordSearchResponseAdapterService.adapt(
        page,
        ENGLISH,
        hits,
        undefined,
        synonyms,
        suppliedFilters,
        queryIntent,
        [],
        generateKeywordSearchFeatureFlags(),

        [],
        []
      );

      expect(adaptedResponse.results[0].patientsDiversity).toEqual({
        races: [],
        age: [],
        sex: []
      });
    });

    it("should use sum of sex count values to calculate percentages for sex percentages when totalPatientCount is null", async () => {
      const page = {
        from: faker.datatype.number(),
        size: faker.datatype.number()
      };

      const hit1PatientDiversity = {
        sex: [
          {
            count: faker.datatype.number(),
            value: faker.datatype.string()
          },
          {
            count: faker.datatype.number(),
            value: faker.datatype.string()
          }
        ],
        races_eng: [],
        age: []
      };

      const hit = generateMockHit({
        patientsDiversity: [hit1PatientDiversity],
        totalPatientCount: null
      });

      const totalPatientCountBySex =
        hit1PatientDiversity.sex[0].count + hit1PatientDiversity.sex[1].count;

      const expectedSex1Percentage = Math.round(
        (hit1PatientDiversity.sex[0].count / totalPatientCountBySex) * 100
      );
      const expectedSex2Percentage = Math.round(
        (hit1PatientDiversity.sex[1].count / totalPatientCountBySex) * 100
      );

      const hits: SearchHitsMetadata<HCPDocument> = {
        total: {
          value: faker.datatype.number()
        } as SearchTotalHits,
        hits: [hit]
      };

      const synonyms = generateMockSynonyms();
      const suppliedFilters = generateFilters();
      const queryIntent = generateMockQueryIntents();

      const affiliationAdapterService = createMockInstance(
        AffiliationAdapterService
      );

      const keywordSearchResponseAdapterService =
        new KeywordSearchResponseAdapterService(affiliationAdapterService);

      const adaptedResponse = await keywordSearchResponseAdapterService.adapt(
        page,
        ENGLISH,
        hits,
        undefined,
        synonyms,
        suppliedFilters,
        queryIntent,
        [],
        generateKeywordSearchFeatureFlags(),

        [],
        []
      );

      expect(adaptedResponse.results[0].patientsDiversity).toEqual({
        age: [],
        races: [],
        sex: [
          {
            count: hit1PatientDiversity.sex[0].count,
            value: hit1PatientDiversity.sex[0].value,
            percent: expectedSex1Percentage
          },
          {
            count: hit1PatientDiversity.sex[1].count,
            value: hit1PatientDiversity.sex[1].value,
            percent: expectedSex2Percentage
          }
        ]
      });
    });

    it("should use sum of sex count values to calculate percentages for sex percentages when totalPatientCount is 0", async () => {
      const page = {
        from: faker.datatype.number(),
        size: faker.datatype.number()
      };

      const hit1PatientDiversity = {
        sex: [
          {
            count: faker.datatype.number(),
            value: faker.datatype.string()
          },
          {
            count: faker.datatype.number(),
            value: faker.datatype.string()
          }
        ],
        races_eng: [],
        age: []
      };

      const hit = generateMockHit({
        patientsDiversity: [hit1PatientDiversity],
        totalPatientCount: 0
      });

      const totalPatientCountBySex =
        hit1PatientDiversity.sex[0].count + hit1PatientDiversity.sex[1].count;

      const expectedSex1Percentage = Math.round(
        (hit1PatientDiversity.sex[0].count / totalPatientCountBySex) * 100
      );
      const expectedSex2Percentage = Math.round(
        (hit1PatientDiversity.sex[1].count / totalPatientCountBySex) * 100
      );

      const hits: SearchHitsMetadata<HCPDocument> = {
        total: {
          value: faker.datatype.number()
        } as SearchTotalHits,
        hits: [hit]
      };

      const synonyms = generateMockSynonyms();
      const suppliedFilters = generateFilters();
      const queryIntent = generateMockQueryIntents();

      const affiliationAdapterService = createMockInstance(
        AffiliationAdapterService
      );

      const keywordSearchResponseAdapterService =
        new KeywordSearchResponseAdapterService(affiliationAdapterService);

      const adaptedResponse = await keywordSearchResponseAdapterService.adapt(
        page,
        ENGLISH,
        hits,
        undefined,
        synonyms,
        suppliedFilters,
        queryIntent,
        [],
        generateKeywordSearchFeatureFlags(),

        [],
        []
      );

      expect(adaptedResponse.results[0].patientsDiversity).toEqual({
        age: [],
        races: [],
        sex: [
          {
            count: hit1PatientDiversity.sex[0].count,
            value: hit1PatientDiversity.sex[0].value,
            percent: expectedSex1Percentage
          },
          {
            count: hit1PatientDiversity.sex[1].count,
            value: hit1PatientDiversity.sex[1].value,
            percent: expectedSex2Percentage
          }
        ]
      });
    });

    it("should use sum of age count values to calculate percentages for age percentages when totalPatientCount is null", async () => {
      const page = {
        from: faker.datatype.number(),
        size: faker.datatype.number()
      };

      const hit1PatientDiversity = {
        sex: [],
        races_eng: [],
        age: [
          {
            count: faker.datatype.number(),
            range: faker.datatype.string()
          },
          {
            count: faker.datatype.number(),
            range: faker.datatype.string()
          }
        ]
      };

      const hit = generateMockHit({
        patientsDiversity: [hit1PatientDiversity],
        totalPatientCount: null
      });

      const totalPatientCountByAge =
        hit1PatientDiversity.age[0].count + hit1PatientDiversity.age[1].count;

      const expectedAge1Percentage = Math.round(
        (hit1PatientDiversity.age[0].count / totalPatientCountByAge) * 100
      );
      const expectedAge2Percentage = Math.round(
        (hit1PatientDiversity.age[1].count / totalPatientCountByAge) * 100
      );

      const hits: SearchHitsMetadata<HCPDocument> = {
        total: {
          value: faker.datatype.number()
        } as SearchTotalHits,
        hits: [hit]
      };

      const synonyms = generateMockSynonyms();
      const suppliedFilters = generateFilters();
      const queryIntent = generateMockQueryIntents();

      const affiliationAdapterService = createMockInstance(
        AffiliationAdapterService
      );

      const keywordSearchResponseAdapterService =
        new KeywordSearchResponseAdapterService(affiliationAdapterService);

      const adaptedResponse = await keywordSearchResponseAdapterService.adapt(
        page,
        ENGLISH,
        hits,
        undefined,
        synonyms,
        suppliedFilters,
        queryIntent,
        [],
        generateKeywordSearchFeatureFlags(),

        [],
        []
      );

      expect(adaptedResponse.results[0].patientsDiversity).toEqual({
        age: [
          {
            count: hit1PatientDiversity.age[0].count,
            range: hit1PatientDiversity.age[0].range,
            percent: expectedAge1Percentage
          },
          {
            count: hit1PatientDiversity.age[1].count,
            range: hit1PatientDiversity.age[1].range,
            percent: expectedAge2Percentage
          }
        ],
        races: [],
        sex: []
      });
    });

    it("should use sum of age count values to calculate percentages for age percentages when totalPatientCount is 0", async () => {
      const page = {
        from: faker.datatype.number(),
        size: faker.datatype.number()
      };

      const hit1PatientDiversity = {
        sex: [],
        races_eng: [],
        age: [
          {
            count: faker.datatype.number(),
            range: faker.datatype.string()
          },
          {
            count: faker.datatype.number(),
            range: faker.datatype.string()
          }
        ]
      };

      const hit = generateMockHit({
        patientsDiversity: [hit1PatientDiversity],
        totalPatientCount: 0
      });

      const totalPatientCountByAge =
        hit1PatientDiversity.age[0].count + hit1PatientDiversity.age[1].count;

      const expectedAge1Percentage = Math.round(
        (hit1PatientDiversity.age[0].count / totalPatientCountByAge) * 100
      );
      const expectedAge2Percentage = Math.round(
        (hit1PatientDiversity.age[1].count / totalPatientCountByAge) * 100
      );

      const hits: SearchHitsMetadata<HCPDocument> = {
        total: {
          value: faker.datatype.number()
        } as SearchTotalHits,
        hits: [hit]
      };

      const affiliationAdapterService = createMockInstance(
        AffiliationAdapterService
      );

      const synonyms = generateMockSynonyms();
      const suppliedFilters = generateFilters();
      const queryIntent = generateMockQueryIntents();

      const keywordSearchResponseAdapterService =
        new KeywordSearchResponseAdapterService(affiliationAdapterService);

      const adaptedResponse = await keywordSearchResponseAdapterService.adapt(
        page,
        ENGLISH,
        hits,
        undefined,
        synonyms,
        suppliedFilters,
        queryIntent,
        [],
        generateKeywordSearchFeatureFlags(),

        [],
        []
      );

      expect(adaptedResponse.results[0].patientsDiversity).toEqual({
        age: [
          {
            count: hit1PatientDiversity.age[0].count,
            range: hit1PatientDiversity.age[0].range,
            percent: expectedAge1Percentage
          },
          {
            count: hit1PatientDiversity.age[1].count,
            range: hit1PatientDiversity.age[1].range,
            percent: expectedAge2Percentage
          }
        ],
        races: [],
        sex: []
      });
    });

    it("should use sum of race count values to calculate percentages for race percentages when patientsDiversityCount is null", async () => {
      const page = {
        from: faker.datatype.number(),
        size: faker.datatype.number()
      };

      const hit1PatientDiversity = {
        sex: [],
        races_eng: [
          {
            race: faker.datatype.string(),
            count: faker.datatype.number(),
            rank: faker.datatype.number()
          },
          {
            race: faker.datatype.string(),
            count: faker.datatype.number(),
            rank: faker.datatype.number()
          }
        ],
        age: []
      };

      const hit = generateMockHit({
        patientsDiversity: [hit1PatientDiversity],
        patientsDiversityCount: null
      });

      const totalPatientCountByRace =
        hit1PatientDiversity.races_eng[0].count +
        hit1PatientDiversity.races_eng[1].count;

      const expectedRace1Percentage = Math.round(
        (hit1PatientDiversity.races_eng[0].count / totalPatientCountByRace) *
          100
      );
      const expectedRace2Percentage = Math.round(
        (hit1PatientDiversity.races_eng[1].count / totalPatientCountByRace) *
          100
      );

      const hits: SearchHitsMetadata<HCPDocument> = {
        total: {
          value: faker.datatype.number()
        } as SearchTotalHits,
        hits: [hit]
      };

      const synonyms = generateMockSynonyms();
      const suppliedFilters = generateFilters();
      const queryIntent = generateMockQueryIntents();

      const affiliationAdapterService = createMockInstance(
        AffiliationAdapterService
      );

      const keywordSearchResponseAdapterService =
        new KeywordSearchResponseAdapterService(affiliationAdapterService);

      const adaptedResponse = await keywordSearchResponseAdapterService.adapt(
        page,
        ENGLISH,
        hits,
        undefined,
        synonyms,
        suppliedFilters,
        queryIntent,
        [],
        generateKeywordSearchFeatureFlags(),

        [],
        []
      );

      expect(adaptedResponse.results[0].patientsDiversity).toEqual({
        age: [],
        races: [
          {
            count: hit1PatientDiversity.races_eng[0].count,
            race: hit1PatientDiversity.races_eng[0].race,
            rank: hit1PatientDiversity.races_eng[0].rank,
            percent: expectedRace1Percentage
          },
          {
            count: hit1PatientDiversity.races_eng[1].count,
            race: hit1PatientDiversity.races_eng[1].race,
            rank: hit1PatientDiversity.races_eng[1].rank,
            percent: expectedRace2Percentage
          }
        ],
        sex: []
      });
    });

    it("should use sum of race count values to calculate percentages for race percentages when patientsDiversityCount is 0", async () => {
      const page = {
        from: faker.datatype.number(),
        size: faker.datatype.number()
      };

      const hit1PatientDiversity = {
        sex: [],
        races_eng: [
          {
            race: faker.datatype.string(),
            count: faker.datatype.number(),
            rank: faker.datatype.number()
          },
          {
            race: faker.datatype.string(),
            count: faker.datatype.number(),
            rank: faker.datatype.number()
          }
        ],
        age: []
      };

      const hit = generateMockHit({
        patientsDiversity: [hit1PatientDiversity],
        patientsDiversityCount: 0
      });

      const totalPatientCountByRace =
        hit1PatientDiversity.races_eng[0].count +
        hit1PatientDiversity.races_eng[1].count;

      const expectedRace1Percentage = Math.round(
        (hit1PatientDiversity.races_eng[0].count / totalPatientCountByRace) *
          100
      );
      const expectedRace2Percentage = Math.round(
        (hit1PatientDiversity.races_eng[1].count / totalPatientCountByRace) *
          100
      );

      const hits: SearchHitsMetadata<HCPDocument> = {
        total: {
          value: faker.datatype.number()
        } as SearchTotalHits,
        hits: [hit]
      };

      const synonyms = generateMockSynonyms();
      const suppliedFilters = generateFilters();
      const queryIntent = generateMockQueryIntents();

      const affiliationAdapterService = createMockInstance(
        AffiliationAdapterService
      );

      const keywordSearchResponseAdapterService =
        new KeywordSearchResponseAdapterService(affiliationAdapterService);

      const adaptedResponse = await keywordSearchResponseAdapterService.adapt(
        page,
        ENGLISH,
        hits,
        undefined,
        synonyms,
        suppliedFilters,
        queryIntent,
        [],
        generateKeywordSearchFeatureFlags(),

        [],
        []
      );

      expect(adaptedResponse.results[0].patientsDiversity).toEqual({
        age: [],
        races: [
          {
            count: hit1PatientDiversity.races_eng[0].count,
            race: hit1PatientDiversity.races_eng[0].race,
            rank: hit1PatientDiversity.races_eng[0].rank,
            percent: expectedRace1Percentage
          },
          {
            count: hit1PatientDiversity.races_eng[1].count,
            race: hit1PatientDiversity.races_eng[1].race,
            rank: hit1PatientDiversity.races_eng[1].rank,
            percent: expectedRace2Percentage
          }
        ],
        sex: []
      });
    });

    describe("Asian Pacific Islander Breakout", () => {
      describe("Asian Pacific Islander Breakout is disabled", () => {
        it("should only use combined Asian Pacific Islander diversity data from parent documents", async () => {
          const page = {
            from: faker.datatype.number(),
            size: faker.datatype.number()
          };

          const hit1PatientDiversity = {
            sex: [],
            races_eng: [
              {
                race: "Asian Pacific Islander",
                count: faker.datatype.number(),
                rank: faker.datatype.number()
              },
              {
                race: faker.datatype.string(),
                count: faker.datatype.number(),
                rank: faker.datatype.number()
              }
            ],
            age: []
          };

          const asianPacificIslanderRatio = faker.datatype.float({
            min: 0.01,
            max: 1
          });
          const asianRatio = faker.datatype.float({
            max: asianPacificIslanderRatio - 0.01
          });
          const pacificIslanderRatio = asianPacificIslanderRatio - asianRatio;

          const hit = generateMockHit({
            patientsDiversity: [hit1PatientDiversity],
            patientsDiversityRatio: {
              asianPacificIslander: asianPacificIslanderRatio,
              asian: asianRatio,
              pacificIslander: pacificIslanderRatio
            },
            patientsDiversityCount: 0
          });

          const totalPatientCountByRace =
            hit1PatientDiversity.races_eng[0].count +
            hit1PatientDiversity.races_eng[1].count;

          const expectedAsianPacificIslanderPercentage = Math.round(
            (hit1PatientDiversity.races_eng[0].count /
              totalPatientCountByRace) *
              100
          );

          const hits: SearchHitsMetadata<HCPDocument> = {
            total: {
              value: faker.datatype.number()
            } as SearchTotalHits,
            hits: [hit]
          };

          const synonyms = generateMockSynonyms();
          const suppliedFilters = generateFilters();
          const queryIntent = generateMockQueryIntents();

          const affiliationAdapterService = createMockInstance(
            AffiliationAdapterService
          );

          const keywordSearchResponseAdapterService =
            new KeywordSearchResponseAdapterService(affiliationAdapterService);

          const adaptedResponse =
            await keywordSearchResponseAdapterService.adapt(
              page,
              ENGLISH,
              hits,
              undefined,
              synonyms,
              suppliedFilters,
              queryIntent,
              [],
              generateKeywordSearchFeatureFlags({
                enableAsianPacificIslanderDiversityBreakOut: false
              }),
              [],
              []
            );

          expect(adaptedResponse.results[0].patientsDiversity).toEqual({
            age: [],
            races: [
              {
                count: hit1PatientDiversity.races_eng[0].count,
                race: "Asian Pacific Islander",
                rank: hit1PatientDiversity.races_eng[0].rank,
                percent: expectedAsianPacificIslanderPercentage
              },
              {
                count: hit1PatientDiversity.races_eng[1].count,
                race: hit1PatientDiversity.races_eng[1].race,
                rank: hit1PatientDiversity.races_eng[1].rank,
                percent: expect.any(Number)
              }
            ],
            sex: []
          });
        });

        it("should only include Asian Pacific Islander diversity data from child documents", async () => {
          const page = {
            from: faker.datatype.number(),
            size: faker.datatype.number()
          };

          const hit1PatientDiversity = {
            sex: [],
            races_eng: [
              {
                race: "Asian Pacific Islander",
                count: faker.datatype.number(),
                rank: faker.datatype.number()
              },
              {
                race: faker.datatype.string(),
                count: faker.datatype.number(),
                rank: faker.datatype.number()
              }
            ],
            age: []
          };

          const asianPacificIslanderRatio = faker.datatype.float({
            min: 0.01,
            max: 1
          });
          const asianRatio = faker.datatype.float({
            max: asianPacificIslanderRatio - 0.01
          });
          const pacificIslanderRatio = asianPacificIslanderRatio - asianRatio;

          const hit = generateMockHit({
            patientsDiversity: [hit1PatientDiversity],
            patientsDiversityRatio: {
              asianPacificIslander: asianPacificIslanderRatio,
              asian: asianRatio,
              pacificIslander: pacificIslanderRatio
            },
            patientsDiversityCount: 0
          });

          const patientDiversityDistribution: Dictionary<{
            race: DocCountBucket[];
            gender: DocCountBucket[];
            age: DocCountBucket[];
          }> = {
            [hit._id]: {
              race: [
                {
                  key: "Asian Pacific Islander",
                  doc_count: faker.datatype.number()
                },
                {
                  key: faker.datatype.string(),
                  doc_count: faker.datatype.number()
                }
              ],
              gender: [],
              age: []
            }
          };

          const totalPatientCountByRace =
            patientDiversityDistribution[hit._id].race[0].doc_count +
            patientDiversityDistribution[hit._id].race[1].doc_count;

          const expectedAsianPacificIslanderPercentage = Math.round(
            (patientDiversityDistribution[hit._id].race[0].doc_count /
              totalPatientCountByRace) *
              100
          );

          const hits: SearchHitsMetadata<HCPDocument> = {
            total: {
              value: faker.datatype.number()
            } as SearchTotalHits,
            hits: [hit]
          };

          const synonyms = generateMockSynonyms();
          const suppliedFilters = generateFilters();
          const queryIntent = generateMockQueryIntents();

          const affiliationAdapterService = createMockInstance(
            AffiliationAdapterService
          );

          const keywordSearchResponseAdapterService =
            new KeywordSearchResponseAdapterService(affiliationAdapterService);

          const adaptedResponse =
            await keywordSearchResponseAdapterService.adapt(
              page,
              ENGLISH,
              hits,
              undefined,
              synonyms,
              suppliedFilters,
              queryIntent,
              [],
              generateKeywordSearchFeatureFlags({
                enableAsianPacificIslanderDiversityBreakOut: false
              }),
              [],
              [],
              undefined,
              undefined,
              undefined,
              patientDiversityDistribution
            );

          expect(adaptedResponse.results[0].patientsDiversity).toEqual({
            age: [],
            races: [
              {
                count: patientDiversityDistribution[hit._id].race[0].doc_count,
                race: "Asian Pacific Islander",
                rank: 0,
                percent: expectedAsianPacificIslanderPercentage
              },
              {
                count: patientDiversityDistribution[hit._id].race[1].doc_count,
                race: patientDiversityDistribution[hit._id].race[1].key,
                rank: 1,
                percent: expect.any(Number)
              }
            ],
            sex: []
          });
        });

        it("should filter out Asian and Pacific Islander categories and exclude from calculations when using parent document data", async () => {
          const page = {
            from: faker.datatype.number(),
            size: faker.datatype.number()
          };

          const hit1PatientDiversity = {
            sex: [],
            races_eng: [
              {
                race: "Asian Pacific Islander",
                count: faker.datatype.number(),
                rank: faker.datatype.number()
              },
              {
                race: "Asian",
                count: faker.datatype.number(),
                rank: faker.datatype.number()
              },
              {
                race: "Pacific Islander",
                count: faker.datatype.number(),
                rank: faker.datatype.number()
              }
            ],
            age: []
          };

          const asianPacificIslanderRatio = faker.datatype.float({
            min: 0.01,
            max: 1
          });
          const asianRatio = faker.datatype.float({
            max: asianPacificIslanderRatio - 0.01
          });
          const pacificIslanderRatio = asianPacificIslanderRatio - asianRatio;

          const hit = generateMockHit({
            patientsDiversity: [hit1PatientDiversity],
            patientsDiversityRatio: {
              asianPacificIslander: asianPacificIslanderRatio,
              asian: asianRatio,
              pacificIslander: pacificIslanderRatio
            },
            patientsDiversityCount: 0
          });

          const totalPatientCountByRace =
            hit1PatientDiversity.races_eng[0].count;

          const expectedAsianPacificIslanderPercentage = Math.round(
            (hit1PatientDiversity.races_eng[0].count /
              totalPatientCountByRace) *
              100
          );

          const hits: SearchHitsMetadata<HCPDocument> = {
            total: {
              value: faker.datatype.number()
            } as SearchTotalHits,
            hits: [hit]
          };

          const synonyms = generateMockSynonyms();
          const suppliedFilters = generateFilters();
          const queryIntent = generateMockQueryIntents();

          const affiliationAdapterService = createMockInstance(
            AffiliationAdapterService
          );

          const keywordSearchResponseAdapterService =
            new KeywordSearchResponseAdapterService(affiliationAdapterService);

          const adaptedResponse =
            await keywordSearchResponseAdapterService.adapt(
              page,
              ENGLISH,
              hits,
              undefined,
              synonyms,
              suppliedFilters,
              queryIntent,
              [],
              generateKeywordSearchFeatureFlags({
                enableAsianPacificIslanderDiversityBreakOut: false
              }),
              [],
              []
            );

          expect(adaptedResponse.results[0].patientsDiversity).toEqual({
            age: [],
            races: [
              {
                count: hit1PatientDiversity.races_eng[0].count,
                race: "Asian Pacific Islander",
                rank: hit1PatientDiversity.races_eng[0].rank,
                percent: expectedAsianPacificIslanderPercentage
              }
            ],
            sex: []
          });
        });
      });

      describe("Asian Pacific Islander Breakout is enabled", () => {
        it("should calculate Asian and Pacific Islander percentages using the combined Asian Pacific Islander diversity data from parent documents", async () => {
          const page = {
            from: faker.datatype.number(),
            size: faker.datatype.number()
          };

          const hit1PatientDiversity = {
            sex: [],
            races_eng: [
              {
                race: "Asian Pacific Islander",
                count: faker.datatype.number(),
                rank: faker.datatype.number()
              },
              {
                race: faker.datatype.string(),
                count: faker.datatype.number(),
                rank: faker.datatype.number()
              }
            ],
            age: []
          };

          const asianPacificIslanderRatio = faker.datatype.float({
            min: 0.01,
            max: 1
          });
          const asianRatio = faker.datatype.float({
            max: asianPacificIslanderRatio - 0.01
          });
          const pacificIslanderRatio = asianPacificIslanderRatio - asianRatio;

          const hit = generateMockHit({
            patientsDiversity: [hit1PatientDiversity],
            patientsDiversityRatio: {
              asianPacificIslander: asianPacificIslanderRatio,
              asian: asianRatio,
              pacificIslander: pacificIslanderRatio
            },
            patientsDiversityCount: 0
          });

          const totalPatientCountByRace =
            hit1PatientDiversity.races_eng[0].count +
            hit1PatientDiversity.races_eng[1].count;

          const expectedAsianPacificIslanderPercentage = Math.round(
            (hit1PatientDiversity.races_eng[0].count /
              totalPatientCountByRace) *
              100
          );

          const hits: SearchHitsMetadata<HCPDocument> = {
            total: {
              value: faker.datatype.number()
            } as SearchTotalHits,
            hits: [hit]
          };

          const synonyms = generateMockSynonyms();
          const suppliedFilters = generateFilters();
          const queryIntent = generateMockQueryIntents();

          const affiliationAdapterService = createMockInstance(
            AffiliationAdapterService
          );

          const keywordSearchResponseAdapterService =
            new KeywordSearchResponseAdapterService(affiliationAdapterService);

          const adaptedResponse =
            await keywordSearchResponseAdapterService.adapt(
              page,
              ENGLISH,
              hits,
              undefined,
              synonyms,
              suppliedFilters,
              queryIntent,
              [],
              generateKeywordSearchFeatureFlags({
                enableAsianPacificIslanderDiversityBreakOut: true
              }),
              [],
              []
            );

          const percentOfAsianOverCombined =
            asianRatio / asianPacificIslanderRatio;
          const percentOfPacificIslanderOverCombined =
            pacificIslanderRatio / asianPacificIslanderRatio;

          expect(adaptedResponse.results[0].patientsDiversity).toEqual({
            age: [],
            races: expect.arrayContaining([
              {
                count: hit1PatientDiversity.races_eng[0].count,
                race: "Asian Pacific Islander",
                rank: hit1PatientDiversity.races_eng[0].rank,
                percent: expectedAsianPacificIslanderPercentage
              },
              {
                count:
                  hit1PatientDiversity.races_eng[0].count *
                  percentOfAsianOverCombined,
                race: "Asian",
                rank: hit1PatientDiversity.races_eng[0].rank,
                percent:
                  expectedAsianPacificIslanderPercentage *
                  percentOfAsianOverCombined
              },
              {
                count:
                  hit1PatientDiversity.races_eng[0].count *
                  percentOfPacificIslanderOverCombined,
                race: "Pacific Islander",
                rank: hit1PatientDiversity.races_eng[0].rank,
                percent:
                  expectedAsianPacificIslanderPercentage *
                  percentOfPacificIslanderOverCombined
              }
            ]),
            sex: []
          });
        });

        it("should not calculate Asian and Pacific Islander percentages when Asian Pacific Islander diversity ratio is 0 in parent documents", async () => {
          const page = {
            from: faker.datatype.number(),
            size: faker.datatype.number()
          };

          const hit1PatientDiversity = {
            sex: [],
            races_eng: [
              {
                race: "Asian Pacific Islander",
                count: 0,
                rank: faker.datatype.number()
              },
              {
                race: faker.datatype.string(),
                count: faker.datatype.number(),
                rank: faker.datatype.number()
              }
            ],
            age: []
          };

          const hit = generateMockHit({
            patientsDiversity: [hit1PatientDiversity],
            patientsDiversityRatio: {
              asianPacificIslander: 0,
              asian: 0,
              pacificIslander: 0
            },
            patientsDiversityCount: 0
          });

          const totalPatientCountByRace =
            hit1PatientDiversity.races_eng[0].count +
            hit1PatientDiversity.races_eng[1].count;

          const expectedAsianPacificIslanderPercentage = Math.round(
            (hit1PatientDiversity.races_eng[0].count /
              totalPatientCountByRace) *
              100
          );

          const hits: SearchHitsMetadata<HCPDocument> = {
            total: {
              value: faker.datatype.number()
            } as SearchTotalHits,
            hits: [hit]
          };

          const synonyms = generateMockSynonyms();
          const suppliedFilters = generateFilters();
          const queryIntent = generateMockQueryIntents();

          const affiliationAdapterService = createMockInstance(
            AffiliationAdapterService
          );

          const keywordSearchResponseAdapterService =
            new KeywordSearchResponseAdapterService(affiliationAdapterService);

          const adaptedResponse =
            await keywordSearchResponseAdapterService.adapt(
              page,
              ENGLISH,
              hits,
              undefined,
              synonyms,
              suppliedFilters,
              queryIntent,
              [],
              generateKeywordSearchFeatureFlags({
                enableAsianPacificIslanderDiversityBreakOut: true
              }),
              [],
              []
            );

          expect(adaptedResponse.results[0].patientsDiversity).toEqual({
            age: [],
            races: [
              {
                count: hit1PatientDiversity.races_eng[0].count,
                race: "Asian Pacific Islander",
                rank: hit1PatientDiversity.races_eng[0].rank,
                percent: expectedAsianPacificIslanderPercentage
              },
              {
                count: hit1PatientDiversity.races_eng[1].count,
                race: hit1PatientDiversity.races_eng[1].race,
                rank: hit1PatientDiversity.races_eng[1].rank,
                percent: expect.any(Number)
              }
            ],
            sex: []
          });
        });

        it("should not calculate Asian and Pacific Islander percentages when Asian Pacific Islander diversity ratio is '0.0' in parent documents", async () => {
          const page = {
            from: faker.datatype.number(),
            size: faker.datatype.number()
          };

          const hit1PatientDiversity = {
            sex: [],
            races_eng: [
              {
                race: "Asian Pacific Islander",
                count: 0,
                rank: faker.datatype.number()
              },
              {
                race: faker.datatype.string(),
                count: faker.datatype.number(),
                rank: faker.datatype.number()
              }
            ],
            age: []
          };

          const hit = generateMockHit({
            patientsDiversity: [hit1PatientDiversity],
            patientsDiversityRatio: {
              //@ts-ignore
              asianPacificIslander: "0.0",
              asian: 0,
              pacificIslander: 0
            },
            patientsDiversityCount: 0
          });

          const totalPatientCountByRace =
            hit1PatientDiversity.races_eng[0].count +
            hit1PatientDiversity.races_eng[1].count;

          const expectedAsianPacificIslanderPercentage = Math.round(
            (hit1PatientDiversity.races_eng[0].count /
              totalPatientCountByRace) *
              100
          );

          const hits: SearchHitsMetadata<HCPDocument> = {
            total: {
              value: faker.datatype.number()
            } as SearchTotalHits,
            hits: [hit]
          };

          const synonyms = generateMockSynonyms();
          const suppliedFilters = generateFilters();
          const queryIntent = generateMockQueryIntents();

          const affiliationAdapterService = createMockInstance(
            AffiliationAdapterService
          );

          const keywordSearchResponseAdapterService =
            new KeywordSearchResponseAdapterService(affiliationAdapterService);

          const adaptedResponse =
            await keywordSearchResponseAdapterService.adapt(
              page,
              ENGLISH,
              hits,
              undefined,
              synonyms,
              suppliedFilters,
              queryIntent,
              [],
              generateKeywordSearchFeatureFlags({
                enableAsianPacificIslanderDiversityBreakOut: true
              }),
              [],
              []
            );

          expect(adaptedResponse.results[0].patientsDiversity).toEqual({
            age: [],
            races: [
              {
                count: hit1PatientDiversity.races_eng[0].count,
                race: "Asian Pacific Islander",
                rank: hit1PatientDiversity.races_eng[0].rank,
                percent: expectedAsianPacificIslanderPercentage
              },
              {
                count: hit1PatientDiversity.races_eng[1].count,
                race: hit1PatientDiversity.races_eng[1].race,
                rank: hit1PatientDiversity.races_eng[1].rank,
                percent: expect.any(Number)
              }
            ],
            sex: []
          });
        });

        it("should calculate Asian and Pacific Islander percentages using the combined Asian Pacific Islander diversity data from child documents", async () => {
          const page = {
            from: faker.datatype.number(),
            size: faker.datatype.number()
          };

          const hit1PatientDiversity = {
            sex: [],
            races_eng: [
              {
                race: "Asian Pacific Islander",
                count: faker.datatype.number(),
                rank: faker.datatype.number()
              },
              {
                race: faker.datatype.string(),
                count: faker.datatype.number(),
                rank: faker.datatype.number()
              }
            ],
            age: []
          };

          const asianPacificIslanderRatio = faker.datatype.float({
            min: 0.01,
            max: 1
          });
          const asianRatio = faker.datatype.float({
            max: asianPacificIslanderRatio - 0.01
          });
          const pacificIslanderRatio = asianPacificIslanderRatio - asianRatio;

          const hit = generateMockHit({
            patientsDiversity: [hit1PatientDiversity],
            patientsDiversityRatio: {
              asianPacificIslander: asianPacificIslanderRatio,
              asian: asianRatio,
              pacificIslander: pacificIslanderRatio
            },
            patientsDiversityCount: 0
          });

          const patientDiversityDistribution: Dictionary<{
            race: DocCountBucket[];
            gender: DocCountBucket[];
            age: DocCountBucket[];
          }> = {
            [hit._id]: {
              race: [
                {
                  key: "Asian Pacific Islander",
                  doc_count: faker.datatype.number()
                },
                {
                  key: faker.datatype.string(),
                  doc_count: faker.datatype.number()
                }
              ],
              gender: [],
              age: []
            }
          };

          const totalPatientCountByRace =
            patientDiversityDistribution[hit._id].race[0].doc_count +
            patientDiversityDistribution[hit._id].race[1].doc_count;

          const expectedAsianPacificIslanderPercentage = Math.round(
            (patientDiversityDistribution[hit._id].race[0].doc_count /
              totalPatientCountByRace) *
              100
          );

          const hits: SearchHitsMetadata<HCPDocument> = {
            total: {
              value: faker.datatype.number()
            } as SearchTotalHits,
            hits: [hit]
          };

          const synonyms = generateMockSynonyms();
          const suppliedFilters = generateFilters();
          const queryIntent = generateMockQueryIntents();

          const affiliationAdapterService = createMockInstance(
            AffiliationAdapterService
          );

          const keywordSearchResponseAdapterService =
            new KeywordSearchResponseAdapterService(affiliationAdapterService);

          const adaptedResponse =
            await keywordSearchResponseAdapterService.adapt(
              page,
              ENGLISH,
              hits,
              undefined,
              synonyms,
              suppliedFilters,
              queryIntent,
              [],
              generateKeywordSearchFeatureFlags({
                enableAsianPacificIslanderDiversityBreakOut: true
              }),
              [],
              [],
              undefined,
              undefined,
              undefined,
              patientDiversityDistribution
            );

          const percentOfAsianOverCombined =
            asianRatio / asianPacificIslanderRatio;
          const percentOfPacificIslanderOverCombined =
            pacificIslanderRatio / asianPacificIslanderRatio;

          expect(adaptedResponse.results[0].patientsDiversity).toEqual({
            age: [],
            races: expect.arrayContaining([
              {
                count: patientDiversityDistribution[hit._id].race[0].doc_count,
                race: "Asian Pacific Islander",
                rank: 0,
                percent: expectedAsianPacificIslanderPercentage
              },
              {
                count:
                  patientDiversityDistribution[hit._id].race[0].doc_count *
                  percentOfAsianOverCombined,
                race: "Asian",
                rank: 0,
                percent:
                  expectedAsianPacificIslanderPercentage *
                  percentOfAsianOverCombined
              },
              {
                count:
                  patientDiversityDistribution[hit._id].race[0].doc_count *
                  percentOfPacificIslanderOverCombined,
                race: "Pacific Islander",
                rank: 0,
                percent:
                  expectedAsianPacificIslanderPercentage *
                  percentOfPacificIslanderOverCombined
              }
            ]),
            sex: []
          });
        });

        it("should not calculate Asian and Pacific Islander percentages using child documents when Asian Pacific Islander diversity ratio is 0 in parent documents", async () => {
          const page = {
            from: faker.datatype.number(),
            size: faker.datatype.number()
          };

          const hit1PatientDiversity = {
            sex: [],
            races_eng: [
              {
                race: "Asian Pacific Islander",
                count: 0,
                rank: faker.datatype.number()
              },
              {
                race: faker.datatype.string(),
                count: faker.datatype.number(),
                rank: faker.datatype.number()
              }
            ],
            age: []
          };

          const hit = generateMockHit({
            patientsDiversity: [hit1PatientDiversity],
            patientsDiversityRatio: {
              asianPacificIslander: 0,
              asian: 0,
              pacificIslander: 0
            },
            patientsDiversityCount: 0
          });

          const patientDiversityDistribution: Dictionary<{
            race: DocCountBucket[];
            gender: DocCountBucket[];
            age: DocCountBucket[];
          }> = {
            [hit._id]: {
              race: [
                {
                  key: "Asian Pacific Islander",
                  doc_count: 0
                },
                {
                  key: faker.datatype.string(),
                  doc_count: faker.datatype.number()
                }
              ],
              gender: [],
              age: []
            }
          };

          const totalPatientCountByRace =
            patientDiversityDistribution[hit._id].race[0].doc_count +
            patientDiversityDistribution[hit._id].race[1].doc_count;

          const expectedAsianPacificIslanderPercentage = Math.round(
            (patientDiversityDistribution[hit._id].race[0].doc_count /
              totalPatientCountByRace) *
              100
          );

          const hits: SearchHitsMetadata<HCPDocument> = {
            total: {
              value: faker.datatype.number()
            } as SearchTotalHits,
            hits: [hit]
          };

          const synonyms = generateMockSynonyms();
          const suppliedFilters = generateFilters();
          const queryIntent = generateMockQueryIntents();

          const affiliationAdapterService = createMockInstance(
            AffiliationAdapterService
          );

          const keywordSearchResponseAdapterService =
            new KeywordSearchResponseAdapterService(affiliationAdapterService);

          const adaptedResponse =
            await keywordSearchResponseAdapterService.adapt(
              page,
              ENGLISH,
              hits,
              undefined,
              synonyms,
              suppliedFilters,
              queryIntent,
              [],
              generateKeywordSearchFeatureFlags({
                enableAsianPacificIslanderDiversityBreakOut: true
              }),
              [],
              [],
              undefined,
              undefined,
              undefined,
              patientDiversityDistribution
            );

          expect(adaptedResponse.results[0].patientsDiversity).toEqual({
            age: [],
            races: [
              {
                count: patientDiversityDistribution[hit._id].race[0].doc_count,
                race: "Asian Pacific Islander",
                rank: 0,
                percent: expectedAsianPacificIslanderPercentage
              },
              {
                count: patientDiversityDistribution[hit._id].race[1].doc_count,
                race: patientDiversityDistribution[hit._id].race[1].key,
                rank: 1,
                percent: expect.any(Number)
              }
            ],
            sex: []
          });
        });

        it("should not calculate Asian and Pacific Islander percentages using child documents when Asian Pacific Islander diversity ratio is '0.0' in parent documents", async () => {
          const page = {
            from: faker.datatype.number(),
            size: faker.datatype.number()
          };

          const hit1PatientDiversity = {
            sex: [],
            races_eng: [
              {
                race: "Asian Pacific Islander",
                count: 0,
                rank: faker.datatype.number()
              },
              {
                race: faker.datatype.string(),
                count: faker.datatype.number(),
                rank: faker.datatype.number()
              }
            ],
            age: []
          };

          const hit = generateMockHit({
            patientsDiversity: [hit1PatientDiversity],
            patientsDiversityRatio: {
              //@ts-ignore
              asianPacificIslander: "0.0",
              asian: 0,
              pacificIslander: 0
            },
            patientsDiversityCount: 0
          });

          const patientDiversityDistribution: Dictionary<{
            race: DocCountBucket[];
            gender: DocCountBucket[];
            age: DocCountBucket[];
          }> = {
            [hit._id]: {
              race: [
                {
                  key: "Asian Pacific Islander",
                  doc_count: 0
                },
                {
                  key: faker.datatype.string(),
                  doc_count: faker.datatype.number()
                }
              ],
              gender: [],
              age: []
            }
          };

          const totalPatientCountByRace =
            patientDiversityDistribution[hit._id].race[0].doc_count +
            patientDiversityDistribution[hit._id].race[1].doc_count;

          const expectedAsianPacificIslanderPercentage = Math.round(
            (patientDiversityDistribution[hit._id].race[0].doc_count /
              totalPatientCountByRace) *
              100
          );

          const hits: SearchHitsMetadata<HCPDocument> = {
            total: {
              value: faker.datatype.number()
            } as SearchTotalHits,
            hits: [hit]
          };

          const synonyms = generateMockSynonyms();
          const suppliedFilters = generateFilters();
          const queryIntent = generateMockQueryIntents();

          const affiliationAdapterService = createMockInstance(
            AffiliationAdapterService
          );

          const keywordSearchResponseAdapterService =
            new KeywordSearchResponseAdapterService(affiliationAdapterService);

          const adaptedResponse =
            await keywordSearchResponseAdapterService.adapt(
              page,
              ENGLISH,
              hits,
              undefined,
              synonyms,
              suppliedFilters,
              queryIntent,
              [],
              generateKeywordSearchFeatureFlags({
                enableAsianPacificIslanderDiversityBreakOut: true
              }),
              [],
              [],
              undefined,
              undefined,
              undefined,
              patientDiversityDistribution
            );

          expect(adaptedResponse.results[0].patientsDiversity).toEqual({
            age: [],
            races: [
              {
                count: patientDiversityDistribution[hit._id].race[0].doc_count,
                race: "Asian Pacific Islander",
                rank: 0,
                percent: expectedAsianPacificIslanderPercentage
              },
              {
                count: patientDiversityDistribution[hit._id].race[1].doc_count,
                race: patientDiversityDistribution[hit._id].race[1].key,
                rank: 1,
                percent: expect.any(Number)
              }
            ],
            sex: []
          });
        });
      });
    });
  });

  describe("provider diversity", () => {
    it("should return empty arrays when providerDiversity is null", async () => {
      const page = {
        from: faker.datatype.number(),
        size: faker.datatype.number()
      };

      const hit = generateMockHit({
        providerDiversity: null
      });

      const hits: SearchHitsMetadata<HCPDocument> = {
        total: {
          value: faker.datatype.number()
        } as SearchTotalHits,
        hits: [hit]
      };

      const synonyms = generateMockSynonyms();
      const suppliedFilters = generateFilters();
      const queryIntent = generateMockQueryIntents();

      const affiliationAdapterService = createMockInstance(
        AffiliationAdapterService
      );

      const keywordSearchResponseAdapterService =
        new KeywordSearchResponseAdapterService(affiliationAdapterService);

      const adaptedResponse = await keywordSearchResponseAdapterService.adapt(
        page,
        ENGLISH,
        hits,
        undefined,
        synonyms,
        suppliedFilters,
        queryIntent,
        [],
        generateKeywordSearchFeatureFlags(),

        [],
        []
      );

      expect(adaptedResponse.results[0].providerDiversity).toEqual({
        races: [],
        sex: [],
        languagesSpoken: []
      });
    });

    it("should return races, sex, and languagesSpoken as-is when populated", async () => {
      const page = {
        from: faker.datatype.number(),
        size: faker.datatype.number()
      };

      const providerDiversity = {
        sex: [faker.datatype.string(), faker.datatype.string()],
        races_eng: [faker.datatype.string(), faker.datatype.string()],
        languagesSpoken: [faker.datatype.string(), faker.datatype.string()]
      };

      const hit = generateMockHit({
        providerDiversity: [providerDiversity]
      });

      const hits: SearchHitsMetadata<HCPDocument> = {
        total: {
          value: faker.datatype.number()
        } as SearchTotalHits,
        hits: [hit]
      };

      const synonyms = generateMockSynonyms();
      const suppliedFilters = generateFilters();
      const queryIntent = generateMockQueryIntents();

      const affiliationAdapterService = createMockInstance(
        AffiliationAdapterService
      );

      const keywordSearchResponseAdapterService =
        new KeywordSearchResponseAdapterService(affiliationAdapterService);

      const adaptedResponse = await keywordSearchResponseAdapterService.adapt(
        page,
        ENGLISH,
        hits,
        undefined,
        synonyms,
        suppliedFilters,
        queryIntent,
        [],
        generateKeywordSearchFeatureFlags(),

        [],
        []
      );

      expect(adaptedResponse.results[0].providerDiversity).toEqual({
        languagesSpoken: providerDiversity.languagesSpoken,
        races: providerDiversity.races_eng,
        sex: providerDiversity.sex
      });
    });
  });

  describe("claims diagnosis and procedures", () => {
    it("should return claims diagnosis data only when filter getMatchedClaims is passed", async () => {
      const page = {
        from: faker.datatype.number(),
        size: faker.datatype.number()
      };

      const mockDiagnosisData: SearchInnerHitsResult = {
        hits: {
          total: {
            value: faker.datatype.number(),
            relation: "eq"
          },
          hits: [
            {
              _id: faker.datatype.string(),
              _index: faker.datatype.string(),
              fields: {
                "DRG_diagnoses.description_eng.keyword": [
                  faker.datatype.string()
                ],
                "DRG_diagnoses.codeScheme.keyword": [faker.datatype.string()],
                "DRG_diagnoses.internalCount": [faker.datatype.number()],
                "DRG_diagnoses.pctOfClaims": [faker.datatype.number()],
                "DRG_diagnoses.diagnosisCode_eng": [faker.datatype.string()]
              }
            },
            {
              _id: faker.datatype.string(),
              _index: faker.datatype.string(),
              fields: {
                "DRG_diagnoses.description_eng.keyword": [
                  faker.datatype.string()
                ],
                "DRG_diagnoses.codeScheme.keyword": [faker.datatype.string()],
                "DRG_diagnoses.internalCount": [faker.datatype.number()],
                "DRG_diagnoses.pctOfClaims": [faker.datatype.number()],
                "DRG_diagnoses.diagnosisCode_eng": [faker.datatype.string()]
              }
            }
          ]
        }
      };
      const DRG_diagnosesCount = faker.datatype.number();
      const DRG_proceduresCount = faker.datatype.number();

      const hit = generateMockHit(
        {
          DRG_diagnosesCount,
          DRG_proceduresCount
        },
        {
          diagnoses_collection: mockDiagnosisData
        }
      );

      const hits: SearchHitsMetadata<HCPDocument> = {
        total: {
          value: faker.datatype.number()
        } as SearchTotalHits,
        hits: [hit]
      };

      const synonyms = generateMockSynonyms();
      const suppliedFilters = generateFilters();
      suppliedFilters.getMatchedClaims = {
        value: true
      };
      const queryIntent = generateMockQueryIntents();

      const affiliationAdapterService = createMockInstance(
        AffiliationAdapterService
      );

      const keywordSearchResponseAdapterService =
        new KeywordSearchResponseAdapterService(affiliationAdapterService);

      const adaptedResponse = await keywordSearchResponseAdapterService.adapt(
        page,
        ENGLISH,
        hits,
        undefined,
        synonyms,
        suppliedFilters,
        queryIntent,
        [],
        generateKeywordSearchFeatureFlags(),

        [],
        []
      );

      expect(adaptedResponse.results[0].matchedDiagnoses).toEqual([
        {
          description:
            mockDiagnosisData.hits.hits[0].fields![
              "DRG_diagnoses.description_eng.keyword"
            ][0],
          diagnosisCode:
            mockDiagnosisData.hits.hits[0].fields![
              "DRG_diagnoses.diagnosisCode_eng"
            ][0],
          percentOfClaims:
            mockDiagnosisData.hits.hits[0].fields![
              "DRG_diagnoses.pctOfClaims"
            ][0],
          percentage:
            (mockDiagnosisData.hits.hits[0].fields![
              "DRG_diagnoses.internalCount"
            ][0] /
              DRG_diagnosesCount) *
            100,
          internalCount:
            mockDiagnosisData.hits.hits[0].fields![
              "DRG_diagnoses.internalCount"
            ][0],
          codeScheme:
            mockDiagnosisData.hits.hits[0].fields![
              "DRG_diagnoses.codeScheme.keyword"
            ][0],
          count:
            mockDiagnosisData.hits.hits[0].fields![
              "DRG_diagnoses.internalCount"
            ][0].toString()
        },
        {
          description:
            mockDiagnosisData.hits.hits[1].fields![
              "DRG_diagnoses.description_eng.keyword"
            ][0],
          diagnosisCode:
            mockDiagnosisData.hits.hits[1].fields![
              "DRG_diagnoses.diagnosisCode_eng"
            ][0],
          percentOfClaims:
            mockDiagnosisData.hits.hits[1].fields![
              "DRG_diagnoses.pctOfClaims"
            ][0],
          percentage:
            (mockDiagnosisData.hits.hits[1].fields![
              "DRG_diagnoses.internalCount"
            ][0] /
              DRG_diagnosesCount) *
            100,
          internalCount:
            mockDiagnosisData.hits.hits[1].fields![
              "DRG_diagnoses.internalCount"
            ][0],
          codeScheme:
            mockDiagnosisData.hits.hits[1].fields![
              "DRG_diagnoses.codeScheme.keyword"
            ][0],
          count:
            mockDiagnosisData.hits.hits[1].fields![
              "DRG_diagnoses.internalCount"
            ][0].toString()
        }
      ]);
    });

    it("should not return claims diagnosis data when filter getMatchedClaims is not passed", async () => {
      const page = {
        from: faker.datatype.number(),
        size: faker.datatype.number()
      };

      const hit = generateMockHit();

      const hits: SearchHitsMetadata<HCPDocument> = {
        total: {
          value: faker.datatype.number()
        } as SearchTotalHits,
        hits: [hit]
      };

      const synonyms = generateMockSynonyms();
      const suppliedFilters = generateFilters();
      const queryIntent = generateMockQueryIntents();

      const affiliationAdapterService = createMockInstance(
        AffiliationAdapterService
      );

      const keywordSearchResponseAdapterService =
        new KeywordSearchResponseAdapterService(affiliationAdapterService);

      const adaptedResponse = await keywordSearchResponseAdapterService.adapt(
        page,
        ENGLISH,
        hits,
        undefined,
        synonyms,
        suppliedFilters,
        queryIntent,
        [],
        generateKeywordSearchFeatureFlags(),

        [],
        []
      );
      expect(adaptedResponse.results[0].matchedDiagnoses).toEqual(undefined);
    });

    it("should return claims procedures data only when filter getMatchedClaims is passed", async () => {
      const page = {
        from: faker.datatype.number(),
        size: faker.datatype.number()
      };
      const DRG_proceduresCount = faker.datatype.number();
      const mockProceduresData: SearchInnerHitsResult = {
        hits: {
          total: {
            value: faker.datatype.number(),
            relation: "eq"
          },
          hits: [
            {
              _id: faker.datatype.string(),
              _index: faker.datatype.string(),
              fields: {
                "DRG_procedures.description_eng.keyword": [
                  faker.datatype.string()
                ],
                "DRG_procedures.codeScheme": [faker.datatype.string()],
                "DRG_procedures.internalCount": [faker.datatype.number()],
                "DRG_procedures.percentage": [faker.datatype.number()],
                "DRG_procedures.procedureCode_eng.keyword": [
                  faker.datatype.string()
                ]
              }
            },
            {
              _id: faker.datatype.string(),
              _index: faker.datatype.string(),
              fields: {
                "DRG_procedures.description_eng.keyword": [
                  faker.datatype.string()
                ],
                "DRG_procedures.codeScheme": [faker.datatype.string()],
                "DRG_procedures.internalCount": [faker.datatype.number()],
                "DRG_procedures.percentage": [faker.datatype.number()],
                "DRG_procedures.procedureCode_eng.keyword": [
                  faker.datatype.string()
                ]
              }
            }
          ]
        }
      };
      const hit = generateMockHit(
        {
          DRG_proceduresCount
        },
        {
          procedures_collection: mockProceduresData
        }
      );

      const hits: SearchHitsMetadata<HCPDocument> = {
        total: {
          value: faker.datatype.number()
        } as SearchTotalHits,
        hits: [hit]
      };

      const synonyms = generateMockSynonyms();
      const suppliedFilters = generateFilters();
      suppliedFilters.getMatchedClaims = {
        value: true
      };
      const queryIntent = generateMockQueryIntents();

      const affiliationAdapterService = createMockInstance(
        AffiliationAdapterService
      );

      const keywordSearchResponseAdapterService =
        new KeywordSearchResponseAdapterService(affiliationAdapterService);

      const adaptedResponse = await keywordSearchResponseAdapterService.adapt(
        page,
        ENGLISH,
        hits,
        undefined,
        synonyms,
        suppliedFilters,
        queryIntent,
        [],
        generateKeywordSearchFeatureFlags(),

        [],
        []
      );

      expect(adaptedResponse.results[0].matchedProcedures).toEqual([
        {
          description:
            mockProceduresData.hits.hits[0].fields![
              "DRG_procedures.description_eng.keyword"
            ][0],
          procedureCode:
            mockProceduresData.hits.hits[0].fields![
              "DRG_procedures.procedureCode_eng.keyword"
            ][0],
          percentOfClaims:
            mockProceduresData.hits.hits[0].fields![
              "DRG_procedures.percentage"
            ][0],
          percentage:
            (mockProceduresData.hits.hits[0].fields![
              "DRG_procedures.internalCount"
            ][0] /
              DRG_proceduresCount) *
            100,
          internalCount:
            mockProceduresData.hits.hits[0].fields![
              "DRG_procedures.internalCount"
            ][0],
          codeScheme:
            mockProceduresData.hits.hits[0].fields![
              "DRG_procedures.codeScheme"
            ][0],
          count:
            mockProceduresData.hits.hits[0].fields![
              "DRG_procedures.internalCount"
            ][0].toString()
        },
        {
          description:
            mockProceduresData.hits.hits[1].fields![
              "DRG_procedures.description_eng.keyword"
            ][0],
          procedureCode:
            mockProceduresData.hits.hits[1].fields![
              "DRG_procedures.procedureCode_eng.keyword"
            ][0],
          percentOfClaims:
            mockProceduresData.hits.hits[1].fields![
              "DRG_procedures.percentage"
            ][0],
          percentage:
            (mockProceduresData.hits.hits[1].fields![
              "DRG_procedures.internalCount"
            ][0] /
              DRG_proceduresCount) *
            100,
          internalCount:
            mockProceduresData.hits.hits[1].fields![
              "DRG_procedures.internalCount"
            ][0],
          codeScheme:
            mockProceduresData.hits.hits[1].fields![
              "DRG_procedures.codeScheme"
            ][0],
          count:
            mockProceduresData.hits.hits[1].fields![
              "DRG_procedures.internalCount"
            ][0].toString()
        }
      ]);
    });

    it("should not return claims procedures data when filter getMatchedClaims is not passed", async () => {
      const page = {
        from: faker.datatype.number(),
        size: faker.datatype.number()
      };

      const hit = generateMockHit();

      const hits: SearchHitsMetadata<HCPDocument> = {
        total: {
          value: faker.datatype.number()
        } as SearchTotalHits,
        hits: [hit]
      };

      const synonyms = generateMockSynonyms();
      const suppliedFilters = generateFilters();
      const queryIntent = generateMockQueryIntents();

      const affiliationAdapterService = createMockInstance(
        AffiliationAdapterService
      );

      const keywordSearchResponseAdapterService =
        new KeywordSearchResponseAdapterService(affiliationAdapterService);

      const adaptedResponse = await keywordSearchResponseAdapterService.adapt(
        page,
        ENGLISH,
        hits,
        undefined,
        synonyms,
        suppliedFilters,
        queryIntent,
        [],
        generateKeywordSearchFeatureFlags(),

        [],
        []
      );
      expect(adaptedResponse.results[0].matchedProcedures).toEqual(undefined);
    });

    it("should return empty array for diagnosis and procedures when inner_hits are undefined", async () => {
      const page = {
        from: faker.datatype.number(),
        size: faker.datatype.number()
      };
      const hit = generateMockHit();

      const hits: SearchHitsMetadata<HCPDocument> = {
        total: {
          value: faker.datatype.number()
        } as SearchTotalHits,
        hits: [hit]
      };

      hits.hits.forEach((hit) => (hit.inner_hits = undefined));

      const synonyms = generateMockSynonyms();
      const suppliedFilters = generateFilters();
      suppliedFilters.getMatchedClaims = {
        value: true
      };
      const queryIntent = generateMockQueryIntents();

      const affiliationAdapterService = createMockInstance(
        AffiliationAdapterService
      );

      const keywordSearchResponseAdapterService =
        new KeywordSearchResponseAdapterService(affiliationAdapterService);

      const adaptedResponse = await keywordSearchResponseAdapterService.adapt(
        page,
        ENGLISH,
        hits,
        undefined,
        synonyms,
        suppliedFilters,
        queryIntent,
        [],
        generateKeywordSearchFeatureFlags(),

        [],
        []
      );

      expect(adaptedResponse.results[0].matchedDiagnoses).toEqual([]);
      expect(adaptedResponse.results[0].matchedProcedures).toEqual([]);
    });

    it("should return claims diagnosis data from IE only when filter getMatchedClaims is passed", async () => {
      const page = {
        from: faker.datatype.number(),
        size: faker.datatype.number()
      };

      const mockDiagnosisData: SearchInnerHitsResult = {
        hits: {
          total: {
            value: faker.datatype.number(),
            relation: "eq"
          },
          hits: [
            {
              _id: faker.datatype.string(),
              _index: faker.datatype.string(),
              fields: {
                "DRG_diagnoses.description_eng.keyword": [
                  faker.datatype.string()
                ],
                "DRG_diagnoses.codeScheme.keyword": [faker.datatype.string()],
                "DRG_diagnoses.internalCount": [faker.datatype.number()],
                "DRG_diagnoses.pctOfClaims": [faker.datatype.number()],
                "DRG_diagnoses.diagnosisCode_eng": [faker.datatype.string()]
              }
            },
            {
              _id: faker.datatype.string(),
              _index: faker.datatype.string(),
              fields: {
                "DRG_diagnoses.description_eng.keyword": [
                  faker.datatype.string()
                ],
                "DRG_diagnoses.codeScheme.keyword": [faker.datatype.string()],
                "DRG_diagnoses.internalCount": [faker.datatype.number()],
                "DRG_diagnoses.pctOfClaims": [faker.datatype.number()],
                "DRG_diagnoses.diagnosisCode_eng": [faker.datatype.string()]
              }
            }
          ]
        }
      };
      const DRG_diagnosesCount = faker.datatype.number();
      const DRG_proceduresCount = faker.datatype.number();
      const totalPatientDocs = faker.datatype.number();

      const hit = generateMockHit(
        {
          DRG_diagnosesCount,
          DRG_proceduresCount,
          totalPatientDocs
        },
        {
          diagnoses_collection: mockDiagnosisData
        }
      );

      const hits: SearchHitsMetadata<HCPDocument> = {
        total: {
          value: faker.datatype.number()
        } as SearchTotalHits,
        hits: [hit]
      };

      const synonyms = generateMockSynonyms();
      const suppliedFilters = generateFilters();
      suppliedFilters.getMatchedClaims = {
        value: true
      };
      const queryIntent = generateMockQueryIntents();

      const affiliationAdapterService = createMockInstance(
        AffiliationAdapterService
      );

      const keywordSearchResponseAdapterService =
        new KeywordSearchResponseAdapterService(affiliationAdapterService);
      const peopleID = hit._id;
      const diagnosisCodeIE = faker.datatype.string();
      const diagnosisCountIE = faker.datatype.number();
      const diagnosisCodeScheme = faker.random.word();
      const diagnosisCodeDesc = faker.datatype.string();
      const claimsCountMap: MatchedClaimCountsFromIEForExports = {
        diagnosesCountMap: {
          [peopleID]: {
            [diagnosisCodeIE]: {
              count: diagnosisCountIE,
              scheme: diagnosisCodeScheme,
              description: diagnosisCodeDesc
            }
          }
        },
        proceduresCountMap: {}
      };
      const adaptedResponse = await keywordSearchResponseAdapterService.adapt(
        page,
        ENGLISH,
        hits,
        undefined,
        synonyms,
        suppliedFilters,
        queryIntent,
        [],
        generateKeywordSearchFeatureFlags(),

        [],
        [],
        undefined,
        undefined,
        undefined,
        undefined,
        undefined,
        undefined,
        undefined,
        claimsCountMap
      );

      expect(adaptedResponse.results[0].matchedDiagnoses).toEqual([
        {
          diagnosisCode: diagnosisCodeIE,
          percentage: (diagnosisCountIE / totalPatientDocs) * 100,
          internalCount: diagnosisCountIE,
          count: diagnosisCountIE.toString(),
          codeScheme: diagnosisCodeScheme,
          percentOfClaims: 0,
          description: diagnosisCodeDesc
        }
      ]);
    });

    it("should return claims procedure data from IE only when filter getMatchedClaims is passed", async () => {
      const page = {
        from: faker.datatype.number(),
        size: faker.datatype.number()
      };
      const DRG_proceduresCount = faker.datatype.number();
      const mockProceduresData: SearchInnerHitsResult = {
        hits: {
          total: {
            value: faker.datatype.number(),
            relation: "eq"
          },
          hits: [
            {
              _id: faker.datatype.string(),
              _index: faker.datatype.string(),
              fields: {
                "DRG_procedures.description_eng.keyword": [
                  faker.datatype.string()
                ],
                "DRG_procedures.codeScheme": [faker.datatype.string()],
                "DRG_procedures.internalCount": [faker.datatype.number()],
                "DRG_procedures.percentage": [faker.datatype.number()],
                "DRG_procedures.procedureCode_eng.keyword": [
                  faker.datatype.string()
                ]
              }
            },
            {
              _id: faker.datatype.string(),
              _index: faker.datatype.string(),
              fields: {
                "DRG_procedures.description_eng.keyword": [
                  faker.datatype.string()
                ],
                "DRG_procedures.codeScheme": [faker.datatype.string()],
                "DRG_procedures.internalCount": [faker.datatype.number()],
                "DRG_procedures.percentage": [faker.datatype.number()],
                "DRG_procedures.procedureCode_eng.keyword": [
                  faker.datatype.string()
                ]
              }
            }
          ]
        }
      };
      const totalPatientDocs = faker.datatype.number();
      const hit = generateMockHit(
        {
          DRG_proceduresCount,
          totalPatientDocs
        },
        {
          procedures_collection: mockProceduresData
        }
      );

      const hits: SearchHitsMetadata<HCPDocument> = {
        total: {
          value: faker.datatype.number()
        } as SearchTotalHits,
        hits: [hit]
      };

      const synonyms = generateMockSynonyms();
      const suppliedFilters = generateFilters();
      suppliedFilters.getMatchedClaims = {
        value: true
      };
      const queryIntent = generateMockQueryIntents();

      const affiliationAdapterService = createMockInstance(
        AffiliationAdapterService
      );

      const keywordSearchResponseAdapterService =
        new KeywordSearchResponseAdapterService(affiliationAdapterService);
      const peopleID = hit._id;
      const procedureCodeIE = faker.datatype.string();
      const procedureCountIE = faker.datatype.number();
      const procedureCodeScheme = faker.random.word();
      const procedureCodeDesc = faker.datatype.string();
      const claimsCountMap: MatchedClaimCountsFromIEForExports = {
        diagnosesCountMap: {},
        proceduresCountMap: {
          [peopleID]: {
            [procedureCodeIE]: {
              count: procedureCountIE,
              scheme: procedureCodeScheme,
              description: procedureCodeDesc
            }
          }
        }
      };
      const adaptedResponse = await keywordSearchResponseAdapterService.adapt(
        page,
        ENGLISH,
        hits,
        undefined,
        synonyms,
        suppliedFilters,
        queryIntent,
        [],
        generateKeywordSearchFeatureFlags(),

        [],
        [],
        undefined,
        undefined,
        undefined,
        undefined,
        undefined,
        undefined,
        undefined,
        claimsCountMap
      );

      expect(adaptedResponse.results[0].matchedProcedures).toEqual([
        {
          procedureCode: procedureCodeIE,
          percentage: (procedureCountIE / totalPatientDocs) * 100,
          internalCount: procedureCountIE,
          count: procedureCountIE.toString(),
          codeScheme: procedureCodeScheme,
          percentOfClaims: 0,
          description: procedureCodeDesc
        }
      ]);
    });
  });

  describe("affiliation highlights", () => {
    it("should combine highlights across different affiliations inner_hits", async () => {
      const page = {
        from: faker.datatype.number(),
        size: faker.datatype.number()
      };

      const affiliationId1 = faker.datatype.string();
      const institutionId1 = faker.datatype.string();
      const city1 = faker.datatype.string();
      const region1 = faker.datatype.string();
      const country1 = faker.datatype.string();

      const affiliationId2 = faker.datatype.string();
      const institutionId2 = faker.datatype.string();
      const city2 = faker.datatype.string();
      const region2 = faker.datatype.string();
      const country2 = faker.datatype.string();

      const hit = generateMockHit(
        {},
        {
          affiliations_0: {
            hits: {
              total: {
                value: 1,
                relation: "eq"
              },
              hits: [
                {
                  _index: "people",
                  _id: faker.datatype.string(),
                  _nested: {
                    field: "affiliations",
                    offset: 0
                  },
                  _score: faker.datatype.number(),
                  fields: {
                    "affiliations.id": [affiliationId1],
                    "affiliations.institution.id": [institutionId1]
                  },
                  highlight: {
                    "affiliations.institution.address.city": [city1],
                    "affiliations.institution.address.region": [region1],
                    "affiliations.institution.address.country": [country1]
                  }
                }
              ]
            }
          },
          affiliations_1: {
            hits: {
              total: {
                value: 1,
                relation: "eq"
              },
              hits: [
                {
                  _index: "people",
                  _id: faker.datatype.string(),
                  _nested: {
                    field: "affiliations",
                    offset: 0
                  },
                  _score: faker.datatype.number(),
                  fields: {
                    "affiliations.id": [affiliationId2],
                    "affiliations.institution.id": [institutionId2]
                  },
                  highlight: {
                    "affiliations.institution.address.city": [city2],
                    "affiliations.institution.address.region": [region2],
                    "affiliations.institution.address.country": [country2]
                  }
                }
              ]
            }
          }
        }
      );

      const hits: SearchHitsMetadata<HCPDocument> = {
        total: {
          value: faker.datatype.number()
        } as SearchTotalHits,
        hits: [hit]
      };

      const synonyms = generateMockSynonyms();
      const suppliedFilters = generateFilters();
      const queryIntent = generateMockQueryIntents();

      const affiliationAdapterService = createMockInstance(
        AffiliationAdapterService
      );

      const keywordSearchResponseAdapterService =
        new KeywordSearchResponseAdapterService(affiliationAdapterService);

      const adaptedResponse = await keywordSearchResponseAdapterService.adapt(
        page,
        ENGLISH,
        hits,
        undefined,
        synonyms,
        suppliedFilters,
        queryIntent,
        [],
        generateKeywordSearchFeatureFlags(),

        [],
        []
      );
      expect(adaptedResponse).toEqual(
        expect.objectContaining({
          results: [
            expect.objectContaining({
              affiliationHighlights: [
                {
                  affiliationId: affiliationId1,
                  highlight: {
                    institution: {
                      institutionId: institutionId1,
                      address: {
                        city: city1,
                        region: region1,
                        country: country1
                      }
                    }
                  }
                },
                {
                  affiliationId: affiliationId2,
                  highlight: {
                    institution: {
                      institutionId: institutionId2,
                      address: {
                        city: city2,
                        region: region2,
                        country: country2
                      }
                    }
                  }
                }
              ]
            })
          ]
        })
      );
    });
  });

  describe("unique patient count", () => {
    it("should correctly fetch response from claims hits when unique patient count flag is on", async () => {
      const page = {
        from: faker.datatype.number(),
        size: faker.datatype.number()
      };

      const hit = generateMockHit(undefined, undefined, true);

      const hits: SearchHitsMetadata<HCPDocument> = {
        total: {
          value: faker.datatype.number()
        } as SearchTotalHits,
        hits: [hit]
      };

      const synonyms = generateMockSynonyms();
      const suppliedFilters = generateFilters();
      suppliedFilters.claims.showUniquePatients!.value = true;
      const queryIntent = generateMockQueryIntents();

      const affiliationAdapterService = createMockInstance(
        AffiliationAdapterService
      );

      const keywordSearchResponseAdapterService =
        new KeywordSearchResponseAdapterService(affiliationAdapterService);

      const adaptedResponse = await keywordSearchResponseAdapterService.adapt(
        page,
        ENGLISH,
        hits,
        undefined,
        synonyms,
        suppliedFilters,
        queryIntent,
        [],
        generateKeywordSearchFeatureFlags({
          enableUniquePatientCountForClaims: true
        }),

        [],
        []
      );
      const totalDiag =
        hits.hits[0].inner_hits?.diagnoses_amount.hits.hits.reduce(
          (sum: number, hit: any) => {
            const claimCount =
              hit.fields["DRG_diagnoses.internalUniqueCount"][0];
            return sum + claimCount;
          },
          0
        );
      const totalProc =
        hits.hits[0].inner_hits?.procedures_amount.hits.hits.reduce(
          (sum: number, hit: any) => {
            const claimCount =
              hit.fields["DRG_procedures.internalUniqueCount"][0];
            return sum + claimCount;
          },
          0
        );

      expect(adaptedResponse.results[0].scores.diagnoses.value).toEqual(
        totalDiag
      );
      expect(adaptedResponse.results[0].scores.procedures.value).toEqual(
        totalProc
      );

      expect(adaptedResponse.results[0].diagnosesCount).toEqual(
        hits.hits[0]._source?.totalPatientDocs
      );
    });
    it("should correctly fetch response from claims hits when unique patient count flag is on and disable procedure unique patient flag is on", async () => {
      const page = {
        from: faker.datatype.number(),
        size: faker.datatype.number()
      };

      const hit = generateMockHit(
        undefined,
        undefined,
        true,
        undefined,
        undefined,
        true
      );

      const hits: SearchHitsMetadata<HCPDocument> = {
        total: {
          value: faker.datatype.number()
        } as SearchTotalHits,
        hits: [hit]
      };

      const synonyms = generateMockSynonyms();
      const suppliedFilters = generateFilters();
      suppliedFilters.claims.showUniquePatients!.value = true;
      const queryIntent = generateMockQueryIntents();

      const affiliationAdapterService = createMockInstance(
        AffiliationAdapterService
      );

      const keywordSearchResponseAdapterService =
        new KeywordSearchResponseAdapterService(affiliationAdapterService);

      const adaptedResponse = await keywordSearchResponseAdapterService.adapt(
        page,
        ENGLISH,
        hits,
        undefined,
        synonyms,
        suppliedFilters,
        queryIntent,
        [],
        generateKeywordSearchFeatureFlags({
          enableUniquePatientCountForClaims: true,
          disableUniquePatientCountForOnlyProcedures: true
        }),

        [],
        []
      );
      const totalDiag =
        hits.hits[0].inner_hits?.diagnoses_amount.hits.hits.reduce(
          (sum: number, hit: any) => {
            const claimCount =
              hit.fields["DRG_diagnoses.internalUniqueCount"][0];
            return sum + claimCount;
          },
          0
        );
      const totalProc =
        hits.hits[0].inner_hits?.procedures_amount.hits.hits.reduce(
          (sum: number, hit: any) => {
            const claimCount = hit.fields["DRG_procedures.internalCount"][0];
            return sum + claimCount;
          },
          0
        );

      expect(adaptedResponse.results[0].scores.diagnoses.value).toEqual(
        totalDiag
      );
      expect(adaptedResponse.results[0].scores.procedures.value).toEqual(
        totalProc
      );
    });
    it("should return appropriate data when filter getMatchedClaims is passed along with timeframe filter and unique patient count flag is on for diagnoses", async () => {
      const page = {
        from: faker.datatype.number(),
        size: faker.datatype.number()
      };

      const mockDiagnosisData: SearchInnerHitsResult = {
        hits: {
          total: {
            value: faker.datatype.number(),
            relation: "eq"
          },
          hits: [
            {
              _id: faker.datatype.string(),
              _index: faker.datatype.string(),
              fields: {
                "DRG_diagnoses.description_eng.keyword": [
                  faker.datatype.string()
                ],
                "DRG_diagnoses.codeScheme.keyword": [faker.datatype.string()],
                "DRG_diagnoses.internalUniqueCount_2_year": [
                  faker.datatype.number()
                ],
                "DRG_diagnoses.pctOfUniqueClaims_2_year": [
                  faker.datatype.number()
                ],
                "DRG_diagnoses.diagnosisCode_eng": [faker.datatype.string()]
              }
            },
            {
              _id: faker.datatype.string(),
              _index: faker.datatype.string(),
              fields: {
                "DRG_diagnoses.description_eng.keyword": [
                  faker.datatype.string()
                ],
                "DRG_diagnoses.codeScheme.keyword": [faker.datatype.string()],
                "DRG_diagnoses.internalUniqueCount_2_year": [
                  faker.datatype.number()
                ],
                "DRG_diagnoses.pctOfUniqueClaims_2_year": [
                  faker.datatype.number()
                ],
                "DRG_diagnoses.diagnosisCode_eng": [faker.datatype.string()]
              }
            }
          ]
        }
      };
      const DRG_diagnosesUniqueCount_2_year = faker.datatype.number();
      const DRG_proceduresUniqueCount_2_year = faker.datatype.number();

      const hit = generateMockHit(
        {
          DRG_diagnosesUniqueCount_2_year,
          DRG_proceduresUniqueCount_2_year
        },
        {
          diagnoses_collection: mockDiagnosisData
        },
        true,
        2
      );

      const hits: SearchHitsMetadata<HCPDocument> = {
        total: {
          value: faker.datatype.number()
        } as SearchTotalHits,
        hits: [hit]
      };

      const synonyms = generateMockSynonyms();
      const suppliedFilters = generateFilters();
      suppliedFilters.getMatchedClaims = {
        value: true
      };
      suppliedFilters.claims.timeFrame = {
        value: 2
      };
      suppliedFilters.claims.showUniquePatients!.value = true;
      const queryIntent = generateMockQueryIntents();

      const affiliationAdapterService = createMockInstance(
        AffiliationAdapterService
      );

      const keywordSearchResponseAdapterService =
        new KeywordSearchResponseAdapterService(affiliationAdapterService);

      const adaptedResponse = await keywordSearchResponseAdapterService.adapt(
        page,
        ENGLISH,
        hits,
        undefined,
        synonyms,
        suppliedFilters,
        queryIntent,
        [],
        generateKeywordSearchFeatureFlags({
          enableUniquePatientCountForClaims: true
        }),

        [],
        []
      );

      expect(adaptedResponse.results[0].matchedDiagnoses).toEqual([
        {
          description:
            mockDiagnosisData.hits.hits[0].fields![
              "DRG_diagnoses.description_eng.keyword"
            ][0],
          diagnosisCode:
            mockDiagnosisData.hits.hits[0].fields![
              "DRG_diagnoses.diagnosisCode_eng"
            ][0],
          percentOfClaims:
            mockDiagnosisData.hits.hits[0].fields![
              "DRG_diagnoses.pctOfUniqueClaims_2_year"
            ][0],
          percentage:
            (mockDiagnosisData.hits.hits[0].fields![
              "DRG_diagnoses.internalUniqueCount_2_year"
            ][0] /
              DRG_diagnosesUniqueCount_2_year) *
            100,
          internalCount:
            mockDiagnosisData.hits.hits[0].fields![
              "DRG_diagnoses.internalUniqueCount_2_year"
            ][0],
          codeScheme:
            mockDiagnosisData.hits.hits[0].fields![
              "DRG_diagnoses.codeScheme.keyword"
            ][0],
          count:
            mockDiagnosisData.hits.hits[0].fields![
              "DRG_diagnoses.internalUniqueCount_2_year"
            ][0].toString()
        },
        {
          description:
            mockDiagnosisData.hits.hits[1].fields![
              "DRG_diagnoses.description_eng.keyword"
            ][0],
          diagnosisCode:
            mockDiagnosisData.hits.hits[1].fields![
              "DRG_diagnoses.diagnosisCode_eng"
            ][0],
          percentOfClaims:
            mockDiagnosisData.hits.hits[1].fields![
              "DRG_diagnoses.pctOfUniqueClaims_2_year"
            ][0],
          percentage:
            (mockDiagnosisData.hits.hits[1].fields![
              "DRG_diagnoses.internalUniqueCount_2_year"
            ][0] /
              DRG_diagnosesUniqueCount_2_year) *
            100,
          internalCount:
            mockDiagnosisData.hits.hits[1].fields![
              "DRG_diagnoses.internalUniqueCount_2_year"
            ][0],
          codeScheme:
            mockDiagnosisData.hits.hits[1].fields![
              "DRG_diagnoses.codeScheme.keyword"
            ][0],
          count:
            mockDiagnosisData.hits.hits[1].fields![
              "DRG_diagnoses.internalUniqueCount_2_year"
            ][0].toString()
        }
      ]);
    });
    it("should return appropriate data when filter getMatchedClaims is passed along with timeframe filter and unique patient count flag is on for procedures", async () => {
      const page = {
        from: faker.datatype.number(),
        size: faker.datatype.number()
      };
      const DRG_proceduresUniqueCount_5_year = faker.datatype.number();
      const mockProceduresData: SearchInnerHitsResult = {
        hits: {
          total: {
            value: faker.datatype.number(),
            relation: "eq"
          },
          hits: [
            {
              _id: faker.datatype.string(),
              _index: faker.datatype.string(),
              fields: {
                "DRG_procedures.description_eng.keyword": [
                  faker.datatype.string()
                ],
                "DRG_procedures.codeScheme": [faker.datatype.string()],
                "DRG_procedures.internalUniqueCount_5_year": [
                  faker.datatype.number()
                ],
                "DRG_procedures.uniquePercentage_5_year": [
                  faker.datatype.number()
                ],
                "DRG_procedures.procedureCode_eng.keyword": [
                  faker.datatype.string()
                ]
              }
            },
            {
              _id: faker.datatype.string(),
              _index: faker.datatype.string(),
              fields: {
                "DRG_procedures.description_eng.keyword": [
                  faker.datatype.string()
                ],
                "DRG_procedures.codeScheme": [faker.datatype.string()],
                "DRG_procedures.internalUniqueCount_5_year": [
                  faker.datatype.number()
                ],
                "DRG_procedures.uniquePercentage_5_year": [
                  faker.datatype.number()
                ],
                "DRG_procedures.procedureCode_eng.keyword": [
                  faker.datatype.string()
                ]
              }
            }
          ]
        }
      };
      const hit = generateMockHit(
        {
          DRG_proceduresUniqueCount_5_year
        },
        {
          procedures_collection: mockProceduresData
        },
        true,
        5
      );

      const hits: SearchHitsMetadata<HCPDocument> = {
        total: {
          value: faker.datatype.number()
        } as SearchTotalHits,
        hits: [hit]
      };

      const synonyms = generateMockSynonyms();
      const suppliedFilters = generateFilters();
      suppliedFilters.getMatchedClaims = {
        value: true
      };
      suppliedFilters.claims.timeFrame = {
        value: 5
      };
      suppliedFilters.claims.showUniquePatients!.value = true;
      const queryIntent = generateMockQueryIntents();

      const affiliationAdapterService = createMockInstance(
        AffiliationAdapterService
      );

      const keywordSearchResponseAdapterService =
        new KeywordSearchResponseAdapterService(affiliationAdapterService);

      const adaptedResponse = await keywordSearchResponseAdapterService.adapt(
        page,
        ENGLISH,
        hits,
        undefined,
        synonyms,
        suppliedFilters,
        queryIntent,
        [],
        generateKeywordSearchFeatureFlags({
          enableUniquePatientCountForClaims: true
        }),

        [],
        []
      );

      expect(adaptedResponse.results[0].matchedProcedures).toEqual([
        {
          description:
            mockProceduresData.hits.hits[0].fields![
              "DRG_procedures.description_eng.keyword"
            ][0],
          procedureCode:
            mockProceduresData.hits.hits[0].fields![
              "DRG_procedures.procedureCode_eng.keyword"
            ][0],
          percentOfClaims:
            mockProceduresData.hits.hits[0].fields![
              "DRG_procedures.uniquePercentage_5_year"
            ][0],
          percentage:
            (mockProceduresData.hits.hits[0].fields![
              "DRG_procedures.internalUniqueCount_5_year"
            ][0] /
              DRG_proceduresUniqueCount_5_year) *
            100,
          internalCount:
            mockProceduresData.hits.hits[0].fields![
              "DRG_procedures.internalUniqueCount_5_year"
            ][0],
          codeScheme:
            mockProceduresData.hits.hits[0].fields![
              "DRG_procedures.codeScheme"
            ][0],
          count:
            mockProceduresData.hits.hits[0].fields![
              "DRG_procedures.internalUniqueCount_5_year"
            ][0].toString()
        },
        {
          description:
            mockProceduresData.hits.hits[1].fields![
              "DRG_procedures.description_eng.keyword"
            ][0],
          procedureCode:
            mockProceduresData.hits.hits[1].fields![
              "DRG_procedures.procedureCode_eng.keyword"
            ][0],
          percentOfClaims:
            mockProceduresData.hits.hits[1].fields![
              "DRG_procedures.uniquePercentage_5_year"
            ][0],
          percentage:
            (mockProceduresData.hits.hits[1].fields![
              "DRG_procedures.internalUniqueCount_5_year"
            ][0] /
              DRG_proceduresUniqueCount_5_year) *
            100,
          internalCount:
            mockProceduresData.hits.hits[1].fields![
              "DRG_procedures.internalUniqueCount_5_year"
            ][0],
          codeScheme:
            mockProceduresData.hits.hits[1].fields![
              "DRG_procedures.codeScheme"
            ][0],
          count:
            mockProceduresData.hits.hits[1].fields![
              "DRG_procedures.internalUniqueCount_5_year"
            ][0].toString()
        }
      ]);
    });

    it("should return appropriate data when filter getMatchedClaims is passed along with timeframe filter and disable unique patient count flag is on for procedures", async () => {
      const page = {
        from: faker.datatype.number(),
        size: faker.datatype.number()
      };
      const DRG_proceduresUniqueCount_5_year = faker.datatype.number();
      const DRG_proceduresCount_5_year = faker.datatype.number();
      const mockProceduresData: SearchInnerHitsResult = {
        hits: {
          total: {
            value: faker.datatype.number(),
            relation: "eq"
          },
          hits: [
            {
              _id: faker.datatype.string(),
              _index: faker.datatype.string(),
              fields: {
                "DRG_procedures.description_eng.keyword": [
                  faker.datatype.string()
                ],
                "DRG_procedures.codeScheme": [faker.datatype.string()],
                "DRG_procedures.internalCount_5_year": [
                  faker.datatype.number()
                ],
                "DRG_procedures.percentage_5_year": [faker.datatype.number()],
                "DRG_procedures.procedureCode_eng.keyword": [
                  faker.datatype.string()
                ]
              }
            },
            {
              _id: faker.datatype.string(),
              _index: faker.datatype.string(),
              fields: {
                "DRG_procedures.description_eng.keyword": [
                  faker.datatype.string()
                ],
                "DRG_procedures.codeScheme": [faker.datatype.string()],
                "DRG_procedures.internalCount_5_year": [
                  faker.datatype.number()
                ],
                "DRG_procedures.percentage_5_year": [faker.datatype.number()],
                "DRG_procedures.procedureCode_eng.keyword": [
                  faker.datatype.string()
                ]
              }
            }
          ]
        }
      };
      const hit = generateMockHit(
        {
          DRG_proceduresUniqueCount_5_year,
          DRG_proceduresCount_5_year
        },
        {
          procedures_collection: mockProceduresData
        },
        true,
        5,
        undefined,
        true
      );

      const hits: SearchHitsMetadata<HCPDocument> = {
        total: {
          value: faker.datatype.number()
        } as SearchTotalHits,
        hits: [hit]
      };

      const synonyms = generateMockSynonyms();
      const suppliedFilters = generateFilters();
      suppliedFilters.getMatchedClaims = {
        value: true
      };
      suppliedFilters.claims.timeFrame = {
        value: 5
      };
      suppliedFilters.claims.showUniquePatients!.value = true;
      const queryIntent = generateMockQueryIntents();

      const affiliationAdapterService = createMockInstance(
        AffiliationAdapterService
      );

      const keywordSearchResponseAdapterService =
        new KeywordSearchResponseAdapterService(affiliationAdapterService);

      const adaptedResponse = await keywordSearchResponseAdapterService.adapt(
        page,
        ENGLISH,
        hits,
        undefined,
        synonyms,
        suppliedFilters,
        queryIntent,
        [],
        generateKeywordSearchFeatureFlags({
          enableUniquePatientCountForClaims: true,
          disableUniquePatientCountForOnlyProcedures: true
        }),

        [],
        []
      );

      expect(adaptedResponse.results[0].matchedProcedures).toEqual([
        {
          description:
            mockProceduresData.hits.hits[0].fields![
              "DRG_procedures.description_eng.keyword"
            ][0],
          procedureCode:
            mockProceduresData.hits.hits[0].fields![
              "DRG_procedures.procedureCode_eng.keyword"
            ][0],
          percentOfClaims:
            mockProceduresData.hits.hits[0].fields![
              "DRG_procedures.percentage_5_year"
            ][0],
          percentage:
            (mockProceduresData.hits.hits[0].fields![
              "DRG_procedures.internalCount_5_year"
            ][0] /
              DRG_proceduresCount_5_year) *
            100,
          internalCount:
            mockProceduresData.hits.hits[0].fields![
              "DRG_procedures.internalCount_5_year"
            ][0],
          codeScheme:
            mockProceduresData.hits.hits[0].fields![
              "DRG_procedures.codeScheme"
            ][0],
          count:
            mockProceduresData.hits.hits[0].fields![
              "DRG_procedures.internalCount_5_year"
            ][0].toString()
        },
        {
          description:
            mockProceduresData.hits.hits[1].fields![
              "DRG_procedures.description_eng.keyword"
            ][0],
          procedureCode:
            mockProceduresData.hits.hits[1].fields![
              "DRG_procedures.procedureCode_eng.keyword"
            ][0],
          percentOfClaims:
            mockProceduresData.hits.hits[1].fields![
              "DRG_procedures.percentage_5_year"
            ][0],
          percentage:
            (mockProceduresData.hits.hits[1].fields![
              "DRG_procedures.internalCount_5_year"
            ][0] /
              DRG_proceduresCount_5_year) *
            100,
          internalCount:
            mockProceduresData.hits.hits[1].fields![
              "DRG_procedures.internalCount_5_year"
            ][0],
          codeScheme:
            mockProceduresData.hits.hits[1].fields![
              "DRG_procedures.codeScheme"
            ][0],
          count:
            mockProceduresData.hits.hits[1].fields![
              "DRG_procedures.internalCount_5_year"
            ][0].toString()
        }
      ]);
    });

    it("should return appropriate data when prescriptionsTimeframe filter without any other filter", async () => {
      const page = {
        from: faker.datatype.number(),
        size: faker.datatype.number()
      };

      const hit = generateMockHit(
        undefined,
        undefined,
        false,
        undefined,
        5,
        true
      );

      hit.inner_hits!.prescriptions_amount = {
        hits: {
          hits: []
        }
      };

      const hits: SearchHitsMetadata<HCPDocument> = {
        total: {
          value: faker.datatype.number()
        } as SearchTotalHits,
        hits: [hit]
      };

      const synonyms = generateMockSynonyms();
      const suppliedFilters = generateFilters();
      suppliedFilters.claims;
      suppliedFilters.claims.prescriptionsTimeFrame = {
        value: 5
      };
      const queryIntent = generateMockQueryIntents();

      const affiliationAdapterService = createMockInstance(
        AffiliationAdapterService
      );

      const keywordSearchResponseAdapterService =
        new KeywordSearchResponseAdapterService(affiliationAdapterService);
      const matchedCountResults = {} as Record<string, AssetToMatchedCount>;
      matchedCountResults[hit._id] = {} as AssetToMatchedCount;

      const adaptedResponse = await keywordSearchResponseAdapterService.adapt(
        page,
        ENGLISH,
        hits,
        undefined,
        synonyms,
        suppliedFilters,
        queryIntent,
        [],
        generateKeywordSearchFeatureFlags(),
        [],
        [],
        undefined,
        matchedCountResults
      );

      expect(adaptedResponse.results[0].scores.prescriptions?.value).toEqual(
        hit._source?.num_prescriptions_5_year
      );
    });

    describe("unique patient count toggle", () => {
      it("should correctly fetch response from claims hits when unique patient count flag is on", async () => {
        const page = {
          from: faker.datatype.number(),
          size: faker.datatype.number()
        };

        const hit = generateMockHit(undefined, undefined, false);

        const hits: SearchHitsMetadata<HCPDocument> = {
          total: {
            value: faker.datatype.number()
          } as SearchTotalHits,
          hits: [hit]
        };

        const synonyms = generateMockSynonyms();
        const suppliedFilters = generateFilters();
        suppliedFilters.claims.showUniquePatients!.value = false;
        const queryIntent = generateMockQueryIntents();

        const affiliationAdapterService = createMockInstance(
          AffiliationAdapterService
        );

        const keywordSearchResponseAdapterService =
          new KeywordSearchResponseAdapterService(affiliationAdapterService);

        const adaptedResponse = await keywordSearchResponseAdapterService.adapt(
          page,
          ENGLISH,
          hits,
          undefined,
          synonyms,
          suppliedFilters,
          queryIntent,
          [],
          generateKeywordSearchFeatureFlags({
            enableUniquePatientCountForClaims: true
          }),

          [],
          []
        );
        const totalDiag =
          hits.hits[0].inner_hits?.diagnoses_amount.hits.hits.reduce(
            (sum: number, hit: any) => {
              const claimCount = hit.fields["DRG_diagnoses.internalCount"][0];
              return sum + claimCount;
            },
            0
          );
        const totalProc =
          hits.hits[0].inner_hits?.procedures_amount.hits.hits.reduce(
            (sum: number, hit: any) => {
              const claimCount = hit.fields["DRG_procedures.internalCount"][0];
              return sum + claimCount;
            },
            0
          );

        expect(adaptedResponse.results[0].scores.diagnoses.value).toEqual(
          totalDiag
        );
        expect(adaptedResponse.results[0].scores.procedures.value).toEqual(
          totalProc
        );
      });
      it("should correctly fetch response from claims hits when unique patient count flag is on and disable procedure unique patient flag is on", async () => {
        const page = {
          from: faker.datatype.number(),
          size: faker.datatype.number()
        };

        const hit = generateMockHit(
          undefined,
          undefined,
          false,
          undefined,
          undefined,
          true
        );

        const hits: SearchHitsMetadata<HCPDocument> = {
          total: {
            value: faker.datatype.number()
          } as SearchTotalHits,
          hits: [hit]
        };

        const synonyms = generateMockSynonyms();
        const suppliedFilters = generateFilters();
        suppliedFilters.claims.showUniquePatients!.value = false;
        const queryIntent = generateMockQueryIntents();

        const affiliationAdapterService = createMockInstance(
          AffiliationAdapterService
        );

        const keywordSearchResponseAdapterService =
          new KeywordSearchResponseAdapterService(affiliationAdapterService);

        const adaptedResponse = await keywordSearchResponseAdapterService.adapt(
          page,
          ENGLISH,
          hits,
          undefined,
          synonyms,
          suppliedFilters,
          queryIntent,
          [],
          generateKeywordSearchFeatureFlags({
            enableUniquePatientCountForClaims: true,
            disableUniquePatientCountForOnlyProcedures: true
          }),

          [],
          []
        );
        const totalDiag =
          hits.hits[0].inner_hits?.diagnoses_amount.hits.hits.reduce(
            (sum: number, hit: any) => {
              const claimCount = hit.fields["DRG_diagnoses.internalCount"][0];
              return sum + claimCount;
            },
            0
          );
        const totalProc =
          hits.hits[0].inner_hits?.procedures_amount.hits.hits.reduce(
            (sum: number, hit: any) => {
              const claimCount = hit.fields["DRG_procedures.internalCount"][0];
              return sum + claimCount;
            },
            0
          );

        expect(adaptedResponse.results[0].scores.diagnoses.value).toEqual(
          totalDiag
        );
        expect(adaptedResponse.results[0].scores.procedures.value).toEqual(
          totalProc
        );
      });
      it("should return appropriate data when filter getMatchedClaims is passed along with timeframe filter and unique patient count flag is on for diagnoses", async () => {
        const page = {
          from: faker.datatype.number(),
          size: faker.datatype.number()
        };

        const mockDiagnosisData: SearchInnerHitsResult = {
          hits: {
            total: {
              value: faker.datatype.number(),
              relation: "eq"
            },
            hits: [
              {
                _id: faker.datatype.string(),
                _index: faker.datatype.string(),
                fields: {
                  "DRG_diagnoses.description_eng.keyword": [
                    faker.datatype.string()
                  ],
                  "DRG_diagnoses.codeScheme.keyword": [faker.datatype.string()],
                  "DRG_diagnoses.internalCount_2_year": [
                    faker.datatype.number()
                  ],
                  "DRG_diagnoses.pctOfClaims_2_year": [faker.datatype.number()],
                  "DRG_diagnoses.diagnosisCode_eng": [faker.datatype.string()]
                }
              },
              {
                _id: faker.datatype.string(),
                _index: faker.datatype.string(),
                fields: {
                  "DRG_diagnoses.description_eng.keyword": [
                    faker.datatype.string()
                  ],
                  "DRG_diagnoses.codeScheme.keyword": [faker.datatype.string()],
                  "DRG_diagnoses.internalCount_2_year": [
                    faker.datatype.number()
                  ],
                  "DRG_diagnoses.pctOfClaims_2_year": [faker.datatype.number()],
                  "DRG_diagnoses.diagnosisCode_eng": [faker.datatype.string()]
                }
              }
            ]
          }
        };
        const DRG_diagnosesUniqueCount_2_year = faker.datatype.number();
        const DRG_diagnosesCount_2_year = faker.datatype.number();
        const DRG_proceduresUniqueCount_2_year = faker.datatype.number();
        const DRG_proceduresCount_2_year = faker.datatype.number();

        const hit = generateMockHit(
          {
            DRG_diagnosesUniqueCount_2_year,
            DRG_diagnosesCount_2_year,
            DRG_proceduresUniqueCount_2_year,
            DRG_proceduresCount_2_year
          },
          {
            diagnoses_collection: mockDiagnosisData
          },
          false,
          2
        );

        const hits: SearchHitsMetadata<HCPDocument> = {
          total: {
            value: faker.datatype.number()
          } as SearchTotalHits,
          hits: [hit]
        };

        const synonyms = generateMockSynonyms();
        const suppliedFilters = generateFilters();
        suppliedFilters.getMatchedClaims = {
          value: true
        };
        suppliedFilters.claims.timeFrame = {
          value: 2
        };
        suppliedFilters.claims.showUniquePatients!.value = false;
        const queryIntent = generateMockQueryIntents();

        const affiliationAdapterService = createMockInstance(
          AffiliationAdapterService
        );

        const keywordSearchResponseAdapterService =
          new KeywordSearchResponseAdapterService(affiliationAdapterService);

        const adaptedResponse = await keywordSearchResponseAdapterService.adapt(
          page,
          ENGLISH,
          hits,
          undefined,
          synonyms,
          suppliedFilters,
          queryIntent,
          [],
          generateKeywordSearchFeatureFlags({
            enableUniquePatientCountForClaims: true
          }),

          [],
          []
        );

        expect(adaptedResponse.results[0].matchedDiagnoses).toEqual([
          {
            description:
              mockDiagnosisData.hits.hits[0].fields![
                "DRG_diagnoses.description_eng.keyword"
              ][0],
            diagnosisCode:
              mockDiagnosisData.hits.hits[0].fields![
                "DRG_diagnoses.diagnosisCode_eng"
              ][0],
            percentOfClaims:
              mockDiagnosisData.hits.hits[0].fields![
                "DRG_diagnoses.pctOfClaims_2_year"
              ][0],
            percentage:
              (mockDiagnosisData.hits.hits[0].fields![
                "DRG_diagnoses.internalCount_2_year"
              ][0] /
                DRG_diagnosesCount_2_year) *
              100,
            internalCount:
              mockDiagnosisData.hits.hits[0].fields![
                "DRG_diagnoses.internalCount_2_year"
              ][0],
            codeScheme:
              mockDiagnosisData.hits.hits[0].fields![
                "DRG_diagnoses.codeScheme.keyword"
              ][0],
            count:
              mockDiagnosisData.hits.hits[0].fields![
                "DRG_diagnoses.internalCount_2_year"
              ][0].toString()
          },
          {
            description:
              mockDiagnosisData.hits.hits[1].fields![
                "DRG_diagnoses.description_eng.keyword"
              ][0],
            diagnosisCode:
              mockDiagnosisData.hits.hits[1].fields![
                "DRG_diagnoses.diagnosisCode_eng"
              ][0],
            percentOfClaims:
              mockDiagnosisData.hits.hits[1].fields![
                "DRG_diagnoses.pctOfClaims_2_year"
              ][0],
            percentage:
              (mockDiagnosisData.hits.hits[1].fields![
                "DRG_diagnoses.internalCount_2_year"
              ][0] /
                DRG_diagnosesCount_2_year) *
              100,
            internalCount:
              mockDiagnosisData.hits.hits[1].fields![
                "DRG_diagnoses.internalCount_2_year"
              ][0],
            codeScheme:
              mockDiagnosisData.hits.hits[1].fields![
                "DRG_diagnoses.codeScheme.keyword"
              ][0],
            count:
              mockDiagnosisData.hits.hits[1].fields![
                "DRG_diagnoses.internalCount_2_year"
              ][0].toString()
          }
        ]);
      });
      it("should return appropriate data when filter getMatchedClaims is passed along with timeframe filter and unique patient toggle flag is off for procedures", async () => {
        const page = {
          from: faker.datatype.number(),
          size: faker.datatype.number()
        };
        const DRG_proceduresUniqueCount_5_year = faker.datatype.number();
        const DRG_proceduresCount_5_year = faker.datatype.number();
        const mockProceduresData: SearchInnerHitsResult = {
          hits: {
            total: {
              value: faker.datatype.number(),
              relation: "eq"
            },
            hits: [
              {
                _id: faker.datatype.string(),
                _index: faker.datatype.string(),
                fields: {
                  "DRG_procedures.description_eng.keyword": [
                    faker.datatype.string()
                  ],
                  "DRG_procedures.codeScheme": [faker.datatype.string()],
                  "DRG_procedures.internalCount_5_year": [
                    faker.datatype.number()
                  ],
                  "DRG_procedures.percentage_5_year": [faker.datatype.number()],
                  "DRG_procedures.procedureCode_eng.keyword": [
                    faker.datatype.string()
                  ]
                }
              },
              {
                _id: faker.datatype.string(),
                _index: faker.datatype.string(),
                fields: {
                  "DRG_procedures.description_eng.keyword": [
                    faker.datatype.string()
                  ],
                  "DRG_procedures.codeScheme": [faker.datatype.string()],
                  "DRG_procedures.internalCount_5_year": [
                    faker.datatype.number()
                  ],
                  "DRG_procedures.percentage_5_year": [faker.datatype.number()],
                  "DRG_procedures.procedureCode_eng.keyword": [
                    faker.datatype.string()
                  ]
                }
              }
            ]
          }
        };
        const hit = generateMockHit(
          {
            DRG_proceduresCount_5_year,
            DRG_proceduresUniqueCount_5_year
          },
          {
            procedures_collection: mockProceduresData
          },
          false,
          5
        );

        const hits: SearchHitsMetadata<HCPDocument> = {
          total: {
            value: faker.datatype.number()
          } as SearchTotalHits,
          hits: [hit]
        };

        const synonyms = generateMockSynonyms();
        const suppliedFilters = generateFilters();
        suppliedFilters.getMatchedClaims = {
          value: true
        };
        suppliedFilters.claims.timeFrame = {
          value: 5
        };
        suppliedFilters.claims.showUniquePatients!.value = false;
        const queryIntent = generateMockQueryIntents();

        const affiliationAdapterService = createMockInstance(
          AffiliationAdapterService
        );

        const keywordSearchResponseAdapterService =
          new KeywordSearchResponseAdapterService(affiliationAdapterService);

        const adaptedResponse = await keywordSearchResponseAdapterService.adapt(
          page,
          ENGLISH,
          hits,
          undefined,
          synonyms,
          suppliedFilters,
          queryIntent,
          [],
          generateKeywordSearchFeatureFlags({
            enableUniquePatientCountForClaims: true
          }),

          [],
          []
        );

        expect(adaptedResponse.results[0].matchedProcedures).toEqual([
          {
            description:
              mockProceduresData.hits.hits[0].fields![
                "DRG_procedures.description_eng.keyword"
              ][0],
            procedureCode:
              mockProceduresData.hits.hits[0].fields![
                "DRG_procedures.procedureCode_eng.keyword"
              ][0],
            percentOfClaims:
              mockProceduresData.hits.hits[0].fields![
                "DRG_procedures.percentage_5_year"
              ][0],
            percentage:
              (mockProceduresData.hits.hits[0].fields![
                "DRG_procedures.internalCount_5_year"
              ][0] /
                DRG_proceduresCount_5_year) *
              100,
            internalCount:
              mockProceduresData.hits.hits[0].fields![
                "DRG_procedures.internalCount_5_year"
              ][0],
            codeScheme:
              mockProceduresData.hits.hits[0].fields![
                "DRG_procedures.codeScheme"
              ][0],
            count:
              mockProceduresData.hits.hits[0].fields![
                "DRG_procedures.internalCount_5_year"
              ][0].toString()
          },
          {
            description:
              mockProceduresData.hits.hits[1].fields![
                "DRG_procedures.description_eng.keyword"
              ][0],
            procedureCode:
              mockProceduresData.hits.hits[1].fields![
                "DRG_procedures.procedureCode_eng.keyword"
              ][0],
            percentOfClaims:
              mockProceduresData.hits.hits[1].fields![
                "DRG_procedures.percentage_5_year"
              ][0],
            percentage:
              (mockProceduresData.hits.hits[1].fields![
                "DRG_procedures.internalCount_5_year"
              ][0] /
                DRG_proceduresCount_5_year) *
              100,
            internalCount:
              mockProceduresData.hits.hits[1].fields![
                "DRG_procedures.internalCount_5_year"
              ][0],
            codeScheme:
              mockProceduresData.hits.hits[1].fields![
                "DRG_procedures.codeScheme"
              ][0],
            count:
              mockProceduresData.hits.hits[1].fields![
                "DRG_procedures.internalCount_5_year"
              ][0].toString()
          }
        ]);
      });

      it("should return appropriate data when filter getMatchedClaims is passed along with timeframe filter and disable unique patient count flag is on for procedures and unique patient toggle is off", async () => {
        const page = {
          from: faker.datatype.number(),
          size: faker.datatype.number()
        };
        const DRG_proceduresUniqueCount_5_year = faker.datatype.number();
        const DRG_proceduresCount_5_year = faker.datatype.number();
        const mockProceduresData: SearchInnerHitsResult = {
          hits: {
            total: {
              value: faker.datatype.number(),
              relation: "eq"
            },
            hits: [
              {
                _id: faker.datatype.string(),
                _index: faker.datatype.string(),
                fields: {
                  "DRG_procedures.description_eng.keyword": [
                    faker.datatype.string()
                  ],
                  "DRG_procedures.codeScheme": [faker.datatype.string()],
                  "DRG_procedures.internalCount_5_year": [
                    faker.datatype.number()
                  ],
                  "DRG_procedures.percentage_5_year": [faker.datatype.number()],
                  "DRG_procedures.procedureCode_eng.keyword": [
                    faker.datatype.string()
                  ]
                }
              },
              {
                _id: faker.datatype.string(),
                _index: faker.datatype.string(),
                fields: {
                  "DRG_procedures.description_eng.keyword": [
                    faker.datatype.string()
                  ],
                  "DRG_procedures.codeScheme": [faker.datatype.string()],
                  "DRG_procedures.internalCount_5_year": [
                    faker.datatype.number()
                  ],
                  "DRG_procedures.percentage_5_year": [faker.datatype.number()],
                  "DRG_procedures.procedureCode_eng.keyword": [
                    faker.datatype.string()
                  ]
                }
              }
            ]
          }
        };
        const hit = generateMockHit(
          {
            DRG_proceduresUniqueCount_5_year,
            DRG_proceduresCount_5_year
          },
          {
            procedures_collection: mockProceduresData
          },
          false,
          5,
          undefined,
          true
        );

        const hits: SearchHitsMetadata<HCPDocument> = {
          total: {
            value: faker.datatype.number()
          } as SearchTotalHits,
          hits: [hit]
        };

        const synonyms = generateMockSynonyms();
        const suppliedFilters = generateFilters();
        suppliedFilters.getMatchedClaims = {
          value: true
        };
        suppliedFilters.claims.timeFrame = {
          value: 5
        };
        suppliedFilters.claims.showUniquePatients!.value = false;
        const queryIntent = generateMockQueryIntents();

        const affiliationAdapterService = createMockInstance(
          AffiliationAdapterService
        );

        const keywordSearchResponseAdapterService =
          new KeywordSearchResponseAdapterService(affiliationAdapterService);

        const adaptedResponse = await keywordSearchResponseAdapterService.adapt(
          page,
          ENGLISH,
          hits,
          undefined,
          synonyms,
          suppliedFilters,
          queryIntent,
          [],
          generateKeywordSearchFeatureFlags({
            enableUniquePatientCountForClaims: true,
            disableUniquePatientCountForOnlyProcedures: true
          }),

          [],
          []
        );

        expect(adaptedResponse.results[0].matchedProcedures).toEqual([
          {
            description:
              mockProceduresData.hits.hits[0].fields![
                "DRG_procedures.description_eng.keyword"
              ][0],
            procedureCode:
              mockProceduresData.hits.hits[0].fields![
                "DRG_procedures.procedureCode_eng.keyword"
              ][0],
            percentOfClaims:
              mockProceduresData.hits.hits[0].fields![
                "DRG_procedures.percentage_5_year"
              ][0],
            percentage:
              (mockProceduresData.hits.hits[0].fields![
                "DRG_procedures.internalCount_5_year"
              ][0] /
                DRG_proceduresCount_5_year) *
              100,
            internalCount:
              mockProceduresData.hits.hits[0].fields![
                "DRG_procedures.internalCount_5_year"
              ][0],
            codeScheme:
              mockProceduresData.hits.hits[0].fields![
                "DRG_procedures.codeScheme"
              ][0],
            count:
              mockProceduresData.hits.hits[0].fields![
                "DRG_procedures.internalCount_5_year"
              ][0].toString()
          },
          {
            description:
              mockProceduresData.hits.hits[1].fields![
                "DRG_procedures.description_eng.keyword"
              ][0],
            procedureCode:
              mockProceduresData.hits.hits[1].fields![
                "DRG_procedures.procedureCode_eng.keyword"
              ][0],
            percentOfClaims:
              mockProceduresData.hits.hits[1].fields![
                "DRG_procedures.percentage_5_year"
              ][0],
            percentage:
              (mockProceduresData.hits.hits[1].fields![
                "DRG_procedures.internalCount_5_year"
              ][0] /
                DRG_proceduresCount_5_year) *
              100,
            internalCount:
              mockProceduresData.hits.hits[1].fields![
                "DRG_procedures.internalCount_5_year"
              ][0],
            codeScheme:
              mockProceduresData.hits.hits[1].fields![
                "DRG_procedures.codeScheme"
              ][0],
            count:
              mockProceduresData.hits.hits[1].fields![
                "DRG_procedures.internalCount_5_year"
              ][0].toString()
          }
        ]);
      });
    });
  });

  describe("matched count service", () => {
    it("should only return diagnosis score when diagnosis code and timeframe applied", async () => {
      const page = {
        from: faker.datatype.number(),
        size: faker.datatype.number()
      };

      const hit = generateMockHit(undefined, undefined, false, 2);

      const hits: SearchHitsMetadata<HCPDocument> = {
        total: {
          value: faker.datatype.number()
        } as SearchTotalHits,
        hits: [hit]
      };

      const synonyms = generateMockSynonyms();
      const suppliedFilters = generateFilters({
        claims: {
          timeFrame: {
            value: 2
          },
          diagnosesICD: {
            values: [faker.datatype.string()]
          },
          proceduresCPT: {
            values: []
          },
          genericNames: {
            values: []
          },
          drugClasses: {
            values: []
          },
          brandNames: {
            values: []
          },
          diagnosesICDMinCount: {
            value: null
          },
          proceduresCPTMinCount: {
            value: null
          },
          prescriptionsMinCount: {
            value: null
          },
          proceduresHCPC: {
            values: []
          },
          proceduresHCPCMinCount: {
            value: null
          }
        }
      });
      const queryIntent = generateMockQueryIntents();

      const affiliationAdapterService = createMockInstance(
        AffiliationAdapterService
      );

      const keywordSearchResponseAdapterService =
        new KeywordSearchResponseAdapterService(affiliationAdapterService);
      const matchedCountResults = {} as Record<string, AssetToMatchedCount>;
      matchedCountResults[hit._id] = {} as AssetToMatchedCount;
      matchedCountResults[hit._id]["diagnoses_amount"] =
        faker.datatype.number();
      const input = generateKeywordSearchInput({
        suppliedFilters
      });
      const adaptedResponse: PersonSearchResponse =
        await keywordSearchResponseAdapterService.adapt(
          page,
          ENGLISH,
          hits,
          undefined,
          synonyms,
          suppliedFilters,
          queryIntent,
          [],
          generateKeywordSearchFeatureFlags({
            enableUniquePatientCountForClaims: false
          }),
          [],
          [],
          undefined,
          matchedCountResults,
          input
        );

      expect(adaptedResponse.results[0].scores.diagnoses.value).toEqual(
        matchedCountResults[hit._id]["diagnoses_amount"]
      );
      expect(adaptedResponse.results[0].scores.procedures.value).toEqual(0);
    });

    it("should only return procedure score when procedure code and timeframe applied", async () => {
      const page = {
        from: faker.datatype.number(),
        size: faker.datatype.number()
      };

      const hit = generateMockHit(undefined, undefined, false, 2);

      const hits: SearchHitsMetadata<HCPDocument> = {
        total: {
          value: faker.datatype.number()
        } as SearchTotalHits,
        hits: [hit]
      };

      const synonyms = generateMockSynonyms();
      const suppliedFilters = generateFilters({
        claims: {
          timeFrame: {
            value: 2
          },
          diagnosesICD: {
            values: []
          },
          proceduresCPT: {
            values: [faker.datatype.string()]
          },
          genericNames: {
            values: []
          },
          drugClasses: {
            values: []
          },
          brandNames: {
            values: []
          },
          diagnosesICDMinCount: {
            value: null
          },
          proceduresCPTMinCount: {
            value: null
          },
          prescriptionsMinCount: {
            value: null
          },
          proceduresHCPC: {
            values: []
          },
          proceduresHCPCMinCount: {
            value: null
          }
        }
      });
      const queryIntent = generateMockQueryIntents();

      const affiliationAdapterService = createMockInstance(
        AffiliationAdapterService
      );

      const keywordSearchResponseAdapterService =
        new KeywordSearchResponseAdapterService(affiliationAdapterService);
      const matchedCountResults = {} as Record<string, AssetToMatchedCount>;
      matchedCountResults[hit._id] = {} as AssetToMatchedCount;
      matchedCountResults[hit._id]["procedures_amount"] =
        faker.datatype.number();
      const input = generateKeywordSearchInput({
        suppliedFilters
      });
      const adaptedResponse: PersonSearchResponse =
        await keywordSearchResponseAdapterService.adapt(
          page,
          ENGLISH,
          hits,
          undefined,
          synonyms,
          suppliedFilters,
          queryIntent,
          [],
          generateKeywordSearchFeatureFlags({
            enableUniquePatientCountForClaims: false
          }),
          [],
          [],
          undefined,
          matchedCountResults,
          input
        );

      expect(adaptedResponse.results[0].scores.diagnoses.value).toEqual(0);
      expect(adaptedResponse.results[0].scores.procedures.value).toEqual(
        matchedCountResults[hit._id]["procedures_amount"]
      );
    });

    it("should return both claims score when no code and timeframe applied", async () => {
      const page = {
        from: faker.datatype.number(),
        size: faker.datatype.number()
      };

      const hit = generateMockHit(undefined, undefined, false, 2);

      const hits: SearchHitsMetadata<HCPDocument> = {
        total: {
          value: faker.datatype.number()
        } as SearchTotalHits,
        hits: [hit]
      };

      const synonyms = generateMockSynonyms();
      const suppliedFilters = generateFilters({
        claims: {
          timeFrame: {
            value: 2
          },
          diagnosesICD: {
            values: []
          },
          proceduresCPT: {
            values: []
          },
          genericNames: {
            values: []
          },
          drugClasses: {
            values: []
          },
          brandNames: {
            values: []
          },
          diagnosesICDMinCount: {
            value: null
          },
          proceduresCPTMinCount: {
            value: null
          },
          prescriptionsMinCount: {
            value: null
          },
          proceduresHCPC: {
            values: []
          },
          proceduresHCPCMinCount: {
            value: null
          }
        }
      });
      const queryIntent = generateMockQueryIntents();

      const affiliationAdapterService = createMockInstance(
        AffiliationAdapterService
      );

      const keywordSearchResponseAdapterService =
        new KeywordSearchResponseAdapterService(affiliationAdapterService);
      const matchedCountResults = {} as Record<string, AssetToMatchedCount>;
      matchedCountResults[hit._id] = {} as AssetToMatchedCount;
      const input = generateKeywordSearchInput({
        suppliedFilters
      });
      const adaptedResponse: PersonSearchResponse =
        await keywordSearchResponseAdapterService.adapt(
          page,
          ENGLISH,
          hits,
          undefined,
          synonyms,
          suppliedFilters,
          queryIntent,
          [],
          generateKeywordSearchFeatureFlags({
            enableUniquePatientCountForClaims: false
          }),
          [],
          [],
          undefined,
          matchedCountResults,
          input
        );

      expect(adaptedResponse.results[0].scores.diagnoses.value).toEqual(
        hit._source?.DRG_diagnosesCount_2_year
      );
      expect(adaptedResponse.results[0].scores.procedures.value).toEqual(
        hit._source?.DRG_proceduresCount_2_year
      );
    });

    it("should return procedure score as sum of procedures and ccsr_px when procedure code and ccsr_px applied", async () => {
      const page = {
        from: faker.datatype.number(),
        size: faker.datatype.number()
      };

      const hit = generateMockHit(undefined, undefined, false, 2);

      const hits: SearchHitsMetadata<HCPDocument> = {
        total: {
          value: faker.datatype.number()
        } as SearchTotalHits,
        hits: [hit]
      };

      const synonyms = generateMockSynonyms();
      const suppliedFilters = generateFilters({
        claims: {
          timeFrame: {
            value: null
          },
          diagnosesICD: {
            values: []
          },
          proceduresCPT: {
            values: [faker.datatype.string()]
          },
          genericNames: {
            values: []
          },
          drugClasses: {
            values: []
          },
          brandNames: {
            values: []
          },
          diagnosesICDMinCount: {
            value: null
          },
          proceduresCPTMinCount: {
            value: null
          },
          prescriptionsMinCount: {
            value: null
          },
          proceduresHCPC: {
            values: []
          },
          proceduresHCPCMinCount: {
            value: null
          },
          ccsrPx: {
            values: [faker.datatype.string()]
          }
        }
      });
      const queryIntent = generateMockQueryIntents();

      const affiliationAdapterService = createMockInstance(
        AffiliationAdapterService
      );

      const keywordSearchResponseAdapterService =
        new KeywordSearchResponseAdapterService(affiliationAdapterService);
      const matchedCountResults = {} as Record<string, AssetToMatchedCount>;
      matchedCountResults[hit._id] = {} as AssetToMatchedCount;
      const proceduresAmount = faker.datatype.number();
      const ccsrPxAmount = faker.datatype.number();
      matchedCountResults[hit._id]["procedures_amount"] = proceduresAmount;
      matchedCountResults[hit._id]["ccsr_px_amount"] = ccsrPxAmount;
      const input = generateKeywordSearchInput({
        suppliedFilters
      });
      const adaptedResponse: PersonSearchResponse =
        await keywordSearchResponseAdapterService.adapt(
          page,
          ENGLISH,
          hits,
          undefined,
          synonyms,
          suppliedFilters,
          queryIntent,
          [],
          generateKeywordSearchFeatureFlags({
            enableUniquePatientCountForClaims: false
          }),
          [],
          [],
          undefined,
          matchedCountResults,
          input
        );

      expect(adaptedResponse.results[0].scores.diagnoses.value).toEqual(0);
      expect(adaptedResponse.results[0].scores.procedures.value).toEqual(
        proceduresAmount + ccsrPxAmount
      );
    });
  });

  describe("Debug", () => {
    it("it should return debug data for rising star when supplied filters has rising star", async () => {
      const page = {
        from: faker.datatype.number(),
        size: faker.datatype.number()
      };

      const hit1 = generateMockHit({
        affiliations: [generateMockAffiliation(), generateMockAffiliation()]
      });
      const hit2 = generateMockHit({
        affiliations: [generateMockAffiliation(), generateMockAffiliation()]
      });

      const hits: SearchHitsMetadata<HCPDocument> = {
        total: {
          value: faker.datatype.number()
        } as SearchTotalHits,
        hits: [hit1, hit2]
      };

      const input = generateKeywordSearchInput();

      const synonyms = generateMockSynonyms();
      const suppliedFilters = generateFilters({
        isRisingStar: {
          value: true
        }
      });
      const queryIntent = generateMockQueryIntents();

      const affiliationAdapterService = createMockInstance(
        AffiliationAdapterService
      );

      const keywordSearchResponseAdapterService =
        new KeywordSearchResponseAdapterService(affiliationAdapterService);

      const adaptedResponse = await keywordSearchResponseAdapterService.adapt(
        page,
        ENGLISH,
        hits,
        undefined,
        synonyms,
        suppliedFilters,
        queryIntent,
        [],
        generateKeywordSearchFeatureFlags({
          enableInternalUsersForRankingDebug: true
        }),
        [],
        [],
        undefined,
        undefined,
        input
      );

      const hit1CitationCount =
        hit1.inner_hits!.citations.hits.hits[0].fields![
          "publications.citationCount"
        ][0] +
        hit1.inner_hits!.citations.hits.hits[1].fields![
          "publications.citationCount"
        ][0];
      const hit1SocialMediaCount =
        hit1.inner_hits!.microBlogging.hits.hits[0].fields![
          "publications.microBloggingCount"
        ][0] +
        hit1.inner_hits!.microBlogging.hits.hits[1].fields![
          "publications.microBloggingCount"
        ][0];

      const hit1TotalIndicationInnerHits = (
        hit1.inner_hits!.indications.hits?.total as SearchTotalHits
      ).value;
      const hit1IndicationRisingScoreTotal =
        hit1.inner_hits!.indications.hits.hits[0].fields![
          "indications.indicationRisingScore"
        ][0] +
        hit1.inner_hits!.indications.hits.hits[1].fields![
          "indications.indicationRisingScore"
        ][0];
      const hit1PaymentAmountsTotal =
        hit1.inner_hits!.payments.hits.hits[0].fields!["payments.amount"][0] +
        hit1.inner_hits!.payments.hits.hits[1].fields!["payments.amount"][0];
      const hit1DiagnosesAmountsTotal =
        hit1.inner_hits!.diagnoses_amount.hits.hits[0].fields![
          "DRG_diagnoses.internalCount"
        ][0] +
        hit1.inner_hits!.diagnoses_amount.hits.hits[1].fields![
          "DRG_diagnoses.internalCount"
        ][0];
      const hit1PrescriptionsAmountsTotal =
        hit1.inner_hits!.prescriptions_amount.hits.hits[0].fields![
          "prescriptions.num_prescriptions"
        ][0] +
        hit1.inner_hits!.prescriptions_amount.hits.hits[1].fields![
          "prescriptions.num_prescriptions"
        ][0];
      const hit1ProceduresAmountsTotal =
        hit1.inner_hits!.procedures_amount.hits.hits[0].fields![
          "DRG_procedures.internalCount"
        ][0] +
        hit1.inner_hits!.procedures_amount.hits.hits[1].fields![
          "DRG_procedures.internalCount"
        ][0];

      const hit2CitationCount =
        hit2.inner_hits!.citations.hits.hits[0].fields![
          "publications.citationCount"
        ][0] +
        hit2.inner_hits!.citations.hits.hits[1].fields![
          "publications.citationCount"
        ][0];
      const hit2SocialMediaCount =
        hit2.inner_hits!.microBlogging.hits.hits[0].fields![
          "publications.microBloggingCount"
        ][0] +
        hit2.inner_hits!.microBlogging.hits.hits[1].fields![
          "publications.microBloggingCount"
        ][0];
      const hit2TotalIndicationInnerHits = (
        hit2.inner_hits!.indications.hits?.total as SearchTotalHits
      ).value;
      const hit2IndicationRisingScoreTotal =
        hit2.inner_hits!.indications.hits.hits[0].fields![
          "indications.indicationRisingScore"
        ][0] +
        hit2.inner_hits!.indications.hits.hits[1].fields![
          "indications.indicationRisingScore"
        ][0];
      const hit2PaymentAmountsTotal =
        hit2.inner_hits!.payments.hits.hits[0].fields!["payments.amount"][0] +
        hit2.inner_hits!.payments.hits.hits[1].fields!["payments.amount"][0];
      const hit2DiagnosesAmountsTotal =
        hit2.inner_hits!.diagnoses_amount.hits.hits[0].fields![
          "DRG_diagnoses.internalCount"
        ][0] +
        hit2.inner_hits!.diagnoses_amount.hits.hits[1].fields![
          "DRG_diagnoses.internalCount"
        ][0];
      const hit2PrescriptionsAmountsTotal =
        hit2.inner_hits!.prescriptions_amount.hits.hits[0].fields![
          "prescriptions.num_prescriptions"
        ][0] +
        hit2.inner_hits!.prescriptions_amount.hits.hits[1].fields![
          "prescriptions.num_prescriptions"
        ][0];
      const hit2ProceduresAmountsTotal =
        hit2.inner_hits!.procedures_amount.hits.hits[0].fields![
          "DRG_procedures.internalCount"
        ][0] +
        hit2.inner_hits!.procedures_amount.hits.hits[1].fields![
          "DRG_procedures.internalCount"
        ][0];

      expect(adaptedResponse).toEqual({
        from: page.from,
        pageSize: page.size,
        total: (hits.total as SearchTotalHits).value,
        results: [
          {
            personId: hit1._source?.id,
            h1dnId: hit1._source?.h1dn_id,
            name: hit1._source?.name_eng,
            firstName: hit1._source?.firstName_eng,
            middleName: hit1._source?.middleName_eng,
            lastName: hit1._source?.lastName_eng,
            nameEng: hit1._source?.name_eng,
            firstNameEng: hit1._source?.firstName_eng,
            lastNameEng: hit1._source?.lastName_eng,
            isFacultyOpinionsMember: hit1._source!.isFacultyOpinionsMember,
            personTranslationEng: {
              fullName: hit1._source!.name_eng,
              firstName: hit1._source!.firstName_eng,
              middleName: hit1._source!.middleName_eng,
              lastName: hit1._source!.lastName_eng,
              languageCode: ENGLISH
            },
            personTranslation: {
              fullName: hit1._source!.name_jpn,
              firstName: hit1._source!.firstName_jpn,
              middleName: hit1._source!.middleName_jpn,
              lastName: hit1._source!.lastName_jpn,
              languageCode: JAPANESE
            },
            affiliations: [
              expect.objectContaining({
                id: hit1._source!.affiliations![0].id
              }),
              expect.objectContaining({
                id: hit1._source!.affiliations![1].id
              })
            ],
            score: hit1._score!,
            scores: {
              normalizedRange: {
                min: 0,
                max: 0
              },
              personId: hit1._source?.id,
              h1Score: hit1._score!,
              publications: {
                minValue: 0,
                maxValue: 0,
                normalizedValue: 0,
                percentile: 0,
                value: (
                  hit1.inner_hits!.publications.hits.total as SearchTotalHits
                ).value
              },
              citations: {
                minValue: 0,
                maxValue: 0,
                normalizedValue: 0,
                percentile: 0,
                value: hit1CitationCount
              },
              trials: {
                minValue: 0,
                maxValue: 0,
                normalizedValue: 0,
                percentile: 0,
                value: (hit1.inner_hits!.trials.hits.total as SearchTotalHits)
                  .value
              },
              payments: {
                minValue: 0,
                maxValue: 0,
                normalizedValue: 0,
                percentile: 0,
                value: hit1PaymentAmountsTotal
              },
              paymentsCount: {
                minValue: 0,
                maxValue: 0,
                normalizedValue: 0,
                percentile: 0,
                value: (hit1.inner_hits!.payments.hits.total as SearchTotalHits)
                  .value
              },
              grants: {
                minValue: 0,
                maxValue: 0,
                normalizedValue: 0,
                percentile: 0,
                value: 0
              },
              grantsCount: {
                minValue: 0,
                maxValue: 0,
                normalizedValue: 0,
                percentile: 0,
                value: 0
              },
              congresses: {
                minValue: 0,
                maxValue: 0,
                normalizedValue: 0,
                percentile: 0,
                value: (hit1.inner_hits!.congress.hits.total as SearchTotalHits)
                  .value
              },
              collaborators: {
                minValue: 0,
                maxValue: 0,
                normalizedValue: 0,
                percentile: 0,
                value: 0
              },
              socialMediaMentions: {
                minValue: 0,
                maxValue: 0,
                normalizedValue: 0,
                percentile: 0,
                value: hit1SocialMediaCount
              },
              procedures: {
                minValue: 0,
                maxValue: 0,
                normalizedValue: 0,
                percentile: 0,
                value: hit1ProceduresAmountsTotal
              },
              diagnoses: {
                minValue: 0,
                maxValue: 0,
                normalizedValue: 0,
                percentile: 0,
                value: hit1DiagnosesAmountsTotal
              },
              prescriptions: {
                minValue: 0,
                maxValue: 0,
                normalizedValue: 0,
                percentile: 0,
                value: hit1PrescriptionsAmountsTotal
              },
              referralsReceived: {
                minValue: 0,
                maxValue: 0,
                normalizedValue: 0,
                percentile: 0,
                value: hit1._source?.referralsReceivedCount
              },
              referralsSent: {
                minValue: 0,
                maxValue: 0,
                normalizedValue: 0,
                percentile: 0,
                value: hit1._source?.referralsSentCount
              },
              twitterFollowersCount: {
                minValue: 0,
                maxValue: 0,
                normalizedValue: 0,
                percentile: 0,
                value: hit1._source?.twitterFollowersCount
              },
              twitterTweetCount: {
                minValue: 0,
                maxValue: 0,
                normalizedValue: 0,
                percentile: 0,
                value: hit1._source?.twitterTweetCount
              }
            },
            totalWorks: hit1._source?.totalWorks,
            countPublications: hit1._source?.publicationCount,
            countPresentWorkAffiliations:
              hit1._source?.presentWorkInstitutionCount,
            publicationsHighlights: [
              {
                publicationsId:
                  hit1.inner_hits?.publications.hits.hits[0].fields![
                    "publications.id"
                  ][0],
                highlight: {
                  keywords:
                    hit1.inner_hits?.publications.hits.hits[0].highlight![
                      "publications.keywords_eng"
                    ][0],
                  publicationAbstract:
                    hit1.inner_hits?.publications.hits.hits[0].highlight![
                      "publications.publicationAbstract_eng"
                    ][0],
                  title:
                    hit1.inner_hits?.publications.hits.hits[0].highlight![
                      "publications.title_eng"
                    ][0]
                }
              }
            ],
            countClinicalTrials: hit1._source?.trialCount,
            trialsHighlights: [
              {
                highlight: {
                  briefTitle:
                    hit1.inner_hits?.trials.hits.hits[0].highlight![
                      "trials.briefTitle_eng"
                    ][0],
                  conditions:
                    hit1.inner_hits?.trials.hits.hits[0].highlight![
                      "trials.conditions_eng"
                    ][0],
                  interventions:
                    hit1.inner_hits?.trials.hits.hits[0].highlight![
                      "trials.interventions_eng"
                    ][0],
                  keywords:
                    hit1.inner_hits?.trials.hits.hits[0].highlight![
                      "trials.keywords_eng"
                    ][0],
                  officialTitle:
                    hit1.inner_hits?.trials.hits.hits[0].highlight![
                      "trials.officialTitle_eng"
                    ][0],
                  summary:
                    hit1.inner_hits?.trials.hits.hits[0].highlight![
                      "trials.summary_eng"
                    ][0]
                },
                trialsId:
                  hit1.inner_hits?.trials.hits.hits[0].fields!["trials.id"][0]
              }
            ],
            claimsDiagnosesHighlights: [
              {
                highlight: {
                  codeAndDescription:
                    hit1.inner_hits?.DRG_diagnoses.hits.hits[0].highlight![
                      "DRG_diagnoses.codeAndDescription_eng"
                    ][0]
                }
              }
            ],
            claimsProceduresHighlights: [
              {
                highlight: {
                  codeAndDescription:
                    hit1.inner_hits?.DRG_procedures.hits.hits[0].highlight![
                      "DRG_procedures.codeAndDescription_eng"
                    ][0]
                }
              }
            ],
            congressHighlights: [
              {
                congressId:
                  hit1.inner_hits?.congress.hits.hits[0].fields![
                    "congress.id"
                  ][0],
                highlight: {
                  keywords:
                    hit1.inner_hits?.congress.hits.hits[0].highlight![
                      "congress.keywords_eng"
                    ][0],
                  title:
                    hit1.inner_hits?.congress.hits.hits[0].highlight![
                      "congress.title_eng"
                    ][0]
                }
              }
            ],
            affiliationHighlights: [],
            socialMediaMentionsTotal: hit1._source?.microBloggingTotal || 0,
            referralsReceivedCount: hit1._source?.referralsReceivedCount,
            referralsSentCount: hit1._source?.referralsSentCount,
            sumPayments: hit1._source?.paymentTotal,
            diagnosesCount: hit1._source?.DRG_diagnosesCount,
            proceduresCount: hit1._source?.DRG_proceduresCount,
            prescriptionsCount: hit1._source?.num_prescriptions,
            trialEnrollmentRate: hit1._source?.trialEnrollmentRate,
            trialOngoingCount: 0,
            trialActivelyRecruitingCount: 0,
            congresses: hit1._source?.congressCount,
            grants: 0, // hard-coded to 0 since there's no data
            sumGrants: 0, // hard-coded to 0 since there's no data
            specialty: hit1._source?.specialty_eng,
            specialtyCmn: hit1._source?.specialty_cmn,
            specialtyJpn: hit1._source?.specialty_jpn,
            congressesDates: [], // from inner_hits
            paymentDates: [], // from inner_hits
            publicationDates: [], // from inner_hits
            trialDates: [], // from inner_hits
            citationCount: hit1._source?.citationTotal,
            citationCountAvg: 0,
            infoRequestsResolved: null,
            tags: [],
            languageCode: ENGLISH,
            designations: hit1._source?.designations,
            patientsDiversity: {
              age: [],
              races: [],
              sex: []
            },
            providerDiversity: {
              languagesSpoken: [],
              races: [],
              sex: []
            },
            diversityPercentile: hit1._source?.patientsDiversityPercentile,
            digitalRank: hit1._source?.digitalRank,
            top1PercentileDigitalRank: hit1._source?.top1PercentileDigitalRank,
            top10PercentileDigitalRank:
              hit1._source?.top10PercentileDigitalRank,
            twitterFollowersCount: hit1._source?.twitterFollowersCount,
            twitterTweetCount: hit1._source?.twitterTweetCount,
            locations: [],
            isOutsideUsersSlice: true,
            diversityRanking: null,
            debugData: expect.objectContaining({
              risingStar: {
                avgIndicationRisingScore:
                  hit1IndicationRisingScoreTotal / hit1TotalIndicationInnerHits,
                overallRisingScore: hit1._source?.overallRisingScore || 0
              }
            }),
            congressSessions: []
          },
          {
            personId: hit2._source?.id,
            h1dnId: hit2._source?.h1dn_id,
            name: hit2._source?.name_eng,
            firstName: hit2._source?.firstName_eng,
            middleName: hit2._source?.middleName_eng,
            lastName: hit2._source?.lastName_eng,
            nameEng: hit2._source?.name_eng,
            firstNameEng: hit2._source?.firstName_eng,
            lastNameEng: hit2._source?.lastName_eng,
            isFacultyOpinionsMember: hit2._source!.isFacultyOpinionsMember,
            personTranslationEng: {
              fullName: hit2._source!.name_eng,
              firstName: hit2._source!.firstName_eng,
              middleName: hit2._source!.middleName_eng,
              lastName: hit2._source!.lastName_eng,
              languageCode: ENGLISH
            },
            personTranslation: {
              fullName: hit2._source!.name_jpn,
              firstName: hit2._source!.firstName_jpn,
              middleName: hit2._source!.middleName_jpn,
              lastName: hit2._source!.lastName_jpn,
              languageCode: JAPANESE
            },
            affiliations: [
              expect.objectContaining({
                id: hit2._source!.affiliations![0].id
              }),
              expect.objectContaining({
                id: hit2._source!.affiliations![1].id
              })
            ],
            score: hit2._score!,
            scores: {
              normalizedRange: {
                min: 0,
                max: 0
              },
              personId: hit2._source?.id,
              h1Score: hit2._score!,
              publications: {
                minValue: 0,
                maxValue: 0,
                normalizedValue: 0,
                percentile: 0,
                value: (
                  hit2.inner_hits!.publications.hits.total as SearchTotalHits
                ).value
              },
              citations: {
                minValue: 0,
                maxValue: 0,
                normalizedValue: 0,
                percentile: 0,
                value: hit2CitationCount
              },
              trials: {
                minValue: 0,
                maxValue: 0,
                normalizedValue: 0,
                percentile: 0,
                value: (hit2.inner_hits!.trials.hits.total as SearchTotalHits)
                  .value
              },
              payments: {
                minValue: 0,
                maxValue: 0,
                normalizedValue: 0,
                percentile: 0,
                value: hit2PaymentAmountsTotal
              },
              paymentsCount: {
                minValue: 0,
                maxValue: 0,
                normalizedValue: 0,
                percentile: 0,
                value: (hit2.inner_hits!.payments.hits.total as SearchTotalHits)
                  .value
              },
              grants: {
                minValue: 0,
                maxValue: 0,
                normalizedValue: 0,
                percentile: 0,
                value: 0
              },
              grantsCount: {
                minValue: 0,
                maxValue: 0,
                normalizedValue: 0,
                percentile: 0,
                value: 0
              },
              congresses: {
                minValue: 0,
                maxValue: 0,
                normalizedValue: 0,
                percentile: 0,
                value: (hit2.inner_hits!.congress.hits.total as SearchTotalHits)
                  .value
              },
              collaborators: {
                minValue: 0,
                maxValue: 0,
                normalizedValue: 0,
                percentile: 0,
                value: 0
              },
              socialMediaMentions: {
                minValue: 0,
                maxValue: 0,
                normalizedValue: 0,
                percentile: 0,
                value: hit2SocialMediaCount
              },
              procedures: {
                minValue: 0,
                maxValue: 0,
                normalizedValue: 0,
                percentile: 0,
                value: hit2ProceduresAmountsTotal
              },
              diagnoses: {
                minValue: 0,
                maxValue: 0,
                normalizedValue: 0,
                percentile: 0,
                value: hit2DiagnosesAmountsTotal
              },
              prescriptions: {
                minValue: 0,
                maxValue: 0,
                normalizedValue: 0,
                percentile: 0,
                value: hit2PrescriptionsAmountsTotal
              },
              referralsReceived: {
                minValue: 0,
                maxValue: 0,
                normalizedValue: 0,
                percentile: 0,
                value: hit2._source?.referralsReceivedCount
              },
              referralsSent: {
                minValue: 0,
                maxValue: 0,
                normalizedValue: 0,
                percentile: 0,
                value: hit2._source?.referralsSentCount
              },
              twitterFollowersCount: {
                minValue: 0,
                maxValue: 0,
                normalizedValue: 0,
                percentile: 0,
                value: hit2._source?.twitterFollowersCount
              },
              twitterTweetCount: {
                minValue: 0,
                maxValue: 0,
                normalizedValue: 0,
                percentile: 0,
                value: hit2._source?.twitterTweetCount
              }
            },
            totalWorks: hit2._source?.totalWorks,
            countPublications: hit2._source?.publicationCount,
            countPresentWorkAffiliations:
              hit2._source?.presentWorkInstitutionCount,
            publicationsHighlights: [
              {
                publicationsId:
                  hit2.inner_hits?.publications.hits.hits[0].fields![
                    "publications.id"
                  ][0],
                highlight: {
                  keywords:
                    hit2.inner_hits?.publications.hits.hits[0].highlight![
                      "publications.keywords_eng"
                    ][0],
                  publicationAbstract:
                    hit2.inner_hits?.publications.hits.hits[0].highlight![
                      "publications.publicationAbstract_eng"
                    ][0],
                  title:
                    hit2.inner_hits?.publications.hits.hits[0].highlight![
                      "publications.title_eng"
                    ][0]
                }
              }
            ],
            countClinicalTrials: hit2._source?.trialCount,
            trialsHighlights: [
              {
                highlight: {
                  briefTitle:
                    hit2.inner_hits?.trials.hits.hits[0].highlight![
                      "trials.briefTitle_eng"
                    ][0],
                  conditions:
                    hit2.inner_hits?.trials.hits.hits[0].highlight![
                      "trials.conditions_eng"
                    ][0],
                  interventions:
                    hit2.inner_hits?.trials.hits.hits[0].highlight![
                      "trials.interventions_eng"
                    ][0],
                  keywords:
                    hit2.inner_hits?.trials.hits.hits[0].highlight![
                      "trials.keywords_eng"
                    ][0],
                  officialTitle:
                    hit2.inner_hits?.trials.hits.hits[0].highlight![
                      "trials.officialTitle_eng"
                    ][0],
                  summary:
                    hit2.inner_hits?.trials.hits.hits[0].highlight![
                      "trials.summary_eng"
                    ][0]
                },
                trialsId:
                  hit2.inner_hits?.trials.hits.hits[0].fields!["trials.id"][0]
              }
            ],
            claimsDiagnosesHighlights: [
              {
                highlight: {
                  codeAndDescription:
                    hit2.inner_hits?.DRG_diagnoses.hits.hits[0].highlight![
                      "DRG_diagnoses.codeAndDescription_eng"
                    ][0]
                }
              }
            ],
            claimsProceduresHighlights: [
              {
                highlight: {
                  codeAndDescription:
                    hit2.inner_hits?.DRG_procedures.hits.hits[0].highlight![
                      "DRG_procedures.codeAndDescription_eng"
                    ][0]
                }
              }
            ],
            congressHighlights: [
              {
                congressId:
                  hit2.inner_hits?.congress.hits.hits[0].fields![
                    "congress.id"
                  ][0],
                highlight: {
                  keywords:
                    hit2.inner_hits?.congress.hits.hits[0].highlight![
                      "congress.keywords_eng"
                    ][0],
                  title:
                    hit2.inner_hits?.congress.hits.hits[0].highlight![
                      "congress.title_eng"
                    ][0]
                }
              }
            ],
            affiliationHighlights: [],
            socialMediaMentionsTotal: hit2._source?.microBloggingTotal || 0,
            referralsReceivedCount: hit2._source?.referralsReceivedCount,
            referralsSentCount: hit2._source?.referralsSentCount,
            sumPayments: hit2._source?.paymentTotal,
            diagnosesCount: hit2._source?.DRG_diagnosesCount,
            proceduresCount: hit2._source?.DRG_proceduresCount,
            prescriptionsCount: hit2._source?.num_prescriptions,
            trialEnrollmentRate: hit2._source?.trialEnrollmentRate,
            trialOngoingCount: 0,
            trialActivelyRecruitingCount: 0,
            congresses: hit2._source?.congressCount,
            grants: 0, // hard-coded to 0 since there's no data
            sumGrants: 0, // hard-coded to 0 since there's no data
            specialty: hit2._source?.specialty_eng,
            specialtyCmn: hit2._source?.specialty_cmn,
            specialtyJpn: hit2._source?.specialty_jpn,
            congressesDates: [], // from inner_hits
            paymentDates: [], // from inner_hits
            publicationDates: [], // from inner_hits
            trialDates: [], // from inner_hits
            citationCount: hit2._source?.citationTotal,
            citationCountAvg: 0,
            infoRequestsResolved: null,
            tags: [],
            languageCode: ENGLISH,
            designations: hit2._source?.designations,
            patientsDiversity: {
              age: [],
              races: [],
              sex: []
            },
            providerDiversity: {
              languagesSpoken: [],
              races: [],
              sex: []
            },
            diversityPercentile: hit2._source?.patientsDiversityPercentile,
            digitalRank: hit2._source?.digitalRank,
            top1PercentileDigitalRank: hit2._source?.top1PercentileDigitalRank,
            top10PercentileDigitalRank:
              hit2._source?.top10PercentileDigitalRank,
            twitterFollowersCount: hit2._source?.twitterFollowersCount,
            twitterTweetCount: hit2._source?.twitterTweetCount,
            locations: [],
            isOutsideUsersSlice: true,
            diversityRanking: null,
            debugData: expect.objectContaining({
              risingStar: {
                avgIndicationRisingScore:
                  hit2IndicationRisingScoreTotal / hit2TotalIndicationInnerHits,
                overallRisingScore: hit2._source?.overallRisingScore || 0
              }
            }),
            congressSessions: []
          }
        ],
        ranges: {
          publicationCount: { min: 0, max: 0 },
          citationCount: { min: 0, max: 0 },
          trialCount: { min: 0, max: 0 },
          paymentCount: { min: 0, max: 0 },
          paymentSum: { min: 0, max: 0 },
          grantCount: { min: 0, max: 0 },
          grantSum: { min: 0, max: 0 },
          congressCount: { min: 0, max: 0 },
          totalCollaborators: { min: 0, max: 0 },
          socialMediaMentions: { min: 0, max: 0 },
          procedures: { min: 0, max: 0 },
          diagnoses: { min: 0, max: 0 },
          referralsReceived: { min: 0, max: 0 },
          referralsSent: { min: 0, max: 0 }
        },
        normalizedRange: { min: 0, max: 0 },
        filterCounts: [
          {
            field: "institutions",
            buckets: []
          },
          {
            field: "affiliations",
            buckets: []
          },
          {
            field: "journals",
            buckets: []
          },
          {
            field: "zipCode",
            buckets: []
          },
          {
            field: "country",
            buckets: []
          },
          {
            field: "specialty",
            buckets: []
          },
          {
            field: "city",
            buckets: []
          },
          {
            field: "procedureCodes",
            buckets: []
          },
          {
            field: "payerCompanies",
            buckets: []
          },
          {
            field: "trialPhases",
            buckets: []
          },
          {
            field: "publicationTypes",
            buckets: []
          },
          {
            field: "congressConferenceNames",
            buckets: []
          },
          {
            field: "medSchool",
            buckets: []
          },
          {
            field: "diagnosisCodes",
            buckets: []
          },
          {
            field: "natureOfPayments",
            buckets: []
          },
          {
            field: "designations",
            buckets: []
          },
          {
            field: "paymentAssociatedDrugs",
            buckets: []
          },
          {
            field: "congressOrganizerNames",
            buckets: []
          },
          {
            field: "referralsServiceLine",
            buckets: []
          },
          {
            field: "trialStatuses",
            buckets: []
          },
          {
            field: "state",
            buckets: []
          },
          {
            field: "trialTypes",
            buckets: []
          }
        ],
        synonyms,
        queryIntent,
        icdCodeSynonyms: [],
        totalResultsOutsideUserSlice: 0
      });
    });

    it("it should not return debug data for rising start when supplied filters does not has rising star", async () => {
      const page = {
        from: faker.datatype.number(),
        size: faker.datatype.number()
      };

      const hit1 = generateMockHit({
        affiliations: [generateMockAffiliation(), generateMockAffiliation()]
      });
      const hit2 = generateMockHit({
        affiliations: [generateMockAffiliation(), generateMockAffiliation()]
      });

      const hits: SearchHitsMetadata<HCPDocument> = {
        total: {
          value: faker.datatype.number()
        } as SearchTotalHits,
        hits: [hit1, hit2]
      };

      const input = generateKeywordSearchInput();

      const synonyms = generateMockSynonyms();
      const suppliedFilters = generateFilters({
        isRisingStar: {
          value: false
        }
      });
      const queryIntent = generateMockQueryIntents();

      const affiliationAdapterService = createMockInstance(
        AffiliationAdapterService
      );

      const keywordSearchResponseAdapterService =
        new KeywordSearchResponseAdapterService(affiliationAdapterService);

      const adaptedResponse = await keywordSearchResponseAdapterService.adapt(
        page,
        ENGLISH,
        hits,
        undefined,
        synonyms,
        suppliedFilters,
        queryIntent,
        [],
        generateKeywordSearchFeatureFlags({
          enableInternalUsersForRankingDebug: true
        }),
        [],
        [],
        undefined,
        undefined,
        input
      );

      const hit1CitationCount =
        hit1.inner_hits!.citations.hits.hits[0].fields![
          "publications.citationCount"
        ][0] +
        hit1.inner_hits!.citations.hits.hits[1].fields![
          "publications.citationCount"
        ][0];
      const hit1SocialMediaCount =
        hit1.inner_hits!.microBlogging.hits.hits[0].fields![
          "publications.microBloggingCount"
        ][0] +
        hit1.inner_hits!.microBlogging.hits.hits[1].fields![
          "publications.microBloggingCount"
        ][0];

      const hit1PaymentAmountsTotal =
        hit1.inner_hits!.payments.hits.hits[0].fields!["payments.amount"][0] +
        hit1.inner_hits!.payments.hits.hits[1].fields!["payments.amount"][0];
      const hit1DiagnosesAmountsTotal =
        hit1.inner_hits!.diagnoses_amount.hits.hits[0].fields![
          "DRG_diagnoses.internalCount"
        ][0] +
        hit1.inner_hits!.diagnoses_amount.hits.hits[1].fields![
          "DRG_diagnoses.internalCount"
        ][0];
      const hit1PrescriptionsAmountsTotal =
        hit1.inner_hits!.prescriptions_amount.hits.hits[0].fields![
          "prescriptions.num_prescriptions"
        ][0] +
        hit1.inner_hits!.prescriptions_amount.hits.hits[1].fields![
          "prescriptions.num_prescriptions"
        ][0];
      const hit1ProceduresAmountsTotal =
        hit1.inner_hits!.procedures_amount.hits.hits[0].fields![
          "DRG_procedures.internalCount"
        ][0] +
        hit1.inner_hits!.procedures_amount.hits.hits[1].fields![
          "DRG_procedures.internalCount"
        ][0];

      const hit2CitationCount =
        hit2.inner_hits!.citations.hits.hits[0].fields![
          "publications.citationCount"
        ][0] +
        hit2.inner_hits!.citations.hits.hits[1].fields![
          "publications.citationCount"
        ][0];
      const hit2SocialMediaCount =
        hit2.inner_hits!.microBlogging.hits.hits[0].fields![
          "publications.microBloggingCount"
        ][0] +
        hit2.inner_hits!.microBlogging.hits.hits[1].fields![
          "publications.microBloggingCount"
        ][0];
      const hit2PaymentAmountsTotal =
        hit2.inner_hits!.payments.hits.hits[0].fields!["payments.amount"][0] +
        hit2.inner_hits!.payments.hits.hits[1].fields!["payments.amount"][0];
      const hit2DiagnosesAmountsTotal =
        hit2.inner_hits!.diagnoses_amount.hits.hits[0].fields![
          "DRG_diagnoses.internalCount"
        ][0] +
        hit2.inner_hits!.diagnoses_amount.hits.hits[1].fields![
          "DRG_diagnoses.internalCount"
        ][0];
      const hit2PrescriptionsAmountsTotal =
        hit2.inner_hits!.prescriptions_amount.hits.hits[0].fields![
          "prescriptions.num_prescriptions"
        ][0] +
        hit2.inner_hits!.prescriptions_amount.hits.hits[1].fields![
          "prescriptions.num_prescriptions"
        ][0];
      const hit2ProceduresAmountsTotal =
        hit2.inner_hits!.procedures_amount.hits.hits[0].fields![
          "DRG_procedures.internalCount"
        ][0] +
        hit2.inner_hits!.procedures_amount.hits.hits[1].fields![
          "DRG_procedures.internalCount"
        ][0];

      expect(adaptedResponse).toEqual({
        from: page.from,
        pageSize: page.size,
        total: (hits.total as SearchTotalHits).value,
        results: [
          {
            personId: hit1._source?.id,
            h1dnId: hit1._source?.h1dn_id,
            name: hit1._source?.name_eng,
            firstName: hit1._source?.firstName_eng,
            middleName: hit1._source?.middleName_eng,
            lastName: hit1._source?.lastName_eng,
            nameEng: hit1._source?.name_eng,
            firstNameEng: hit1._source?.firstName_eng,
            lastNameEng: hit1._source?.lastName_eng,
            isFacultyOpinionsMember: hit1._source!.isFacultyOpinionsMember,
            personTranslationEng: {
              fullName: hit1._source!.name_eng,
              firstName: hit1._source!.firstName_eng,
              middleName: hit1._source!.middleName_eng,
              lastName: hit1._source!.lastName_eng,
              languageCode: ENGLISH
            },
            personTranslation: {
              fullName: hit1._source!.name_jpn,
              firstName: hit1._source!.firstName_jpn,
              middleName: hit1._source!.middleName_jpn,
              lastName: hit1._source!.lastName_jpn,
              languageCode: JAPANESE
            },
            affiliations: [
              expect.objectContaining({
                id: hit1._source!.affiliations![0].id
              }),
              expect.objectContaining({
                id: hit1._source!.affiliations![1].id
              })
            ],
            score: hit1._score!,
            scores: {
              normalizedRange: {
                min: 0,
                max: 0
              },
              personId: hit1._source?.id,
              h1Score: hit1._score!,
              publications: {
                minValue: 0,
                maxValue: 0,
                normalizedValue: 0,
                percentile: 0,
                value: (
                  hit1.inner_hits!.publications.hits.total as SearchTotalHits
                ).value
              },
              citations: {
                minValue: 0,
                maxValue: 0,
                normalizedValue: 0,
                percentile: 0,
                value: hit1CitationCount
              },
              trials: {
                minValue: 0,
                maxValue: 0,
                normalizedValue: 0,
                percentile: 0,
                value: (hit1.inner_hits!.trials.hits.total as SearchTotalHits)
                  .value
              },
              payments: {
                minValue: 0,
                maxValue: 0,
                normalizedValue: 0,
                percentile: 0,
                value: hit1PaymentAmountsTotal
              },
              paymentsCount: {
                minValue: 0,
                maxValue: 0,
                normalizedValue: 0,
                percentile: 0,
                value: (hit1.inner_hits!.payments.hits.total as SearchTotalHits)
                  .value
              },
              grants: {
                minValue: 0,
                maxValue: 0,
                normalizedValue: 0,
                percentile: 0,
                value: 0
              },
              grantsCount: {
                minValue: 0,
                maxValue: 0,
                normalizedValue: 0,
                percentile: 0,
                value: 0
              },
              congresses: {
                minValue: 0,
                maxValue: 0,
                normalizedValue: 0,
                percentile: 0,
                value: (hit1.inner_hits!.congress.hits.total as SearchTotalHits)
                  .value
              },
              collaborators: {
                minValue: 0,
                maxValue: 0,
                normalizedValue: 0,
                percentile: 0,
                value: 0
              },
              socialMediaMentions: {
                minValue: 0,
                maxValue: 0,
                normalizedValue: 0,
                percentile: 0,
                value: hit1SocialMediaCount
              },
              procedures: {
                minValue: 0,
                maxValue: 0,
                normalizedValue: 0,
                percentile: 0,
                value: hit1ProceduresAmountsTotal
              },
              diagnoses: {
                minValue: 0,
                maxValue: 0,
                normalizedValue: 0,
                percentile: 0,
                value: hit1DiagnosesAmountsTotal
              },
              prescriptions: {
                minValue: 0,
                maxValue: 0,
                normalizedValue: 0,
                percentile: 0,
                value: hit1PrescriptionsAmountsTotal
              },
              referralsReceived: {
                minValue: 0,
                maxValue: 0,
                normalizedValue: 0,
                percentile: 0,
                value: hit1._source?.referralsReceivedCount
              },
              referralsSent: {
                minValue: 0,
                maxValue: 0,
                normalizedValue: 0,
                percentile: 0,
                value: hit1._source?.referralsSentCount
              },
              twitterFollowersCount: {
                minValue: 0,
                maxValue: 0,
                normalizedValue: 0,
                percentile: 0,
                value: hit1._source?.twitterFollowersCount
              },
              twitterTweetCount: {
                minValue: 0,
                maxValue: 0,
                normalizedValue: 0,
                percentile: 0,
                value: hit1._source?.twitterTweetCount
              }
            },
            totalWorks: hit1._source?.totalWorks,
            countPublications: hit1._source?.publicationCount,
            countPresentWorkAffiliations:
              hit1._source?.presentWorkInstitutionCount,
            publicationsHighlights: [
              {
                publicationsId:
                  hit1.inner_hits?.publications.hits.hits[0].fields![
                    "publications.id"
                  ][0],
                highlight: {
                  keywords:
                    hit1.inner_hits?.publications.hits.hits[0].highlight![
                      "publications.keywords_eng"
                    ][0],
                  publicationAbstract:
                    hit1.inner_hits?.publications.hits.hits[0].highlight![
                      "publications.publicationAbstract_eng"
                    ][0],
                  title:
                    hit1.inner_hits?.publications.hits.hits[0].highlight![
                      "publications.title_eng"
                    ][0]
                }
              }
            ],
            countClinicalTrials: hit1._source?.trialCount,
            trialsHighlights: [
              {
                highlight: {
                  briefTitle:
                    hit1.inner_hits?.trials.hits.hits[0].highlight![
                      "trials.briefTitle_eng"
                    ][0],
                  conditions:
                    hit1.inner_hits?.trials.hits.hits[0].highlight![
                      "trials.conditions_eng"
                    ][0],
                  interventions:
                    hit1.inner_hits?.trials.hits.hits[0].highlight![
                      "trials.interventions_eng"
                    ][0],
                  keywords:
                    hit1.inner_hits?.trials.hits.hits[0].highlight![
                      "trials.keywords_eng"
                    ][0],
                  officialTitle:
                    hit1.inner_hits?.trials.hits.hits[0].highlight![
                      "trials.officialTitle_eng"
                    ][0],
                  summary:
                    hit1.inner_hits?.trials.hits.hits[0].highlight![
                      "trials.summary_eng"
                    ][0]
                },
                trialsId:
                  hit1.inner_hits?.trials.hits.hits[0].fields!["trials.id"][0]
              }
            ],
            claimsDiagnosesHighlights: [
              {
                highlight: {
                  codeAndDescription:
                    hit1.inner_hits?.DRG_diagnoses.hits.hits[0].highlight![
                      "DRG_diagnoses.codeAndDescription_eng"
                    ][0]
                }
              }
            ],
            claimsProceduresHighlights: [
              {
                highlight: {
                  codeAndDescription:
                    hit1.inner_hits?.DRG_procedures.hits.hits[0].highlight![
                      "DRG_procedures.codeAndDescription_eng"
                    ][0]
                }
              }
            ],
            congressHighlights: [
              {
                congressId:
                  hit1.inner_hits?.congress.hits.hits[0].fields![
                    "congress.id"
                  ][0],
                highlight: {
                  keywords:
                    hit1.inner_hits?.congress.hits.hits[0].highlight![
                      "congress.keywords_eng"
                    ][0],
                  title:
                    hit1.inner_hits?.congress.hits.hits[0].highlight![
                      "congress.title_eng"
                    ][0]
                }
              }
            ],
            affiliationHighlights: [],
            socialMediaMentionsTotal: hit1._source?.microBloggingTotal || 0,
            referralsReceivedCount: hit1._source?.referralsReceivedCount,
            referralsSentCount: hit1._source?.referralsSentCount,
            sumPayments: hit1._source?.paymentTotal,
            diagnosesCount: hit1._source?.DRG_diagnosesCount,
            proceduresCount: hit1._source?.DRG_proceduresCount,
            prescriptionsCount: hit1._source?.num_prescriptions,
            trialEnrollmentRate: hit1._source?.trialEnrollmentRate,
            trialOngoingCount: 0,
            trialActivelyRecruitingCount: 0,
            congresses: hit1._source?.congressCount,
            grants: 0, // hard-coded to 0 since there's no data
            sumGrants: 0, // hard-coded to 0 since there's no data
            specialty: hit1._source?.specialty_eng,
            specialtyCmn: hit1._source?.specialty_cmn,
            specialtyJpn: hit1._source?.specialty_jpn,
            congressesDates: [], // from inner_hits
            paymentDates: [], // from inner_hits
            publicationDates: [], // from inner_hits
            trialDates: [], // from inner_hits
            citationCount: hit1._source?.citationTotal,
            citationCountAvg: 0,
            infoRequestsResolved: null,
            tags: [],
            languageCode: ENGLISH,
            designations: hit1._source?.designations,
            patientsDiversity: {
              age: [],
              races: [],
              sex: []
            },
            providerDiversity: {
              languagesSpoken: [],
              races: [],
              sex: []
            },
            diversityPercentile: hit1._source?.patientsDiversityPercentile,
            digitalRank: hit1._source?.digitalRank,
            top1PercentileDigitalRank: hit1._source?.top1PercentileDigitalRank,
            top10PercentileDigitalRank:
              hit1._source?.top10PercentileDigitalRank,
            twitterFollowersCount: hit1._source?.twitterFollowersCount,
            twitterTweetCount: hit1._source?.twitterTweetCount,
            locations: [],
            isOutsideUsersSlice: true,
            diversityRanking: null,
            debugData: {
              risingStar: undefined
            },
            congressSessions: []
          },
          {
            personId: hit2._source?.id,
            h1dnId: hit2._source?.h1dn_id,
            name: hit2._source?.name_eng,
            firstName: hit2._source?.firstName_eng,
            middleName: hit2._source?.middleName_eng,
            lastName: hit2._source?.lastName_eng,
            nameEng: hit2._source?.name_eng,
            firstNameEng: hit2._source?.firstName_eng,
            lastNameEng: hit2._source?.lastName_eng,
            isFacultyOpinionsMember: hit2._source!.isFacultyOpinionsMember,
            personTranslationEng: {
              fullName: hit2._source!.name_eng,
              firstName: hit2._source!.firstName_eng,
              middleName: hit2._source!.middleName_eng,
              lastName: hit2._source!.lastName_eng,
              languageCode: ENGLISH
            },
            personTranslation: {
              fullName: hit2._source!.name_jpn,
              firstName: hit2._source!.firstName_jpn,
              middleName: hit2._source!.middleName_jpn,
              lastName: hit2._source!.lastName_jpn,
              languageCode: JAPANESE
            },
            affiliations: [
              expect.objectContaining({
                id: hit2._source!.affiliations![0].id
              }),
              expect.objectContaining({
                id: hit2._source!.affiliations![1].id
              })
            ],
            score: hit2._score!,
            scores: {
              normalizedRange: {
                min: 0,
                max: 0
              },
              personId: hit2._source?.id,
              h1Score: hit2._score!,
              publications: {
                minValue: 0,
                maxValue: 0,
                normalizedValue: 0,
                percentile: 0,
                value: (
                  hit2.inner_hits!.publications.hits.total as SearchTotalHits
                ).value
              },
              citations: {
                minValue: 0,
                maxValue: 0,
                normalizedValue: 0,
                percentile: 0,
                value: hit2CitationCount
              },
              trials: {
                minValue: 0,
                maxValue: 0,
                normalizedValue: 0,
                percentile: 0,
                value: (hit2.inner_hits!.trials.hits.total as SearchTotalHits)
                  .value
              },
              payments: {
                minValue: 0,
                maxValue: 0,
                normalizedValue: 0,
                percentile: 0,
                value: hit2PaymentAmountsTotal
              },
              paymentsCount: {
                minValue: 0,
                maxValue: 0,
                normalizedValue: 0,
                percentile: 0,
                value: (hit2.inner_hits!.payments.hits.total as SearchTotalHits)
                  .value
              },
              grants: {
                minValue: 0,
                maxValue: 0,
                normalizedValue: 0,
                percentile: 0,
                value: 0
              },
              grantsCount: {
                minValue: 0,
                maxValue: 0,
                normalizedValue: 0,
                percentile: 0,
                value: 0
              },
              congresses: {
                minValue: 0,
                maxValue: 0,
                normalizedValue: 0,
                percentile: 0,
                value: (hit2.inner_hits!.congress.hits.total as SearchTotalHits)
                  .value
              },
              collaborators: {
                minValue: 0,
                maxValue: 0,
                normalizedValue: 0,
                percentile: 0,
                value: 0
              },
              socialMediaMentions: {
                minValue: 0,
                maxValue: 0,
                normalizedValue: 0,
                percentile: 0,
                value: hit2SocialMediaCount
              },
              procedures: {
                minValue: 0,
                maxValue: 0,
                normalizedValue: 0,
                percentile: 0,
                value: hit2ProceduresAmountsTotal
              },
              diagnoses: {
                minValue: 0,
                maxValue: 0,
                normalizedValue: 0,
                percentile: 0,
                value: hit2DiagnosesAmountsTotal
              },
              prescriptions: {
                minValue: 0,
                maxValue: 0,
                normalizedValue: 0,
                percentile: 0,
                value: hit2PrescriptionsAmountsTotal
              },
              referralsReceived: {
                minValue: 0,
                maxValue: 0,
                normalizedValue: 0,
                percentile: 0,
                value: hit2._source?.referralsReceivedCount
              },
              referralsSent: {
                minValue: 0,
                maxValue: 0,
                normalizedValue: 0,
                percentile: 0,
                value: hit2._source?.referralsSentCount
              },
              twitterFollowersCount: {
                minValue: 0,
                maxValue: 0,
                normalizedValue: 0,
                percentile: 0,
                value: hit2._source?.twitterFollowersCount
              },
              twitterTweetCount: {
                minValue: 0,
                maxValue: 0,
                normalizedValue: 0,
                percentile: 0,
                value: hit2._source?.twitterTweetCount
              }
            },
            totalWorks: hit2._source?.totalWorks,
            countPublications: hit2._source?.publicationCount,
            countPresentWorkAffiliations:
              hit2._source?.presentWorkInstitutionCount,
            publicationsHighlights: [
              {
                publicationsId:
                  hit2.inner_hits?.publications.hits.hits[0].fields![
                    "publications.id"
                  ][0],
                highlight: {
                  keywords:
                    hit2.inner_hits?.publications.hits.hits[0].highlight![
                      "publications.keywords_eng"
                    ][0],
                  publicationAbstract:
                    hit2.inner_hits?.publications.hits.hits[0].highlight![
                      "publications.publicationAbstract_eng"
                    ][0],
                  title:
                    hit2.inner_hits?.publications.hits.hits[0].highlight![
                      "publications.title_eng"
                    ][0]
                }
              }
            ],
            countClinicalTrials: hit2._source?.trialCount,
            trialsHighlights: [
              {
                highlight: {
                  briefTitle:
                    hit2.inner_hits?.trials.hits.hits[0].highlight![
                      "trials.briefTitle_eng"
                    ][0],
                  conditions:
                    hit2.inner_hits?.trials.hits.hits[0].highlight![
                      "trials.conditions_eng"
                    ][0],
                  interventions:
                    hit2.inner_hits?.trials.hits.hits[0].highlight![
                      "trials.interventions_eng"
                    ][0],
                  keywords:
                    hit2.inner_hits?.trials.hits.hits[0].highlight![
                      "trials.keywords_eng"
                    ][0],
                  officialTitle:
                    hit2.inner_hits?.trials.hits.hits[0].highlight![
                      "trials.officialTitle_eng"
                    ][0],
                  summary:
                    hit2.inner_hits?.trials.hits.hits[0].highlight![
                      "trials.summary_eng"
                    ][0]
                },
                trialsId:
                  hit2.inner_hits?.trials.hits.hits[0].fields!["trials.id"][0]
              }
            ],
            claimsDiagnosesHighlights: [
              {
                highlight: {
                  codeAndDescription:
                    hit2.inner_hits?.DRG_diagnoses.hits.hits[0].highlight![
                      "DRG_diagnoses.codeAndDescription_eng"
                    ][0]
                }
              }
            ],
            claimsProceduresHighlights: [
              {
                highlight: {
                  codeAndDescription:
                    hit2.inner_hits?.DRG_procedures.hits.hits[0].highlight![
                      "DRG_procedures.codeAndDescription_eng"
                    ][0]
                }
              }
            ],
            congressHighlights: [
              {
                congressId:
                  hit2.inner_hits?.congress.hits.hits[0].fields![
                    "congress.id"
                  ][0],
                highlight: {
                  keywords:
                    hit2.inner_hits?.congress.hits.hits[0].highlight![
                      "congress.keywords_eng"
                    ][0],
                  title:
                    hit2.inner_hits?.congress.hits.hits[0].highlight![
                      "congress.title_eng"
                    ][0]
                }
              }
            ],
            affiliationHighlights: [],
            socialMediaMentionsTotal: hit2._source?.microBloggingTotal || 0,
            referralsReceivedCount: hit2._source?.referralsReceivedCount,
            referralsSentCount: hit2._source?.referralsSentCount,
            sumPayments: hit2._source?.paymentTotal,
            diagnosesCount: hit2._source?.DRG_diagnosesCount,
            proceduresCount: hit2._source?.DRG_proceduresCount,
            prescriptionsCount: hit2._source?.num_prescriptions,
            trialEnrollmentRate: hit2._source?.trialEnrollmentRate,
            trialOngoingCount: 0,
            trialActivelyRecruitingCount: 0,
            congresses: hit2._source?.congressCount,
            grants: 0, // hard-coded to 0 since there's no data
            sumGrants: 0, // hard-coded to 0 since there's no data
            specialty: hit2._source?.specialty_eng,
            specialtyCmn: hit2._source?.specialty_cmn,
            specialtyJpn: hit2._source?.specialty_jpn,
            congressesDates: [], // from inner_hits
            paymentDates: [], // from inner_hits
            publicationDates: [], // from inner_hits
            trialDates: [], // from inner_hits
            citationCount: hit2._source?.citationTotal,
            citationCountAvg: 0,
            infoRequestsResolved: null,
            tags: [],
            languageCode: ENGLISH,
            designations: hit2._source?.designations,
            patientsDiversity: {
              age: [],
              races: [],
              sex: []
            },
            providerDiversity: {
              languagesSpoken: [],
              races: [],
              sex: []
            },
            diversityPercentile: hit2._source?.patientsDiversityPercentile,
            digitalRank: hit2._source?.digitalRank,
            top1PercentileDigitalRank: hit2._source?.top1PercentileDigitalRank,
            top10PercentileDigitalRank:
              hit2._source?.top10PercentileDigitalRank,
            twitterFollowersCount: hit2._source?.twitterFollowersCount,
            twitterTweetCount: hit2._source?.twitterTweetCount,
            locations: [],
            isOutsideUsersSlice: true,
            diversityRanking: null,
            debugData: {
              risingStar: undefined
            },
            congressSessions: []
          }
        ],
        ranges: {
          publicationCount: { min: 0, max: 0 },
          citationCount: { min: 0, max: 0 },
          trialCount: { min: 0, max: 0 },
          paymentCount: { min: 0, max: 0 },
          paymentSum: { min: 0, max: 0 },
          grantCount: { min: 0, max: 0 },
          grantSum: { min: 0, max: 0 },
          congressCount: { min: 0, max: 0 },
          totalCollaborators: { min: 0, max: 0 },
          socialMediaMentions: { min: 0, max: 0 },
          procedures: { min: 0, max: 0 },
          diagnoses: { min: 0, max: 0 },
          referralsReceived: { min: 0, max: 0 },
          referralsSent: { min: 0, max: 0 }
        },
        normalizedRange: { min: 0, max: 0 },
        filterCounts: [
          {
            field: "institutions",
            buckets: []
          },
          {
            field: "affiliations",
            buckets: []
          },
          {
            field: "journals",
            buckets: []
          },
          {
            field: "zipCode",
            buckets: []
          },
          {
            field: "country",
            buckets: []
          },
          {
            field: "specialty",
            buckets: []
          },
          {
            field: "city",
            buckets: []
          },
          {
            field: "procedureCodes",
            buckets: []
          },
          {
            field: "payerCompanies",
            buckets: []
          },
          {
            field: "trialPhases",
            buckets: []
          },
          {
            field: "publicationTypes",
            buckets: []
          },
          {
            field: "congressConferenceNames",
            buckets: []
          },
          {
            field: "medSchool",
            buckets: []
          },
          {
            field: "diagnosisCodes",
            buckets: []
          },
          {
            field: "natureOfPayments",
            buckets: []
          },
          {
            field: "designations",
            buckets: []
          },
          {
            field: "paymentAssociatedDrugs",
            buckets: []
          },
          {
            field: "congressOrganizerNames",
            buckets: []
          },
          {
            field: "referralsServiceLine",
            buckets: []
          },
          {
            field: "trialStatuses",
            buckets: []
          },
          {
            field: "state",
            buckets: []
          },
          {
            field: "trialTypes",
            buckets: []
          }
        ],
        synonyms,
        queryIntent,
        icdCodeSynonyms: [],
        totalResultsOutsideUserSlice: 0
      });
    });

    it("it should return debug data only if user is a internal user enableInternalUsersForRankingDebug is on", async () => {
      const page = {
        from: faker.datatype.number(),
        size: faker.datatype.number()
      };

      const hit1 = generateMockHit({
        affiliations: [generateMockAffiliation(), generateMockAffiliation()]
      });
      const hit2 = generateMockHit({
        affiliations: [generateMockAffiliation(), generateMockAffiliation()]
      });

      const hits: SearchHitsMetadata<HCPDocument> = {
        total: {
          value: faker.datatype.number()
        } as SearchTotalHits,
        hits: [hit1, hit2]
      };

      const input = generateKeywordSearchInput();

      const synonyms = generateMockSynonyms();
      const suppliedFilters = generateFilters({
        isRisingStar: {
          value: true
        }
      });
      const queryIntent = generateMockQueryIntents();

      const affiliationAdapterService = createMockInstance(
        AffiliationAdapterService
      );

      const keywordSearchResponseAdapterService =
        new KeywordSearchResponseAdapterService(affiliationAdapterService);

      const adaptedResponse = await keywordSearchResponseAdapterService.adapt(
        page,
        ENGLISH,
        hits,
        undefined,
        synonyms,
        suppliedFilters,
        queryIntent,
        [],
        generateKeywordSearchFeatureFlags({
          enableInternalUsersForRankingDebug: true
        }),
        [],
        [],
        undefined,
        undefined,
        input
      );

      const hit1CitationCount =
        hit1.inner_hits!.citations.hits.hits[0].fields![
          "publications.citationCount"
        ][0] +
        hit1.inner_hits!.citations.hits.hits[1].fields![
          "publications.citationCount"
        ][0];
      const hit1SocialMediaCount =
        hit1.inner_hits!.microBlogging.hits.hits[0].fields![
          "publications.microBloggingCount"
        ][0] +
        hit1.inner_hits!.microBlogging.hits.hits[1].fields![
          "publications.microBloggingCount"
        ][0];

      const hit1TotalIndicationInnerHits = (
        hit1.inner_hits!.indications.hits?.total as SearchTotalHits
      ).value;
      const hit1IndicationRisingScoreTotal =
        hit1.inner_hits!.indications.hits.hits[0].fields![
          "indications.indicationRisingScore"
        ][0] +
        hit1.inner_hits!.indications.hits.hits[1].fields![
          "indications.indicationRisingScore"
        ][0];
      const hit1PaymentAmountsTotal =
        hit1.inner_hits!.payments.hits.hits[0].fields!["payments.amount"][0] +
        hit1.inner_hits!.payments.hits.hits[1].fields!["payments.amount"][0];
      const hit1DiagnosesAmountsTotal =
        hit1.inner_hits!.diagnoses_amount.hits.hits[0].fields![
          "DRG_diagnoses.internalCount"
        ][0] +
        hit1.inner_hits!.diagnoses_amount.hits.hits[1].fields![
          "DRG_diagnoses.internalCount"
        ][0];
      const hit1PrescriptionsAmountsTotal =
        hit1.inner_hits!.prescriptions_amount.hits.hits[0].fields![
          "prescriptions.num_prescriptions"
        ][0] +
        hit1.inner_hits!.prescriptions_amount.hits.hits[1].fields![
          "prescriptions.num_prescriptions"
        ][0];
      const hit1ProceduresAmountsTotal =
        hit1.inner_hits!.procedures_amount.hits.hits[0].fields![
          "DRG_procedures.internalCount"
        ][0] +
        hit1.inner_hits!.procedures_amount.hits.hits[1].fields![
          "DRG_procedures.internalCount"
        ][0];

      const hit2CitationCount =
        hit2.inner_hits!.citations.hits.hits[0].fields![
          "publications.citationCount"
        ][0] +
        hit2.inner_hits!.citations.hits.hits[1].fields![
          "publications.citationCount"
        ][0];
      const hit2SocialMediaCount =
        hit2.inner_hits!.microBlogging.hits.hits[0].fields![
          "publications.microBloggingCount"
        ][0] +
        hit2.inner_hits!.microBlogging.hits.hits[1].fields![
          "publications.microBloggingCount"
        ][0];
      const hit2TotalIndicationInnerHits = (
        hit2.inner_hits!.indications.hits?.total as SearchTotalHits
      ).value;
      const hit2IndicationRisingScoreTotal =
        hit2.inner_hits!.indications.hits.hits[0].fields![
          "indications.indicationRisingScore"
        ][0] +
        hit2.inner_hits!.indications.hits.hits[1].fields![
          "indications.indicationRisingScore"
        ][0];
      const hit2PaymentAmountsTotal =
        hit2.inner_hits!.payments.hits.hits[0].fields!["payments.amount"][0] +
        hit2.inner_hits!.payments.hits.hits[1].fields!["payments.amount"][0];
      const hit2DiagnosesAmountsTotal =
        hit2.inner_hits!.diagnoses_amount.hits.hits[0].fields![
          "DRG_diagnoses.internalCount"
        ][0] +
        hit2.inner_hits!.diagnoses_amount.hits.hits[1].fields![
          "DRG_diagnoses.internalCount"
        ][0];
      const hit2PrescriptionsAmountsTotal =
        hit2.inner_hits!.prescriptions_amount.hits.hits[0].fields![
          "prescriptions.num_prescriptions"
        ][0] +
        hit2.inner_hits!.prescriptions_amount.hits.hits[1].fields![
          "prescriptions.num_prescriptions"
        ][0];
      const hit2ProceduresAmountsTotal =
        hit2.inner_hits!.procedures_amount.hits.hits[0].fields![
          "DRG_procedures.internalCount"
        ][0] +
        hit2.inner_hits!.procedures_amount.hits.hits[1].fields![
          "DRG_procedures.internalCount"
        ][0];

      expect(adaptedResponse).toEqual({
        from: page.from,
        pageSize: page.size,
        total: (hits.total as SearchTotalHits).value,
        results: [
          {
            personId: hit1._source?.id,
            h1dnId: hit1._source?.h1dn_id,
            name: hit1._source?.name_eng,
            firstName: hit1._source?.firstName_eng,
            middleName: hit1._source?.middleName_eng,
            lastName: hit1._source?.lastName_eng,
            nameEng: hit1._source?.name_eng,
            firstNameEng: hit1._source?.firstName_eng,
            lastNameEng: hit1._source?.lastName_eng,
            isFacultyOpinionsMember: hit1._source!.isFacultyOpinionsMember,
            personTranslationEng: {
              fullName: hit1._source!.name_eng,
              firstName: hit1._source!.firstName_eng,
              middleName: hit1._source!.middleName_eng,
              lastName: hit1._source!.lastName_eng,
              languageCode: ENGLISH
            },
            personTranslation: {
              fullName: hit1._source!.name_jpn,
              firstName: hit1._source!.firstName_jpn,
              middleName: hit1._source!.middleName_jpn,
              lastName: hit1._source!.lastName_jpn,
              languageCode: JAPANESE
            },
            affiliations: [
              expect.objectContaining({
                id: hit1._source!.affiliations![0].id
              }),
              expect.objectContaining({
                id: hit1._source!.affiliations![1].id
              })
            ],
            score: hit1._score!,
            scores: {
              normalizedRange: {
                min: 0,
                max: 0
              },
              personId: hit1._source?.id,
              h1Score: hit1._score!,
              publications: {
                minValue: 0,
                maxValue: 0,
                normalizedValue: 0,
                percentile: 0,
                value: (
                  hit1.inner_hits!.publications.hits.total as SearchTotalHits
                ).value
              },
              citations: {
                minValue: 0,
                maxValue: 0,
                normalizedValue: 0,
                percentile: 0,
                value: hit1CitationCount
              },
              trials: {
                minValue: 0,
                maxValue: 0,
                normalizedValue: 0,
                percentile: 0,
                value: (hit1.inner_hits!.trials.hits.total as SearchTotalHits)
                  .value
              },
              payments: {
                minValue: 0,
                maxValue: 0,
                normalizedValue: 0,
                percentile: 0,
                value: hit1PaymentAmountsTotal
              },
              paymentsCount: {
                minValue: 0,
                maxValue: 0,
                normalizedValue: 0,
                percentile: 0,
                value: (hit1.inner_hits!.payments.hits.total as SearchTotalHits)
                  .value
              },
              grants: {
                minValue: 0,
                maxValue: 0,
                normalizedValue: 0,
                percentile: 0,
                value: 0
              },
              grantsCount: {
                minValue: 0,
                maxValue: 0,
                normalizedValue: 0,
                percentile: 0,
                value: 0
              },
              congresses: {
                minValue: 0,
                maxValue: 0,
                normalizedValue: 0,
                percentile: 0,
                value: (hit1.inner_hits!.congress.hits.total as SearchTotalHits)
                  .value
              },
              collaborators: {
                minValue: 0,
                maxValue: 0,
                normalizedValue: 0,
                percentile: 0,
                value: 0
              },
              socialMediaMentions: {
                minValue: 0,
                maxValue: 0,
                normalizedValue: 0,
                percentile: 0,
                value: hit1SocialMediaCount
              },
              procedures: {
                minValue: 0,
                maxValue: 0,
                normalizedValue: 0,
                percentile: 0,
                value: hit1ProceduresAmountsTotal
              },
              diagnoses: {
                minValue: 0,
                maxValue: 0,
                normalizedValue: 0,
                percentile: 0,
                value: hit1DiagnosesAmountsTotal
              },
              prescriptions: {
                minValue: 0,
                maxValue: 0,
                normalizedValue: 0,
                percentile: 0,
                value: hit1PrescriptionsAmountsTotal
              },
              referralsReceived: {
                minValue: 0,
                maxValue: 0,
                normalizedValue: 0,
                percentile: 0,
                value: hit1._source?.referralsReceivedCount
              },
              referralsSent: {
                minValue: 0,
                maxValue: 0,
                normalizedValue: 0,
                percentile: 0,
                value: hit1._source?.referralsSentCount
              },
              twitterFollowersCount: {
                minValue: 0,
                maxValue: 0,
                normalizedValue: 0,
                percentile: 0,
                value: hit1._source?.twitterFollowersCount
              },
              twitterTweetCount: {
                minValue: 0,
                maxValue: 0,
                normalizedValue: 0,
                percentile: 0,
                value: hit1._source?.twitterTweetCount
              }
            },
            totalWorks: hit1._source?.totalWorks,
            countPublications: hit1._source?.publicationCount,
            countPresentWorkAffiliations:
              hit1._source?.presentWorkInstitutionCount,
            publicationsHighlights: [
              {
                publicationsId:
                  hit1.inner_hits?.publications.hits.hits[0].fields![
                    "publications.id"
                  ][0],
                highlight: {
                  keywords:
                    hit1.inner_hits?.publications.hits.hits[0].highlight![
                      "publications.keywords_eng"
                    ][0],
                  publicationAbstract:
                    hit1.inner_hits?.publications.hits.hits[0].highlight![
                      "publications.publicationAbstract_eng"
                    ][0],
                  title:
                    hit1.inner_hits?.publications.hits.hits[0].highlight![
                      "publications.title_eng"
                    ][0]
                }
              }
            ],
            countClinicalTrials: hit1._source?.trialCount,
            trialsHighlights: [
              {
                highlight: {
                  briefTitle:
                    hit1.inner_hits?.trials.hits.hits[0].highlight![
                      "trials.briefTitle_eng"
                    ][0],
                  conditions:
                    hit1.inner_hits?.trials.hits.hits[0].highlight![
                      "trials.conditions_eng"
                    ][0],
                  interventions:
                    hit1.inner_hits?.trials.hits.hits[0].highlight![
                      "trials.interventions_eng"
                    ][0],
                  keywords:
                    hit1.inner_hits?.trials.hits.hits[0].highlight![
                      "trials.keywords_eng"
                    ][0],
                  officialTitle:
                    hit1.inner_hits?.trials.hits.hits[0].highlight![
                      "trials.officialTitle_eng"
                    ][0],
                  summary:
                    hit1.inner_hits?.trials.hits.hits[0].highlight![
                      "trials.summary_eng"
                    ][0]
                },
                trialsId:
                  hit1.inner_hits?.trials.hits.hits[0].fields!["trials.id"][0]
              }
            ],
            claimsDiagnosesHighlights: [
              {
                highlight: {
                  codeAndDescription:
                    hit1.inner_hits?.DRG_diagnoses.hits.hits[0].highlight![
                      "DRG_diagnoses.codeAndDescription_eng"
                    ][0]
                }
              }
            ],
            claimsProceduresHighlights: [
              {
                highlight: {
                  codeAndDescription:
                    hit1.inner_hits?.DRG_procedures.hits.hits[0].highlight![
                      "DRG_procedures.codeAndDescription_eng"
                    ][0]
                }
              }
            ],
            congressHighlights: [
              {
                congressId:
                  hit1.inner_hits?.congress.hits.hits[0].fields![
                    "congress.id"
                  ][0],
                highlight: {
                  keywords:
                    hit1.inner_hits?.congress.hits.hits[0].highlight![
                      "congress.keywords_eng"
                    ][0],
                  title:
                    hit1.inner_hits?.congress.hits.hits[0].highlight![
                      "congress.title_eng"
                    ][0]
                }
              }
            ],
            affiliationHighlights: [],
            socialMediaMentionsTotal: hit1._source?.microBloggingTotal || 0,
            referralsReceivedCount: hit1._source?.referralsReceivedCount,
            referralsSentCount: hit1._source?.referralsSentCount,
            sumPayments: hit1._source?.paymentTotal,
            diagnosesCount: hit1._source?.DRG_diagnosesCount,
            proceduresCount: hit1._source?.DRG_proceduresCount,
            prescriptionsCount: hit1._source?.num_prescriptions,
            trialEnrollmentRate: hit1._source?.trialEnrollmentRate,
            trialOngoingCount: 0,
            trialActivelyRecruitingCount: 0,
            congresses: hit1._source?.congressCount,
            grants: 0, // hard-coded to 0 since there's no data
            sumGrants: 0, // hard-coded to 0 since there's no data
            specialty: hit1._source?.specialty_eng,
            specialtyCmn: hit1._source?.specialty_cmn,
            specialtyJpn: hit1._source?.specialty_jpn,
            congressesDates: [], // from inner_hits
            paymentDates: [], // from inner_hits
            publicationDates: [], // from inner_hits
            trialDates: [], // from inner_hits
            citationCount: hit1._source?.citationTotal,
            citationCountAvg: 0,
            infoRequestsResolved: null,
            tags: [],
            languageCode: ENGLISH,
            designations: hit1._source?.designations,
            patientsDiversity: {
              age: [],
              races: [],
              sex: []
            },
            providerDiversity: {
              languagesSpoken: [],
              races: [],
              sex: []
            },
            diversityPercentile: hit1._source?.patientsDiversityPercentile,
            digitalRank: hit1._source?.digitalRank,
            top1PercentileDigitalRank: hit1._source?.top1PercentileDigitalRank,
            top10PercentileDigitalRank:
              hit1._source?.top10PercentileDigitalRank,
            twitterFollowersCount: hit1._source?.twitterFollowersCount,
            twitterTweetCount: hit1._source?.twitterTweetCount,
            locations: [],
            isOutsideUsersSlice: true,
            diversityRanking: null,
            debugData: {
              risingStar: {
                avgIndicationRisingScore:
                  hit1IndicationRisingScoreTotal / hit1TotalIndicationInnerHits,
                overallRisingScore: hit1._source?.overallRisingScore || 0
              }
            },
            congressSessions: []
          },
          {
            personId: hit2._source?.id,
            h1dnId: hit2._source?.h1dn_id,
            name: hit2._source?.name_eng,
            firstName: hit2._source?.firstName_eng,
            middleName: hit2._source?.middleName_eng,
            lastName: hit2._source?.lastName_eng,
            nameEng: hit2._source?.name_eng,
            firstNameEng: hit2._source?.firstName_eng,
            lastNameEng: hit2._source?.lastName_eng,
            isFacultyOpinionsMember: hit2._source!.isFacultyOpinionsMember,
            personTranslationEng: {
              fullName: hit2._source!.name_eng,
              firstName: hit2._source!.firstName_eng,
              middleName: hit2._source!.middleName_eng,
              lastName: hit2._source!.lastName_eng,
              languageCode: ENGLISH
            },
            personTranslation: {
              fullName: hit2._source!.name_jpn,
              firstName: hit2._source!.firstName_jpn,
              middleName: hit2._source!.middleName_jpn,
              lastName: hit2._source!.lastName_jpn,
              languageCode: JAPANESE
            },
            affiliations: [
              expect.objectContaining({
                id: hit2._source!.affiliations![0].id
              }),
              expect.objectContaining({
                id: hit2._source!.affiliations![1].id
              })
            ],
            score: hit2._score!,
            scores: {
              normalizedRange: {
                min: 0,
                max: 0
              },
              personId: hit2._source?.id,
              h1Score: hit2._score!,
              publications: {
                minValue: 0,
                maxValue: 0,
                normalizedValue: 0,
                percentile: 0,
                value: (
                  hit2.inner_hits!.publications.hits.total as SearchTotalHits
                ).value
              },
              citations: {
                minValue: 0,
                maxValue: 0,
                normalizedValue: 0,
                percentile: 0,
                value: hit2CitationCount
              },
              trials: {
                minValue: 0,
                maxValue: 0,
                normalizedValue: 0,
                percentile: 0,
                value: (hit2.inner_hits!.trials.hits.total as SearchTotalHits)
                  .value
              },
              payments: {
                minValue: 0,
                maxValue: 0,
                normalizedValue: 0,
                percentile: 0,
                value: hit2PaymentAmountsTotal
              },
              paymentsCount: {
                minValue: 0,
                maxValue: 0,
                normalizedValue: 0,
                percentile: 0,
                value: (hit2.inner_hits!.payments.hits.total as SearchTotalHits)
                  .value
              },
              grants: {
                minValue: 0,
                maxValue: 0,
                normalizedValue: 0,
                percentile: 0,
                value: 0
              },
              grantsCount: {
                minValue: 0,
                maxValue: 0,
                normalizedValue: 0,
                percentile: 0,
                value: 0
              },
              congresses: {
                minValue: 0,
                maxValue: 0,
                normalizedValue: 0,
                percentile: 0,
                value: (hit2.inner_hits!.congress.hits.total as SearchTotalHits)
                  .value
              },
              collaborators: {
                minValue: 0,
                maxValue: 0,
                normalizedValue: 0,
                percentile: 0,
                value: 0
              },
              socialMediaMentions: {
                minValue: 0,
                maxValue: 0,
                normalizedValue: 0,
                percentile: 0,
                value: hit2SocialMediaCount
              },
              procedures: {
                minValue: 0,
                maxValue: 0,
                normalizedValue: 0,
                percentile: 0,
                value: hit2ProceduresAmountsTotal
              },
              diagnoses: {
                minValue: 0,
                maxValue: 0,
                normalizedValue: 0,
                percentile: 0,
                value: hit2DiagnosesAmountsTotal
              },
              prescriptions: {
                minValue: 0,
                maxValue: 0,
                normalizedValue: 0,
                percentile: 0,
                value: hit2PrescriptionsAmountsTotal
              },
              referralsReceived: {
                minValue: 0,
                maxValue: 0,
                normalizedValue: 0,
                percentile: 0,
                value: hit2._source?.referralsReceivedCount
              },
              referralsSent: {
                minValue: 0,
                maxValue: 0,
                normalizedValue: 0,
                percentile: 0,
                value: hit2._source?.referralsSentCount
              },
              twitterFollowersCount: {
                minValue: 0,
                maxValue: 0,
                normalizedValue: 0,
                percentile: 0,
                value: hit2._source?.twitterFollowersCount
              },
              twitterTweetCount: {
                minValue: 0,
                maxValue: 0,
                normalizedValue: 0,
                percentile: 0,
                value: hit2._source?.twitterTweetCount
              }
            },
            totalWorks: hit2._source?.totalWorks,
            countPublications: hit2._source?.publicationCount,
            countPresentWorkAffiliations:
              hit2._source?.presentWorkInstitutionCount,
            publicationsHighlights: [
              {
                publicationsId:
                  hit2.inner_hits?.publications.hits.hits[0].fields![
                    "publications.id"
                  ][0],
                highlight: {
                  keywords:
                    hit2.inner_hits?.publications.hits.hits[0].highlight![
                      "publications.keywords_eng"
                    ][0],
                  publicationAbstract:
                    hit2.inner_hits?.publications.hits.hits[0].highlight![
                      "publications.publicationAbstract_eng"
                    ][0],
                  title:
                    hit2.inner_hits?.publications.hits.hits[0].highlight![
                      "publications.title_eng"
                    ][0]
                }
              }
            ],
            countClinicalTrials: hit2._source?.trialCount,
            trialsHighlights: [
              {
                highlight: {
                  briefTitle:
                    hit2.inner_hits?.trials.hits.hits[0].highlight![
                      "trials.briefTitle_eng"
                    ][0],
                  conditions:
                    hit2.inner_hits?.trials.hits.hits[0].highlight![
                      "trials.conditions_eng"
                    ][0],
                  interventions:
                    hit2.inner_hits?.trials.hits.hits[0].highlight![
                      "trials.interventions_eng"
                    ][0],
                  keywords:
                    hit2.inner_hits?.trials.hits.hits[0].highlight![
                      "trials.keywords_eng"
                    ][0],
                  officialTitle:
                    hit2.inner_hits?.trials.hits.hits[0].highlight![
                      "trials.officialTitle_eng"
                    ][0],
                  summary:
                    hit2.inner_hits?.trials.hits.hits[0].highlight![
                      "trials.summary_eng"
                    ][0]
                },
                trialsId:
                  hit2.inner_hits?.trials.hits.hits[0].fields!["trials.id"][0]
              }
            ],
            claimsDiagnosesHighlights: [
              {
                highlight: {
                  codeAndDescription:
                    hit2.inner_hits?.DRG_diagnoses.hits.hits[0].highlight![
                      "DRG_diagnoses.codeAndDescription_eng"
                    ][0]
                }
              }
            ],
            claimsProceduresHighlights: [
              {
                highlight: {
                  codeAndDescription:
                    hit2.inner_hits?.DRG_procedures.hits.hits[0].highlight![
                      "DRG_procedures.codeAndDescription_eng"
                    ][0]
                }
              }
            ],
            congressHighlights: [
              {
                congressId:
                  hit2.inner_hits?.congress.hits.hits[0].fields![
                    "congress.id"
                  ][0],
                highlight: {
                  keywords:
                    hit2.inner_hits?.congress.hits.hits[0].highlight![
                      "congress.keywords_eng"
                    ][0],
                  title:
                    hit2.inner_hits?.congress.hits.hits[0].highlight![
                      "congress.title_eng"
                    ][0]
                }
              }
            ],
            affiliationHighlights: [],
            socialMediaMentionsTotal: hit2._source?.microBloggingTotal || 0,
            referralsReceivedCount: hit2._source?.referralsReceivedCount,
            referralsSentCount: hit2._source?.referralsSentCount,
            sumPayments: hit2._source?.paymentTotal,
            diagnosesCount: hit2._source?.DRG_diagnosesCount,
            proceduresCount: hit2._source?.DRG_proceduresCount,
            prescriptionsCount: hit2._source?.num_prescriptions,
            trialEnrollmentRate: hit2._source?.trialEnrollmentRate,
            trialOngoingCount: 0,
            trialActivelyRecruitingCount: 0,
            congresses: hit2._source?.congressCount,
            grants: 0, // hard-coded to 0 since there's no data
            sumGrants: 0, // hard-coded to 0 since there's no data
            specialty: hit2._source?.specialty_eng,
            specialtyCmn: hit2._source?.specialty_cmn,
            specialtyJpn: hit2._source?.specialty_jpn,
            congressesDates: [], // from inner_hits
            paymentDates: [], // from inner_hits
            publicationDates: [], // from inner_hits
            trialDates: [], // from inner_hits
            citationCount: hit2._source?.citationTotal,
            citationCountAvg: 0,
            infoRequestsResolved: null,
            tags: [],
            languageCode: ENGLISH,
            designations: hit2._source?.designations,
            patientsDiversity: {
              age: [],
              races: [],
              sex: []
            },
            providerDiversity: {
              languagesSpoken: [],
              races: [],
              sex: []
            },
            diversityPercentile: hit2._source?.patientsDiversityPercentile,
            digitalRank: hit2._source?.digitalRank,
            top1PercentileDigitalRank: hit2._source?.top1PercentileDigitalRank,
            top10PercentileDigitalRank:
              hit2._source?.top10PercentileDigitalRank,
            twitterFollowersCount: hit2._source?.twitterFollowersCount,
            twitterTweetCount: hit2._source?.twitterTweetCount,
            locations: [],
            isOutsideUsersSlice: true,
            diversityRanking: null,
            debugData: {
              risingStar: {
                avgIndicationRisingScore:
                  hit2IndicationRisingScoreTotal / hit2TotalIndicationInnerHits,
                overallRisingScore: hit2._source?.overallRisingScore || 0
              }
            },
            congressSessions: []
          }
        ],
        ranges: {
          publicationCount: { min: 0, max: 0 },
          citationCount: { min: 0, max: 0 },
          trialCount: { min: 0, max: 0 },
          paymentCount: { min: 0, max: 0 },
          paymentSum: { min: 0, max: 0 },
          grantCount: { min: 0, max: 0 },
          grantSum: { min: 0, max: 0 },
          congressCount: { min: 0, max: 0 },
          totalCollaborators: { min: 0, max: 0 },
          socialMediaMentions: { min: 0, max: 0 },
          procedures: { min: 0, max: 0 },
          diagnoses: { min: 0, max: 0 },
          referralsReceived: { min: 0, max: 0 },
          referralsSent: { min: 0, max: 0 }
        },
        normalizedRange: { min: 0, max: 0 },
        filterCounts: [
          {
            field: "institutions",
            buckets: []
          },
          {
            field: "affiliations",
            buckets: []
          },
          {
            field: "journals",
            buckets: []
          },
          {
            field: "zipCode",
            buckets: []
          },
          {
            field: "country",
            buckets: []
          },
          {
            field: "specialty",
            buckets: []
          },
          {
            field: "city",
            buckets: []
          },
          {
            field: "procedureCodes",
            buckets: []
          },
          {
            field: "payerCompanies",
            buckets: []
          },
          {
            field: "trialPhases",
            buckets: []
          },
          {
            field: "publicationTypes",
            buckets: []
          },
          {
            field: "congressConferenceNames",
            buckets: []
          },
          {
            field: "medSchool",
            buckets: []
          },
          {
            field: "diagnosisCodes",
            buckets: []
          },
          {
            field: "natureOfPayments",
            buckets: []
          },
          {
            field: "designations",
            buckets: []
          },
          {
            field: "paymentAssociatedDrugs",
            buckets: []
          },
          {
            field: "congressOrganizerNames",
            buckets: []
          },
          {
            field: "referralsServiceLine",
            buckets: []
          },
          {
            field: "trialStatuses",
            buckets: []
          },
          {
            field: "state",
            buckets: []
          },
          {
            field: "trialTypes",
            buckets: []
          }
        ],
        synonyms,
        queryIntent,
        icdCodeSynonyms: [],
        totalResultsOutsideUserSlice: 0
      });
    });
  });

  describe("congressContributerRank", () => {
    it("should return congress sessions matching name filter value", async () => {
      const page = {
        from: faker.datatype.number(),
        size: faker.datatype.number()
      };

      const congressName = faker.datatype.string();

      const overrides: Partial<HCPDocument> = {
        congress: [
          {
            id: faker.datatype.string(),
            role: faker.datatype.string(),
            name_eng: congressName,
            title_eng: faker.datatype.string(),
            title_cmn: faker.datatype.string(),
            title_jpn: faker.datatype.string()
          },
          {
            id: faker.datatype.string(),
            role: faker.datatype.string(),
            name_eng: faker.datatype.string(),
            title_eng: faker.datatype.string(),
            title_cmn: faker.datatype.string(),
            title_jpn: faker.datatype.string()
          },
          {
            id: faker.datatype.string(),
            role: faker.datatype.string(),
            name_eng: congressName,
            title_eng: faker.datatype.string(),
            title_cmn: faker.datatype.string(),
            title_jpn: faker.datatype.string()
          }
        ]
      };

      const hit = generateMockHit(overrides);

      const hits: SearchHitsMetadata<HCPDocument> = {
        total: {
          value: faker.datatype.number()
        } as SearchTotalHits,
        hits: [hit]
      };

      const synonyms = generateMockSynonyms();
      const suppliedFilters = generateFilters();
      const queryIntent = generateMockQueryIntents();

      const affiliationAdapterService = createMockInstance(
        AffiliationAdapterService
      );

      const keywordSearchResponseAdapterService =
        new KeywordSearchResponseAdapterService(affiliationAdapterService);

      const input: KeywordSearchInput = {
        projectId: faker.datatype.string(),
        userId: faker.datatype.string(),
        query: faker.datatype.string(),
        suppliedFilters: {
          ...suppliedFilters,
          congresses: {
            name: {
              values: [congressName]
            },
            contributorRole: {
              values: []
            },
            minCount: {
              value: null
            },
            type: {
              values: []
            },
            organizerName: {
              values: []
            },
            sessionType: {
              values: []
            }
          }
        },
        page,
        sortBy: generateStandardWeightedSortBy({
          congressContributerRank: 1
        }),
        language: ENGLISH,
        projectFeatures: {
          advancedOperators: true,
          claims: true,
          referrals: true,
          engagementsV2: faker.datatype.boolean(),
          translateTaiwan: true
        }
      };

      const {
        results: [result]
      } = await keywordSearchResponseAdapterService.adapt(
        page,
        ENGLISH,
        hits,
        undefined,
        synonyms,
        suppliedFilters,
        queryIntent,
        [],
        generateKeywordSearchFeatureFlags(),
        [],
        [],
        undefined,
        undefined,
        input
      );

      expect(result.congressSessions).toEqual([
        {
          id: hit._source?.congress?.[0].id,
          name: hit._source?.congress?.[0].title_eng,
          role: hit._source?.congress?.[0].role
        },
        {
          id: hit._source?.congress?.[2].id,
          name: hit._source?.congress?.[2].title_eng,
          role: hit._source?.congress?.[2].role
        }
      ]);
    });
  });
});
