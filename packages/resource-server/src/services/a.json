{
  "bool": {
    "must": undefined,
    "should": [
      {
        "has_child": {
          "type": "claim",
          "score_mode": "sum",
          "query": {
            "function_score": {
              "query": {
                "bool": {
                  "filter": [
                    {
                      "terms": {
                        "patientClaims.diagnosisIndications": ["breast cancer"]
                      }
                    }
                  ]
                }
              },
              "functions": [
                {
                  "weight": 10
                },
                {
                  "filter": {
                    "bool": {
                      "must_not": {
                        "exists": {
                          "field": "patientClaims.diversity"
                        }
                      }
                    }
                  },
                  "weight": 0
                },
                {
                  "filter": {
                    "term": {
                      "patientClaims.diversity": "White Non-Hispanic"
                    }
                  },
                  "weight": 0
                },
                {
                  "filter": {
                    "term": {
                      "patientClaims.diversity": "Other"
                    }
                  },
                  "weight": 0
                },
                {
                  "filter": {
                    "term": {
                      "patientClaims.diversity": "Not Disclosed"
                    }
                  },
                  "weight": 0
                }
              ],
              "score_mode": "multiply",
              "boost_mode": "replace"
            }
          },
          "inner_hits": {
            "name": "patient_claims_matching_count",
            "size": 0,
            "_source": false
          }
        }
      },
      {
        "function_score": {
          "query": {
            "nested": {
              "path": "procedures",
              "score_mode": "sum",
              "query": {
                "function_score": {
                  "boost_mode": "replace",
                  "query": {
                    "simple_query_string": {
                      "_name": "procedures",
                      "query": "( ( breast cancer )  | ( breast carcinoma )  | ( breast malign tumor )  | ( breast neopl )  | ( breast neoplasm )  | ( breast tumor )  | ( breast tumour )  | ( carcinoma breast )  | ( human mammary carcinoma )  | ( human mammary neoplasm )  | ( malign tumor breast )  | ( mammary cancer )  | ( neopl breast )  | ( tumor breast )  | ( 乳かん )  | ( 乳カン )  | ( 乳房かん )  | ( 乳房新生物 )  | ( 乳房腫瘍 )  | ( 乳癌 )  | ( 乳腺mucocele like tumor )  | ( 乳腺悪性腫瘍 )  | ( 乳腺癌 )  | ( 乳腺腫瘍 ヒト )  | ( 乳腺腫瘍 人間 )  )",
                      "fields": [
                        "procedures.description_eng",
                        "procedures.code_eng"
                      ],
                      "default_operator": "and",
                      "analyzer": "main_analyzer"
                    }
                  },
                  "functions": [
                    {
                      "field_value_factor": {
                        "field": "procedures.count",
                        "missing": 0
                      }
                    }
                  ]
                }
              },
              "inner_hits": {
                "_source": false,
                "docvalue_fields": ["procedures.count"],
                "size": 1000
              }
            }
          },
          "boost_mode": "replace",
          "functions": [
            {
              "script_score": {
                "script": {
                  "source": "doc['patientsDiversityRatio.whiteNonHispanic'].size() == 0 ? 0 : saturation(_score, params['saturation_param']) * ((doc['patientsDiversityRatio.americanIndianOrAlaskaNative'].size() == 0 ? 0 : doc['patientsDiversityRatio.americanIndianOrAlaskaNative'].value) + (doc['patientsDiversityRatio.asianPacificIslander'].size() == 0 ? 0 : doc['patientsDiversityRatio.asianPacificIslander'].value) + (doc['patientsDiversityRatio.blackNonHispanic'].size() == 0 ? 0 : doc['patientsDiversityRatio.blackNonHispanic'].value) + (doc['patientsDiversityRatio.hispanic'].size() == 0 ? 0 : doc['patientsDiversityRatio.hispanic'].value))",
                  "params": {
                    "saturation_param": 7000
                  }
                }
              }
            }
          ]
        }
      },
      {
        "function_score": {
          "query": {
            "nested": {
              "path": "prescriptions",
              "score_mode": "sum",
              "query": {
                "function_score": {
                  "boost_mode": "replace",
                  "query": {
                    "simple_query_string": {
                      "_name": "prescriptions",
                      "query": "( ( breast cancer )  | ( breast carcinoma )  | ( breast malign tumor )  | ( breast neopl )  | ( breast neoplasm )  | ( breast tumor )  | ( breast tumour )  | ( carcinoma breast )  | ( human mammary carcinoma )  | ( human mammary neoplasm )  | ( malign tumor breast )  | ( mammary cancer )  | ( neopl breast )  | ( tumor breast )  | ( 乳かん )  | ( 乳カン )  | ( 乳房かん )  | ( 乳房新生物 )  | ( 乳房腫瘍 )  | ( 乳癌 )  | ( 乳腺mucocele like tumor )  | ( 乳腺悪性腫瘍 )  | ( 乳腺癌 )  | ( 乳腺腫瘍 ヒト )  | ( 乳腺腫瘍 人間 )  )",
                      "fields": ["prescriptions.generic_name.text"],
                      "default_operator": "and",
                      "analyzer": "main_analyzer"
                    }
                  },
                  "functions": [
                    {
                      "field_value_factor": {
                        "field": "prescriptions.num_prescriptions",
                        "missing": 0
                      }
                    }
                  ]
                }
              },
              "inner_hits": {
                "_source": false,
                "docvalue_fields": ["prescriptions.num_prescriptions"],
                "size": 1000
              }
            }
          },
          "boost_mode": "replace",
          "functions": [
            {
              "script_score": {
                "script": {
                  "source": "doc['patientsDiversityRatio.whiteNonHispanic'].size() == 0 ? 0 : saturation(_score, params['saturation_param']) * ((doc['patientsDiversityRatio.americanIndianOrAlaskaNative'].size() == 0 ? 0 : doc['patientsDiversityRatio.americanIndianOrAlaskaNative'].value) + (doc['patientsDiversityRatio.asianPacificIslander'].size() == 0 ? 0 : doc['patientsDiversityRatio.asianPacificIslander'].value) + (doc['patientsDiversityRatio.blackNonHispanic'].size() == 0 ? 0 : doc['patientsDiversityRatio.blackNonHispanic'].value) + (doc['patientsDiversityRatio.hispanic'].size() == 0 ? 0 : doc['patientsDiversityRatio.hispanic'].value))",
                  "params": {
                    "saturation_param": 4000
                  }
                }
              }
            }
          ]
        }
      },
      {
        "function_score": {
          "query": {
            "nested": {
              "path": "trials",
              "score_mode": "sum",
              "query": {
                "function_score": {
                  "boost_mode": "replace",
                  "query": {
                    "simple_query_string": {
                      "_name": "trials",
                      "query": "( ( breast cancer )  | ( breast carcinoma )  | ( breast malign tumor )  | ( breast neopl )  | ( breast neoplasm )  | ( breast tumor )  | ( breast tumour )  | ( carcinoma breast )  | ( human mammary carcinoma )  | ( human mammary neoplasm )  | ( malign tumor breast )  | ( mammary cancer )  | ( neopl breast )  | ( tumor breast )  | ( 乳かん )  | ( 乳カン )  | ( 乳房かん )  | ( 乳房新生物 )  | ( 乳房腫瘍 )  | ( 乳癌 )  | ( 乳腺mucocele like tumor )  | ( 乳腺悪性腫瘍 )  | ( 乳腺癌 )  | ( 乳腺腫瘍 ヒト )  | ( 乳腺腫瘍 人間 )  )",
                      "fields": ["trials.trials_info"],
                      "default_operator": "and",
                      "analyzer": undefined
                    }
                  }
                }
              },
              "inner_hits": {
                "_source": false
              }
            }
          },
          "boost_mode": "replace",
          "functions": [
            {
              "script_score": {
                "script": {
                  "source": "doc['patientsDiversityRatio.whiteNonHispanic'].size() == 0 ? 0 : saturation(_score, params['saturation_param']) * ((doc['patientsDiversityRatio.americanIndianOrAlaskaNative'].size() == 0 ? 0 : doc['patientsDiversityRatio.americanIndianOrAlaskaNative'].value) + (doc['patientsDiversityRatio.asianPacificIslander'].size() == 0 ? 0 : doc['patientsDiversityRatio.asianPacificIslander'].value) + (doc['patientsDiversityRatio.blackNonHispanic'].size() == 0 ? 0 : doc['patientsDiversityRatio.blackNonHispanic'].value) + (doc['patientsDiversityRatio.hispanic'].size() == 0 ? 0 : doc['patientsDiversityRatio.hispanic'].value))",
                  "params": {
                    "saturation_param": 10
                  }
                }
              }
            }
          ]
        }
      },
      {
        "nested": {
          "path": "congresses",
          "query": {
            "constant_score": {
              "filter": {
                "simple_query_string": {
                  "_name": "congresses",
                  "query": "( ( breast cancer )  | ( breast carcinoma )  | ( breast malign tumor )  | ( breast neopl )  | ( breast neoplasm )  | ( breast tumor )  | ( breast tumour )  | ( carcinoma breast )  | ( human mammary carcinoma )  | ( human mammary neoplasm )  | ( malign tumor breast )  | ( mammary cancer )  | ( neopl breast )  | ( tumor breast )  | ( 乳かん )  | ( 乳カン )  | ( 乳房かん )  | ( 乳房新生物 )  | ( 乳房腫瘍 )  | ( 乳癌 )  | ( 乳腺mucocele like tumor )  | ( 乳腺悪性腫瘍 )  | ( 乳腺癌 )  | ( 乳腺腫瘍 ヒト )  | ( 乳腺腫瘍 人間 )  )",
                  "fields": ["congresses.congress_info_eng"],
                  "default_operator": "and",
                  "analyzer": undefined
                }
              },
              "boost": 0
            }
          },
          "inner_hits": {
            "_source": false
          }
        }
      },
      {
        "nested": {
          "path": "payments",
          "query": {
            "constant_score": {
              "filter": {
                "simple_query_string": {
                  "_name": "payments",
                  "query": "( ( breast cancer )  | ( breast carcinoma )  | ( breast malign tumor )  | ( breast neopl )  | ( breast neoplasm )  | ( breast tumor )  | ( breast tumour )  | ( carcinoma breast )  | ( human mammary carcinoma )  | ( human mammary neoplasm )  | ( malign tumor breast )  | ( mammary cancer )  | ( neopl breast )  | ( tumor breast )  | ( 乳かん )  | ( 乳カン )  | ( 乳房かん )  | ( 乳房新生物 )  | ( 乳房腫瘍 )  | ( 乳癌 )  | ( 乳腺mucocele like tumor )  | ( 乳腺悪性腫瘍 )  | ( 乳腺癌 )  | ( 乳腺腫瘍 ヒト )  | ( 乳腺腫瘍 人間 )  )",
                  "fields": ["payments.payment_info"],
                  "default_operator": "and",
                  "analyzer": undefined
                }
              },
              "boost": 0
            }
          },
          "inner_hits": {
            "_source": false,
            "docvalue_fields": ["payments.amount"],
            "size": 10000
          }
        }
      }
    ],
    "minimum_should_match": 1,
    "filter": [
      {
        "bool": {
          "should": [
            {
              "bool": {
                "must": [
                  {
                    "exists": {
                      "field": "projectIds"
                    }
                  },
                  {
                    "terms": {
                      "projectIds": ["1"]
                    }
                  }
                ]
              }
            },
            {
              "bool": {
                "must_not": [
                  {
                    "exists": {
                      "field": "projectIds"
                    }
                  }
                ]
              }
            }
          ]
        }
      },
      {
        "bool": {
          "must_not": [
            {
              "terms": {
                "orgTypes.keyword": ["Company"]
              }
            },
            {
              "terms": {
                "orgTypesLevel2.keyword": [
                  "Respite Care Facility",
                  "Suppliers",
                  "Laboratories",
                  "Nursing & Custodial Care Facilities",
                  "Transportation Services"
                ]
              }
            },
            {
              "terms": {
                "orgTypesLevel3.keyword": [
                  "Ambulatory Surgical Clinic/Center",
                  "Emergency Care Clinic/Center",
                  "State or Local Public Health Clinic/Center",
                  "Assisted Living Facility (Mental Illness)",
                  "Assisted Living Facility (Behavioral Disturbances)",
                  "Meals Provider",
                  "Mammography Clinic/Center",
                  "Ambulatory Family Planning Facility",
                  "Federal Public Health Clinic/Center",
                  "Foster Care Agency",
                  "Lodging Provider",
                  "Urgent Care Clinic/Center",
                  "Radiology Clinic/Center",
                  "Voluntary or Charitable Agency",
                  "Dental Clinic/Center",
                  "Radiation Oncology Clinic/Center",
                  "Mobile Mammography Clinic/Center",
                  "Mobile Radiology Clinic/Center",
                  "Home Delivered Meals",
                  "Home Health Agency",
                  "Public Health or Welfare Agency",
                  "Podiatric Clinic/Center",
                  "Magnetic Resonance Imaging (MRI) Clinic/Center",
                  "Local Education Agency (LEA)",
                  "Nursing Care Agency",
                  "Supports Brokerage Agency"
                ]
              }
            }
          ]
        }
      },
      {
        "term": {
          "join_field": "iol"
        }
      }
    ]
  }
}