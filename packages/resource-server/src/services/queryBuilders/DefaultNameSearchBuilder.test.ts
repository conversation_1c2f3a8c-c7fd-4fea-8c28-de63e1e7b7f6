import { faker } from "@faker-js/faker";
import { QueryIntent } from "../../proto/query_intent_pb";
import { QueryUnderstandingServiceResponse } from "../../proto/query_understanding_service_pb";
import DefaultNameSearchBuilder, {
  FUZZINESS,
  INDICATION_PERSONALISATION_BOOST,
  LOCATION_PERSONALISATION_BOOST,
  MIN_SHOULD_MATCH_CRITERION
} from "./DefaultNameSearchBuilder";
import { NameSearchBuilderArgWithKeyword } from "./NameSearchBuilder";
import { SORT_TIEBREAKER_FOR_INDICATIONS } from "../KeywordSearchMatchedCountService";
import { QueryDslQueryContainer } from "@elastic/elasticsearch/lib/api/types";
import { NameSearchFeatureFlags } from "../NameSearchResourceServiceRewrite";

export const EMPTY_ARRAY: string[] = [];

const FEATURE_FLAG_DEFAULTS: NameSearchFeatureFlags = {
  enableHighConfHCPFeature: false,
  enableQueryIntent: false,
  enableIntentBasedSearchQuery: false,
  enableNameSearchPersonalisation: false,
  enableCTMSV2: false,
  enableResultsOutsideUsersSlice: false,
  enableTagsInElasticsearch: false,
  enableBrazilianClaims: true,
  enableUniquePatientCountForClaims: false,
  disableUniquePatientCountForOnlyProcedures: false,
  enableNestedIndicationFilter: false,
  enableCcsrExclusionForMatchedCounts: false,
  enableLocationFilterRegionRollup: false,
  enableNewGlobalLeaderTier: false
};

const generateFeatureFlags = (
  overrides: Partial<NameSearchFeatureFlags>
): NameSearchFeatureFlags => {
  return {
    ...FEATURE_FLAG_DEFAULTS,
    ...overrides
  };
};

describe("Name Search Builder Tests", () => {
  describe("Intent based query modifications", () => {
    it("expect specialist ranking function to be present when query intent has specialist", () => {
      const query = faker.datatype.string();
      const projectId = faker.datatype.string();
      const specialistIntent = new QueryIntent.Intent();
      specialistIntent.setIntentType(QueryIntent.IntentType.SPECIALIST);
      specialistIntent.setScore(1.0);
      const span = new QueryIntent.Span();
      span.setEntity(query);
      span.addLabels(specialistIntent);
      const queryIntents = new QueryIntent();
      queryIntents.addEntities(span);
      const qusWithSpecialistIntent = new QueryUnderstandingServiceResponse();
      qusWithSpecialistIntent.setQueryIntent(queryIntents);

      const nameSearchBuilder = DefaultNameSearchBuilder.getInstance();
      const args: NameSearchBuilderArgWithKeyword = {
        query,
        numberOfNameVariationsToMatch: 1,
        queryUnderstandingServiceResponse: qusWithSpecialistIntent,
        from: 0,
        size: 10,
        filter: [],
        language: "eng",
        featureFlags: generateFeatureFlags({
          enableHighConfHCPFeature: false,
          enableQueryIntent: false,
          enableIntentBasedSearchQuery: true,
          enableNameSearchPersonalisation: false,
          enableCTMSV2: false,
          enableResultsOutsideUsersSlice: false,
          enableTagsInElasticsearch: false
        }),
        onboardingData: {
          indications: EMPTY_ARRAY,
          countries: EMPTY_ARRAY,
          states: EMPTY_ARRAY
        },
        projectId,
        useCmnAndJpnFields: false
      };
      const actualQuery = nameSearchBuilder.createNameSearchBody(args);
      const shouldClauses = (
        actualQuery.query!.bool!.must! as QueryDslQueryContainer
      ).function_score!.query!.bool!.should;

      expect(shouldClauses).toBeDefined();
      const expectedSpecialistShouldClause = {
        bool: {
          filter: {
            match: {
              name_eng: {
                query
              }
            }
          },
          should: [
            {
              match: {
                specialty_eng: {
                  query,
                  operator: "and",
                  boost: 1
                }
              }
            }
          ],
          minimum_should_match: 1
        }
      };

      expect(shouldClauses).toContainEqual(expectedSpecialistShouldClause);
    });
    it("expect specialist ranking function to NOT be present when query intent does not have specialist", () => {
      const query = faker.datatype.string();
      const projectId = faker.datatype.string();
      const qusWithoutSpecialistIntent =
        new QueryUnderstandingServiceResponse();

      const nameSearchBuilder = DefaultNameSearchBuilder.getInstance();
      const args: NameSearchBuilderArgWithKeyword = {
        query,
        numberOfNameVariationsToMatch: 1,
        queryUnderstandingServiceResponse: qusWithoutSpecialistIntent,
        from: 0,
        size: 10,
        filter: [],
        language: "eng",
        featureFlags: generateFeatureFlags({
          enableHighConfHCPFeature: false,
          enableQueryIntent: false,
          enableIntentBasedSearchQuery: true,
          enableNameSearchPersonalisation: false,
          enableCTMSV2: false,
          enableResultsOutsideUsersSlice: false,
          enableTagsInElasticsearch: false
        }),
        onboardingData: {
          indications: EMPTY_ARRAY,
          countries: EMPTY_ARRAY,
          states: EMPTY_ARRAY
        },
        projectId,
        useCmnAndJpnFields: false
      };
      const actualQuery = nameSearchBuilder.createNameSearchBody(args);
      const shouldClauses = (
        actualQuery.query!.bool!.must! as QueryDslQueryContainer
      ).function_score!.query!.bool!.should;

      expect(shouldClauses).toBeDefined();
      const expectedSpecialistShouldClause = {
        bool: {
          filter: {
            match: {
              name_eng: {
                query
              }
            }
          },
          should: [
            {
              match: {
                specialty_eng: {
                  query,
                  operator: "and",
                  boost: 1
                }
              }
            }
          ],
          minimum_should_match: 1
        }
      };

      expect(shouldClauses).not.toContainEqual(expectedSpecialistShouldClause);
    });
    it("expect indication should clause to be present when query intent has indication", () => {
      const query = faker.datatype.string();
      const projectId = faker.datatype.string();
      const indicationIntent = new QueryIntent.Intent();
      indicationIntent.setIntentType(QueryIntent.IntentType.INDICATION);
      indicationIntent.setScore(1.0);
      const span = new QueryIntent.Span();
      span.setEntity(query);
      span.addLabels(indicationIntent);
      const queryIntents = new QueryIntent();
      queryIntents.addEntities(span);
      const qusWithIndicationIntent = new QueryUnderstandingServiceResponse();
      qusWithIndicationIntent.setQueryIntent(queryIntents);

      const nameSearchBuilder = DefaultNameSearchBuilder.getInstance();
      const args: NameSearchBuilderArgWithKeyword = {
        query,
        numberOfNameVariationsToMatch: 1,
        queryUnderstandingServiceResponse: qusWithIndicationIntent,
        from: 0,
        size: 10,
        filter: [],
        language: "eng",
        featureFlags: generateFeatureFlags({
          enableHighConfHCPFeature: false,
          enableQueryIntent: false,
          enableIntentBasedSearchQuery: true,
          enableNameSearchPersonalisation: false,
          enableCTMSV2: false,
          enableResultsOutsideUsersSlice: false,
          enableTagsInElasticsearch: false
        }),
        onboardingData: {
          indications: EMPTY_ARRAY,
          countries: EMPTY_ARRAY,
          states: EMPTY_ARRAY
        },
        projectId,
        useCmnAndJpnFields: false
      };
      const actualQuery = nameSearchBuilder.createNameSearchBody(args);
      const shouldClauses = (
        actualQuery.query!.bool!.must! as QueryDslQueryContainer
      ).function_score!.query!.bool!.should;

      expect(shouldClauses).toBeDefined();
      const expectedIndicationShouldClause = {
        bool: {
          filter: {
            match: {
              name_eng: {
                query
              }
            }
          },
          should: [
            {
              nested: {
                path: "indications",
                query: {
                  function_score: {
                    functions: [
                      {
                        field_value_factor: {
                          factor: 4,
                          field: "indications.indicationScore",
                          missing: 0
                        }
                      }
                    ],
                    query: {
                      term: {
                        "indications.indication.keyword": query
                      }
                    }
                  }
                }
              }
            }
          ],
          minimum_should_match: 1
        }
      };

      expect(shouldClauses).toContainEqual(expectedIndicationShouldClause);
    });
    it("expect indication should clause to be NOT present when query intent does not have indication", () => {
      const query = faker.datatype.string();
      const projectId = faker.datatype.string();
      const qusWithoutIndicationIntent =
        new QueryUnderstandingServiceResponse();

      const nameSearchBuilder = DefaultNameSearchBuilder.getInstance();
      const args: NameSearchBuilderArgWithKeyword = {
        query,
        numberOfNameVariationsToMatch: 1,
        queryUnderstandingServiceResponse: qusWithoutIndicationIntent,
        from: 0,
        size: 10,
        filter: [],
        language: "eng",
        featureFlags: generateFeatureFlags({
          enableHighConfHCPFeature: false,
          enableQueryIntent: false,
          enableIntentBasedSearchQuery: true,
          enableNameSearchPersonalisation: false,
          enableCTMSV2: false,
          enableResultsOutsideUsersSlice: false,
          enableTagsInElasticsearch: false
        }),
        onboardingData: {
          indications: EMPTY_ARRAY,
          countries: EMPTY_ARRAY,
          states: EMPTY_ARRAY
        },
        projectId,
        useCmnAndJpnFields: false
      };
      const actualQuery = nameSearchBuilder.createNameSearchBody(args);
      const shouldClauses = (
        actualQuery.query!.bool!.must! as QueryDslQueryContainer
      ).function_score!.query!.bool!.should;

      expect(shouldClauses).toBeDefined();
      const expectedIndicationShouldClause = {
        bool: {
          filter: {
            match: {
              name_eng: {
                query
              }
            }
          },
          should: [
            {
              nested: {
                path: "indications",
                query: {
                  function_score: {
                    functions: [
                      {
                        field_value_factor: {
                          factor: 4,
                          field: "indications.indicationScore",
                          missing: 0
                        }
                      }
                    ],
                    query: {
                      term: {
                        "indications.indication.keyword": query
                      }
                    }
                  }
                }
              }
            }
          ],
          minimum_should_match: 1
        }
      };

      expect(shouldClauses).not.toContainEqual(expectedIndicationShouldClause);
    });

    it("expect location should clause to be present when query intent has location", () => {
      const query = faker.datatype.string();
      const projectId = faker.datatype.string();
      const locationIntent = new QueryIntent.Intent();
      locationIntent.setIntentType(QueryIntent.IntentType.LOCATION);
      locationIntent.setScore(1.0);
      const span = new QueryIntent.Span();
      span.setEntity(query);
      span.addLabels(locationIntent);
      const queryIntents = new QueryIntent();
      queryIntents.addEntities(span);
      const qusWithLocationIntent = new QueryUnderstandingServiceResponse();
      qusWithLocationIntent.setQueryIntent(queryIntents);

      const nameSearchBuilder = DefaultNameSearchBuilder.getInstance();
      const args: NameSearchBuilderArgWithKeyword = {
        query,
        numberOfNameVariationsToMatch: 1,
        queryUnderstandingServiceResponse: qusWithLocationIntent,
        from: 0,
        size: 10,
        filter: [],
        language: "eng",
        featureFlags: generateFeatureFlags({
          enableHighConfHCPFeature: false,
          enableQueryIntent: false,
          enableIntentBasedSearchQuery: true,
          enableNameSearchPersonalisation: false,
          enableCTMSV2: false,
          enableResultsOutsideUsersSlice: false,
          enableTagsInElasticsearch: false
        }),
        onboardingData: {
          indications: EMPTY_ARRAY,
          countries: EMPTY_ARRAY,
          states: EMPTY_ARRAY
        },
        projectId,
        useCmnAndJpnFields: false
      };
      const actualQuery = nameSearchBuilder.createNameSearchBody(args);
      const shouldClauses = (
        actualQuery.query!.bool!.must! as QueryDslQueryContainer
      ).function_score!.query!.bool!.should;

      expect(shouldClauses).toBeDefined();
      const expectedLocationShouldClause = {
        bool: {
          filter: {
            match: {
              name_eng: {
                query,
                operator: "AND",
                prefix_length: 2,
                fuzziness: "AUTO"
              }
            }
          },
          should: [
            {
              nested: {
                path: "affiliations",
                query: {
                  function_score: {
                    query: {
                      constant_score: {
                        filter: {
                          multi_match: {
                            query,
                            fields: [
                              "affiliations.institution.address.city",
                              "affiliations.institution.address.region",
                              "affiliations.institution.address.country"
                            ],
                            operator: "and"
                          }
                        }
                      }
                    },
                    functions: [
                      {
                        field_value_factor: {
                          field: "affiliations.accuracyScore",
                          factor: 3.16,
                          modifier: "square",
                          missing: 0
                        }
                      }
                    ]
                  }
                },
                inner_hits: {
                  highlight: {
                    order: "score",
                    fields: {
                      "affiliations.institution.address.city": {},
                      "affiliations.institution.address.region": {},
                      "affiliations.institution.address.country": {}
                    }
                  },
                  _source: false,
                  size: 2,
                  docvalue_fields: [
                    "affiliations.id",
                    "affiliations.institution.id"
                  ],
                  name: "affiliations_0"
                }
              }
            }
          ],
          minimum_should_match: 1
        }
      };

      expect(shouldClauses).toContainEqual(expectedLocationShouldClause);
    });

    it("expect name filter to have only name tokens when query intent has location and names", () => {
      const nameToken = faker.datatype.string();
      const locationToken = faker.datatype.string();
      const query = nameToken + " " + locationToken;
      const projectId = faker.datatype.string();

      const locationIntent = new QueryIntent.Intent();
      locationIntent.setIntentType(QueryIntent.IntentType.LOCATION);
      locationIntent.setScore(1.0);
      const locationSpan = new QueryIntent.Span();
      locationSpan.setEntity(locationToken);
      locationSpan.addLabels(locationIntent);
      const queryIntents = new QueryIntent();
      queryIntents.addEntities(locationSpan);

      const nameIntent = new QueryIntent.Intent();
      nameIntent.setIntentType(QueryIntent.IntentType.PERSON_NAME);
      nameIntent.setScore(1.0);
      const nameSpan = new QueryIntent.Span();
      nameSpan.setEntity(nameToken);
      nameSpan.addLabels(nameIntent);
      queryIntents.addEntities(nameSpan);

      const qusResponse = new QueryUnderstandingServiceResponse();
      qusResponse.setQueryIntent(queryIntents);

      const nameSearchBuilder = DefaultNameSearchBuilder.getInstance();
      const args: NameSearchBuilderArgWithKeyword = {
        query,
        numberOfNameVariationsToMatch: 1,
        queryUnderstandingServiceResponse: qusResponse,
        from: 0,
        size: 10,
        filter: [],
        language: "eng",
        featureFlags: generateFeatureFlags({
          enableHighConfHCPFeature: false,
          enableQueryIntent: false,
          enableIntentBasedSearchQuery: true,
          enableNameSearchPersonalisation: false,
          enableCTMSV2: false,
          enableResultsOutsideUsersSlice: false,
          enableTagsInElasticsearch: false
        }),
        onboardingData: {
          indications: EMPTY_ARRAY,
          countries: EMPTY_ARRAY,
          states: EMPTY_ARRAY
        },
        projectId,
        useCmnAndJpnFields: false
      };
      const actualQuery = nameSearchBuilder.createNameSearchBody(args);

      const shouldClauses = (
        actualQuery.query!.bool!.must! as QueryDslQueryContainer
      ).function_score!.query!.bool!.should;

      expect(shouldClauses).toBeDefined();
      const expectedLocationShouldClause = {
        bool: {
          filter: {
            match: {
              name_eng: {
                query: nameToken,
                operator: "AND",
                prefix_length: 2,
                fuzziness: "AUTO"
              }
            }
          },
          should: [
            {
              nested: expect.objectContaining({
                path: "affiliations",
                query: {
                  function_score: {
                    query: {
                      constant_score: {
                        filter: {
                          multi_match: {
                            query: locationToken,
                            fields: [
                              "affiliations.institution.address.city",
                              "affiliations.institution.address.region",
                              "affiliations.institution.address.country"
                            ],
                            operator: "and"
                          }
                        }
                      }
                    },
                    functions: [
                      {
                        field_value_factor: {
                          field: "affiliations.accuracyScore",
                          factor: 3.16,
                          modifier: "square",
                          missing: 0
                        }
                      }
                    ]
                  }
                },
                inner_hits: {
                  highlight: {
                    order: "score",
                    fields: {
                      "affiliations.institution.address.city": {},
                      "affiliations.institution.address.region": {},
                      "affiliations.institution.address.country": {}
                    }
                  },
                  _source: false,
                  size: 2,
                  docvalue_fields: [
                    "affiliations.id",
                    "affiliations.institution.id"
                  ],
                  name: "affiliations_0"
                }
              })
            }
          ],
          minimum_should_match: 1
        }
      };

      expect(shouldClauses).toContainEqual(expectedLocationShouldClause);
    });

    it("should assign different names if more than one location is present in query", () => {
      const nameToken = faker.datatype.string();
      const locationToken1 = faker.datatype.string();
      const locationToken2 = faker.datatype.string();
      const query = [nameToken, locationToken1, locationToken2].join(" ");

      const projectId = faker.datatype.string();
      const locationIntent = new QueryIntent.Intent();
      locationIntent.setIntentType(QueryIntent.IntentType.LOCATION);
      locationIntent.setScore(1.0);
      const locationSpan1 = new QueryIntent.Span();
      locationSpan1.setEntity(locationToken1);
      locationSpan1.addLabels(locationIntent);
      locationIntent.setScore(0.8);
      const locationSpan2 = new QueryIntent.Span();
      locationSpan2.setEntity(locationToken2);
      locationSpan2.addLabels(locationIntent);
      const queryIntents = new QueryIntent();
      queryIntents.addEntities(locationSpan1);
      queryIntents.addEntities(locationSpan2);

      const nameIntent = new QueryIntent.Intent();
      nameIntent.setIntentType(QueryIntent.IntentType.PERSON_NAME);
      nameIntent.setScore(1.0);
      const nameSpan = new QueryIntent.Span();
      nameSpan.setEntity(nameToken);
      nameSpan.addLabels(nameIntent);
      queryIntents.addEntities(nameSpan);

      const qusResponse = new QueryUnderstandingServiceResponse();
      qusResponse.setQueryIntent(queryIntents);

      const nameSearchBuilder = DefaultNameSearchBuilder.getInstance();
      const args: NameSearchBuilderArgWithKeyword = {
        query,
        numberOfNameVariationsToMatch: 1,
        queryUnderstandingServiceResponse: qusResponse,
        from: 0,
        size: 10,
        filter: [],
        language: "eng",
        featureFlags: generateFeatureFlags({
          enableHighConfHCPFeature: false,
          enableQueryIntent: false,
          enableIntentBasedSearchQuery: true,
          enableNameSearchPersonalisation: false,
          enableCTMSV2: false,
          enableResultsOutsideUsersSlice: false,
          enableTagsInElasticsearch: false
        }),
        onboardingData: {
          indications: EMPTY_ARRAY,
          countries: EMPTY_ARRAY,
          states: EMPTY_ARRAY
        },
        projectId,
        useCmnAndJpnFields: false
      };
      const actualQuery = nameSearchBuilder.createNameSearchBody(args);

      const shouldClauses = (
        actualQuery.query!.bool!.must! as QueryDslQueryContainer
      ).function_score!.query!.bool!.should;

      expect(shouldClauses).toBeDefined();
      const expectedLocationShouldClause = {
        bool: expect.objectContaining({
          should: [
            {
              nested: expect.objectContaining({
                path: "affiliations",

                inner_hits: {
                  highlight: {
                    order: "score",
                    fields: {
                      "affiliations.institution.address.city": {},
                      "affiliations.institution.address.region": {},
                      "affiliations.institution.address.country": {}
                    }
                  },
                  _source: false,
                  size: 2,
                  docvalue_fields: [
                    "affiliations.id",
                    "affiliations.institution.id"
                  ],
                  name: "affiliations_0"
                }
              })
            },
            {
              nested: expect.objectContaining({
                path: "affiliations",
                inner_hits: {
                  highlight: {
                    order: "score",
                    fields: {
                      "affiliations.institution.address.city": {},
                      "affiliations.institution.address.region": {},
                      "affiliations.institution.address.country": {}
                    }
                  },
                  _source: false,
                  size: 2,
                  docvalue_fields: [
                    "affiliations.id",
                    "affiliations.institution.id"
                  ],
                  name: "affiliations_1"
                }
              })
            }
          ],
          minimum_should_match: 1
        })
      };

      expect(shouldClauses).toContainEqual(expectedLocationShouldClause);
    });
  });

  describe("Personalisation based query modification", () => {
    describe("Location Preference", () => {
      it("should add location personalisation clause when query has no location intent and user has location preference", () => {
        const query = faker.datatype.string();
        const projectId = faker.datatype.string();
        const userPreferredCountry = faker.address.country();
        const userPreferredState = faker.address.state();
        const qusWithoutLocationIntent =
          new QueryUnderstandingServiceResponse();

        const nameSearchBuilder = DefaultNameSearchBuilder.getInstance();
        const args: NameSearchBuilderArgWithKeyword = {
          query,
          numberOfNameVariationsToMatch: 1,
          queryUnderstandingServiceResponse: qusWithoutLocationIntent,
          from: 0,
          size: 10,
          filter: [],
          language: "eng",
          featureFlags: generateFeatureFlags({
            enableHighConfHCPFeature: false,
            enableQueryIntent: false,
            enableIntentBasedSearchQuery: true,
            enableNameSearchPersonalisation: true,
            enableCTMSV2: true,
            enableResultsOutsideUsersSlice: false,
            enableTagsInElasticsearch: false
          }),
          onboardingData: {
            indications: EMPTY_ARRAY,
            countries: [userPreferredCountry],
            states: [userPreferredState]
          },
          projectId,
          useCmnAndJpnFields: false
        };
        const actualQuery = nameSearchBuilder.createNameSearchBody(args);
        const rescoreClause = actualQuery.rescore!;
        if (!Array.isArray(rescoreClause)) {
          const shouldClauses =
            rescoreClause!.query!.rescore_query!.bool!.should;

          expect(shouldClauses).toBeDefined();

          const expectedLocationShouldClause = {
            bool: {
              filter: {
                match: {
                  firstLastName_eng: {
                    query: query,
                    prefix_length: 1,
                    operator: "OR",
                    fuzziness: FUZZINESS,
                    boost: 1,
                    minimum_should_match: MIN_SHOULD_MATCH_CRITERION
                  }
                }
              },
              should: [
                {
                  function_score: {
                    query: {
                      nested: {
                        path: "affiliations",
                        query: {
                          bool: {
                            must: {
                              match_all: {}
                            },
                            filter: [
                              {
                                terms: {
                                  "affiliations.institution.filters.country": [
                                    userPreferredCountry
                                  ]
                                }
                              },
                              {
                                term: {
                                  "affiliations.accuracyScore": 1.0
                                }
                              },
                              {
                                term: {
                                  "affiliations.isCurrent": true
                                }
                              },
                              {
                                term: {
                                  "affiliations.type": "Work Affiliation"
                                }
                              }
                            ]
                          }
                        }
                      }
                    },
                    functions: [
                      {
                        weight: LOCATION_PERSONALISATION_BOOST
                      }
                    ]
                  }
                },
                {
                  function_score: {
                    query: {
                      nested: {
                        path: "affiliations",
                        query: {
                          bool: {
                            must: {
                              match_all: {}
                            },
                            filter: [
                              {
                                terms: {
                                  "affiliations.institution.filters.region": [
                                    userPreferredState
                                  ]
                                }
                              },
                              {
                                term: {
                                  "affiliations.accuracyScore": 1.0
                                }
                              },
                              {
                                term: {
                                  "affiliations.isCurrent": true
                                }
                              },
                              {
                                term: {
                                  "affiliations.type": "Work Affiliation"
                                }
                              }
                            ]
                          }
                        }
                      }
                    },
                    functions: [
                      {
                        weight: LOCATION_PERSONALISATION_BOOST
                      }
                    ]
                  }
                }
              ],
              minimum_should_match: 1
            }
          };

          expect(shouldClauses).toContainEqual(expectedLocationShouldClause);
        } else {
          fail("rescore query component is not handled properly");
        }
      });

      it("should not add location personalisation clause when query has location intent", () => {
        const query = faker.datatype.string();
        const projectId = faker.datatype.string();
        const userPreferredCountry = faker.address.country();
        const userPreferredState = faker.address.state();
        const locationIntent = new QueryIntent.Intent();
        locationIntent.setIntentType(QueryIntent.IntentType.LOCATION);
        locationIntent.setScore(1.0);
        const span = new QueryIntent.Span();
        span.setEntity(query);
        span.addLabels(locationIntent);
        const queryIntents = new QueryIntent();
        queryIntents.addEntities(span);
        const qusWithLocationIntent = new QueryUnderstandingServiceResponse();
        qusWithLocationIntent.setQueryIntent(queryIntents);

        const nameSearchBuilder = DefaultNameSearchBuilder.getInstance();
        const args: NameSearchBuilderArgWithKeyword = {
          query,
          numberOfNameVariationsToMatch: 1,
          queryUnderstandingServiceResponse: qusWithLocationIntent,
          from: 0,
          size: 10,
          filter: [],
          language: "eng",
          featureFlags: generateFeatureFlags({
            enableHighConfHCPFeature: false,
            enableQueryIntent: false,
            enableIntentBasedSearchQuery: true,
            enableNameSearchPersonalisation: true,
            enableCTMSV2: true,
            enableResultsOutsideUsersSlice: false,
            enableTagsInElasticsearch: false
          }),
          onboardingData: {
            indications: EMPTY_ARRAY,
            countries: [userPreferredCountry],
            states: [userPreferredState]
          },
          projectId,
          useCmnAndJpnFields: false
        };
        const actualQuery = nameSearchBuilder.createNameSearchBody(args);
        const rescoreClause = actualQuery!.rescore!;
        if (!Array.isArray(rescoreClause)) {
          const boolQuery = rescoreClause!.query!.rescore_query!.bool;

          expect(boolQuery).not.toBeDefined();
        } else {
          fail("rescore query component is not handled properly");
        }
      });

      it("should not add location personalisation clause when user location preferrence is missing", () => {
        const query = faker.datatype.string();
        const projectId = faker.datatype.string();

        const qusWithoutLocationIntent =
          new QueryUnderstandingServiceResponse();

        const nameSearchBuilder = DefaultNameSearchBuilder.getInstance();
        const args: NameSearchBuilderArgWithKeyword = {
          query,
          numberOfNameVariationsToMatch: 1,
          queryUnderstandingServiceResponse: qusWithoutLocationIntent,
          from: 0,
          size: 10,
          filter: [],
          language: "eng",
          featureFlags: generateFeatureFlags({
            enableHighConfHCPFeature: false,
            enableQueryIntent: false,
            enableIntentBasedSearchQuery: true,
            enableNameSearchPersonalisation: true,
            enableCTMSV2: true,
            enableResultsOutsideUsersSlice: false,
            enableTagsInElasticsearch: false
          }),
          onboardingData: {
            indications: EMPTY_ARRAY,
            countries: EMPTY_ARRAY,
            states: EMPTY_ARRAY
          },
          projectId,
          useCmnAndJpnFields: false
        };
        const actualQuery = nameSearchBuilder.createNameSearchBody(args);
        const rescoreClause = actualQuery!.rescore!;
        if (!Array.isArray(rescoreClause)) {
          const boolQuery = rescoreClause!.query!.rescore_query!.bool;

          expect(boolQuery).not.toBeDefined();
        } else {
          fail("rescore query component is not handled properly");
        }
      });

      it("should not add location personalisation clause when enableNameSearchPersonalisation is false", () => {
        const query = faker.datatype.string();
        const projectId = faker.datatype.string();
        const userPreferredCountry = faker.address.country();
        const userPreferredState = faker.address.state();
        const qusWithoutLocationIntent =
          new QueryUnderstandingServiceResponse();

        const nameSearchBuilder = DefaultNameSearchBuilder.getInstance();
        const args: NameSearchBuilderArgWithKeyword = {
          query,
          numberOfNameVariationsToMatch: 1,
          queryUnderstandingServiceResponse: qusWithoutLocationIntent,
          from: 0,
          size: 10,
          filter: [],
          language: "eng",
          featureFlags: generateFeatureFlags({
            enableHighConfHCPFeature: false,
            enableQueryIntent: false,
            enableIntentBasedSearchQuery: true,
            enableNameSearchPersonalisation: false,
            enableCTMSV2: false,
            enableResultsOutsideUsersSlice: false,
            enableTagsInElasticsearch: false
          }),
          onboardingData: {
            indications: EMPTY_ARRAY,
            countries: [userPreferredCountry],
            states: [userPreferredState]
          },
          projectId,
          useCmnAndJpnFields: false
        };
        const actualQuery = nameSearchBuilder.createNameSearchBody(args);
        const rescoreClause = actualQuery!.rescore!;
        if (!Array.isArray(rescoreClause)) {
          const boolQuery = rescoreClause!.query!.rescore_query!.bool;

          expect(boolQuery).not.toBeDefined();
        } else {
          fail("rescore query component is not handled properly");
        }
      });
    });

    describe("Indication Preference", () => {
      it("should add indication personalisation clause when query has no indication intent and user has indication preference", () => {
        const query = faker.datatype.string();
        const projectId = faker.datatype.string();
        const userPreferredIndication = faker.datatype.string();
        const qusWithoutIndicationIntent =
          new QueryUnderstandingServiceResponse();

        const nameSearchBuilder = DefaultNameSearchBuilder.getInstance();
        const args: NameSearchBuilderArgWithKeyword = {
          query,
          numberOfNameVariationsToMatch: 1,
          queryUnderstandingServiceResponse: qusWithoutIndicationIntent,
          from: 0,
          size: 10,
          filter: [],
          language: "eng",
          featureFlags: generateFeatureFlags({
            enableHighConfHCPFeature: false,
            enableQueryIntent: false,
            enableIntentBasedSearchQuery: true,
            enableNameSearchPersonalisation: true,
            enableCTMSV2: true,
            enableResultsOutsideUsersSlice: false,
            enableTagsInElasticsearch: false
          }),
          onboardingData: {
            indications: [userPreferredIndication],
            countries: EMPTY_ARRAY,
            states: EMPTY_ARRAY
          },
          projectId,
          useCmnAndJpnFields: false
        };
        const actualQuery = nameSearchBuilder.createNameSearchBody(args);
        const rescoreClause = actualQuery!.rescore!;
        if (!Array.isArray(rescoreClause)) {
          const shouldClauses =
            rescoreClause!.query!.rescore_query!.bool!.should;

          expect(shouldClauses).toBeDefined();

          const expectedLocationShouldClause = {
            bool: {
              filter: {
                match: {
                  firstLastName_eng: {
                    query: query,
                    prefix_length: 1,
                    operator: "OR",
                    fuzziness: FUZZINESS,
                    boost: 1,
                    minimum_should_match: MIN_SHOULD_MATCH_CRITERION
                  }
                }
              },
              should: [
                {
                  nested: {
                    path: "indications",
                    query: {
                      function_score: {
                        query: {
                          constant_score: {
                            filter: {
                              term: {
                                "indications.indication.keyword":
                                  userPreferredIndication
                              }
                            }
                          }
                        },
                        functions: [
                          {
                            field_value_factor: {
                              field: "indications.indicationScore",
                              factor: INDICATION_PERSONALISATION_BOOST,
                              missing: 0
                            }
                          }
                        ]
                      }
                    }
                  }
                }
              ],
              minimum_should_match: 1
            }
          };

          expect(shouldClauses).toContainEqual(expectedLocationShouldClause);
        } else {
          fail("rescore query component is not handled properly");
        }
      });

      it("should not add indication personalisation clause when query has indication intent", () => {
        const query = faker.datatype.string();
        const projectId = faker.datatype.string();
        const userPreferredIndication = faker.datatype.string();
        const indicationIntent = new QueryIntent.Intent();
        indicationIntent.setIntentType(QueryIntent.IntentType.INDICATION);
        indicationIntent.setScore(1.0);
        const span = new QueryIntent.Span();
        span.setEntity(query);
        span.addLabels(indicationIntent);
        const queryIntents = new QueryIntent();
        queryIntents.addEntities(span);
        const qusWithIndicationIntent = new QueryUnderstandingServiceResponse();
        qusWithIndicationIntent.setQueryIntent(queryIntents);

        const nameSearchBuilder = DefaultNameSearchBuilder.getInstance();
        const args: NameSearchBuilderArgWithKeyword = {
          query,
          numberOfNameVariationsToMatch: 1,
          queryUnderstandingServiceResponse: qusWithIndicationIntent,
          from: 0,
          size: 10,
          filter: [],
          language: "eng",
          featureFlags: generateFeatureFlags({
            enableHighConfHCPFeature: false,
            enableQueryIntent: false,
            enableIntentBasedSearchQuery: true,
            enableNameSearchPersonalisation: true,
            enableCTMSV2: true,
            enableResultsOutsideUsersSlice: false,
            enableTagsInElasticsearch: false
          }),
          onboardingData: {
            indications: [userPreferredIndication],
            countries: EMPTY_ARRAY,
            states: EMPTY_ARRAY
          },
          projectId,
          useCmnAndJpnFields: false
        };
        const actualQuery = nameSearchBuilder.createNameSearchBody(args);
        const rescoreClause = actualQuery!.rescore!;
        if (!Array.isArray(rescoreClause)) {
          const boolQuery = rescoreClause!.query!.rescore_query!.bool;

          expect(boolQuery).not.toBeDefined();
        } else {
          fail("rescore query component is not handled properly");
        }
      });

      it("should not add indication personalisation clause when user indication preferrence is missing", () => {
        const query = faker.datatype.string();
        const projectId = faker.datatype.string();
        const qusWithoutIndicationIntent =
          new QueryUnderstandingServiceResponse();

        const nameSearchBuilder = DefaultNameSearchBuilder.getInstance();
        const args: NameSearchBuilderArgWithKeyword = {
          query,
          numberOfNameVariationsToMatch: 1,
          queryUnderstandingServiceResponse: qusWithoutIndicationIntent,
          from: 0,
          size: 10,
          filter: [],
          language: "eng",
          featureFlags: generateFeatureFlags({
            enableHighConfHCPFeature: false,
            enableQueryIntent: false,
            enableIntentBasedSearchQuery: true,
            enableNameSearchPersonalisation: true,
            enableCTMSV2: true,
            enableResultsOutsideUsersSlice: false,
            enableTagsInElasticsearch: false
          }),
          onboardingData: {
            indications: EMPTY_ARRAY,
            countries: EMPTY_ARRAY,
            states: EMPTY_ARRAY
          },
          projectId,
          useCmnAndJpnFields: false
        };
        const actualQuery = nameSearchBuilder.createNameSearchBody(args);
        const rescoreClause = actualQuery.rescore!;
        if (!Array.isArray(rescoreClause)) {
          const boolQuery = rescoreClause!.query!.rescore_query!.bool;

          expect(boolQuery).not.toBeDefined();
        } else {
          fail("rescore query component is not handled properly");
        }
      });

      it("should not add indication personalisation clause when enableNameSearchPersonalisation is false", () => {
        const query = faker.datatype.string();
        const projectId = faker.datatype.string();
        const userPreferredIndication = faker.datatype.string();

        const qusWithoutIndicationIntent =
          new QueryUnderstandingServiceResponse();

        const nameSearchBuilder = DefaultNameSearchBuilder.getInstance();
        const args: NameSearchBuilderArgWithKeyword = {
          query,
          numberOfNameVariationsToMatch: 1,
          queryUnderstandingServiceResponse: qusWithoutIndicationIntent,
          from: 0,
          size: 10,
          filter: [],
          language: "eng",
          featureFlags: generateFeatureFlags({
            enableHighConfHCPFeature: false,
            enableQueryIntent: false,
            enableIntentBasedSearchQuery: true,
            enableNameSearchPersonalisation: false,
            enableCTMSV2: false,
            enableResultsOutsideUsersSlice: false,
            enableTagsInElasticsearch: false
          }),
          onboardingData: {
            indications: [userPreferredIndication],
            countries: EMPTY_ARRAY,
            states: EMPTY_ARRAY
          },
          projectId,
          useCmnAndJpnFields: false
        };
        const actualQuery = nameSearchBuilder.createNameSearchBody(args);
        const rescoreClause = actualQuery!.rescore!;
        if (!Array.isArray(rescoreClause)) {
          const boolQuery = rescoreClause!.query!.rescore_query!.bool;

          expect(boolQuery).not.toBeDefined();
        } else {
          fail("rescore query component is not handled properly");
        }
      });
    });
  });

  describe("Indication highlight query", () => {
    it("should add l1 and l3 should clauses in name search query", () => {
      const query = faker.datatype.string();
      const projectId = faker.datatype.string();
      const userPreferredCountry = faker.address.country();
      const userPreferredState = faker.address.state();
      const qusWithoutLocationIntent = new QueryUnderstandingServiceResponse();

      const nameSearchBuilder = DefaultNameSearchBuilder.getInstance();
      const args: NameSearchBuilderArgWithKeyword = {
        query,
        numberOfNameVariationsToMatch: 1,
        queryUnderstandingServiceResponse: qusWithoutLocationIntent,
        from: 0,
        size: 10,
        filter: [],
        language: "eng",
        featureFlags: generateFeatureFlags({
          enableHighConfHCPFeature: false,
          enableQueryIntent: false,
          enableIntentBasedSearchQuery: true,
          enableNameSearchPersonalisation: true,
          enableCTMSV2: true,
          enableResultsOutsideUsersSlice: false,
          enableTagsInElasticsearch: false
        }),
        onboardingData: {
          indications: EMPTY_ARRAY,
          countries: [userPreferredCountry],
          states: [userPreferredState]
        },
        projectId,
        useCmnAndJpnFields: false
      };
      const actualQuery = nameSearchBuilder.createNameSearchBody(args);

      const l1IndicationForHCP: QueryDslQueryContainer = {
        nested: {
          path: "indications",
          query: {
            bool: {
              filter: [
                {
                  terms: {
                    ["indications.indicationType"]: ["L1"]
                  }
                },
                {
                  terms: {
                    ["indications.indicationSource"]: ["all"]
                  }
                }
              ]
            }
          },
          inner_hits: {
            _source: false,
            sort: SORT_TIEBREAKER_FOR_INDICATIONS,
            size: 100,
            name: "l1_indications",
            docvalue_fields: ["indications.indication.keyword"]
          },
          score_mode: "none"
        }
      };

      const topL3IndicationForHCP: QueryDslQueryContainer = {
        nested: {
          path: "indications",
          query: {
            function_score: {
              query: {
                bool: {
                  filter: [
                    {
                      terms: {
                        ["indications.indicationType"]: ["L3"]
                      }
                    },
                    {
                      terms: {
                        ["indications.indicationSource"]: ["all"]
                      }
                    }
                  ]
                }
              },
              boost_mode: "replace",
              functions: [
                {
                  field_value_factor: {
                    field: "indications.indicationScore",
                    missing: 0
                  }
                }
              ]
            }
          },
          inner_hits: {
            _source: false,
            sort: SORT_TIEBREAKER_FOR_INDICATIONS,
            size: 5,
            name: "l3_indications",
            docvalue_fields: ["indications.indication.keyword"]
          },
          score_mode: "none"
        }
      };
      const indicationShouldClauses = actualQuery.query!.bool!
        .should as QueryDslQueryContainer[];

      const expectedIndicationShouldClauses = [
        l1IndicationForHCP,
        topL3IndicationForHCP
      ];

      expect(indicationShouldClauses).toEqual(expectedIndicationShouldClauses);
    });
  });

  describe("Congress contributor ranking", () => {
    it("should add congress contributor ranking query with congress filters", () => {
      const query = faker.datatype.string();
      const projectId = faker.datatype.string();
      const userPreferredCountry = faker.address.country();
      const userPreferredState = faker.address.state();
      const qusWithoutLocationIntent = new QueryUnderstandingServiceResponse();

      const nameSearchBuilder = DefaultNameSearchBuilder.getInstance();
      const args: NameSearchBuilderArgWithKeyword = {
        query,
        numberOfNameVariationsToMatch: 1,
        queryUnderstandingServiceResponse: qusWithoutLocationIntent,
        from: 0,
        size: 10,
        filter: [],
        language: "eng",
        featureFlags: generateFeatureFlags({
          enableHighConfHCPFeature: false,
          enableQueryIntent: false,
          enableIntentBasedSearchQuery: true,
          enableNameSearchPersonalisation: true,
          enableCTMSV2: true,
          enableResultsOutsideUsersSlice: false,
          enableTagsInElasticsearch: false
        }),
        onboardingData: {
          indications: EMPTY_ARRAY,
          countries: [userPreferredCountry],
          states: [userPreferredState]
        },
        projectId,
        useCmnAndJpnFields: false,
        useCongressContributorRanking: true,
        congressFilters: [
          {
            nested: {
              path: "congress",
              query: {
                bool: {
                  filter: [
                    {
                      term: {
                        h1_conference_id: faker.datatype.string()
                      }
                    }
                  ]
                }
              }
            }
          }
        ]
      };
      const actualQuery = nameSearchBuilder.createNameSearchBody(args);

      const congressContributorRankingQueries = (
        actualQuery.query!.bool!.must as QueryDslQueryContainer
      ).function_score!.query!.bool!.must as QueryDslQueryContainer[];

      expect(congressContributorRankingQueries).toEqual([
        {
          bool: {
            should: [
              {
                nested: {
                  path: "congress",
                  query: {
                    function_score: {
                      query: {
                        bool: {
                          filter:
                            args.congressFilters?.[0]?.nested?.query?.bool
                              ?.filter,
                          should: [
                            {
                              simple_query_string: {
                                query,
                                fields: [
                                  "congress.role.search",
                                  "congress.title_eng"
                                ],
                                default_operator: "OR"
                              }
                            }
                          ],
                          minimum_should_match: 0
                        }
                      },
                      functions: [
                        {
                          script_score: {
                            script: {
                              source:
                                "doc['congress.role'].value === 'Keynote' ? 2 : 1"
                            }
                          }
                        }
                      ]
                    }
                  }
                }
              },
              {
                term: {
                  isScholarLeader: {
                    value: true,
                    boost: 2
                  }
                }
              }
            ]
          }
        }
      ]);
    });
  });
});
