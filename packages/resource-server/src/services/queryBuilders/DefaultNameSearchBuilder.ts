import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>ield,
  Query<PERSON>lQueryContainer,
  SearchRequest,
  SortCombinations
} from "@elastic/elasticsearch/lib/api/types";
import { QueryIntent } from "../../proto/query_intent_pb";
import { QueryUnderstandingServiceResponse } from "../../proto/query_understanding_service_pb";
import { Language } from "../LanguageDetectService";
import { NameSearchFeatureFlags } from "../NameSearchResourceServiceRewrite";
import {
  HIGH_CONF_PERSON_INNER_HIT_QUERY,
  NameSearchBuilder,
  NameSearchBuilderArgWithKeyword,
  NameSearchBuilderArgWithoutKeyword,
  PROMINENCE_SCORE_WITH_TAG_COUNT
} from "./NameSearchBuilder";
import _ from "lodash";
import { SORT_TIEBREAKER_FOR_INDICATIONS } from "../KeywordSearchMatchedCountService";

export const FUZZINESS = "AUTO";
const NICK_NAME_ANALYZER = "name_nickname_analyzer";
const LAST_NAME_APOSTROPHE_ANALYZER = "last_name_apostrophe_analyzer";
export const MIN_SHOULD_MATCH_CRITERION = "2<-1 3<-2";
const LOCATION_INTENT_SCORE_THRESHOLD = 0.3;
const NAME_SEARCH_TOKEN_THRESHOLD = 0.8;
const INDICATION_BOOST = 4;
const LOCATION_INTENT_BOOST = 3.16;
export const LOCATION_PERSONALISATION_BOOST = 10;
export const INDICATION_PERSONALISATION_BOOST = 5;

export const AT_LEAST_ONE = 1;
export const ALL_ASSETS_OPTIONAL = 0;
export const DEFAULT_NAME_VARIATION_MATCH_COUNT = 2;
export const AFFILIATION_HIGHLIGHT_NAME_PREFIX = "affiliations_";
const NUMBER_OF_HIGHLIGHTS = 2;
const NO_HIGHLIGHT_FIELD_PARAMETERS: Readonly<SearchHighlightField> = {};
export const EXACT_HIT_IN_FULL_NAME = "fullName_hit";
const PROJECT_ID_BOOST = 5;
const KEYNOTE_SPEAKER_BOOST = 2;
const RESEARCH_LEADER_BOOST = 2;

const NAME_SEARCH_SORT: Array<SortCombinations> = [
  {
    _score: {
      order: "desc"
    }
  },
  {
    _script: {
      type: "number",
      script: {
        lang: "painless",
        source: "doc['lastName_eng.sort'].value.length()"
      },
      order: "asc"
    }
  },
  {
    "lastName_eng.sort": "asc"
  },
  {
    _script: {
      type: "number",
      script: {
        lang: "painless",
        source: "doc['firstName_eng.sort'].value.length()"
      },
      order: "asc"
    }
  },
  {
    "firstName_eng.sort": "asc"
  },
  {
    _script: {
      type: "number",
      script: {
        lang: "painless",
        source: "doc['middleName_eng.sort'].value.length()"
      },
      order: "asc"
    }
  },
  {
    "middleName_eng.sort": "asc"
  }
];

const sourceFields = [
  "firstName_eng",
  "lastName_eng",
  "middleName_eng",
  "name_eng",
  "id",
  "h1dn_id",
  "designations",
  "totalWorks",
  "trialCount",
  "paymentTotal",
  "specialty_eng",
  "publicationCount",
  "microBloggingTotal",
  "DRG_diagnosesCount",
  "DRG_proceduresCount",
  "num_prescriptions",
  "presentWorkInstitutionCount",
  "citationTotal",
  "congressCount",
  "affiliations",
  "hasCtms",
  "projectIds",
  "inCtmsNetwork",
  "locations",
  "isInactive",
  "isIndustry",
  "hasSocietyAffiliation",
  "trialEnrollmentRate",
  "totalPatientDocs",
  "isGlobalLeader",
  "isNationalLeader",
  "isRegionalLeader",
  "isLocalLeader",
  "DRG_diagnosesUniqueCount",
  "DRG_proceduresUniqueCount",
  "prescriptions_patient_count"
];

export function isLocationIntentPresent(
  queryUnderstandingServiceResponse:
    | QueryUnderstandingServiceResponse
    | undefined
): boolean {
  if (!queryUnderstandingServiceResponse?.getQueryIntent()?.getEntitiesList()) {
    return false;
  }

  for (const entity of queryUnderstandingServiceResponse
    .getQueryIntent()!
    .getEntitiesList()) {
    const label = entity
      .getLabelsList()
      .find((e) => e.getIntentType() === QueryIntent.IntentType.LOCATION);
    if (
      label &&
      entity.getEntity().length > 0 &&
      label.getScore() > LOCATION_INTENT_SCORE_THRESHOLD
    ) {
      return true;
    }
  }

  return false;
}

export function getProjectIdBoostQuery(projectID: string, boost: number) {
  return {
    constant_score: {
      filter: getProjectIDFilter(projectID),
      boost
    }
  };
}

export function getProjectIDFilter(projectId: string) {
  return {
    term: {
      projectIds: projectId
    }
  };
}

// TODO : This should be revisited once affiliation scores are non binary. Currently they are 1.0 and 0.0
export function getLocationQueryForOnboardingData(
  locations: string[],
  filterField: string,
  weight: number
): QueryDslQueryContainer {
  return {
    function_score: {
      query: {
        nested: {
          path: "affiliations",
          query: {
            bool: {
              must: {
                match_all: {}
              },
              filter: [
                {
                  terms: {
                    [filterField]: locations
                  }
                },
                {
                  term: {
                    "affiliations.accuracyScore": 1.0
                  }
                },
                {
                  term: {
                    "affiliations.isCurrent": true
                  }
                },
                {
                  term: {
                    "affiliations.type": "Work Affiliation"
                  }
                }
              ]
            }
          }
        }
      },
      functions: [
        {
          weight
        }
      ]
    }
  };
}

export default class DefaultNameSearchBuilder implements NameSearchBuilder {
  private static instance: DefaultNameSearchBuilder;

  // eslint-disable-next-line @typescript-eslint/no-empty-function
  private constructor() {}

  public static getInstance(): DefaultNameSearchBuilder {
    if (!DefaultNameSearchBuilder.instance) {
      DefaultNameSearchBuilder.instance = new DefaultNameSearchBuilder();
    }

    return DefaultNameSearchBuilder.instance;
  }

  createNameSearchBody = (
    args: NameSearchBuilderArgWithKeyword
  ): SearchRequest => {
    const l1IndicationForHCP: QueryDslQueryContainer = {
      nested: {
        path: "indications",
        query: {
          bool: {
            filter: [
              {
                terms: {
                  ["indications.indicationType"]: ["L1"]
                }
              },
              {
                terms: {
                  ["indications.indicationSource"]: ["all"]
                }
              }
            ]
          }
        },
        inner_hits: {
          _source: false,
          sort: SORT_TIEBREAKER_FOR_INDICATIONS,
          size: 100,
          name: "l1_indications",
          docvalue_fields: ["indications.indication.keyword"]
        },
        score_mode: "none"
      }
    };

    const topL3IndicationForHCP: QueryDslQueryContainer = {
      nested: {
        path: "indications",
        query: {
          function_score: {
            query: {
              bool: {
                filter: [
                  {
                    terms: {
                      ["indications.indicationType"]: ["L3"]
                    }
                  },
                  {
                    terms: {
                      ["indications.indicationSource"]: ["all"]
                    }
                  }
                ]
              }
            },
            boost_mode: "replace",
            functions: [
              {
                field_value_factor: {
                  field: "indications.indicationScore",
                  missing: 0
                }
              }
            ]
          }
        },
        inner_hits: {
          _source: false,
          sort: SORT_TIEBREAKER_FOR_INDICATIONS,
          size: 5,
          name: "l3_indications",
          docvalue_fields: ["indications.indication.keyword"]
        },
        score_mode: "none"
      }
    };
    const indicationShouldClauses = [l1IndicationForHCP, topL3IndicationForHCP];
    const sizeOf = args.size;
    let _source = args.sourceOverride ?? sourceFields;

    if (args.useCongressContributorRanking) {
      _source = [..._source, "congress"];
    }

    const from = args.from;
    const searchRequest: SearchRequest = {
      track_total_hits: true,
      _source_includes: _source,
      from,
      size: sizeOf,
      query: {
        bool: {
          must:
            args.featureFlags.enableHighConfHCPFeature && from == 0
              ? this.nameQueryWithHighConfPersonNestedInfo(args)
              : this.nameQueryWithoutHighConfPersonNestedInfo(args),
          should: indicationShouldClauses,
          minimum_should_match: 0
        }
      },
      rescore: {
        // Rescore top 10 results for each shard
        window_size: 10,
        query: {
          rescore_query: this.rescoreQuery(args),
          query_weight: 1,
          rescore_query_weight: 1
        }
      }
    };

    return searchRequest;
  };

  createNameSearchBodyNoKeyWord = (
    args: NameSearchBuilderArgWithoutKeyword
  ): SearchRequest => {
    const _source = sourceFields;
    const sizeOf = args.size;
    const from = args.from;
    const filter = args.filter;
    return {
      track_total_hits: true,
      _source_includes: _source,
      from,
      size: sizeOf,
      query: {
        function_score: {
          query: {
            bool: {
              filter,
              must: [{ match_all: {} }]
            }
          },
          functions: PROMINENCE_SCORE_WITH_TAG_COUNT,
          // All script scores should be added
          boost_mode: "sum",
          // Final score from function score should be added to the document score
          score_mode: "sum"
        }
      },
      sort: NAME_SEARCH_SORT
    };
  };

  private firstNameVariationQueries(query: string): QueryDslQueryContainer {
    return {
      dis_max: {
        queries: [
          {
            match: {
              firstName_eng: {
                _name: "first_name_fuzzy",
                query: query,
                fuzziness: FUZZINESS,
                boost: 0.5
              }
            }
          },
          {
            match: {
              firstName_eng: {
                _name: "firstName",
                query: query
              }
            }
          },
          {
            match: {
              "firstName_eng.partial_name": {
                _name: "firstName_partial_name",
                query: query,
                boost: 0.7
              }
            }
          },
          {
            match: {
              firstName_eng: {
                _name: "firstName_nick_name_query_time",
                query: query,
                analyzer: NICK_NAME_ANALYZER,
                boost: 0.7
              }
            }
          },
          // Handle data issue where we have nick name in profile
          // Once data issue is fixed this won't be required.
          {
            match: {
              "firstName_eng.nickname": {
                _name: "firstName_nick_name_index_time",
                query: query,
                boost: 0.7
              }
            }
          }
        ]
      }
    };
  }

  private middleNameVariationQueries(query: string): QueryDslQueryContainer {
    return {
      dis_max: {
        queries: [
          {
            match: {
              middleName_eng: {
                _name: "middle_name_fuzzy",
                query: query,
                boost: 0.25,
                fuzziness: FUZZINESS
              }
            }
          },
          {
            match: {
              middleName_eng: {
                _name: "middle_name",
                query: query,
                boost: 0.5
              }
            }
          },
          {
            match: {
              middleName_eng: {
                _name: "middle_name_nickname",
                query: query,
                analyzer: NICK_NAME_ANALYZER,
                boost: 0.3
              }
            }
          },
          {
            match: {
              "middleName_eng.initial": {
                _name: "middle_name_initial",
                query: query,
                boost: 0.2
              }
            }
          }
        ]
      }
    };
  }

  private lastNameVariationsQueries(query: string): QueryDslQueryContainer {
    return {
      dis_max: {
        queries: [
          {
            match: {
              lastName_eng: {
                _name: "last_name",
                query: query,
                boost: 1.5
              }
            }
          },
          {
            match: {
              lastName_eng: {
                _name: "last_name_apostrophe",
                query: query,
                analyzer: LAST_NAME_APOSTROPHE_ANALYZER,
                boost: 1.2
              }
            }
          },
          {
            match: {
              "lastName_eng.apostrophe_variations": {
                _name: "last_name_apostrophe_index_time_variations",
                query: query,
                boost: 1.2
              }
            }
          },
          {
            match: {
              "lastName_eng.stripped": {
                _name: "last_name_stripped",
                query: query,
                boost: 1.0
              }
            }
          },
          {
            match: {
              lastName_eng: {
                _name: "last_name_fuzzy",
                query: query,
                boost: 0.7,
                fuzziness: FUZZINESS
              }
            }
          },
          {
            match: {
              lastName_eng: {
                _name: "last_name_apostrophe_fuzzy",
                query: query,
                analyzer: LAST_NAME_APOSTROPHE_ANALYZER,
                boost: 0.7,
                fuzziness: FUZZINESS
              }
            }
          },
          {
            match: {
              "lastName_eng.metaphone": {
                _name: "last_name_metaphone",
                query: query,
                boost: 0.5
              }
            }
          }
        ]
      }
    };
  }

  private nameVariationsQuery(
    query: string,
    numberOfNameVariationsToMatch: number,
    projectId: string
  ): QueryDslQueryContainer {
    return {
      bool: {
        filter: getProjectIDFilter(projectId),
        should: [
          this.firstNameVariationQueries(query),
          this.middleNameVariationQueries(query),
          this.lastNameVariationsQueries(query)
        ],
        minimum_should_match: numberOfNameVariationsToMatch
      }
    };
  }

  private crossFieldMultimatchQuery(
    query: string,
    projectId: string
  ): QueryDslQueryContainer {
    return {
      bool: {
        filter: getProjectIDFilter(projectId),
        must: [
          {
            multi_match: {
              _name: "multi_match_name",
              query: query,
              operator: "or",
              minimum_should_match: MIN_SHOULD_MATCH_CRITERION,
              type: "cross_fields",
              fields: [
                "firstName_eng",
                "middleName_eng^0.5",
                "lastName_eng^1.5"
              ]
            }
          }
        ]
      }
    };
  }

  private nameAndAttributeMatchQuery(
    query: string,
    projectId: string
  ): QueryDslQueryContainer {
    return {
      bool: {
        filter: [
          {
            match: {
              name_eng: {
                query: query
              }
            }
          },
          getProjectIDFilter(projectId)
        ],
        must: [
          {
            multi_match: {
              _name: "multi_match_name_and_attributes",
              query: query,
              operator: "and",
              type: "cross_fields",
              boost: 1,
              fields: [
                "firstName_eng^2",
                "firstName_eng.partial_name^0.5",
                "middleName_eng^1.5",
                "middleName_eng.initial^0.2",
                "lastName_eng^2.5",
                "lastName_eng.apostrophe_variations^0.5",
                "specialty_eng.search",
                "presentWorkInstitutionNames_eng.search^0.5",
                "locations.country_eng.search",
                "locations.state_eng.search",
                "locations.city_eng.search^0.75"
              ]
            }
          }
        ]
      }
    };
  }

  // Here boost is 0 as this query is just used as retrieval criterion to qualify results outside the slice
  // and if they are actually relevant they are boosted in rescore query.
  private getExactMatchQueryInFullName(query: string): QueryDslQueryContainer {
    return {
      match: {
        name_eng: {
          _name: EXACT_HIT_IN_FULL_NAME,
          query: query,
          operator: "AND",
          boost: 0
        }
      }
    };
  }

  private rescoreQuery(
    args: NameSearchBuilderArgWithKeyword
  ): QueryDslQueryContainer {
    const personalisationShouldCluase: QueryDslQueryContainer[] =
      this.getPersonalisationBoostQuery(args);

    if (
      !args.featureFlags.enableNameSearchPersonalisation ||
      _.isEmpty(personalisationShouldCluase)
    ) {
      if (args.featureFlags.enableResultsOutsideUsersSlice) {
        return this.getRescoreQueryWithProjectIDBoost(
          args.query,
          args.projectId
        );
      } else {
        return this.getDisMaxRescoreQuery(args.query);
      }
    } else {
      const rescoreShouldClause: QueryDslQueryContainer[] = [];
      rescoreShouldClause.push(this.getDisMaxRescoreQuery(args.query));

      if (args.featureFlags.enableResultsOutsideUsersSlice) {
        rescoreShouldClause.push(
          getProjectIdBoostQuery(args.projectId, PROJECT_ID_BOOST)
        );
      }

      return this.getRescoreQueryWithPersonalisation(
        args.query,
        personalisationShouldCluase,
        rescoreShouldClause
      );
    }
  }

  private getDisMaxRescoreQuery(query: string): QueryDslQueryContainer {
    return {
      dis_max: {
        queries: [
          {
            match: {
              firstLastName_eng: {
                query: query,
                prefix_length: 1,
                operator: "OR",
                fuzziness: FUZZINESS,
                boost: 1,
                minimum_should_match: MIN_SHOULD_MATCH_CRITERION
              }
            }
          },
          {
            match: {
              firstLastName_eng: {
                query: query,
                boost: 4,
                operator: "AND"
              }
            }
          },
          {
            match: {
              name_eng: {
                query: query,
                boost: 2,
                operator: "AND"
              }
            }
          }
        ]
      }
    };
  }

  private getRescoreQueryWithPersonalisation(
    query: string,
    personalisationShouldClause: QueryDslQueryContainer[],
    rescoreShouldClause: QueryDslQueryContainer[]
  ): QueryDslQueryContainer {
    return {
      bool: {
        should: [
          ...rescoreShouldClause,
          {
            bool: {
              filter: {
                match: {
                  firstLastName_eng: {
                    query: query,
                    prefix_length: 1,
                    operator: "OR",
                    fuzziness: FUZZINESS,
                    boost: 1,
                    minimum_should_match: MIN_SHOULD_MATCH_CRITERION
                  }
                }
              },
              should: personalisationShouldClause,
              minimum_should_match: AT_LEAST_ONE
            }
          }
        ]
      }
    };
  }

  private getRescoreQueryWithProjectIDBoost(
    query: string,
    projectID: string
  ): QueryDslQueryContainer {
    return {
      bool: {
        should: [
          this.getDisMaxRescoreQuery(query),
          getProjectIdBoostQuery(projectID, PROJECT_ID_BOOST)
        ]
      }
    };
  }

  private getPersonalisationBoostQuery(
    args: NameSearchBuilderArgWithKeyword
  ): QueryDslQueryContainer[] {
    const shoulds: QueryDslQueryContainer[] = [];
    const { onboardingData } = args;

    if (
      !this.isIndicationIntentPresent(args.queryUnderstandingServiceResponse) &&
      !_.isEmpty(onboardingData.indications)
    ) {
      shoulds.push(
        ...this.getIndicationQueryForOnboardingData(onboardingData.indications)
      );
    }

    if (!isLocationIntentPresent(args.queryUnderstandingServiceResponse)) {
      if (!_.isEmpty(onboardingData.countries)) {
        shoulds.push(
          getLocationQueryForOnboardingData(
            onboardingData.countries,
            "affiliations.institution.filters.country",
            LOCATION_PERSONALISATION_BOOST
          )
        );
      }

      if (!_.isEmpty(onboardingData.states)) {
        shoulds.push(
          getLocationQueryForOnboardingData(
            onboardingData.states,
            "affiliations.institution.filters.region",
            LOCATION_PERSONALISATION_BOOST
          )
        );
      }
    }

    return shoulds;
  }

  // TODO : Need to add indication synonyms clause as well
  // TODO: add indication source filter (= 'all') once its available in index.
  private getIndicationQueryForOnboardingData(
    indications: string[]
  ): QueryDslQueryContainer[] {
    return indications.map((indication) => {
      return {
        nested: {
          path: "indications",
          query: {
            function_score: {
              query: {
                constant_score: {
                  filter: {
                    term: {
                      "indications.indication.keyword": indication
                    }
                  }
                }
              },
              functions: [
                {
                  field_value_factor: {
                    field: "indications.indicationScore",
                    factor: INDICATION_PERSONALISATION_BOOST,
                    missing: 0
                  }
                }
              ]
            }
          }
        }
      };
    });
  }

  private nameQueryWithoutHighConfPersonNestedInfo(
    args: NameSearchBuilderArgWithKeyword
  ): QueryDslQueryContainer {
    return {
      function_score: {
        query: {
          bool: {
            filter: args.filter,
            must: args.useCongressContributorRanking
              ? this.getCongressContributorRankingQueries(
                  args.query,
                  args.congressFilters
                )
              : undefined,
            should: this.getNameQueryShouldClauses(
              args.query,
              args.projectId,
              args.useCmnAndJpnFields,
              args.numberOfNameVariationsToMatch,
              args.language,
              args.queryUnderstandingServiceResponse,
              args.featureFlags
            ),
            minimum_should_match: AT_LEAST_ONE
          }
        },
        functions: PROMINENCE_SCORE_WITH_TAG_COUNT,
        // All script scores should be added
        boost_mode: "sum",
        // Final score from function score should be added to the document score
        score_mode: "sum"
      }
    };
  }

  public getNameQueryShouldClauses(
    query: string,
    projectId: string,
    useCmnAndJpnFields = false,
    numberOfNameVariationsToMatch: number,
    lang?: Language,
    queryUnderstandingServiceResponse?: QueryUnderstandingServiceResponse,
    featureFlags?: NameSearchFeatureFlags
  ): QueryDslQueryContainer[] {
    const shoulds: QueryDslQueryContainer[] = [];
    shoulds.push(
      this.nameVariationsQuery(query, numberOfNameVariationsToMatch, projectId)
    );
    shoulds.push(this.crossFieldMultimatchQuery(query, projectId));
    shoulds.push(this.nameAndAttributeMatchQuery(query, projectId));
    shoulds.push(this.getExactMatchQueryInFullName(query));

    if (featureFlags?.enableIntentBasedSearchQuery) {
      const indicationClause = this.indicationMatchClause(
        query,
        queryUnderstandingServiceResponse
      );
      if (indicationClause) {
        shoulds.push(indicationClause);
      }

      const locationClause = this.locationMatchClause(
        query,
        queryUnderstandingServiceResponse
      );
      if (locationClause) {
        shoulds.push(locationClause);
      }

      const specialtyClause = this.specialtyMatchClause(
        query,
        queryUnderstandingServiceResponse
      );
      if (specialtyClause) {
        shoulds.push(specialtyClause);
      }
    }

    return shoulds;
  }

  private nameQueryWithHighConfPersonNestedInfo(
    args: NameSearchBuilderArgWithKeyword
  ): QueryDslQueryContainer {
    const congressContributorRankingQueries = args.useCongressContributorRanking
      ? this.getCongressContributorRankingQueries(
          args.query,
          args.congressFilters
        )
      : [];

    return {
      function_score: {
        query: {
          bool: {
            filter: args.filter,
            must: [
              {
                bool: {
                  should: this.getNameQueryShouldClauses(
                    args.query,
                    args.projectId,
                    args.useCmnAndJpnFields,
                    args.numberOfNameVariationsToMatch,
                    args.language,
                    args.queryUnderstandingServiceResponse,
                    args.featureFlags
                  ),
                  minimum_should_match: AT_LEAST_ONE
                }
              },
              ...congressContributorRankingQueries
            ],
            should: HIGH_CONF_PERSON_INNER_HIT_QUERY
          }
        },
        functions: PROMINENCE_SCORE_WITH_TAG_COUNT,
        // All script scores should be added
        boost_mode: "sum",
        // Final score from function score should be added to the document score
        score_mode: "sum"
      }
    };
  }

  private isIndicationIntentPresent(
    queryUnderstandingServiceResponse:
      | QueryUnderstandingServiceResponse
      | undefined
  ): boolean {
    if (
      !queryUnderstandingServiceResponse?.getQueryIntent()?.getEntitiesList()
    ) {
      return false;
    }

    for (const entity of queryUnderstandingServiceResponse
      .getQueryIntent()!
      .getEntitiesList()) {
      const label = entity
        .getLabelsList()
        .find((e) => e.getIntentType() === QueryIntent.IntentType.INDICATION);
      if (label && entity.getEntity().length > 0 && label.getScore() == 1.0) {
        return true;
      }
    }

    return false;
  }

  // TODO: add indication source filter (= 'all') once its available.
  private getNestedIndicationTermQuery(
    indication: string,
    boost: number,
    indicationScore: number
  ) {
    return {
      nested: {
        path: "indications",
        query: {
          function_score: {
            query: {
              term: {
                "indications.indication.keyword": indication
              }
            },
            functions: [
              {
                field_value_factor: {
                  field: "indications.indicationScore",
                  factor: indicationScore * boost,
                  missing: 0
                }
              }
            ]
          }
        }
      }
    };
  }

  private indicationMatchClause(
    query: string,
    queryUnderstandingServiceResponse:
      | QueryUnderstandingServiceResponse
      | undefined
  ): QueryDslQueryContainer | undefined {
    if (
      !queryUnderstandingServiceResponse?.getQueryIntent()?.getEntitiesList()
    ) {
      return undefined;
    }
    const shouldClauses: QueryDslQueryContainer[] = [];
    for (const entity of queryUnderstandingServiceResponse
      .getQueryIntent()!
      .getEntitiesList()) {
      const label = entity
        .getLabelsList()
        .find((e) => e.getIntentType() === QueryIntent.IntentType.INDICATION);
      if (label && entity.getEntity().length > 0 && label.getScore() == 1.0) {
        shouldClauses.push(
          this.getNestedIndicationTermQuery(
            entity.getEntity(),
            INDICATION_BOOST,
            label.getScore()
          )
        );
      }
    }
    return shouldClauses.length > 0
      ? {
          bool: {
            filter: {
              match: {
                name_eng: {
                  query: this.tokensToSearchInNames(
                    query,
                    queryUnderstandingServiceResponse
                  )
                }
              }
            },
            should: shouldClauses,
            minimum_should_match: AT_LEAST_ONE
          }
        }
      : undefined;
  }

  private locationMatchClause(
    query: string,
    queryUnderstandingServiceResponse:
      | QueryUnderstandingServiceResponse
      | undefined
  ): QueryDslQueryContainer | undefined {
    if (
      !queryUnderstandingServiceResponse?.getQueryIntent()?.getEntitiesList()
    ) {
      return undefined;
    }
    let innerHitCount = 0;
    const shouldClauses: QueryDslQueryContainer[] = [];
    for (const entity of queryUnderstandingServiceResponse
      .getQueryIntent()!
      .getEntitiesList()) {
      const label = entity
        .getLabelsList()
        .find((e) => e.getIntentType() === QueryIntent.IntentType.LOCATION);
      if (
        label &&
        entity.getEntity().length > 0 &&
        label.getScore() > LOCATION_INTENT_SCORE_THRESHOLD
      ) {
        shouldClauses.push({
          nested: {
            path: "affiliations",
            query: {
              function_score: {
                query: {
                  constant_score: {
                    filter: {
                      multi_match: {
                        query: entity.getEntity(),
                        fields: [
                          "affiliations.institution.address.city",
                          "affiliations.institution.address.region",
                          "affiliations.institution.address.country"
                        ],
                        operator: "and"
                      }
                    }
                  }
                },
                functions: [
                  {
                    field_value_factor: {
                      field: "affiliations.accuracyScore",
                      factor: label.getScore() * LOCATION_INTENT_BOOST,
                      modifier: "square",
                      missing: 0
                    }
                  }
                ]
              }
            },
            inner_hits: {
              highlight: {
                order: "score",
                fields: {
                  "affiliations.institution.address.city":
                    NO_HIGHLIGHT_FIELD_PARAMETERS,
                  "affiliations.institution.address.region":
                    NO_HIGHLIGHT_FIELD_PARAMETERS,
                  "affiliations.institution.address.country":
                    NO_HIGHLIGHT_FIELD_PARAMETERS
                }
              },
              _source: false,
              size: NUMBER_OF_HIGHLIGHTS,
              docvalue_fields: [
                "affiliations.id",
                "affiliations.institution.id"
              ],
              name: `${AFFILIATION_HIGHLIGHT_NAME_PREFIX}${innerHitCount}`
            }
          }
        });
        innerHitCount++;
      }
    }
    return shouldClauses.length > 0
      ? {
          bool: {
            filter: {
              match: {
                name_eng: {
                  query: this.tokensToSearchInNames(
                    query,
                    queryUnderstandingServiceResponse
                  ),
                  operator: "AND",
                  prefix_length: 2,
                  fuzziness: FUZZINESS
                }
              }
            },
            should: shouldClauses,
            minimum_should_match: AT_LEAST_ONE
          }
        }
      : undefined;
  }

  private specialtyMatchClause(
    query: string,
    queryUnderstandingServiceResponse:
      | QueryUnderstandingServiceResponse
      | undefined
  ): QueryDslQueryContainer | undefined {
    if (
      !queryUnderstandingServiceResponse?.getQueryIntent()?.getEntitiesList()
    ) {
      return undefined;
    }
    const shouldClauses: QueryDslQueryContainer[] = [];
    for (const entity of queryUnderstandingServiceResponse
      .getQueryIntent()!
      .getEntitiesList()) {
      const label = entity
        .getLabelsList()
        .find((e) => e.getIntentType() === QueryIntent.IntentType.SPECIALIST);
      if (label && entity.getEntity().length > 0 && label.getScore() > 0) {
        shouldClauses.push({
          match: {
            specialty_eng: {
              query: entity.getEntity(),
              operator: "and",
              boost: label.getScore()
            }
          }
        });
      }
    }
    return shouldClauses.length > 0
      ? {
          bool: {
            filter: {
              match: {
                name_eng: {
                  query: this.tokensToSearchInNames(
                    query,
                    queryUnderstandingServiceResponse
                  )
                }
              }
            },
            should: shouldClauses,
            minimum_should_match: AT_LEAST_ONE
          }
        }
      : undefined;
  }

  private tokensToSearchInNames(
    query: string,
    queryUnderstandingServiceResponse: QueryUnderstandingServiceResponse
  ): string {
    const tokensToSearchInNames: string[] = [];
    for (const entity of queryUnderstandingServiceResponse
      .getQueryIntent()!
      .getEntitiesList()) {
      const label = entity
        .getLabelsList()
        .find(
          (e) =>
            e.getIntentType() === QueryIntent.IntentType.LOCATION ||
            e.getIntentType() === QueryIntent.IntentType.SPECIALIST ||
            e.getIntentType() === QueryIntent.IntentType.INDICATION
        );
      // remove tokens that are high confidence location, specialist or indication
      if (!(label && label.getScore() > NAME_SEARCH_TOKEN_THRESHOLD)) {
        tokensToSearchInNames.push(entity.getEntity());
      }
    }

    return tokensToSearchInNames.length === 0
      ? query
      : tokensToSearchInNames.join(" ");
  }

  private getCongressContributorRankingQueries(
    query: string,
    congressFilters?: QueryDslQueryContainer[]
  ): QueryDslQueryContainer[] {
    if (!congressFilters?.length) {
      return [
        {
          match_all: {}
        }
      ];
    }

    const filters =
      congressFilters[0].function_score?.query?.nested?.query?.bool?.filter ??
      congressFilters[0].nested?.query.bool?.filter;

    const congressNestedQuery: QueryDslQueryContainer = {
      nested: {
        path: "congress",
        query: {
          function_score: {
            query: {
              bool: {
                filter: filters,
                should: [
                  {
                    simple_query_string: {
                      query,
                      fields: ["congress.role.search", "congress.title_eng"],
                      default_operator: "OR"
                    }
                  }
                ],
                minimum_should_match: ALL_ASSETS_OPTIONAL
              }
            },
            functions: [
              {
                script_score: {
                  script: {
                    source: `doc['congress.role'].value === 'Keynote' ? ${KEYNOTE_SPEAKER_BOOST} : 1`
                  }
                }
              }
            ]
          }
        }
      }
    };

    const isResearchLeaderQuery = {
      term: {
        isScholarLeader: {
          value: true,
          boost: RESEARCH_LEADER_BOOST
        }
      }
    };

    return [
      {
        bool: {
          should: [congressNestedQuery, isResearchLeaderQuery]
        }
      }
    ];
  }
}
