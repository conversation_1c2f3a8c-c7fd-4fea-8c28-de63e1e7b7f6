import { Service } from "typedi";
import { RpcMethod, RpcResourceService, RpcService } from "@h1nyc/systems-rpc";
import { QueryUnderstandingResource } from "@h1nyc/search-sdk/dist/resources/QueryUnderstandingResource";
import { Trace } from "../Tracer";
import { createLogger } from "../lib/Logger";
import { QueryParserService } from "./QueryParserService";
import { ConfigService } from "./ConfigService";
import {
  QueryIntent,
  RPC_NAMESPACE_QUERY_UNDERSTANDING,
  SearchTypes
} from "@h1nyc/search-sdk";
import { QueryUnderstandingServiceClient } from "./QueryUnderstandingServiceClient";
import { getQueryIntents } from "../lib/query_understanding/QueryUnderstandingResponseAnalyzer";
import { uniq } from "lodash";
import { getLanguageDetector } from "./KeywordSearchResourceServiceRewrite";
import { Language, JAPANESE, CHINESE, ENGLISH } from "./LanguageDetectService";
import { UserResourceClient } from "@h1nyc/account-sdk";
import { coerceStringLanguageToTypeLanguage } from "./KeywordAutocompleteResourceService";

const HAS_ADVANCED_OPERATORS = /(\sAND\s|\sOR\s|NOT\s)/;

@Service()
@RpcService()
export class QueryUnderstandingResourceService
  extends RpcResourceService
  implements QueryUnderstandingResource
{
  private readonly logger = createLogger(this);

  constructor(
    config: ConfigService,
    private queryParserService: QueryParserService,
    private queryUnderstandingServiceClient: QueryUnderstandingServiceClient,
    private userClient: UserResourceClient
  ) {
    super(
      config.searchRpcSigningKey,
      RPC_NAMESPACE_QUERY_UNDERSTANDING,
      config.searchRedisOptions
    );
  }

  @RpcMethod()
  isReady(): Promise<boolean> {
    return Promise.resolve(true);
  }

  @RpcMethod()
  @Trace("h1-search.qus.synonyms")
  async getKeywordSynonyms(keyword: string): Promise<string[]> {
    this.logger.debug(`Requested synonyms for keyword ${keyword}`);
    return this.queryParserService.getKeywordSynonyms(keyword);
  }

  @Trace("h1-search.qus.getUsersPreferredLanguage")
  private async getUsersPreferredLanguage(
    userId = "",
    projectId: string,
    language?: string
  ): Promise<Language> {
    const suppliedLanguage = coerceStringLanguageToTypeLanguage(language);

    if (suppliedLanguage) {
      return suppliedLanguage;
    }

    try {
      const usersPreferredLanguage =
        await this.userClient.getUsersPreferredLanguage(userId, projectId);
      this.logger.debug(
        `users preferred language: ${usersPreferredLanguage?.language}`
      );

      switch (usersPreferredLanguage?.language) {
        case "japanese":
        case JAPANESE:
          return JAPANESE;
        case "chinese_simplified":
        case "chinese_traditional":
        case CHINESE:
          return CHINESE;
        case "english":
        case ENGLISH:
        default:
          return ENGLISH;
      }
    } catch (err) {
      this.logger.error(
        { err },
        "Error thrown fetching users preferred language"
      );
      return ENGLISH;
    }
  }

  @RpcMethod()
  @Trace("h1-search.qus.getSearchTypes")
  async getSearchTypes(
    query: string,
    projectId: string,
    userId?: string,
    language?: string
  ): Promise<SearchTypes[]> {
    // TODO: This should be handled by QUS once advanced queries are supported
    if (HAS_ADVANCED_OPERATORS.test(query)) {
      return [SearchTypes.KEYWORD];
    }

    const usersPreferredLanguage = await this.getUsersPreferredLanguage(
      userId,
      projectId,
      language
    );
    const languageDetector = getLanguageDetector(usersPreferredLanguage);
    const detectedLanguage = languageDetector(query);
    const queryUnderstandingServiceResponse =
      await this.queryUnderstandingServiceClient.analyze(
        query.trim(),
        detectedLanguage
      );
    const queryIntents = getQueryIntents(
      query,
      queryUnderstandingServiceResponse
    );
    const searchTypes: SearchTypes[] = [];

    if (queryIntents.includes(QueryIntent.INSTITUTION)) {
      searchTypes.push(SearchTypes.INSTITUTION);
    }

    if (
      queryIntents.includes(QueryIntent.DISEASE) &&
      !queryIntents.includes(QueryIntent.PERSON)
    ) {
      searchTypes.push(SearchTypes.KEYWORD);
    }

    if (
      queryIntents.includes(QueryIntent.PERSON) &&
      !queryIntents.includes(QueryIntent.DISEASE)
    ) {
      searchTypes.push(SearchTypes.NAME);
    }

    if (queryIntents.includes(QueryIntent.CONFERENCE)) {
      searchTypes.push(SearchTypes.KEYWORD);
    }

    if (queryIntents.includes(QueryIntent.SPECIALIST)) {
      searchTypes.push(SearchTypes.KEYWORD);
    }

    if (searchTypes.length == 0) {
      return [SearchTypes.NAME, SearchTypes.KEYWORD];
    } else {
      return uniq(searchTypes);
    }
  }
}
