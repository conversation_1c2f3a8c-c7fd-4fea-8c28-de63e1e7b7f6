import { faker } from "@faker-js/faker";
import { AGGREGATIONS_THAT_NEED_NESTED_FILTERING } from "./KeywordAutocompleteResourceService";
import { KeywordFilterAutocompleteFilterField } from "@h1nyc/search-sdk";
import {
  DocCountBucket,
  FilteredDocCountBucket,
  KeywordAutocompleteResponseAdapterService
} from "./KeywordAutocompleteResponseAdapterService";
import _, { toLower } from "lodash";
import { createMockInstance } from "../util/TestUtils";
import { LocationLabelFormatterService } from "./LocationLabelFormatterService";
import {
  CcsrIcdMappingRepository,
  CcsrPxMappingRepository
} from "@h1nyc/pipeline-repositories";
import { generateFilters } from "./KeywordSearchResourceServiceRewrite.test";

function generateMockAggregates(buckets: Array<DocCountBucket> = []) {
  return {
    doc_count_error_upper_bound: faker.datatype.number(),
    sum_other_doc_count: faker.datatype.number(),
    buckets: buckets as DocCountBucket[]
  };
}

function generateMockAggregatesFilteredDocCount(
  buckets: Array<FilteredDocCountBucket> = []
) {
  return {
    doc_count_error_upper_bound: faker.datatype.number(),
    sum_other_doc_count: faker.datatype.number(),
    buckets: buckets as FilteredDocCountBucket[]
  };
}

describe("KeywordAutocompleteResponseAdapterService", () => {
  it("should parse basic matching aggregations and skip buckets with 0 doc_count", async () => {
    const bucketValues: Array<DocCountBucket> = [
      {
        key: faker.datatype.string(),
        doc_count: faker.datatype.number()
      },
      {
        key: faker.datatype.string(),
        doc_count: 0
      },
      {
        key: faker.datatype.string(),
        doc_count: faker.datatype.number()
      }
    ];
    const aggregations = {
      matching: generateMockAggregates(bucketValues)
    };

    const locationLabelFormatterService = createMockInstance(
      LocationLabelFormatterService
    );
    const ccsrIcdMappingRepository = createMockInstance(
      CcsrIcdMappingRepository
    );
    const ccsrPxMappingRepository = createMockInstance(CcsrPxMappingRepository);

    const keywordAutocompleteResponseAdapterService =
      new KeywordAutocompleteResponseAdapterService(
        locationLabelFormatterService,
        ccsrIcdMappingRepository,
        ccsrPxMappingRepository
      );

    const filterField = KeywordFilterAutocompleteFilterField.SPECIALTY;

    const buckets = await keywordAutocompleteResponseAdapterService.adapt(
      aggregations,
      filterField,
      []
    );

    expect(buckets).toEqual([
      {
        id: aggregations.matching.buckets[0].key,
        count: aggregations.matching.buckets[0].doc_count
      },
      {
        id: aggregations.matching.buckets[2].key,
        count: aggregations.matching.buckets[2].doc_count
      }
    ]);
  });

  describe("nested->filtered->matching location and region aggregations", () => {
    it("COUNTRY: should expand bucket keys using country expander", async () => {
      const expandedKey1 = faker.datatype.string();
      const expandedKey2 = faker.datatype.string();
      const expandedKey3 = faker.datatype.string();
      const expandedKey4 = faker.datatype.string();
      const expandedKey5 = faker.datatype.string();
      const expandedKey6 = faker.datatype.string();

      const locationBucketValues: Array<FilteredDocCountBucket> = [
        {
          key: faker.datatype.string(),
          doc_count: faker.datatype.number(),
          matching: {
            doc_count: faker.datatype.number()
          },
          regions_in: {
            buckets: [
              {
                key: faker.datatype.string(),
                doc_count: faker.datatype.number()
              }
            ]
          }
        },
        {
          key: faker.datatype.string(),
          doc_count: faker.datatype.number(),
          matching: {
            doc_count: 0
          }
        },
        {
          key: faker.datatype.string(),
          doc_count: faker.datatype.number(),
          matching: {
            doc_count: faker.datatype.number()
          },
          regions_in: {
            buckets: [
              {
                key: faker.datatype.string(),
                doc_count: faker.datatype.number()
              }
            ]
          }
        }
      ];
      const regionBucketValues: Array<FilteredDocCountBucket> = [
        {
          key: faker.datatype.string(),
          doc_count: faker.datatype.number(),
          matching: {
            doc_count: faker.datatype.number()
          },
          locations_in_region: {
            buckets: [
              {
                key: faker.datatype.string(),
                doc_count: faker.datatype.number(),
                matching: {
                  doc_count: faker.datatype.number()
                }
              }
            ]
          }
        },
        {
          key: faker.datatype.string(),
          doc_count: faker.datatype.number(),
          matching: {
            doc_count: 0
          },
          locations_in_region: {
            buckets: []
          }
        },
        {
          key: faker.datatype.string(),
          doc_count: faker.datatype.number(),
          matching: {
            doc_count: faker.datatype.number()
          },
          locations_in_region: {
            buckets: [
              {
                key: faker.datatype.string(),
                doc_count: faker.datatype.number(),
                matching: {
                  doc_count: faker.datatype.number()
                }
              }
            ]
          }
        }
      ];
      const aggregations = {
        nested: {
          filtered_matching: {
            filtered_matching_locations: {
              matching_locations:
                generateMockAggregatesFilteredDocCount(locationBucketValues)
            },
            filtered_matching_regions: {
              matching_regions:
                generateMockAggregatesFilteredDocCount(regionBucketValues)
            }
          }
        }
      };

      const locationLabelFormatterService = createMockInstance(
        LocationLabelFormatterService
      );
      locationLabelFormatterService.country.mockReturnValueOnce(expandedKey1);
      locationLabelFormatterService.country.mockReturnValueOnce(expandedKey2);
      locationLabelFormatterService.country.mockReturnValueOnce(expandedKey3);
      locationLabelFormatterService.country.mockReturnValueOnce(expandedKey4);
      locationLabelFormatterService.country.mockReturnValueOnce(expandedKey5);
      locationLabelFormatterService.country.mockReturnValueOnce(expandedKey6);
      const ccsrIcdMappingRepository = createMockInstance(
        CcsrIcdMappingRepository
      );
      const ccsrPxMappingRepository = createMockInstance(
        CcsrPxMappingRepository
      );

      const keywordAutocompleteResponseAdapterService =
        new KeywordAutocompleteResponseAdapterService(
          locationLabelFormatterService,
          ccsrIcdMappingRepository,
          ccsrPxMappingRepository
        );

      const filterField = KeywordFilterAutocompleteFilterField.COUNTRY;

      const buckets = await keywordAutocompleteResponseAdapterService.adapt(
        aggregations,
        filterField,
        []
      );

      expect(buckets).toEqual([
        {
          id: expandedKey1,
          count:
            aggregations.nested.filtered_matching.filtered_matching_locations
              ?.matching_locations?.buckets[0]?.matching.doc_count,
          regionsIn:
            aggregations.nested.filtered_matching.filtered_matching_locations?.matching_locations?.buckets[0]?.regions_in?.buckets.map(
              (bucket) => bucket.key
            )
        },
        {
          id: expandedKey2,
          count:
            aggregations.nested.filtered_matching.filtered_matching_locations
              ?.matching_locations?.buckets[2]?.matching.doc_count,
          regionsIn:
            aggregations.nested.filtered_matching.filtered_matching_locations?.matching_locations?.buckets[2]?.regions_in?.buckets.map(
              (bucket) => bucket.key
            )
        },
        {
          id: expandedKey3,
          count:
            aggregations.nested.filtered_matching.filtered_matching_regions
              ?.matching_regions?.buckets[0]?.matching.doc_count,
          locationsInRegion: [
            {
              id: expandedKey4,
              count:
                aggregations.nested.filtered_matching.filtered_matching_regions
                  ?.matching_regions?.buckets[0]?.locations_in_region
                  ?.buckets[0]?.matching.doc_count
            }
          ]
        },
        {
          id: expandedKey5,
          count:
            aggregations.nested.filtered_matching.filtered_matching_regions
              ?.matching_regions?.buckets[2]?.matching.doc_count,
          locationsInRegion: [
            {
              id: expandedKey6,
              count:
                aggregations.nested.filtered_matching.filtered_matching_regions
                  ?.matching_regions?.buckets[2]?.locations_in_region
                  ?.buckets[0]?.matching.doc_count
            }
          ]
        }
      ]);

      expect(locationLabelFormatterService.city).not.toHaveBeenCalled();
      expect(locationLabelFormatterService.region).not.toHaveBeenCalled();
      expect(locationLabelFormatterService.postalCode).not.toHaveBeenCalled();
      expect(locationLabelFormatterService.country).toHaveBeenCalled();
    });

    it("REGION: should expand bucket keys using region expander", async () => {
      const expandedKey1 = faker.datatype.string();
      const expandedKey2 = faker.datatype.string();
      const expandedKey3 = faker.datatype.string();
      const expandedKey4 = faker.datatype.string();
      const expandedKey5 = faker.datatype.string();
      const expandedKey6 = faker.datatype.string();

      const locationBucketValues: Array<FilteredDocCountBucket> = [
        {
          key: "us|AL",
          doc_count: faker.datatype.number(),
          matching: {
            doc_count: faker.datatype.number()
          },
          regions_in: {
            buckets: [
              {
                key: faker.datatype.string(),
                doc_count: faker.datatype.number()
              }
            ]
          }
        },
        {
          key: faker.datatype.string(),
          doc_count: faker.datatype.number(),
          matching: {
            doc_count: 0
          }
        },
        {
          key: "us|MD",
          doc_count: faker.datatype.number(),
          matching: {
            doc_count: faker.datatype.number()
          },
          regions_in: {
            buckets: [
              {
                key: faker.datatype.string(),
                doc_count: faker.datatype.number()
              }
            ]
          }
        }
      ];
      const regionBucketValues: Array<FilteredDocCountBucket> = [
        {
          key: faker.datatype.string(),
          doc_count: faker.datatype.number(),
          matching: {
            doc_count: faker.datatype.number()
          },
          locations_in_region: {
            buckets: [
              {
                key: faker.datatype.string(),
                doc_count: faker.datatype.number(),
                matching: {
                  doc_count: faker.datatype.number()
                }
              }
            ]
          }
        },
        {
          key: faker.datatype.string(),
          doc_count: faker.datatype.number(),
          matching: {
            doc_count: 0
          },
          locations_in_region: {
            buckets: []
          }
        },
        {
          key: faker.datatype.string(),
          doc_count: faker.datatype.number(),
          matching: {
            doc_count: faker.datatype.number()
          },
          locations_in_region: {
            buckets: [
              {
                key: faker.datatype.string(),
                doc_count: faker.datatype.number(),
                matching: {
                  doc_count: faker.datatype.number()
                }
              }
            ]
          }
        }
      ];
      const aggregations = {
        nested: {
          filtered_matching: {
            filtered_matching_locations: {
              matching_locations:
                generateMockAggregatesFilteredDocCount(locationBucketValues)
            },
            filtered_matching_regions: {
              matching_regions:
                generateMockAggregatesFilteredDocCount(regionBucketValues)
            }
          }
        }
      };

      const locationLabelFormatterService = createMockInstance(
        LocationLabelFormatterService
      );
      locationLabelFormatterService.region.mockReturnValueOnce(expandedKey1);
      locationLabelFormatterService.region.mockReturnValueOnce(expandedKey2);
      locationLabelFormatterService.region.mockReturnValueOnce(expandedKey3);
      locationLabelFormatterService.region.mockReturnValueOnce(expandedKey4);
      locationLabelFormatterService.region.mockReturnValueOnce(expandedKey5);
      locationLabelFormatterService.region.mockReturnValueOnce(expandedKey6);
      const ccsrIcdMappingRepository = createMockInstance(
        CcsrIcdMappingRepository
      );
      const ccsrPxMappingRepository = createMockInstance(
        CcsrPxMappingRepository
      );

      const keywordAutocompleteResponseAdapterService =
        new KeywordAutocompleteResponseAdapterService(
          locationLabelFormatterService,
          ccsrIcdMappingRepository,
          ccsrPxMappingRepository
        );

      const filterField = KeywordFilterAutocompleteFilterField.REGION;

      const buckets = await keywordAutocompleteResponseAdapterService.adapt(
        aggregations,
        filterField,
        []
      );

      expect(buckets).toEqual([
        {
          id: expandedKey1,
          count:
            aggregations.nested.filtered_matching.filtered_matching_locations
              ?.matching_locations?.buckets[0]?.matching.doc_count,
          regionsIn:
            aggregations.nested.filtered_matching.filtered_matching_locations?.matching_locations?.buckets[0]?.regions_in?.buckets.map(
              (bucket) => bucket.key
            )
        },
        {
          id: expandedKey2,
          count:
            aggregations.nested.filtered_matching.filtered_matching_locations
              ?.matching_locations?.buckets[2]?.matching.doc_count,
          regionsIn:
            aggregations.nested.filtered_matching.filtered_matching_locations?.matching_locations?.buckets[2]?.regions_in?.buckets.map(
              (bucket) => bucket.key
            )
        },
        {
          id: expandedKey3,
          count:
            aggregations.nested.filtered_matching.filtered_matching_regions
              ?.matching_regions?.buckets[0]?.matching.doc_count,
          locationsInRegion: [
            {
              id: expandedKey4,
              count:
                aggregations.nested.filtered_matching.filtered_matching_regions
                  ?.matching_regions?.buckets[0]?.locations_in_region
                  ?.buckets[0]?.matching.doc_count
            }
          ]
        },
        {
          id: expandedKey5,
          count:
            aggregations.nested.filtered_matching.filtered_matching_regions
              ?.matching_regions?.buckets[2]?.matching.doc_count,
          locationsInRegion: [
            {
              id: expandedKey6,
              count:
                aggregations.nested.filtered_matching.filtered_matching_regions
                  ?.matching_regions?.buckets[2]?.locations_in_region
                  ?.buckets[0]?.matching.doc_count
            }
          ]
        }
      ]);

      expect(locationLabelFormatterService.city).not.toHaveBeenCalled();
      expect(locationLabelFormatterService.region).toHaveBeenCalled();
      expect(locationLabelFormatterService.postalCode).not.toHaveBeenCalled();
      expect(locationLabelFormatterService.country).not.toHaveBeenCalled();
    });

    it("CITY: should expand bucket keys using city expander", async () => {
      const expandedKey1 = faker.datatype.string();
      const expandedKey2 = faker.datatype.string();
      const expandedKey3 = faker.datatype.string();
      const expandedKey4 = faker.datatype.string();
      const expandedKey5 = faker.datatype.string();
      const expandedKey6 = faker.datatype.string();

      const locationBucketValues: Array<FilteredDocCountBucket> = [
        {
          key: `us|AL|${faker.datatype.string()}`,
          doc_count: faker.datatype.number(),
          matching: {
            doc_count: faker.datatype.number()
          },
          regions_in: {
            buckets: [
              {
                key: faker.datatype.string(),
                doc_count: faker.datatype.number()
              }
            ]
          }
        },
        {
          key: faker.datatype.string(),
          doc_count: faker.datatype.number(),
          matching: {
            doc_count: 0
          }
        },
        {
          key: `us|MD|${faker.datatype.string()}`,
          doc_count: faker.datatype.number(),
          matching: {
            doc_count: faker.datatype.number()
          },
          regions_in: {
            buckets: [
              {
                key: faker.datatype.string(),
                doc_count: faker.datatype.number()
              }
            ]
          }
        }
      ];
      const regionBucketValues: Array<FilteredDocCountBucket> = [
        {
          key: faker.datatype.string(),
          doc_count: faker.datatype.number(),
          matching: {
            doc_count: faker.datatype.number()
          },
          locations_in_region: {
            buckets: [
              {
                key: faker.datatype.string(),
                doc_count: faker.datatype.number(),
                matching: {
                  doc_count: faker.datatype.number()
                }
              }
            ]
          }
        },
        {
          key: faker.datatype.string(),
          doc_count: faker.datatype.number(),
          matching: {
            doc_count: 0
          },
          locations_in_region: {
            buckets: []
          }
        },
        {
          key: faker.datatype.string(),
          doc_count: faker.datatype.number(),
          matching: {
            doc_count: faker.datatype.number()
          },
          locations_in_region: {
            buckets: [
              {
                key: faker.datatype.string(),
                doc_count: faker.datatype.number(),
                matching: {
                  doc_count: faker.datatype.number()
                }
              }
            ]
          }
        }
      ];
      const aggregations = {
        nested: {
          filtered_matching: {
            filtered_matching_locations: {
              matching_locations:
                generateMockAggregatesFilteredDocCount(locationBucketValues)
            },
            filtered_matching_regions: {
              matching_regions:
                generateMockAggregatesFilteredDocCount(regionBucketValues)
            }
          }
        }
      };

      const locationLabelFormatterService = createMockInstance(
        LocationLabelFormatterService
      );
      locationLabelFormatterService.city.mockReturnValueOnce(expandedKey1);
      locationLabelFormatterService.city.mockReturnValueOnce(expandedKey2);
      locationLabelFormatterService.city.mockReturnValueOnce(expandedKey3);
      locationLabelFormatterService.city.mockReturnValueOnce(expandedKey4);
      locationLabelFormatterService.city.mockReturnValueOnce(expandedKey5);
      locationLabelFormatterService.city.mockReturnValueOnce(expandedKey6);
      const ccsrIcdMappingRepository = createMockInstance(
        CcsrIcdMappingRepository
      );
      const ccsrPxMappingRepository = createMockInstance(
        CcsrPxMappingRepository
      );

      const keywordAutocompleteResponseAdapterService =
        new KeywordAutocompleteResponseAdapterService(
          locationLabelFormatterService,
          ccsrIcdMappingRepository,
          ccsrPxMappingRepository
        );

      const filterField = KeywordFilterAutocompleteFilterField.CITY;

      const buckets = await keywordAutocompleteResponseAdapterService.adapt(
        aggregations,
        filterField,
        []
      );

      expect(buckets).toEqual([
        {
          id: expandedKey1,
          count:
            aggregations.nested.filtered_matching.filtered_matching_locations
              ?.matching_locations?.buckets[0]?.matching.doc_count,
          regionsIn:
            aggregations.nested.filtered_matching.filtered_matching_locations?.matching_locations?.buckets[0]?.regions_in?.buckets.map(
              (bucket) => bucket.key
            )
        },
        {
          id: expandedKey2,
          count:
            aggregations.nested.filtered_matching.filtered_matching_locations
              ?.matching_locations?.buckets[2]?.matching.doc_count,
          regionsIn:
            aggregations.nested.filtered_matching.filtered_matching_locations?.matching_locations?.buckets[2]?.regions_in?.buckets.map(
              (bucket) => bucket.key
            )
        },
        {
          id: expandedKey3,
          count:
            aggregations.nested.filtered_matching.filtered_matching_regions
              ?.matching_regions?.buckets[0]?.matching.doc_count,
          locationsInRegion: [
            {
              id: expandedKey4,
              count:
                aggregations.nested.filtered_matching.filtered_matching_regions
                  ?.matching_regions?.buckets[0]?.locations_in_region
                  ?.buckets[0]?.matching.doc_count
            }
          ]
        },
        {
          id: expandedKey5,
          count:
            aggregations.nested.filtered_matching.filtered_matching_regions
              ?.matching_regions?.buckets[2]?.matching.doc_count,
          locationsInRegion: [
            {
              id: expandedKey6,
              count:
                aggregations.nested.filtered_matching.filtered_matching_regions
                  ?.matching_regions?.buckets[2]?.locations_in_region
                  ?.buckets[0]?.matching.doc_count
            }
          ]
        }
      ]);

      expect(locationLabelFormatterService.city).toHaveBeenCalled();
      expect(locationLabelFormatterService.region).not.toHaveBeenCalled();
      expect(locationLabelFormatterService.postalCode).not.toHaveBeenCalled();
      expect(locationLabelFormatterService.country).not.toHaveBeenCalled();
    });

    it("POSTAL_CODE: should expand bucket keys using postalCode expander", async () => {
      const expandedKey1 = faker.datatype.string();
      const expandedKey2 = faker.datatype.string();

      const locationBucketValues: Array<FilteredDocCountBucket> = [
        {
          key: `us|AL|${faker.datatype.string()}|${faker.datatype.string()}`,
          doc_count: faker.datatype.number(),
          matching: {
            doc_count: faker.datatype.number()
          },
          regions_in: {
            buckets: [
              {
                key: faker.datatype.string(),
                doc_count: faker.datatype.number()
              }
            ]
          }
        },
        {
          key: faker.datatype.string(),
          doc_count: faker.datatype.number(),
          matching: {
            doc_count: 0
          }
        },
        {
          key: `us|MD|${faker.datatype.string()}|${faker.datatype.string()}`,
          doc_count: faker.datatype.number(),
          matching: {
            doc_count: faker.datatype.number()
          },
          regions_in: {
            buckets: [
              {
                key: faker.datatype.string(),
                doc_count: faker.datatype.number()
              }
            ]
          }
        }
      ];
      const aggregations = {
        nested: {
          filtered_matching: {
            filtered_matching_locations: {
              matching_locations:
                generateMockAggregatesFilteredDocCount(locationBucketValues)
            }
          }
        }
      };

      const locationLabelFormatterService = createMockInstance(
        LocationLabelFormatterService
      );
      locationLabelFormatterService.postalCode.mockReturnValueOnce(
        expandedKey1
      );
      locationLabelFormatterService.postalCode.mockReturnValueOnce(
        expandedKey2
      );
      const ccsrIcdMappingRepository = createMockInstance(
        CcsrIcdMappingRepository
      );
      const ccsrPxMappingRepository = createMockInstance(
        CcsrPxMappingRepository
      );

      const keywordAutocompleteResponseAdapterService =
        new KeywordAutocompleteResponseAdapterService(
          locationLabelFormatterService,
          ccsrIcdMappingRepository,
          ccsrPxMappingRepository
        );

      const filterField = KeywordFilterAutocompleteFilterField.POSTAL_CODE;

      const buckets = await keywordAutocompleteResponseAdapterService.adapt(
        aggregations,
        filterField,
        []
      );

      expect(buckets).toEqual([
        {
          id: expandedKey1,
          count:
            aggregations.nested.filtered_matching.filtered_matching_locations
              ?.matching_locations?.buckets[0]?.matching.doc_count,
          regionsIn:
            aggregations.nested.filtered_matching.filtered_matching_locations?.matching_locations?.buckets[0]?.regions_in?.buckets.map(
              (bucket) => bucket.key
            )
        },
        {
          id: expandedKey2,
          count:
            aggregations.nested.filtered_matching.filtered_matching_locations
              ?.matching_locations?.buckets[2]?.matching.doc_count,
          regionsIn:
            aggregations.nested.filtered_matching.filtered_matching_locations?.matching_locations?.buckets[2]?.regions_in?.buckets.map(
              (bucket) => bucket.key
            )
        }
      ]);

      expect(locationLabelFormatterService.city).not.toHaveBeenCalled();
      expect(locationLabelFormatterService.region).not.toHaveBeenCalled();
      expect(locationLabelFormatterService.postalCode).toHaveBeenCalled();
      expect(locationLabelFormatterService.country).not.toHaveBeenCalled();
    });
  });

  describe("nested->filtered->matching aggregations", () => {
    describe("location filterFields", () => {
      it("COUNTRY: should expand bucket keys using country expander", async () => {
        const expandedKey1 = faker.datatype.string();
        const expandedKey2 = faker.datatype.string();

        const bucketValues: Array<DocCountBucket> = [
          {
            key: faker.datatype.string(),
            doc_count: faker.datatype.number()
          },
          {
            key: faker.datatype.string(),
            doc_count: 0
          },
          {
            key: faker.datatype.string(),
            doc_count: faker.datatype.number()
          }
        ];
        const aggregations = {
          nested: {
            filtered_matching: {
              matching: generateMockAggregates(bucketValues)
            }
          }
        };

        const locationLabelFormatterService = createMockInstance(
          LocationLabelFormatterService
        );
        locationLabelFormatterService.country.mockReturnValueOnce(expandedKey1);
        locationLabelFormatterService.country.mockReturnValueOnce(expandedKey2);
        const ccsrIcdMappingRepository = createMockInstance(
          CcsrIcdMappingRepository
        );
        const ccsrPxMappingRepository = createMockInstance(
          CcsrPxMappingRepository
        );

        const keywordAutocompleteResponseAdapterService =
          new KeywordAutocompleteResponseAdapterService(
            locationLabelFormatterService,
            ccsrIcdMappingRepository,
            ccsrPxMappingRepository
          );

        const filterField = KeywordFilterAutocompleteFilterField.COUNTRY;

        const buckets = await keywordAutocompleteResponseAdapterService.adapt(
          aggregations,
          filterField,
          []
        );

        expect(buckets).toEqual([
          {
            id: expandedKey1,
            count:
              aggregations.nested.filtered_matching.matching.buckets[0]
                .doc_count
          },
          {
            id: expandedKey2,
            count:
              aggregations.nested.filtered_matching.matching.buckets[2]
                .doc_count
          }
        ]);

        expect(locationLabelFormatterService.city).not.toHaveBeenCalled();
        expect(locationLabelFormatterService.region).not.toHaveBeenCalled();
        expect(locationLabelFormatterService.postalCode).not.toHaveBeenCalled();
        expect(locationLabelFormatterService.country).toHaveBeenCalled();
      });

      it("REGION: should expand bucket keys using region expander", async () => {
        const expandedKey1 = faker.datatype.string();
        const expandedKey2 = faker.datatype.string();

        const bucketValues: Array<DocCountBucket> = [
          {
            key: faker.datatype.string(),
            doc_count: faker.datatype.number()
          },
          {
            key: faker.datatype.string(),
            doc_count: 0
          },
          {
            key: faker.datatype.string(),
            doc_count: faker.datatype.number()
          }
        ];
        const aggregations = {
          nested: {
            filtered_matching: {
              matching: generateMockAggregates(bucketValues)
            }
          }
        };

        const locationLabelFormatterService = createMockInstance(
          LocationLabelFormatterService
        );
        locationLabelFormatterService.region.mockReturnValueOnce(expandedKey1);
        locationLabelFormatterService.region.mockReturnValueOnce(expandedKey2);

        const ccsrIcdMappingRepository = createMockInstance(
          CcsrIcdMappingRepository
        );
        const ccsrPxMappingRepository = createMockInstance(
          CcsrPxMappingRepository
        );

        const keywordAutocompleteResponseAdapterService =
          new KeywordAutocompleteResponseAdapterService(
            locationLabelFormatterService,
            ccsrIcdMappingRepository,
            ccsrPxMappingRepository
          );

        const filterField = KeywordFilterAutocompleteFilterField.REGION;

        const buckets = await keywordAutocompleteResponseAdapterService.adapt(
          aggregations,
          filterField,
          []
        );

        expect(buckets).toEqual([
          {
            id: expandedKey1,
            count:
              aggregations.nested.filtered_matching.matching.buckets[0]
                .doc_count
          },
          {
            id: expandedKey2,
            count:
              aggregations.nested.filtered_matching.matching.buckets[2]
                .doc_count
          }
        ]);

        expect(locationLabelFormatterService.city).not.toHaveBeenCalled();
        expect(locationLabelFormatterService.region).toHaveBeenCalled();
        expect(locationLabelFormatterService.postalCode).not.toHaveBeenCalled();
        expect(locationLabelFormatterService.country).not.toHaveBeenCalled();
      });

      it("CITY: should expand bucket keys using city expander", async () => {
        const expandedKey1 = faker.datatype.string();
        const expandedKey2 = faker.datatype.string();

        const bucketValues: Array<DocCountBucket> = [
          {
            key: faker.datatype.string(),
            doc_count: faker.datatype.number()
          },
          {
            key: faker.datatype.string(),
            doc_count: 0
          },
          {
            key: faker.datatype.string(),
            doc_count: faker.datatype.number()
          }
        ];
        const aggregations = {
          nested: {
            filtered_matching: {
              matching: generateMockAggregates(bucketValues)
            }
          }
        };

        const locationLabelFormatterService = createMockInstance(
          LocationLabelFormatterService
        );
        locationLabelFormatterService.city.mockReturnValueOnce(expandedKey1);
        locationLabelFormatterService.city.mockReturnValueOnce(expandedKey2);

        const ccsrIcdMappingRepository = createMockInstance(
          CcsrIcdMappingRepository
        );
        const ccsrPxMappingRepository = createMockInstance(
          CcsrPxMappingRepository
        );

        const keywordAutocompleteResponseAdapterService =
          new KeywordAutocompleteResponseAdapterService(
            locationLabelFormatterService,
            ccsrIcdMappingRepository,
            ccsrPxMappingRepository
          );

        const filterField = KeywordFilterAutocompleteFilterField.CITY;

        const buckets = await keywordAutocompleteResponseAdapterService.adapt(
          aggregations,
          filterField,
          []
        );

        expect(buckets).toEqual([
          {
            id: expandedKey1,
            count:
              aggregations.nested.filtered_matching.matching.buckets[0]
                .doc_count
          },
          {
            id: expandedKey2,
            count:
              aggregations.nested.filtered_matching.matching.buckets[2]
                .doc_count
          }
        ]);

        expect(locationLabelFormatterService.city).toHaveBeenCalled();
        expect(locationLabelFormatterService.region).not.toHaveBeenCalled();
        expect(locationLabelFormatterService.postalCode).not.toHaveBeenCalled();
        expect(locationLabelFormatterService.country).not.toHaveBeenCalled();
      });

      it("POSTAL_CODE: should expand bucket keys using postalCode expander", async () => {
        const expandedKey1 = faker.datatype.string();
        const expandedKey2 = faker.datatype.string();

        const bucketValues: Array<DocCountBucket> = [
          {
            key: faker.datatype.string(),
            doc_count: faker.datatype.number()
          },
          {
            key: faker.datatype.string(),
            doc_count: 0
          },
          {
            key: faker.datatype.string(),
            doc_count: faker.datatype.number()
          }
        ];
        const aggregations = {
          nested: {
            filtered_matching: {
              matching: generateMockAggregates(bucketValues)
            }
          }
        };

        const locationLabelFormatterService = createMockInstance(
          LocationLabelFormatterService
        );
        locationLabelFormatterService.postalCode.mockReturnValueOnce(
          expandedKey1
        );
        locationLabelFormatterService.postalCode.mockReturnValueOnce(
          expandedKey2
        );

        const ccsrIcdMappingRepository = createMockInstance(
          CcsrIcdMappingRepository
        );
        const ccsrPxMappingRepository = createMockInstance(
          CcsrPxMappingRepository
        );

        const keywordAutocompleteResponseAdapterService =
          new KeywordAutocompleteResponseAdapterService(
            locationLabelFormatterService,
            ccsrIcdMappingRepository,
            ccsrPxMappingRepository
          );

        const filterField = KeywordFilterAutocompleteFilterField.POSTAL_CODE;

        const buckets = await keywordAutocompleteResponseAdapterService.adapt(
          aggregations,
          filterField,
          []
        );

        expect(buckets).toEqual([
          {
            id: expandedKey1,
            count:
              aggregations.nested.filtered_matching.matching.buckets[0]
                .doc_count
          },
          {
            id: expandedKey2,
            count:
              aggregations.nested.filtered_matching.matching.buckets[2]
                .doc_count
          }
        ]);

        expect(locationLabelFormatterService.city).not.toHaveBeenCalled();
        expect(locationLabelFormatterService.region).not.toHaveBeenCalled();
        expect(locationLabelFormatterService.postalCode).toHaveBeenCalled();
        expect(locationLabelFormatterService.country).not.toHaveBeenCalled();
      });
    });

    describe("non-location filterFields that produce nested aggregations", () => {
      it("should parse nested.filtered_matching.matching aggregations", async () => {
        const locationLabelFormatterService = createMockInstance(
          LocationLabelFormatterService
        );

        const ccsrIcdMappingRepository = createMockInstance(
          CcsrIcdMappingRepository
        );
        const ccsrPxMappingRepository = createMockInstance(
          CcsrPxMappingRepository
        );

        const keywordAutocompleteResponseAdapterService =
          new KeywordAutocompleteResponseAdapterService(
            locationLabelFormatterService,
            ccsrIcdMappingRepository,
            ccsrPxMappingRepository
          );

        for (const filterField of AGGREGATIONS_THAT_NEED_NESTED_FILTERING.keys()) {
          if (
            filterField ===
              KeywordFilterAutocompleteFilterField.CCSR_DIAGNOSES ||
            filterField === KeywordFilterAutocompleteFilterField.CCSR_PROCEDURES
          ) {
            continue;
          }
          const bucketValues: Array<DocCountBucket> = [
            {
              key: faker.datatype.string(),
              doc_count: faker.datatype.number()
            },
            {
              key: faker.datatype.string(),
              doc_count: 0
            },
            {
              key: faker.datatype.string(),
              doc_count: faker.datatype.number()
            }
          ];
          const aggregations = {
            nested: {
              filtered_matching: {
                matching: generateMockAggregates(bucketValues)
              }
            }
          };

          const buckets = await keywordAutocompleteResponseAdapterService.adapt(
            aggregations,
            filterField,
            []
          );

          expect(buckets).toEqual([
            {
              id: aggregations.nested.filtered_matching.matching.buckets[0].key,
              count:
                aggregations.nested.filtered_matching.matching.buckets[0]
                  .doc_count
            },
            {
              id: aggregations.nested.filtered_matching.matching.buckets[2].key,
              count:
                aggregations.nested.filtered_matching.matching.buckets[2]
                  .doc_count
            }
          ]);
        }

        expect(locationLabelFormatterService.city).not.toHaveBeenCalled();
        expect(locationLabelFormatterService.region).not.toHaveBeenCalled();
        expect(locationLabelFormatterService.postalCode).not.toHaveBeenCalled();
        expect(locationLabelFormatterService.country).not.toHaveBeenCalled();
      });

      describe("bucket is FilteredDocCountBucket", () => {
        it("should use bucket.matching.doc_count for bucket count over bucket.matching.value", async () => {
          const locationLabelFormatterService = createMockInstance(
            LocationLabelFormatterService
          );

          const ccsrIcdMappingRepository = createMockInstance(
            CcsrIcdMappingRepository
          );
          const ccsrPxMappingRepository = createMockInstance(
            CcsrPxMappingRepository
          );

          const keywordAutocompleteResponseAdapterService =
            new KeywordAutocompleteResponseAdapterService(
              locationLabelFormatterService,
              ccsrIcdMappingRepository,
              ccsrPxMappingRepository
            );

          for (const filterField of AGGREGATIONS_THAT_NEED_NESTED_FILTERING.keys()) {
            if (
              filterField ===
                KeywordFilterAutocompleteFilterField.CCSR_DIAGNOSES ||
              filterField ===
                KeywordFilterAutocompleteFilterField.CCSR_PROCEDURES
            ) {
              continue;
            }
            const bucketValues: Array<FilteredDocCountBucket> = [
              {
                key: faker.datatype.string(),
                doc_count: faker.datatype.number(),
                matching: {
                  doc_count: faker.datatype.number(),
                  value: faker.datatype.number()
                }
              },
              {
                key: faker.datatype.string(),
                doc_count: faker.datatype.number(),
                matching: {
                  doc_count: 0,
                  value: faker.datatype.number()
                }
              },
              {
                key: faker.datatype.string(),
                doc_count: faker.datatype.number(),
                matching: {
                  doc_count: faker.datatype.number(),
                  value: faker.datatype.number()
                }
              }
            ];
            const aggregations = {
              nested: {
                filtered_matching: {
                  matching: generateMockAggregatesFilteredDocCount(bucketValues)
                }
              }
            };

            const buckets =
              await keywordAutocompleteResponseAdapterService.adapt(
                aggregations,
                filterField,
                []
              );

            expect(buckets).toEqual([
              {
                id: aggregations.nested.filtered_matching.matching.buckets[0]
                  .key,
                count:
                  aggregations.nested.filtered_matching.matching.buckets[0]
                    .matching.doc_count
              },
              {
                id: aggregations.nested.filtered_matching.matching.buckets[2]
                  .key,
                count:
                  aggregations.nested.filtered_matching.matching.buckets[2]
                    .matching.doc_count
              }
            ]);
          }
        });

        it("should use bucket.matching.value for bucket count when bucket.matching.doc_count is unavailable", async () => {
          const locationLabelFormatterService = createMockInstance(
            LocationLabelFormatterService
          );

          const ccsrIcdMappingRepository = createMockInstance(
            CcsrIcdMappingRepository
          );
          const ccsrPxMappingRepository = createMockInstance(
            CcsrPxMappingRepository
          );

          const keywordAutocompleteResponseAdapterService =
            new KeywordAutocompleteResponseAdapterService(
              locationLabelFormatterService,
              ccsrIcdMappingRepository,
              ccsrPxMappingRepository
            );

          for (const filterField of AGGREGATIONS_THAT_NEED_NESTED_FILTERING.keys()) {
            if (
              filterField ===
                KeywordFilterAutocompleteFilterField.CCSR_DIAGNOSES ||
              filterField ===
                KeywordFilterAutocompleteFilterField.CCSR_PROCEDURES
            ) {
              continue;
            }
            const bucketValues: Array<FilteredDocCountBucket> = [
              {
                key: faker.datatype.string(),
                doc_count: faker.datatype.number(),
                matching: {
                  doc_count: undefined,
                  value: faker.datatype.number()
                }
              },
              {
                key: faker.datatype.string(),
                doc_count: faker.datatype.number(),
                matching: {
                  doc_count: undefined,
                  value: 0
                }
              },
              {
                key: faker.datatype.string(),
                doc_count: faker.datatype.number(),
                matching: {
                  doc_count: undefined,
                  value: faker.datatype.number()
                }
              }
            ];
            const aggregations = {
              nested: {
                filtered_matching: {
                  matching: generateMockAggregatesFilteredDocCount(bucketValues)
                }
              }
            };

            const buckets =
              await keywordAutocompleteResponseAdapterService.adapt(
                aggregations,
                filterField,
                []
              );

            expect(buckets).toEqual([
              {
                id: aggregations.nested.filtered_matching.matching.buckets[0]
                  .key,
                count:
                  aggregations.nested.filtered_matching.matching.buckets[0]
                    .matching.value
              },
              {
                id: aggregations.nested.filtered_matching.matching.buckets[2]
                  .key,
                count:
                  aggregations.nested.filtered_matching.matching.buckets[2]
                    .matching.value
              }
            ]);
          }
        });
      });
    });

    describe("ccsr", () => {
      it("should parse nested.filtered_matching.buckets aggregations when filterField is CCSR_DIAGNOSES", async () => {
        const locationLabelFormatterService = createMockInstance(
          LocationLabelFormatterService
        );

        const ccsrIcdMappingRepository = createMockInstance(
          CcsrIcdMappingRepository
        );
        const icd1 = "A";
        const icd2 = "B";
        const icd3 = "C";
        const mockIcdCodeList = [icd1, icd2, icd3].map(toLower);
        const mockCcsrDesc = faker.datatype.string();

        const mockCcsrIcdMapping = mockIcdCodeList.map((code) => ({
          ccsr: mockCcsrDesc,
          icdCode: code,
          claimSize: faker.datatype.number()
        }));
        ccsrIcdMappingRepository.getIcdCodesForCcsr.mockResolvedValue(
          mockCcsrIcdMapping
        );
        const ccsrPxMappingRepository = createMockInstance(
          CcsrPxMappingRepository
        );

        const keywordAutocompleteResponseAdapterService =
          new KeywordAutocompleteResponseAdapterService(
            locationLabelFormatterService,
            ccsrIcdMappingRepository,
            ccsrPxMappingRepository
          );

        const filterField = KeywordFilterAutocompleteFilterField.CCSR_DIAGNOSES;

        const bucketValues: Record<string, any> = {
          [icd1]: {
            doc_count: faker.datatype.number(),
            matching_desc: {
              buckets: [
                {
                  key: `${icd1} - ${faker.datatype.string()}`,
                  doc_count: faker.datatype.number()
                }
              ]
            }
          },
          [icd2]: {
            doc_count: faker.datatype.number(),
            matching_desc: {
              buckets: [
                {
                  key: `${icd2} - ${faker.datatype.string()}`,
                  doc_count: faker.datatype.number()
                }
              ]
            }
          },
          [icd3]: {
            doc_count: faker.datatype.number(),
            matching_desc: {
              buckets: [
                {
                  key: `${icd3} - ${faker.datatype.string()}`,
                  doc_count: faker.datatype.number()
                }
              ]
            }
          }
        };
        const aggregations = {
          nested: {
            filtered_matching: {
              buckets: bucketValues
            }
          }
        };
        const projectId = faker.datatype.string();
        const projectFeatures = {
          engagementsV2: faker.datatype.boolean()
        };
        const userId = faker.datatype.string();
        const query = undefined;

        const filterValue = faker.random.word();

        const suppliedFilters = generateFilters();
        const buckets = await keywordAutocompleteResponseAdapterService.adapt(
          aggregations,
          filterField,
          [icd1, icd2, icd3],
          undefined,
          {
            projectId,
            projectFeatures,
            userId,
            query,
            filterField,
            filterValue,
            suppliedFilters,
            ccsrToExpand: mockCcsrDesc,
            ccsrIcdSize: 5,
            ccsrIcdOffset: 0
          }
        );

        expect(buckets).toEqual([
          {
            id: aggregations.nested.filtered_matching.buckets[icd1]
              .matching_desc?.buckets[0].key,
            count: aggregations.nested.filtered_matching.buckets[icd1].doc_count
          },
          {
            id: aggregations.nested.filtered_matching.buckets[icd2]
              .matching_desc?.buckets[0].key,
            count: aggregations.nested.filtered_matching.buckets[icd2].doc_count
          },
          {
            id: aggregations.nested.filtered_matching.buckets[icd3]
              .matching_desc?.buckets[0].key,
            count: aggregations.nested.filtered_matching.buckets[icd3].doc_count
          }
        ]);
      });

      it("should fetch ccsrDescriptions when filterField is DRG_DIAGNOSES or DIAGNOSES", async () => {
        const locationLabelFormatterService = createMockInstance(
          LocationLabelFormatterService
        );

        const ccsrIcdMappingRepository = createMockInstance(
          CcsrIcdMappingRepository
        );
        const mockIcdCodeList = [
          faker.datatype.string(),
          faker.datatype.string(),
          faker.datatype.string()
        ].map(toLower);
        const mockCcsrDesc = faker.datatype.string();

        const mockCcsrIcdMapping = mockIcdCodeList.map((code) => ({
          ccsr: mockCcsrDesc,
          icdCode: code,
          claimSize: faker.datatype.number()
        }));
        ccsrIcdMappingRepository.getCcsrForIcd.mockResolvedValue(
          mockCcsrIcdMapping
        );
        const ccsrPxMappingRepository = createMockInstance(
          CcsrPxMappingRepository
        );

        const keywordAutocompleteResponseAdapterService =
          new KeywordAutocompleteResponseAdapterService(
            locationLabelFormatterService,
            ccsrIcdMappingRepository,
            ccsrPxMappingRepository
          );

        const filterField = KeywordFilterAutocompleteFilterField.DIAGNOSES;

        const bucketValues: Array<DocCountBucket> = [
          {
            key: mockIcdCodeList[0],
            doc_count: faker.datatype.number()
          },
          {
            key: mockIcdCodeList[1],
            doc_count: 0
          },
          {
            key: mockIcdCodeList[2],
            doc_count: faker.datatype.number()
          }
        ];
        const aggregations = {
          nested: {
            filtered_matching: {
              matching: generateMockAggregates(bucketValues)
            }
          }
        };

        const buckets = await keywordAutocompleteResponseAdapterService.adapt(
          aggregations,
          filterField,
          []
        );

        expect(buckets).toEqual([
          {
            id: aggregations.nested.filtered_matching.matching.buckets[0].key,
            count:
              aggregations.nested.filtered_matching.matching.buckets[0]
                .doc_count,
            ccsr: [mockCcsrDesc]
          },
          {
            id: aggregations.nested.filtered_matching.matching.buckets[2].key,
            count:
              aggregations.nested.filtered_matching.matching.buckets[2]
                .doc_count,
            ccsr: [mockCcsrDesc]
          }
        ]);
      });

      it("should parse nested.filtered_matching.buckets aggregations when filterField is CCSR_PROCEDURES", async () => {
        const locationLabelFormatterService = createMockInstance(
          LocationLabelFormatterService
        );

        const ccsrIcdMappingRepository = createMockInstance(
          CcsrIcdMappingRepository
        );
        const cpt1 = "A";
        const cpt2 = "B";
        const cpt3 = "C";
        const mockIcdCodeList = [cpt1, cpt2, cpt3].map(toLower);
        const mockCcsrDesc = faker.datatype.string();

        const mockCcsrCptMapping = mockIcdCodeList.map((code) => ({
          ccsr: mockCcsrDesc,
          procedureCode: code,
          claimSize: faker.datatype.number()
        }));

        const ccsrPxMappingRepository = createMockInstance(
          CcsrPxMappingRepository
        );
        ccsrPxMappingRepository.getProcedureCodesForCcsr.mockResolvedValue(
          mockCcsrCptMapping
        );
        const keywordAutocompleteResponseAdapterService =
          new KeywordAutocompleteResponseAdapterService(
            locationLabelFormatterService,
            ccsrIcdMappingRepository,
            ccsrPxMappingRepository
          );

        const filterField =
          KeywordFilterAutocompleteFilterField.CCSR_PROCEDURES;

        const bucketValues: Record<string, any> = {
          [cpt1]: {
            doc_count: faker.datatype.number(),
            matching_desc: {
              buckets: [
                {
                  key: `${cpt1} - ${faker.datatype.string()}`,
                  doc_count: faker.datatype.number()
                }
              ]
            }
          },
          [cpt2]: {
            doc_count: faker.datatype.number(),
            matching_desc: {
              buckets: [
                {
                  key: `${cpt2} - ${faker.datatype.string()}`,
                  doc_count: faker.datatype.number()
                }
              ]
            }
          },
          [cpt3]: {
            doc_count: faker.datatype.number(),
            matching_desc: {
              buckets: [
                {
                  key: `${cpt3} - ${faker.datatype.string()}`,
                  doc_count: faker.datatype.number()
                }
              ]
            }
          }
        };
        const aggregations = {
          nested: {
            filtered_matching: {
              buckets: bucketValues
            }
          }
        };
        const projectId = faker.datatype.string();
        const projectFeatures = {
          engagementsV2: faker.datatype.boolean()
        };
        const userId = faker.datatype.string();
        const query = undefined;

        const filterValue = faker.random.word();

        const suppliedFilters = generateFilters();
        const buckets = await keywordAutocompleteResponseAdapterService.adapt(
          aggregations,
          filterField,
          [cpt1, cpt2, cpt3],
          undefined,
          {
            projectId,
            projectFeatures,
            userId,
            query,
            filterField,
            filterValue,
            suppliedFilters,
            ccsrToExpand: mockCcsrDesc,
            ccsrIcdSize: 5,
            ccsrIcdOffset: 0
          }
        );

        expect(buckets).toEqual([
          {
            id: aggregations.nested.filtered_matching.buckets[cpt1]
              .matching_desc?.buckets[0].key,
            count: aggregations.nested.filtered_matching.buckets[cpt1].doc_count
          },
          {
            id: aggregations.nested.filtered_matching.buckets[cpt2]
              .matching_desc?.buckets[0].key,
            count: aggregations.nested.filtered_matching.buckets[cpt2].doc_count
          },
          {
            id: aggregations.nested.filtered_matching.buckets[cpt3]
              .matching_desc?.buckets[0].key,
            count: aggregations.nested.filtered_matching.buckets[cpt3].doc_count
          }
        ]);
      });

      it("should fetch ccsrDescriptions when filterField is PROCEDURES", async () => {
        const locationLabelFormatterService = createMockInstance(
          LocationLabelFormatterService
        );

        const ccsrIcdMappingRepository = createMockInstance(
          CcsrIcdMappingRepository
        );
        const mockCptCodeList = [
          faker.datatype.string(),
          faker.datatype.string(),
          faker.datatype.string()
        ].map(toLower);
        const mockCcsrDesc = faker.datatype.string();

        const mockCcsrCptMapping = mockCptCodeList.map((code) => ({
          ccsr: mockCcsrDesc,
          procedureCode: code,
          claimSize: faker.datatype.number()
        }));

        const ccsrPxMappingRepository = createMockInstance(
          CcsrPxMappingRepository
        );
        ccsrPxMappingRepository.getCcsrForProcedureCodesWithSize.mockResolvedValue(
          mockCcsrCptMapping
        );
        const keywordAutocompleteResponseAdapterService =
          new KeywordAutocompleteResponseAdapterService(
            locationLabelFormatterService,
            ccsrIcdMappingRepository,
            ccsrPxMappingRepository
          );

        const filterField = KeywordFilterAutocompleteFilterField.PROCEDURES;

        const bucketValues: Array<DocCountBucket> = [
          {
            key: mockCptCodeList[0],
            doc_count: faker.datatype.number()
          },
          {
            key: mockCptCodeList[1],
            doc_count: 0
          },
          {
            key: mockCptCodeList[2],
            doc_count: faker.datatype.number()
          }
        ];
        const aggregations = {
          nested: {
            filtered_matching: {
              matching: generateMockAggregates(bucketValues)
            }
          }
        };

        const buckets = await keywordAutocompleteResponseAdapterService.adapt(
          aggregations,
          filterField,
          []
        );

        expect(buckets).toEqual([
          {
            id: aggregations.nested.filtered_matching.matching.buckets[0].key,
            count:
              aggregations.nested.filtered_matching.matching.buckets[0]
                .doc_count,
            ccsr: [mockCcsrDesc]
          },
          {
            id: aggregations.nested.filtered_matching.matching.buckets[2].key,
            count:
              aggregations.nested.filtered_matching.matching.buckets[2]
                .doc_count,
            ccsr: [mockCcsrDesc]
          }
        ]);
      });

      it("should add care clusters to autocomplete list when filterField is PROCEDURES and peopleIds filter is applied", async () => {
        const locationLabelFormatterService = createMockInstance(
          LocationLabelFormatterService
        );

        const ccsrIcdMappingRepository = createMockInstance(
          CcsrIcdMappingRepository
        );
        const mockCptCodeList = [
          faker.datatype.string(),
          faker.datatype.string(),
          faker.datatype.string()
        ].map(toLower);
        const mockCcsrDesc = faker.datatype.string();
        const size = faker.datatype.number();
        const mockCcsrCptMapping = mockCptCodeList.map((code) => ({
          ccsr: mockCcsrDesc,
          procedureCode: code,
          claimSize: size
        }));

        const ccsrPxMappingRepository = createMockInstance(
          CcsrPxMappingRepository
        );
        ccsrPxMappingRepository.getCcsrForProcedureCodesWithSize.mockResolvedValue(
          mockCcsrCptMapping
        );
        const keywordAutocompleteResponseAdapterService =
          new KeywordAutocompleteResponseAdapterService(
            locationLabelFormatterService,
            ccsrIcdMappingRepository,
            ccsrPxMappingRepository
          );

        const filterField = KeywordFilterAutocompleteFilterField.PROCEDURES;

        const bucketValues: Array<DocCountBucket> = [
          {
            key: mockCptCodeList[0],
            doc_count: faker.datatype.number()
          },
          {
            key: mockCptCodeList[1],
            doc_count: 0
          },
          {
            key: mockCptCodeList[2],
            doc_count: faker.datatype.number()
          }
        ];
        const aggregations = {
          nested: {
            filtered_matching: {
              matching: generateMockAggregates(bucketValues)
            }
          }
        };
        const projectId = faker.datatype.string();
        const projectFeatures = {
          engagementsV2: faker.datatype.boolean(),
          searchMultiLanguage: false
        };
        const userId = faker.datatype.string();

        const filterValue = faker.random.word();

        const suppliedFilters = generateFilters({
          peopleIds: [faker.datatype.string()]
        });

        const buckets = await keywordAutocompleteResponseAdapterService.adapt(
          aggregations,
          filterField,
          [],
          undefined,
          {
            projectId,
            projectFeatures,
            userId,
            filterField,
            filterValue,
            suppliedFilters
          }
        );

        expect(buckets).toEqual([
          {
            id: aggregations.nested.filtered_matching.matching.buckets[0].key,
            count:
              aggregations.nested.filtered_matching.matching.buckets[0]
                .doc_count,
            ccsr: [mockCcsrDesc]
          },
          {
            id: aggregations.nested.filtered_matching.matching.buckets[2].key,
            count:
              aggregations.nested.filtered_matching.matching.buckets[2]
                .doc_count,
            ccsr: [mockCcsrDesc]
          },
          {
            id: mockCcsrDesc,
            count: 0,
            ccsrIcdSize: size.toString()
          }
        ]);
      });
    });
  });

  describe("nested->matching aggregations", () => {
    it("should parse basic nested matching aggregations", async () => {
      const bucketValues: Array<DocCountBucket> = [
        {
          key: faker.datatype.string(),
          doc_count: faker.datatype.number()
        },
        {
          key: faker.datatype.string(),
          doc_count: 0
        },
        {
          key: faker.datatype.string(),
          doc_count: faker.datatype.number()
        }
      ];
      const aggregations = {
        nested: {
          matching: generateMockAggregates(bucketValues)
        }
      };

      const locationLabelFormatterService = createMockInstance(
        LocationLabelFormatterService
      );

      const ccsrIcdMappingRepository = createMockInstance(
        CcsrIcdMappingRepository
      );
      const ccsrPxMappingRepository = createMockInstance(
        CcsrPxMappingRepository
      );

      const keywordAutocompleteResponseAdapterService =
        new KeywordAutocompleteResponseAdapterService(
          locationLabelFormatterService,
          ccsrIcdMappingRepository,
          ccsrPxMappingRepository
        );

      const filterField = KeywordFilterAutocompleteFilterField.SPECIALTY;

      const buckets = await keywordAutocompleteResponseAdapterService.adapt(
        aggregations,
        filterField,
        []
      );

      expect(buckets).toEqual([
        {
          id: aggregations.nested.matching.buckets[0].key,
          count: aggregations.nested.matching.buckets[0].doc_count
        },
        {
          id: aggregations.nested.matching.buckets[2].key,
          count: aggregations.nested.matching.buckets[2].doc_count
        }
      ]);
    });
  });

  describe("patient diversity", () => {
    it("should skip the unsupported Asian and Pacific Islander categories/buckets", async () => {
      const bucketValues: Array<DocCountBucket> = [
        {
          key: "Asian",
          doc_count: faker.datatype.number()
        },
        {
          key: "Pacific Islander",
          doc_count: faker.datatype.number()
        },
        {
          key: "Asian Pacific Islander",
          doc_count: faker.datatype.number()
        },
        {
          key: faker.datatype.string(),
          doc_count: faker.datatype.number()
        }
      ];
      const aggregations = {
        matching: generateMockAggregates(bucketValues)
      };

      const locationLabelFormatterService = createMockInstance(
        LocationLabelFormatterService
      );

      const ccsrIcdMappingRepository = createMockInstance(
        CcsrIcdMappingRepository
      );
      const ccsrPxMappingRepository = createMockInstance(
        CcsrPxMappingRepository
      );

      const keywordAutocompleteResponseAdapterService =
        new KeywordAutocompleteResponseAdapterService(
          locationLabelFormatterService,
          ccsrIcdMappingRepository,
          ccsrPxMappingRepository
        );

      const filterField =
        KeywordFilterAutocompleteFilterField.DIVERSITY_PATIENT_DIVERSITY;

      const buckets = await keywordAutocompleteResponseAdapterService.adapt(
        aggregations,
        filterField,
        []
      );

      expect(buckets).toEqual([
        {
          id: "Asian Pacific Islander",
          count: aggregations.matching.buckets[2].doc_count
        },
        {
          id: aggregations.matching.buckets[3].key,
          count: aggregations.matching.buckets[3].doc_count
        }
      ]);
    });
  });
});
