import {
  createMockInstance,
  getEmptyKeywordSearchFilters
} from "../util/TestUtils";
import { faker } from "@faker-js/faker";
import {
  QueryDslQueryContainer,
  AggregationsAggregateOrder
} from "@elastic/elasticsearch/lib/api/types";
import {
  FilterInterface,
  KeywordFilterAutocompleteFilterField
} from "@h1nyc/search-sdk";
import {
  AggregationContainerBuilderService,
  expandFilterValueToRegex
} from "./AggregationContainerBuilderService";
import {
  generateMockElasticsearchTermQuery,
  mockParseTreeToElasticsearchQueries
} from "./HCPDocumentTestingUtils";
import {
  AutocompleteFeatureFlags,
  featureFlagDefaults
} from "./KeywordAutocompleteResourceService";
import { NestedPath } from "../util/QueryParsingUtils";
import { ENGLISH, CHINESE, JAPANESE, Language } from "./LanguageDetectService";
import { ParsedQueryTreeToElasticsearchQueriesService } from "./ParsedQueryTreeToElasticsearchQueries";
import { KeywordFilterClauseBuilderService } from "./KeywordFilterClauseBuilderService";
import { buildTermQuery } from "../util/QueryBuildingUtils";

const REGEXP_SPECIAL_CHARACTERS = [
  ".",
  "+",
  "*",
  "?",
  "^",
  "$",
  "(",
  ")",
  "[",
  "]",
  "{",
  "}",
  "|",
  "&",
  "\\"
];

const COMPUTER_JPN = "コンピュータ";
const COMPUTER_CMN = "计算机";
const LANGUAGE_DETECTOR_ENGLISH = () => ENGLISH as Language;
const LANGUAGE_DETECTOR_JAPANESE = () => JAPANESE as Language;
const LANGUAGE_DETECTOR_CMN = () => CHINESE as Language;
const SORT_BY_MATCHING_DOC_COUNT: AggregationsAggregateOrder = {
  ["matching.doc_count"]: "desc"
};

function generateFilters(
  overrides: Partial<FilterInterface> = {}
): FilterInterface {
  const baseFilters: FilterInterface = getEmptyKeywordSearchFilters();

  return { ...baseFilters, ...overrides };
}

function mockFeatureFlagsValue(
  overrides: Partial<AutocompleteFeatureFlags> = {}
) {
  return Object.entries(featureFlagDefaults).reduce((acc, [key, value]) => {
    if (typeof overrides[key as keyof AutocompleteFeatureFlags] !== undefined) {
      acc[key] = overrides[key as keyof AutocompleteFeatureFlags]!;
    } else {
      acc[key] = value.default;
    }
    return acc;
  }, {} as Record<string, boolean>);
}

describe("aggregations", () => {
  describe("building", () => {
    it("should expand filterValue into safe regex", async () => {
      const parsedQueryTreeToElasticsearchQueriesService = createMockInstance(
        ParsedQueryTreeToElasticsearchQueriesService
      );

      const keywordFilterClauseBuilderService = createMockInstance(
        KeywordFilterClauseBuilderService
      );

      const aggregationContainerBuilderService =
        new AggregationContainerBuilderService(
          parsedQueryTreeToElasticsearchQueriesService,
          keywordFilterClauseBuilderService
        );
      const aggregations = [
        {
          filterField: KeywordFilterAutocompleteFilterField.COUNTRY,
          aggregationField: "country_multi"
        },
        {
          filterField: KeywordFilterAutocompleteFilterField.CITY,
          aggregationField: "locationToken_multi"
        },
        {
          filterField: KeywordFilterAutocompleteFilterField.REGION,
          aggregationField: "state_multi"
        },
        {
          filterField: KeywordFilterAutocompleteFilterField.POSTAL_CODE,
          aggregationField: "zipCode5"
        },
        {
          filterField: KeywordFilterAutocompleteFilterField.SPECIALTY,
          aggregationField: "specialty_eng"
        },
        {
          filterField: KeywordFilterAutocompleteFilterField.AFFILIATION,
          aggregationField: "presentWorkInstitutionNames_multi"
        },
        {
          filterField: KeywordFilterAutocompleteFilterField.MEDICAL_SCHOOL,
          aggregationField: "studentInstitutionNames_eng"
        },
        {
          filterField:
            KeywordFilterAutocompleteFilterField.REFERRALS_SERVICE_LINE,
          aggregationField: "referralsServiceLine_eng"
        },
        {
          filterField:
            KeywordFilterAutocompleteFilterField.DIVERSITY_PATIENT_DIVERSITY,
          aggregationField: "patientsDiversityRaces_eng"
        },
        {
          filterField:
            KeywordFilterAutocompleteFilterField.DIVERSITY_PATIENT_AGE,
          aggregationField: "patientsDiversityAgeRanges_eng"
        },
        {
          filterField:
            KeywordFilterAutocompleteFilterField.DIVERSITY_PATIENT_SEX,
          aggregationField: "patientsDiversitySex_eng"
        },
        {
          filterField:
            KeywordFilterAutocompleteFilterField.DIVERSITY_PROVIDER_RACE,
          aggregationField: "providerDiversityRaces_eng"
        },
        {
          filterField:
            KeywordFilterAutocompleteFilterField.DIVERSITY_PROVIDER_LANGUAGES,
          aggregationField: "providerDiversityLanguagesSpoken_eng"
        },
        {
          filterField:
            KeywordFilterAutocompleteFilterField.DIVERSITY_PROVIDER_SEX,
          aggregationField: "providerDiversitySex_eng"
        }
      ];

      for (const aggregation of aggregations) {
        const projectId = faker.datatype.string();
        const projectFeatures = {
          engagementsV2: faker.datatype.boolean()
        };
        const userId = faker.datatype.string();
        const query = undefined;
        const filterField = aggregation.filterField;
        const whitespace = " ";
        const letter1 = faker.random.alpha();
        const letter2 = faker.random.alpha();
        const letter3 = faker.random.alpha();
        const number1 = faker.datatype.number();
        const number2 = faker.datatype.number();
        const specialChar1 = faker.helpers.arrayElement(
          REGEXP_SPECIAL_CHARACTERS
        );
        const specialChar2 = faker.helpers.arrayElement(
          REGEXP_SPECIAL_CHARACTERS
        );
        const specialChar3 = faker.helpers.arrayElement(
          REGEXP_SPECIAL_CHARACTERS
        );
        const specialChar4 = faker.helpers.arrayElement(
          REGEXP_SPECIAL_CHARACTERS
        );

        const filterValue = [
          whitespace,
          letter1,
          specialChar1,
          number1,
          specialChar2,
          letter2,
          specialChar3,
          number2,
          specialChar4,
          letter3
        ].join("");

        const expandedFilterValue = [
          `[${letter1.toLowerCase()}${letter1.toUpperCase()}]`,
          "\\" + specialChar1,
          number1,
          "\\" + specialChar2,
          `[${letter2.toLowerCase()}${letter2.toUpperCase()}]`,
          "\\" + specialChar3,
          number2,
          "\\" + specialChar4,
          `[${letter3.toLowerCase()}${letter3.toUpperCase()}]`
        ].join("");

        const expectedRegexString = `(.*[ \\-#~.]${expandedFilterValue}.*)|(${expandedFilterValue}.*)`;

        const suppliedFilters = generateFilters();
        const featureFlagValues: any = mockFeatureFlagsValue();
        const aggregationContainer =
          await aggregationContainerBuilderService.buildAggregationContainer(
            {
              projectId,
              projectFeatures,
              userId,
              query,
              filterField,
              filterValue,
              suppliedFilters
            },
            LANGUAGE_DETECTOR_ENGLISH,
            [],
            featureFlagValues,
            [],
            undefined
          );

        expect(aggregationContainer).toMatchObject(
          expect.objectContaining({
            matching: expect.objectContaining({
              terms: {
                field: aggregation.aggregationField,
                include: expectedRegexString
              }
            })
          })
        );
      }
    });

    it("should expand filterValue into safe regex when more than one word is present", async () => {
      const parsedQueryTreeToElasticsearchQueriesService = createMockInstance(
        ParsedQueryTreeToElasticsearchQueriesService
      );

      const keywordFilterClauseBuilderService = createMockInstance(
        KeywordFilterClauseBuilderService
      );

      const aggregationContainerBuilderService =
        new AggregationContainerBuilderService(
          parsedQueryTreeToElasticsearchQueriesService,
          keywordFilterClauseBuilderService
        );
      const aggregations = [
        {
          filterField: KeywordFilterAutocompleteFilterField.COUNTRY,
          aggregationField: "country_multi"
        },
        {
          filterField: KeywordFilterAutocompleteFilterField.CITY,
          aggregationField: "locationToken_multi"
        },
        {
          filterField: KeywordFilterAutocompleteFilterField.REGION,
          aggregationField: "state_multi"
        },
        {
          filterField: KeywordFilterAutocompleteFilterField.POSTAL_CODE,
          aggregationField: "zipCode5"
        },
        {
          filterField: KeywordFilterAutocompleteFilterField.SPECIALTY,
          aggregationField: "specialty_eng"
        },
        {
          filterField: KeywordFilterAutocompleteFilterField.AFFILIATION,
          aggregationField: "presentWorkInstitutionNames_multi"
        },
        {
          filterField: KeywordFilterAutocompleteFilterField.MEDICAL_SCHOOL,
          aggregationField: "studentInstitutionNames_eng"
        },
        {
          filterField:
            KeywordFilterAutocompleteFilterField.REFERRALS_SERVICE_LINE,
          aggregationField: "referralsServiceLine_eng"
        },
        {
          filterField:
            KeywordFilterAutocompleteFilterField.DIVERSITY_PATIENT_DIVERSITY,
          aggregationField: "patientsDiversityRaces_eng"
        },
        {
          filterField:
            KeywordFilterAutocompleteFilterField.DIVERSITY_PATIENT_AGE,
          aggregationField: "patientsDiversityAgeRanges_eng"
        },
        {
          filterField:
            KeywordFilterAutocompleteFilterField.DIVERSITY_PATIENT_SEX,
          aggregationField: "patientsDiversitySex_eng"
        },
        {
          filterField:
            KeywordFilterAutocompleteFilterField.DIVERSITY_PROVIDER_RACE,
          aggregationField: "providerDiversityRaces_eng"
        },
        {
          filterField:
            KeywordFilterAutocompleteFilterField.DIVERSITY_PROVIDER_LANGUAGES,
          aggregationField: "providerDiversityLanguagesSpoken_eng"
        },
        {
          filterField:
            KeywordFilterAutocompleteFilterField.DIVERSITY_PROVIDER_SEX,
          aggregationField: "providerDiversitySex_eng"
        }
      ];

      for (const aggregation of aggregations) {
        const projectId = faker.datatype.string();
        const projectFeatures = {
          engagementsV2: faker.datatype.boolean()
        };
        const userId = faker.datatype.string();
        const query = undefined;
        const filterField = aggregation.filterField;
        const word1Letter1 = faker.random.alpha();
        const word1Letter2 = faker.random.alpha();
        const word1Letter3 = faker.random.alpha();
        const word1Number1 = faker.datatype.number();
        const word1Number2 = faker.datatype.number();
        const word1SpecialChar1 = faker.helpers.arrayElement(
          REGEXP_SPECIAL_CHARACTERS
        );
        const word1SpecialChar2 = faker.helpers.arrayElement(
          REGEXP_SPECIAL_CHARACTERS
        );
        const word1SpecialChar3 = faker.helpers.arrayElement(
          REGEXP_SPECIAL_CHARACTERS
        );
        const word1SpecialChar4 = faker.helpers.arrayElement(
          REGEXP_SPECIAL_CHARACTERS
        );

        const word2Letter1 = faker.random.alpha();
        const word2Letter2 = faker.random.alpha();
        const word2Letter3 = faker.random.alpha();
        const word2Number1 = faker.datatype.number();
        const word2Number2 = faker.datatype.number();
        const word2SpecialChar1 = faker.helpers.arrayElement(
          REGEXP_SPECIAL_CHARACTERS
        );
        const word2SpecialChar2 = faker.helpers.arrayElement(
          REGEXP_SPECIAL_CHARACTERS
        );
        const word2SpecialChar3 = faker.helpers.arrayElement(
          REGEXP_SPECIAL_CHARACTERS
        );
        const word2SpecialChar4 = faker.helpers.arrayElement(
          REGEXP_SPECIAL_CHARACTERS
        );

        const filterValue = [
          [
            word1Letter1,
            word1SpecialChar1,
            word1Number1,
            word1SpecialChar2,
            word1Letter2,
            word1SpecialChar3,
            word1Number2,
            word1SpecialChar4,
            word1Letter3
          ].join(""),
          [
            word2Letter1,
            word2SpecialChar1,
            word2Number1,
            word2SpecialChar2,
            word2Letter2,
            word2SpecialChar3,
            word2Number2,
            word2SpecialChar4,
            word2Letter3
          ].join("")
        ].join(" ");

        const expandedFilterValue = [
          [
            `[${word1Letter1.toLowerCase()}${word1Letter1.toUpperCase()}]`,
            "\\" + word1SpecialChar1,
            word1Number1,
            "\\" + word1SpecialChar2,
            `[${word1Letter2.toLowerCase()}${word1Letter2.toUpperCase()}]`,
            "\\" + word1SpecialChar3,
            word1Number2,
            "\\" + word1SpecialChar4,
            `[${word1Letter3.toLowerCase()}${word1Letter3.toUpperCase()}]`
          ].join(""),
          [
            `[${word2Letter1.toLowerCase()}${word2Letter1.toUpperCase()}]`,
            "\\" + word2SpecialChar1,
            word2Number1,
            "\\" + word2SpecialChar2,
            `[${word2Letter2.toLowerCase()}${word2Letter2.toUpperCase()}]`,
            "\\" + word2SpecialChar3,
            word2Number2,
            "\\" + word2SpecialChar4,
            `[${word2Letter3.toLowerCase()}${word2Letter3.toUpperCase()}]`
          ].join("")
        ].join(".{1,2}");

        const expectedRegexString = `(.*[ \\-#~.]${expandedFilterValue}.*)|(${expandedFilterValue}.*)`;

        const suppliedFilters = generateFilters();
        const featureFlagValues: any = mockFeatureFlagsValue();
        const aggregationContainer =
          await aggregationContainerBuilderService.buildAggregationContainer(
            {
              projectId,
              projectFeatures,
              userId,
              query,
              filterField,
              filterValue,
              suppliedFilters
            },
            LANGUAGE_DETECTOR_ENGLISH,
            [],
            featureFlagValues,
            [],
            undefined
          );

        expect(aggregationContainer).toMatchObject(
          expect.objectContaining({
            matching: expect.objectContaining({
              terms: {
                field: aggregation.aggregationField,
                include: expectedRegexString
              }
            })
          })
        );
      }
    });

    it("should not include 'include' regex when filterValue is an empty string", async () => {
      const parsedQueryTreeToElasticsearchQueriesService = createMockInstance(
        ParsedQueryTreeToElasticsearchQueriesService
      );

      const featureFlagValues: any = mockFeatureFlagsValue();

      const keywordFilterClauseBuilderService = createMockInstance(
        KeywordFilterClauseBuilderService
      );

      const aggregationContainerBuilderService =
        new AggregationContainerBuilderService(
          parsedQueryTreeToElasticsearchQueriesService,
          keywordFilterClauseBuilderService
        );
      const aggregations = [
        {
          filterField: KeywordFilterAutocompleteFilterField.COUNTRY,
          aggregationField: "country_multi"
        },
        {
          filterField: KeywordFilterAutocompleteFilterField.CITY,
          aggregationField: "locationToken_multi"
        },
        {
          filterField: KeywordFilterAutocompleteFilterField.REGION,
          aggregationField: "state_multi"
        },
        {
          filterField: KeywordFilterAutocompleteFilterField.POSTAL_CODE,
          aggregationField: "zipCode5"
        },
        {
          filterField: KeywordFilterAutocompleteFilterField.SPECIALTY,
          aggregationField: "specialty_eng"
        },
        {
          filterField: KeywordFilterAutocompleteFilterField.AFFILIATION,
          aggregationField: "presentWorkInstitutionNames_multi"
        },
        {
          filterField: KeywordFilterAutocompleteFilterField.MEDICAL_SCHOOL,
          aggregationField: "studentInstitutionNames_eng"
        },
        {
          filterField:
            KeywordFilterAutocompleteFilterField.REFERRALS_SERVICE_LINE,
          aggregationField: "referralsServiceLine_eng"
        },
        {
          filterField:
            KeywordFilterAutocompleteFilterField.DIVERSITY_PATIENT_DIVERSITY,
          aggregationField: "patientsDiversityRaces_eng"
        },
        {
          filterField:
            KeywordFilterAutocompleteFilterField.DIVERSITY_PATIENT_AGE,
          aggregationField: "patientsDiversityAgeRanges_eng"
        },
        {
          filterField:
            KeywordFilterAutocompleteFilterField.DIVERSITY_PATIENT_SEX,
          aggregationField: "patientsDiversitySex_eng"
        },
        {
          filterField:
            KeywordFilterAutocompleteFilterField.DIVERSITY_PROVIDER_RACE,
          aggregationField: "providerDiversityRaces_eng"
        },
        {
          filterField:
            KeywordFilterAutocompleteFilterField.DIVERSITY_PROVIDER_LANGUAGES,
          aggregationField: "providerDiversityLanguagesSpoken_eng"
        },
        {
          filterField:
            KeywordFilterAutocompleteFilterField.DIVERSITY_PROVIDER_SEX,
          aggregationField: "providerDiversitySex_eng"
        }
      ];

      for (const aggregation of aggregations) {
        const projectId = faker.datatype.string();
        const projectFeatures = {
          engagementsV2: faker.datatype.boolean()
        };
        const userId = faker.datatype.string();
        const query = undefined;
        const filterField = aggregation.filterField;
        const filterValue = "";

        const suppliedFilters = generateFilters();

        const aggregationContainer =
          await aggregationContainerBuilderService.buildAggregationContainer(
            {
              projectId,
              projectFeatures,
              userId,
              query,
              filterField,
              filterValue,
              suppliedFilters
            },
            LANGUAGE_DETECTOR_ENGLISH,
            [],
            featureFlagValues,
            [],
            undefined
          );

        expect(aggregationContainer).toMatchObject(
          expect.objectContaining({
            matching: {
              terms: {
                field: aggregation.aggregationField,
                exclude: "",
                shard_size: 1000
              }
            }
          })
        );
      }
    });

    describe("designations", () => {
      it("should aggregate on allowedDesignations and wildcard expansion when allowedDesignations is not empty", async () => {
        const parsedQueryTreeToElasticsearchQueriesService = createMockInstance(
          ParsedQueryTreeToElasticsearchQueriesService
        );

        const featureFlagsValue: any = mockFeatureFlagsValue();

        const keywordFilterClauseBuilderService = createMockInstance(
          KeywordFilterClauseBuilderService
        );

        const aggregationContainerBuilderService =
          new AggregationContainerBuilderService(
            parsedQueryTreeToElasticsearchQueriesService,
            keywordFilterClauseBuilderService
          );

        const projectId = faker.datatype.string();
        const projectFeatures = {
          engagementsV2: faker.datatype.boolean()
        };
        const userId = faker.datatype.string();
        const query = undefined;
        const filterField = KeywordFilterAutocompleteFilterField.DESIGNATION;
        const filterValue = "a";

        const designations = ["Ab", "aC", "dA", "Da", "eAf", "FaE", "BCD"];

        const suppliedFilters = generateFilters();

        const aggregationContainer =
          await aggregationContainerBuilderService.buildAggregationContainer(
            {
              projectId,
              projectFeatures,
              userId,
              query,
              filterField,
              filterValue,
              suppliedFilters,
              designations
            },
            LANGUAGE_DETECTOR_ENGLISH,
            [],
            featureFlagsValue,
            [],
            undefined
          );

        expect(aggregationContainer).toMatchObject(
          expect.objectContaining({
            matching: {
              terms: {
                field: "designations",
                include: ["Ab", "aC"]
              }
            }
          })
        );
      });

      it("should only aggregate on wildcard expansion when allowedDesignations is empty", async () => {
        const parsedQueryTreeToElasticsearchQueriesService = createMockInstance(
          ParsedQueryTreeToElasticsearchQueriesService
        );

        const featureFlagsValue: any = mockFeatureFlagsValue();

        const keywordFilterClauseBuilderService = createMockInstance(
          KeywordFilterClauseBuilderService
        );

        const aggregationContainerBuilderService =
          new AggregationContainerBuilderService(
            parsedQueryTreeToElasticsearchQueriesService,
            keywordFilterClauseBuilderService
          );

        const projectId = faker.datatype.string();
        const projectFeatures = {
          engagementsV2: faker.datatype.boolean()
        };
        const userId = faker.datatype.string();
        const query = undefined;
        const filterField = KeywordFilterAutocompleteFilterField.DESIGNATION;
        const filterValue = faker.random.word();

        const designations = undefined;

        const suppliedFilters = generateFilters();

        const aggregationContainer =
          await aggregationContainerBuilderService.buildAggregationContainer(
            {
              projectId,
              userId,
              query,
              filterField,
              filterValue,
              suppliedFilters,
              designations,
              projectFeatures
            },
            LANGUAGE_DETECTOR_ENGLISH,
            [],
            featureFlagsValue,
            [],
            undefined
          );

        expect(aggregationContainer).toMatchObject(
          expect.objectContaining({
            matching: {
              terms: {
                field: "designations"
              }
            }
          })
        );
      });
    });

    describe("claims", () => {
      it("should not include 'include' regex when filterValue is an empty string", async () => {
        const filters = [
          {
            nested: {
              path: "DRG_diagnoses",
              query: {
                bool: {
                  filter: []
                }
              }
            }
          }
        ];

        const parsedQueryTreeToElasticsearchQueriesService = createMockInstance(
          ParsedQueryTreeToElasticsearchQueriesService
        );

        const featureFlagsValue: any = mockFeatureFlagsValue();

        const keywordFilterClauseBuilderService = createMockInstance(
          KeywordFilterClauseBuilderService
        );

        const aggregationContainerBuilderService =
          new AggregationContainerBuilderService(
            parsedQueryTreeToElasticsearchQueriesService,
            keywordFilterClauseBuilderService
          );

        const aggregations = [
          {
            filterField: KeywordFilterAutocompleteFilterField.DIAGNOSES,
            aggregationField: "DRG_diagnoses.codeAndDescription_eng"
          },
          {
            filterField: KeywordFilterAutocompleteFilterField.DRG_DIAGNOSES,
            aggregationField: "DRG_diagnoses.codeAndDescription_eng"
          },
          {
            filterField: KeywordFilterAutocompleteFilterField.PROCEDURES,
            aggregationField: "DRG_procedures.codeAndDescription_eng"
          },
          {
            filterField: KeywordFilterAutocompleteFilterField.DRG_PROCEDURES,
            aggregationField: "DRG_procedures.codeAndDescription_eng"
          },
          {
            filterField: KeywordFilterAutocompleteFilterField.GENERIC_NAME,
            aggregationField: "prescriptions.generic_name"
          }
        ];

        for (const aggregation of aggregations) {
          const projectId = faker.datatype.string();
          const projectFeatures = {
            engagementsV2: faker.datatype.boolean()
          };
          const userId = faker.datatype.string();
          const query = undefined;
          const filterField = aggregation.filterField;
          const filterValue = "";
          let subAgg = undefined;

          const suppliedFilters = generateFilters();

          let path: NestedPath = "DRG_procedures";
          let field = `${aggregation.aggregationField}.keyword`;
          if (
            aggregation.filterField ===
              KeywordFilterAutocompleteFilterField.DIAGNOSES ||
            aggregation.filterField ===
              KeywordFilterAutocompleteFilterField.DRG_DIAGNOSES
          ) {
            path = "DRG_diagnoses";
          } else if (
            aggregation.filterField ===
            KeywordFilterAutocompleteFilterField.GENERIC_NAME
          ) {
            path = "prescriptions";
            field = `${aggregation.aggregationField}`;
            subAgg = {
              matching: {
                reverse_nested: {}
              }
            };
          }

          const aggregationContainer =
            await aggregationContainerBuilderService.buildAggregationContainer(
              {
                projectId,
                projectFeatures,
                userId,
                query,
                filterField,
                filterValue,
                suppliedFilters
              },
              LANGUAGE_DETECTOR_ENGLISH,
              filters,
              featureFlagsValue,
              [],
              undefined
            );

          expect(aggregationContainer).toMatchObject(
            expect.objectContaining({
              nested: {
                nested: {
                  path
                },
                aggs: {
                  filtered_matching: {
                    filter: {
                      bool: {
                        filter: []
                      }
                    },
                    aggs: {
                      matching: {
                        terms: {
                          field,
                          exclude: ""
                        },
                        aggs: subAgg
                      }
                    }
                  }
                }
              }
            })
          );
        }
      });

      it("should include global search term parsed query if supplied and no other claims filters were supplied", async () => {
        const parsedQueryTreeToElasticsearchQueriesService = createMockInstance(
          ParsedQueryTreeToElasticsearchQueriesService
        );

        const pathSpecificQueries: Record<NestedPath, QueryDslQueryContainer> =
          {
            trials: generateMockElasticsearchTermQuery(),
            publications: generateMockElasticsearchTermQuery(),
            congress: generateMockElasticsearchTermQuery(),
            payments: generateMockElasticsearchTermQuery(),
            DRG_diagnoses: generateMockElasticsearchTermQuery(),
            DRG_procedures: generateMockElasticsearchTermQuery(),
            prescriptions: generateMockElasticsearchTermQuery(),
            ccsr: generateMockElasticsearchTermQuery(),
            ccsr_px: generateMockElasticsearchTermQuery()
          };

        const mockParseTreeToQueries =
          mockParseTreeToElasticsearchQueries(pathSpecificQueries);

        parsedQueryTreeToElasticsearchQueriesService.parse.mockImplementation(
          mockParseTreeToQueries
        );

        const featureFlagsValue: any = mockFeatureFlagsValue({
          enableBrazilianClaims: false
        });

        const keywordFilterClauseBuilderService = createMockInstance(
          KeywordFilterClauseBuilderService
        );

        const aggregationContainerBuilderService =
          new AggregationContainerBuilderService(
            parsedQueryTreeToElasticsearchQueriesService,
            keywordFilterClauseBuilderService
          );

        const aggregations = [
          {
            filterField: KeywordFilterAutocompleteFilterField.DIAGNOSES,
            aggregationField: "DRG_diagnoses.codeAndDescription_eng"
          },
          {
            filterField: KeywordFilterAutocompleteFilterField.DRG_DIAGNOSES,
            aggregationField: "DRG_diagnoses.codeAndDescription_eng"
          },
          {
            filterField: KeywordFilterAutocompleteFilterField.PROCEDURES,
            aggregationField: "DRG_procedures.codeAndDescription_eng"
          },
          {
            filterField: KeywordFilterAutocompleteFilterField.DRG_PROCEDURES,
            aggregationField: "DRG_procedures.codeAndDescription_eng"
          },
          {
            filterField: KeywordFilterAutocompleteFilterField.GENERIC_NAME,
            aggregationField: "prescriptions.generic_name"
          }
        ];

        for (const aggregation of aggregations) {
          const projectId = faker.datatype.string();
          const projectFeatures = {
            engagementsV2: faker.datatype.boolean()
          };
          const userId = faker.datatype.string();
          const query = faker.datatype.string();
          const filterField = aggregation.filterField;
          const filterValue = faker.random.alpha();

          const suppliedFilters = generateFilters();

          const queryWord1 = faker.datatype.string();
          const queryWord2 = faker.datatype.string();
          const queryWord3 = faker.datatype.string();

          const parsedQuery =
            `${queryWord1} OR ${queryWord2} OR ${queryWord3}`.replace(
              /\bOR\b/g,
              "|"
            );

          let path: NestedPath = "DRG_procedures";
          let field = `${aggregation.aggregationField}.keyword`;
          let subAgg = undefined;
          if (
            aggregation.filterField ===
              KeywordFilterAutocompleteFilterField.DIAGNOSES ||
            aggregation.filterField ===
              KeywordFilterAutocompleteFilterField.DRG_DIAGNOSES
          ) {
            path = "DRG_diagnoses";
          } else if (
            aggregation.filterField ===
            KeywordFilterAutocompleteFilterField.GENERIC_NAME
          ) {
            path = "prescriptions";
            field = `${aggregation.aggregationField}`;
            subAgg = {
              matching: {
                reverse_nested: {}
              }
            };
          }

          const aggregationContainer =
            await aggregationContainerBuilderService.buildAggregationContainer(
              {
                projectId,
                projectFeatures,
                userId,
                query,
                filterField,
                filterValue,
                suppliedFilters
              },
              LANGUAGE_DETECTOR_ENGLISH,
              [],
              featureFlagsValue,
              [],
              parsedQuery
            );

          expect(aggregationContainer).toMatchObject(
            expect.objectContaining({
              nested: {
                nested: {
                  path
                },
                aggs: {
                  filtered_matching: {
                    filter: {
                      bool: {
                        filter: [pathSpecificQueries[path]]
                      }
                    },
                    aggs: {
                      matching: {
                        terms: {
                          field
                        },
                        aggs: subAgg
                      }
                    }
                  }
                }
              }
            })
          );
        }
      });

      describe("ccsr diagnoses", () => {
        it("should construct appropriate query when CCSR_DIAGNOSES is filterField", async () => {
          const featureFlagsValue: any = mockFeatureFlagsValue({
            enableBrazilianClaims: true
          });
          const parsedQueryTreeToElasticsearchQueriesService =
            createMockInstance(ParsedQueryTreeToElasticsearchQueriesService);

          const ccsrToExpand = faker.datatype.string();
          const mockIcdCodeList = [
            `A-${faker.datatype.string()}`,
            `B-${faker.datatype.string()}`,
            `C-${faker.datatype.string()}`
          ];

          const keywordFilterClauseBuilderService = createMockInstance(
            KeywordFilterClauseBuilderService
          );

          const aggregationContainerBuilderService =
            new AggregationContainerBuilderService(
              parsedQueryTreeToElasticsearchQueriesService,
              keywordFilterClauseBuilderService
            );

          const aggregation = {
            filterField: KeywordFilterAutocompleteFilterField.CCSR_DIAGNOSES,
            aggregationField: "DRG_diagnoses.diagnosisCode_eng"
          };

          const projectId = faker.datatype.string();
          const projectFeatures = {
            engagementsV2: faker.datatype.boolean()
          };
          const userId = faker.datatype.string();
          const query = undefined;
          const filterField = aggregation.filterField;
          const suppliedFilters = generateFilters();
          const path = "DRG_diagnoses";
          const ccsrIcdOffset = 0;
          const ccsrIcdSize = 3;

          const aggregationContainer =
            await aggregationContainerBuilderService.buildAggregationContainer(
              {
                projectId,
                projectFeatures,
                userId,
                query,
                filterField,
                filterValue: undefined,
                suppliedFilters,
                ccsrToExpand,
                ccsrIcdOffset,
                ccsrIcdSize
              },
              LANGUAGE_DETECTOR_ENGLISH,
              [],
              featureFlagsValue,
              mockIcdCodeList,
              undefined
            );

          const expectedFilters = mockIcdCodeList
            .slice(0, ccsrIcdSize)
            .reduce((acc, code) => {
              acc[code] = {
                bool: {
                  filter: [
                    buildTermQuery("DRG_diagnoses.diagnosisCode_eng", code)
                  ]
                }
              };
              return acc;
            }, {} as Record<string, any>);

          expect(aggregationContainer).toMatchObject(
            expect.objectContaining({
              nested: {
                nested: {
                  path
                },
                aggs: {
                  filtered_matching: {
                    filters: {
                      filters: expectedFilters
                    },
                    aggs: {
                      matching_desc: {
                        terms: {
                          field: "DRG_diagnoses.codeAndDescription_eng.keyword",
                          size: ccsrIcdSize
                        }
                      }
                    }
                  }
                }
              }
            })
          );
        });
      });

      describe("ccsr procedures", () => {
        it("should construct appropriate query when CCSR_PROCEDURES is filterField", async () => {
          const featureFlagsValue: any = mockFeatureFlagsValue({
            enableBrazilianClaims: true
          });
          const parsedQueryTreeToElasticsearchQueriesService =
            createMockInstance(ParsedQueryTreeToElasticsearchQueriesService);

          const ccsrToExpand = faker.datatype.string();
          const mockClaimCodeList = [
            `A-${faker.datatype.string()}`,
            `B-${faker.datatype.string()}`,
            `C-${faker.datatype.string()}`
          ];

          const keywordFilterClauseBuilderService = createMockInstance(
            KeywordFilterClauseBuilderService
          );

          const aggregationContainerBuilderService =
            new AggregationContainerBuilderService(
              parsedQueryTreeToElasticsearchQueriesService,
              keywordFilterClauseBuilderService
            );

          const aggregation = {
            filterField: KeywordFilterAutocompleteFilterField.CCSR_PROCEDURES,
            aggregationField: "DRG_procedures.procedureCode_eng"
          };

          const projectId = faker.datatype.string();
          const projectFeatures = {
            engagementsV2: faker.datatype.boolean()
          };
          const userId = faker.datatype.string();
          const query = undefined;
          const filterField = aggregation.filterField;
          const suppliedFilters = generateFilters();
          const path = "DRG_procedures";
          const ccsrIcdOffset = 0;
          const ccsrIcdSize = 3;

          const aggregationContainer =
            await aggregationContainerBuilderService.buildAggregationContainer(
              {
                projectId,
                projectFeatures,
                userId,
                query,
                filterField,
                filterValue: undefined,
                suppliedFilters,
                ccsrToExpand,
                ccsrIcdOffset,
                ccsrIcdSize
              },
              LANGUAGE_DETECTOR_ENGLISH,
              [],
              featureFlagsValue,
              mockClaimCodeList,
              undefined
            );

          const expectedFilters = mockClaimCodeList.reduce((acc, code) => {
            acc[code] = {
              bool: {
                filter: [
                  buildTermQuery("DRG_procedures.procedureCode_eng", code)
                ]
              }
            };
            return acc;
          }, {} as Record<string, any>);

          expect(aggregationContainer).toMatchObject(
            expect.objectContaining({
              nested: {
                nested: {
                  path
                },
                aggs: {
                  filtered_matching: {
                    filters: {
                      filters: expectedFilters
                    },
                    aggs: {
                      matching_desc: {
                        terms: {
                          field:
                            "DRG_procedures.codeAndDescription_eng.keyword",
                          size: ccsrIcdSize
                        }
                      }
                    }
                  }
                }
              }
            })
          );
        });
      });

      describe("Brazilian claims enabled", () => {
        it("should use reverse nested aggregation for claims filters when min count is NOT supplied", async () => {
          const parsedQueryTreeToElasticsearchQueriesService =
            createMockInstance(ParsedQueryTreeToElasticsearchQueriesService);

          const featureFlagsValue: any = mockFeatureFlagsValue({
            enableBrazilianClaims: true
          });

          const keywordFilterClauseBuilderService = createMockInstance(
            KeywordFilterClauseBuilderService
          );

          const aggregationContainerBuilderService =
            new AggregationContainerBuilderService(
              parsedQueryTreeToElasticsearchQueriesService,
              keywordFilterClauseBuilderService
            );

          const aggregations = [
            {
              filterField: KeywordFilterAutocompleteFilterField.DIAGNOSES,
              aggregationField: "DRG_diagnoses.codeAndDescription_eng"
            },
            {
              filterField: KeywordFilterAutocompleteFilterField.DRG_DIAGNOSES,
              aggregationField: "DRG_diagnoses.codeAndDescription_eng"
            },
            {
              filterField: KeywordFilterAutocompleteFilterField.PROCEDURES,
              aggregationField: "DRG_procedures.codeAndDescription_eng"
            },
            {
              filterField: KeywordFilterAutocompleteFilterField.DRG_PROCEDURES,
              aggregationField: "DRG_procedures.codeAndDescription_eng"
            },
            {
              filterField: KeywordFilterAutocompleteFilterField.CCSR,
              aggregationField: "ccsr.description_eng"
            },
            {
              filterField: KeywordFilterAutocompleteFilterField.CCSR_PX,
              aggregationField: "ccsr_px.description_eng"
            }
          ];

          for (const aggregation of aggregations) {
            const projectId = faker.datatype.string();
            const projectFeatures = {
              engagementsV2: faker.datatype.boolean()
            };
            const userId = faker.datatype.string();
            const query = undefined;
            const filterField = aggregation.filterField;
            const filterValue = faker.random.alpha();
            const suppliedFilters = generateFilters();

            let path =
              aggregation.filterField ==
                KeywordFilterAutocompleteFilterField.DIAGNOSES ||
              aggregation.filterField ==
                KeywordFilterAutocompleteFilterField.DRG_DIAGNOSES
                ? "DRG_diagnoses"
                : "DRG_procedures";

            path =
              aggregation.filterField ===
              KeywordFilterAutocompleteFilterField.CCSR
                ? "ccsr"
                : path;

            path =
              aggregation.filterField ===
              KeywordFilterAutocompleteFilterField.CCSR_PX
                ? "ccsr_px"
                : path;

            const filters = [
              {
                nested: {
                  path,
                  query: {
                    bool: {
                      filter: [
                        {
                          match_phrase: {
                            [`${aggregation.aggregationField}.autocomplete_search`]:
                              {
                                query: filterValue,
                                slop: 5
                              }
                          }
                        }
                      ]
                    }
                  }
                }
              }
            ];

            const aggregationContainer =
              await aggregationContainerBuilderService.buildAggregationContainer(
                {
                  projectId,
                  projectFeatures,
                  userId,
                  query,
                  filterField,
                  filterValue,
                  suppliedFilters
                },
                LANGUAGE_DETECTOR_ENGLISH,
                filters,
                featureFlagsValue,
                [],
                undefined
              );
            const size = path === "ccsr" ? "icdSize" : "cptSize";
            const expectedAggregationForCcsr = {
              matching: {
                terms: {
                  field: aggregation.aggregationField
                },
                aggs: {
                  icd_size: {
                    terms: {
                      field: `${path}.${size}`
                    }
                  }
                }
              }
            };

            const expectedAggregationForNonCcsr = {
              matching: {
                terms: {
                  field: `${aggregation.aggregationField}.keyword`
                }
              }
            };
            const isCcsr =
              aggregation.filterField ==
                KeywordFilterAutocompleteFilterField.CCSR ||
              aggregation.filterField ==
                KeywordFilterAutocompleteFilterField.CCSR_PX;
            expect(aggregationContainer).toMatchObject(
              expect.objectContaining({
                nested: {
                  nested: {
                    path
                  },
                  aggs: {
                    filtered_matching: {
                      filter: {
                        bool: {
                          filter: [
                            {
                              match_phrase: {
                                [`${aggregation.aggregationField}.autocomplete_search`]:
                                  {
                                    query: filterValue,
                                    slop: 5
                                  }
                              }
                            }
                          ]
                        }
                      },
                      aggs: isCcsr
                        ? expectedAggregationForCcsr
                        : expectedAggregationForNonCcsr
                    }
                  }
                }
              })
            );
          }
        });

        describe("supplied claims filter values", () => {
          it("should be used to filter nested claims aggregation", async () => {
            const featureFlagsValue: any = mockFeatureFlagsValue({
              enableBrazilianClaims: true
            });
            const parsedQueryTreeToElasticsearchQueriesService =
              createMockInstance(ParsedQueryTreeToElasticsearchQueriesService);

            const keywordFilterClauseBuilderService = createMockInstance(
              KeywordFilterClauseBuilderService
            );

            const aggregationContainerBuilderService =
              new AggregationContainerBuilderService(
                parsedQueryTreeToElasticsearchQueriesService,
                keywordFilterClauseBuilderService
              );

            const aggregations = [
              {
                filterField: KeywordFilterAutocompleteFilterField.DIAGNOSES,
                aggregationField: "DRG_diagnoses.codeAndDescription_eng"
              },
              {
                filterField: KeywordFilterAutocompleteFilterField.DRG_DIAGNOSES,
                aggregationField: "DRG_diagnoses.codeAndDescription_eng"
              },
              {
                filterField: KeywordFilterAutocompleteFilterField.PROCEDURES,
                aggregationField: "DRG_procedures.codeAndDescription_eng"
              },
              {
                filterField:
                  KeywordFilterAutocompleteFilterField.DRG_PROCEDURES,
                aggregationField: "DRG_procedures.codeAndDescription_eng"
              },
              {
                filterField: KeywordFilterAutocompleteFilterField.CCSR,
                aggregationField: "ccsr.description_eng"
              },
              {
                filterField: KeywordFilterAutocompleteFilterField.CCSR_PX,
                aggregationField: "ccsr_px.description_eng"
              }
            ];

            for (const aggregation of aggregations) {
              const projectId = faker.datatype.string();
              const projectFeatures = {
                engagementsV2: faker.datatype.boolean()
              };
              const userId = faker.datatype.string();
              const query = undefined;
              const filterField = aggregation.filterField;
              const filterValue = faker.random.alpha();
              const suppliedFilters = generateFilters();
              let path =
                aggregation.filterField ==
                  KeywordFilterAutocompleteFilterField.DIAGNOSES ||
                aggregation.filterField ==
                  KeywordFilterAutocompleteFilterField.DRG_DIAGNOSES
                  ? "DRG_diagnoses"
                  : "DRG_procedures";

              path =
                aggregation.filterField ===
                KeywordFilterAutocompleteFilterField.CCSR
                  ? "ccsr"
                  : path;
              path =
                aggregation.filterField ===
                KeywordFilterAutocompleteFilterField.CCSR_PX
                  ? "ccsr_px"
                  : path;

              const expectedFilters: QueryDslQueryContainer[] = [
                {
                  match_phrase: {
                    [`${aggregation.aggregationField}.autocomplete_search`]: {
                      query: filterValue,
                      slop: 5
                    }
                  }
                }
              ];

              if (
                aggregation.filterField !==
                KeywordFilterAutocompleteFilterField.DIAGNOSES
              ) {
                suppliedFilters.claims.diagnosesICD.values = [
                  faker.datatype.string()
                ];

                expectedFilters.push({
                  terms: {
                    "DRG_diagnoses.codeAndDescription_eng":
                      suppliedFilters.claims.diagnosesICD.values
                  }
                });
              }
              if (
                aggregation.filterField !==
                KeywordFilterAutocompleteFilterField.DRG_DIAGNOSES
              ) {
                suppliedFilters.claims.diagnosesICD.values = [
                  faker.datatype.string()
                ];

                expectedFilters.push({
                  terms: {
                    "DRG_diagnoses.codeAndDescription_eng":
                      suppliedFilters.claims.diagnosesICD.values
                  }
                });
              }
              if (
                aggregation.filterField !==
                KeywordFilterAutocompleteFilterField.PROCEDURES
              ) {
                suppliedFilters.claims.proceduresCPT.values = [
                  faker.datatype.string()
                ];

                expectedFilters.push({
                  terms: {
                    "DRG_procedures.codeAndDescription_eng":
                      suppliedFilters.claims.proceduresCPT.values
                  }
                });
              }
              if (
                aggregation.filterField !==
                KeywordFilterAutocompleteFilterField.DRG_PROCEDURES
              ) {
                suppliedFilters.claims.proceduresCPT.values = [
                  faker.datatype.string()
                ];

                expectedFilters.push({
                  terms: {
                    "DRG_procedures.codeAndDescription_eng":
                      suppliedFilters.claims.proceduresCPT.values
                  }
                });
              }

              if (
                aggregation.filterField !==
                KeywordFilterAutocompleteFilterField.CCSR
              ) {
                suppliedFilters.claims.ccsr!.values = [faker.datatype.string()];

                expectedFilters.push({
                  terms: {
                    "ccsr.description_eng": suppliedFilters.claims.ccsr!.values
                  }
                });
              }

              const filters = [
                {
                  nested: {
                    path,
                    query: {
                      bool: {
                        filter: expectedFilters
                      }
                    }
                  }
                }
              ];

              const aggregationContainer =
                await aggregationContainerBuilderService.buildAggregationContainer(
                  {
                    projectId,
                    projectFeatures,
                    userId,
                    query,
                    filterField,
                    filterValue,
                    suppliedFilters
                  },
                  LANGUAGE_DETECTOR_ENGLISH,
                  filters,
                  featureFlagsValue,
                  [],
                  undefined
                );
              const size = path === "ccsr" ? "icdSize" : "cptSize";
              const expectedAggregationForCcsr = {
                matching: {
                  terms: {
                    field: aggregation.aggregationField
                  },
                  aggs: {
                    icd_size: {
                      terms: {
                        field: `${path}.${size}`
                      }
                    }
                  }
                }
              };

              const expectedAggregationForNonCcsr = {
                matching: {
                  terms: {
                    field: `${aggregation.aggregationField}.keyword`
                  }
                }
              };

              const isCcsr =
                aggregation.filterField ==
                  KeywordFilterAutocompleteFilterField.CCSR ||
                aggregation.filterField ==
                  KeywordFilterAutocompleteFilterField.CCSR_PX;
              expect(aggregationContainer).toMatchObject(
                expect.objectContaining({
                  nested: {
                    nested: {
                      path
                    },
                    aggs: {
                      filtered_matching: {
                        filter: {
                          bool: {
                            filter: expect.arrayContaining(expectedFilters)
                          }
                        },
                        aggs: isCcsr
                          ? expectedAggregationForCcsr
                          : expectedAggregationForNonCcsr
                      }
                    }
                  }
                })
              );
            }
          });

          describe("min claims filters", () => {
            it("should be used to filter nested claims aggregation using a range query", async () => {
              const parsedQueryTreeToElasticsearchQueriesService =
                createMockInstance(
                  ParsedQueryTreeToElasticsearchQueriesService
                );

              const featureFlagsValue: any = mockFeatureFlagsValue({
                enableBrazilianClaims: true
              });

              const keywordFilterClauseBuilderService = createMockInstance(
                KeywordFilterClauseBuilderService
              );

              const aggregationContainerBuilderService =
                new AggregationContainerBuilderService(
                  parsedQueryTreeToElasticsearchQueriesService,
                  keywordFilterClauseBuilderService
                );

              const aggregations = [
                {
                  filterField: KeywordFilterAutocompleteFilterField.DIAGNOSES,
                  aggregationField: "DRG_diagnoses.codeAndDescription_eng"
                },
                {
                  filterField:
                    KeywordFilterAutocompleteFilterField.DRG_DIAGNOSES,
                  aggregationField: "DRG_diagnoses.codeAndDescription_eng"
                },
                {
                  filterField: KeywordFilterAutocompleteFilterField.PROCEDURES,
                  aggregationField: "DRG_procedures.codeAndDescription_eng"
                },
                {
                  filterField:
                    KeywordFilterAutocompleteFilterField.DRG_PROCEDURES,
                  aggregationField: "DRG_procedures.codeAndDescription_eng"
                },
                {
                  filterField: KeywordFilterAutocompleteFilterField.CCSR,
                  aggregationField: "ccsr.description_eng"
                },
                {
                  filterField: KeywordFilterAutocompleteFilterField.CCSR_PX,
                  aggregationField: "ccsr_px.description_eng"
                }
              ];

              for (const aggregation of aggregations) {
                const projectId = faker.datatype.string();
                const projectFeatures = {
                  engagementsV2: faker.datatype.boolean()
                };
                const userId = faker.datatype.string();
                const query = undefined;
                const filterField = aggregation.filterField;
                const filterValue = faker.random.alpha();
                const suppliedFilters = generateFilters();
                let path =
                  aggregation.filterField ==
                    KeywordFilterAutocompleteFilterField.DIAGNOSES ||
                  aggregation.filterField ==
                    KeywordFilterAutocompleteFilterField.DRG_DIAGNOSES
                    ? "DRG_diagnoses"
                    : "DRG_procedures";

                path =
                  aggregation.filterField ===
                  KeywordFilterAutocompleteFilterField.CCSR
                    ? "ccsr"
                    : path;
                path =
                  aggregation.filterField ===
                  KeywordFilterAutocompleteFilterField.CCSR_PX
                    ? "ccsr_px"
                    : path;

                const minCount = faker.datatype.number();
                suppliedFilters.claims.diagnosesICDMinCount.value = minCount;
                suppliedFilters.claims.proceduresCPTMinCount.value = minCount;

                const expectedFilters: QueryDslQueryContainer[] = [
                  {
                    match_phrase: {
                      [`${aggregation.aggregationField}.autocomplete_search`]: {
                        query: filterValue,
                        slop: 5
                      }
                    }
                  }
                ];

                if (
                  aggregation.filterField !==
                  KeywordFilterAutocompleteFilterField.DIAGNOSES
                ) {
                  suppliedFilters.claims.diagnosesICD.values = [
                    faker.datatype.string()
                  ];

                  expectedFilters.push({
                    terms: {
                      "DRG_diagnoses.codeAndDescription_eng":
                        suppliedFilters.claims.diagnosesICD.values
                    }
                  });
                }
                if (
                  aggregation.filterField !==
                  KeywordFilterAutocompleteFilterField.DRG_DIAGNOSES
                ) {
                  suppliedFilters.claims.diagnosesICD.values = [
                    faker.datatype.string()
                  ];

                  expectedFilters.push({
                    terms: {
                      "DRG_diagnoses.codeAndDescription_eng":
                        suppliedFilters.claims.diagnosesICD.values
                    }
                  });
                }
                if (
                  aggregation.filterField !==
                  KeywordFilterAutocompleteFilterField.PROCEDURES
                ) {
                  suppliedFilters.claims.proceduresCPT.values = [
                    faker.datatype.string()
                  ];

                  expectedFilters.push({
                    terms: {
                      "DRG_procedures.codeAndDescription_eng":
                        suppliedFilters.claims.proceduresCPT.values
                    }
                  });
                }
                if (
                  aggregation.filterField !==
                  KeywordFilterAutocompleteFilterField.DRG_PROCEDURES
                ) {
                  suppliedFilters.claims.proceduresCPT.values = [
                    faker.datatype.string()
                  ];

                  expectedFilters.push({
                    terms: {
                      "DRG_procedures.codeAndDescription_eng":
                        suppliedFilters.claims.proceduresCPT.values
                    }
                  });
                }

                if (
                  aggregation.filterField !==
                  KeywordFilterAutocompleteFilterField.CCSR
                ) {
                  suppliedFilters.claims.ccsr!.values = [
                    faker.datatype.string()
                  ];

                  expectedFilters.push({
                    terms: {
                      "ccsr.description_eng":
                        suppliedFilters.claims.ccsr!.values
                    }
                  });
                }

                const filters: QueryDslQueryContainer[] = [
                  {
                    function_score: {
                      query: {
                        nested: {
                          path,
                          query: {
                            function_score: {
                              boost_mode: "replace",
                              score_mode: "sum",
                              query: {
                                bool: {
                                  must: expectedFilters
                                }
                              },
                              functions: [
                                {
                                  field_value_factor: {
                                    field: `${path}.internalCount`,
                                    missing: 0
                                  }
                                }
                              ]
                            }
                          },
                          score_mode: "sum"
                        }
                      },
                      min_score: minCount
                    }
                  }
                ];

                const aggregationContainer =
                  await aggregationContainerBuilderService.buildAggregationContainer(
                    {
                      projectId,
                      projectFeatures,
                      userId,
                      query,
                      filterField,
                      filterValue,
                      suppliedFilters
                    },
                    LANGUAGE_DETECTOR_ENGLISH,
                    filters,
                    featureFlagsValue,
                    [],
                    undefined
                  );
                const size = path === "ccsr" ? "icdSize" : "cptSize";
                const expectedAggregationForCcsr = {
                  matching: {
                    terms: {
                      field: aggregation.aggregationField
                    },
                    aggs: {
                      icd_size: {
                        terms: {
                          field: `${path}.${size}`
                        }
                      }
                    }
                  }
                };

                const expectedAggregationForNonCcsr = {
                  matching: {
                    terms: {
                      field: `${aggregation.aggregationField}.keyword`
                    }
                  }
                };
                const isCcsr =
                  aggregation.filterField ==
                    KeywordFilterAutocompleteFilterField.CCSR ||
                  aggregation.filterField ==
                    KeywordFilterAutocompleteFilterField.CCSR_PX;
                expect(aggregationContainer).toMatchObject(
                  expect.objectContaining({
                    nested: {
                      nested: {
                        path
                      },
                      aggs: {
                        filtered_matching: {
                          filter: {
                            bool: {
                              filter: [
                                ...expectedFilters,
                                {
                                  range: {
                                    [`${path}.internalCount`]: {
                                      gte: minCount
                                    }
                                  }
                                }
                              ]
                            }
                          },
                          aggs: isCcsr
                            ? expectedAggregationForCcsr
                            : expectedAggregationForNonCcsr
                        }
                      }
                    }
                  })
                );
              }
            });
          });

          describe("max claims filters", () => {
            it("should be used to filter nested claims aggregation using a range query", async () => {
              const parsedQueryTreeToElasticsearchQueriesService =
                createMockInstance(
                  ParsedQueryTreeToElasticsearchQueriesService
                );

              const featureFlagsValue: any = mockFeatureFlagsValue({
                enableBrazilianClaims: true
              });

              const keywordFilterClauseBuilderService = createMockInstance(
                KeywordFilterClauseBuilderService
              );

              const aggregationContainerBuilderService =
                new AggregationContainerBuilderService(
                  parsedQueryTreeToElasticsearchQueriesService,
                  keywordFilterClauseBuilderService
                );

              const aggregations = [
                {
                  filterField: KeywordFilterAutocompleteFilterField.DIAGNOSES,
                  aggregationField: "DRG_diagnoses.codeAndDescription_eng"
                },
                {
                  filterField:
                    KeywordFilterAutocompleteFilterField.DRG_DIAGNOSES,
                  aggregationField: "DRG_diagnoses.codeAndDescription_eng"
                },
                {
                  filterField: KeywordFilterAutocompleteFilterField.PROCEDURES,
                  aggregationField: "DRG_procedures.codeAndDescription_eng"
                },
                {
                  filterField:
                    KeywordFilterAutocompleteFilterField.DRG_PROCEDURES,
                  aggregationField: "DRG_procedures.codeAndDescription_eng"
                },
                {
                  filterField: KeywordFilterAutocompleteFilterField.CCSR,
                  aggregationField: "ccsr.description_eng"
                },
                {
                  filterField: KeywordFilterAutocompleteFilterField.CCSR_PX,
                  aggregationField: "ccsr_px.description_eng"
                }
              ];

              for (const aggregation of aggregations) {
                const projectId = faker.datatype.string();
                const projectFeatures = {
                  engagementsV2: faker.datatype.boolean()
                };
                const userId = faker.datatype.string();
                const query = undefined;
                const filterField = aggregation.filterField;
                const filterValue = faker.random.alpha();
                const suppliedFilters = generateFilters();
                let path =
                  aggregation.filterField ==
                    KeywordFilterAutocompleteFilterField.DIAGNOSES ||
                  aggregation.filterField ==
                    KeywordFilterAutocompleteFilterField.DRG_DIAGNOSES
                    ? "DRG_diagnoses"
                    : "DRG_procedures";
                path =
                  aggregation.filterField ===
                  KeywordFilterAutocompleteFilterField.CCSR
                    ? "ccsr"
                    : path;
                path =
                  aggregation.filterField ===
                  KeywordFilterAutocompleteFilterField.CCSR_PX
                    ? "ccsr_px"
                    : path;

                const maxCount = faker.datatype.number();
                suppliedFilters.claims.diagnosesICDMaxCount!.value = maxCount;
                suppliedFilters.claims.proceduresCPTMaxCount!.value = maxCount;

                const expectedFilters: QueryDslQueryContainer[] = [
                  {
                    match_phrase: {
                      [`${aggregation.aggregationField}.autocomplete_search`]: {
                        query: filterValue,
                        slop: 5
                      }
                    }
                  }
                ];

                if (
                  aggregation.filterField !==
                  KeywordFilterAutocompleteFilterField.DIAGNOSES
                ) {
                  suppliedFilters.claims.diagnosesICD.values = [
                    faker.datatype.string()
                  ];

                  expectedFilters.push({
                    terms: {
                      "DRG_diagnoses.codeAndDescription_eng":
                        suppliedFilters.claims.diagnosesICD.values
                    }
                  });
                }
                if (
                  aggregation.filterField !==
                  KeywordFilterAutocompleteFilterField.DRG_DIAGNOSES
                ) {
                  suppliedFilters.claims.diagnosesICD.values = [
                    faker.datatype.string()
                  ];

                  expectedFilters.push({
                    terms: {
                      "DRG_diagnoses.codeAndDescription_eng":
                        suppliedFilters.claims.diagnosesICD.values
                    }
                  });
                }
                if (
                  aggregation.filterField !==
                  KeywordFilterAutocompleteFilterField.PROCEDURES
                ) {
                  suppliedFilters.claims.proceduresCPT.values = [
                    faker.datatype.string()
                  ];

                  expectedFilters.push({
                    terms: {
                      "DRG_procedures.codeAndDescription_eng":
                        suppliedFilters.claims.proceduresCPT.values
                    }
                  });
                }
                if (
                  aggregation.filterField !==
                  KeywordFilterAutocompleteFilterField.DRG_PROCEDURES
                ) {
                  suppliedFilters.claims.proceduresCPT.values = [
                    faker.datatype.string()
                  ];

                  expectedFilters.push({
                    terms: {
                      "DRG_procedures.codeAndDescription_eng":
                        suppliedFilters.claims.proceduresCPT.values
                    }
                  });
                }
                if (
                  aggregation.filterField !==
                  KeywordFilterAutocompleteFilterField.CCSR
                ) {
                  suppliedFilters.claims.ccsr!.values = [
                    faker.datatype.string()
                  ];

                  expectedFilters.push({
                    terms: {
                      "ccsr.description_eng":
                        suppliedFilters.claims.ccsr!.values
                    }
                  });
                }

                const filters: QueryDslQueryContainer[] = [
                  {
                    function_score: {
                      query: {
                        nested: {
                          path,
                          query: {
                            function_score: {
                              boost_mode: "replace",
                              score_mode: "sum",
                              query: {
                                bool: {
                                  must: expectedFilters
                                }
                              },
                              functions: [
                                {
                                  field_value_factor: {
                                    field: `${path}.internalCount`,
                                    missing: 0
                                  }
                                }
                              ]
                            }
                          },
                          score_mode: "sum"
                        }
                      },
                      min_score: maxCount
                    }
                  }
                ];

                const aggregationContainer =
                  await aggregationContainerBuilderService.buildAggregationContainer(
                    {
                      projectId,
                      projectFeatures,
                      userId,
                      query,
                      filterField,
                      filterValue,
                      suppliedFilters
                    },
                    LANGUAGE_DETECTOR_ENGLISH,
                    filters,
                    featureFlagsValue,
                    [],
                    undefined
                  );
                const size = path === "ccsr" ? "icdSize" : "cptSize";
                const expectedAggregationForCcsr = {
                  matching: {
                    terms: {
                      field: aggregation.aggregationField
                    },
                    aggs: {
                      icd_size: {
                        terms: {
                          field: `${path}.${size}`
                        }
                      }
                    }
                  }
                };

                const expectedAggregationForNonCcsr = {
                  matching: {
                    terms: {
                      field: `${aggregation.aggregationField}.keyword`
                    }
                  }
                };
                const isCcsr =
                  aggregation.filterField ==
                    KeywordFilterAutocompleteFilterField.CCSR ||
                  aggregation.filterField ==
                    KeywordFilterAutocompleteFilterField.CCSR_PX;
                expect(aggregationContainer).toMatchObject(
                  expect.objectContaining({
                    nested: {
                      nested: {
                        path
                      },
                      aggs: {
                        filtered_matching: {
                          filter: {
                            bool: {
                              filter: [
                                ...expectedFilters,
                                {
                                  range: {
                                    [`${path}.internalCount`]: {
                                      lte: maxCount
                                    }
                                  }
                                }
                              ]
                            }
                          },
                          aggs: isCcsr
                            ? expectedAggregationForCcsr
                            : expectedAggregationForNonCcsr
                        }
                      }
                    }
                  })
                );
              }
            });
          });

          describe("claims timeframe filter", () => {
            it("should be used to filter nested claims aggregation using a range query, parsing filters from must clause in function_score", async () => {
              const parsedQueryTreeToElasticsearchQueriesService =
                createMockInstance(
                  ParsedQueryTreeToElasticsearchQueriesService
                );

              const featureFlagsValue: any = mockFeatureFlagsValue({
                enableBrazilianClaims: true
              });

              const keywordFilterClauseBuilderService = createMockInstance(
                KeywordFilterClauseBuilderService
              );

              const aggregationContainerBuilderService =
                new AggregationContainerBuilderService(
                  parsedQueryTreeToElasticsearchQueriesService,
                  keywordFilterClauseBuilderService
                );

              const aggregations = [
                {
                  filterField: KeywordFilterAutocompleteFilterField.DIAGNOSES,
                  aggregationField: "DRG_diagnoses.codeAndDescription_eng"
                },
                {
                  filterField:
                    KeywordFilterAutocompleteFilterField.DRG_DIAGNOSES,
                  aggregationField: "DRG_diagnoses.codeAndDescription_eng"
                },
                {
                  filterField: KeywordFilterAutocompleteFilterField.PROCEDURES,
                  aggregationField: "DRG_procedures.codeAndDescription_eng"
                },
                {
                  filterField:
                    KeywordFilterAutocompleteFilterField.DRG_PROCEDURES,
                  aggregationField: "DRG_procedures.codeAndDescription_eng"
                },
                {
                  filterField: KeywordFilterAutocompleteFilterField.CCSR,
                  aggregationField: "ccsr.description_eng"
                },
                {
                  filterField: KeywordFilterAutocompleteFilterField.CCSR_PX,
                  aggregationField: "ccsr_px.description_eng"
                }
              ];

              for (const timeframe of [1, 2, 5]) {
                for (const aggregation of aggregations) {
                  const projectId = faker.datatype.string();
                  const projectFeatures = {
                    engagementsV2: faker.datatype.boolean()
                  };
                  const userId = faker.datatype.string();
                  const query = undefined;
                  const filterField = aggregation.filterField;
                  const filterValue = faker.random.alpha();
                  const suppliedFilters = generateFilters();
                  let path =
                    aggregation.filterField ==
                      KeywordFilterAutocompleteFilterField.DIAGNOSES ||
                    aggregation.filterField ==
                      KeywordFilterAutocompleteFilterField.DRG_DIAGNOSES
                      ? "DRG_diagnoses"
                      : "DRG_procedures";
                  path =
                    aggregation.filterField ===
                    KeywordFilterAutocompleteFilterField.CCSR
                      ? "ccsr"
                      : path;

                  path =
                    aggregation.filterField ===
                    KeywordFilterAutocompleteFilterField.CCSR_PX
                      ? "ccsr_px"
                      : path;

                  suppliedFilters.claims.timeFrame!.value = timeframe;

                  const expectedFilters: QueryDslQueryContainer[] = [
                    {
                      match_phrase: {
                        [`${aggregation.aggregationField}.autocomplete_search`]:
                          {
                            query: filterValue,
                            slop: 5
                          }
                      }
                    }
                  ];

                  if (
                    aggregation.filterField !==
                    KeywordFilterAutocompleteFilterField.DIAGNOSES
                  ) {
                    suppliedFilters.claims.diagnosesICD.values = [
                      faker.datatype.string()
                    ];

                    expectedFilters.push({
                      terms: {
                        "DRG_diagnoses.codeAndDescription_eng":
                          suppliedFilters.claims.diagnosesICD.values
                      }
                    });
                  }
                  if (
                    aggregation.filterField !==
                    KeywordFilterAutocompleteFilterField.DRG_DIAGNOSES
                  ) {
                    suppliedFilters.claims.diagnosesICD.values = [
                      faker.datatype.string()
                    ];

                    expectedFilters.push({
                      terms: {
                        "DRG_diagnoses.codeAndDescription_eng":
                          suppliedFilters.claims.diagnosesICD.values
                      }
                    });
                  }
                  if (
                    aggregation.filterField !==
                    KeywordFilterAutocompleteFilterField.PROCEDURES
                  ) {
                    suppliedFilters.claims.proceduresCPT.values = [
                      faker.datatype.string()
                    ];

                    expectedFilters.push({
                      terms: {
                        "DRG_procedures.codeAndDescription_eng":
                          suppliedFilters.claims.proceduresCPT.values
                      }
                    });
                  }
                  if (
                    aggregation.filterField !==
                    KeywordFilterAutocompleteFilterField.DRG_PROCEDURES
                  ) {
                    suppliedFilters.claims.proceduresCPT.values = [
                      faker.datatype.string()
                    ];

                    expectedFilters.push({
                      terms: {
                        "DRG_procedures.codeAndDescription_eng":
                          suppliedFilters.claims.proceduresCPT.values
                      }
                    });
                  }
                  if (
                    aggregation.filterField !==
                    KeywordFilterAutocompleteFilterField.CCSR
                  ) {
                    suppliedFilters.claims.ccsr!.values = [
                      faker.datatype.string()
                    ];

                    expectedFilters.push({
                      terms: {
                        "ccsr.description_eng":
                          suppliedFilters.claims.ccsr!.values
                      }
                    });
                  }

                  const filters: QueryDslQueryContainer[] = [
                    {
                      function_score: {
                        min_score: 1,
                        query: {
                          nested: {
                            path,
                            query: {
                              function_score: {
                                score_mode: "sum",
                                boost_mode: "replace",
                                query: {
                                  bool: {
                                    must: expectedFilters
                                  }
                                },
                                functions: [
                                  {
                                    field_value_factor: {
                                      field: `${path}.internalCount_${timeframe}_year`,
                                      missing: 0
                                    }
                                  }
                                ]
                              }
                            },
                            score_mode: "sum"
                          }
                        }
                      }
                    }
                  ];

                  const aggregationContainer =
                    await aggregationContainerBuilderService.buildAggregationContainer(
                      {
                        projectId,
                        projectFeatures,
                        userId,
                        query,
                        filterField,
                        filterValue,
                        suppliedFilters
                      },
                      LANGUAGE_DETECTOR_ENGLISH,
                      filters,
                      featureFlagsValue,
                      [],
                      undefined
                    );
                  const size = path === "ccsr" ? "icdSize" : "cptSize";
                  const expectedAggregationForCcsr = {
                    matching: {
                      terms: {
                        field: aggregation.aggregationField
                      },
                      aggs: {
                        icd_size: {
                          terms: {
                            field: `${path}.${size}`
                          }
                        }
                      }
                    }
                  };

                  const expectedAggregationForNonCcsr = {
                    matching: {
                      terms: {
                        field: `${aggregation.aggregationField}.keyword`
                      }
                    }
                  };
                  const isCcsr =
                    aggregation.filterField ==
                      KeywordFilterAutocompleteFilterField.CCSR ||
                    aggregation.filterField ==
                      KeywordFilterAutocompleteFilterField.CCSR_PX;
                  expect(aggregationContainer).toMatchObject(
                    expect.objectContaining({
                      nested: {
                        nested: {
                          path
                        },
                        aggs: {
                          filtered_matching: {
                            filter: {
                              bool: {
                                filter: [
                                  ...expectedFilters,
                                  {
                                    range: {
                                      [`${path}.internalCount_${timeframe}_year`]:
                                        {
                                          gte: 1
                                        }
                                    }
                                  }
                                ]
                              }
                            },
                            aggs: isCcsr
                              ? expectedAggregationForCcsr
                              : expectedAggregationForNonCcsr
                          }
                        }
                      }
                    })
                  );
                }
              }
            });

            it("should be used to filter nested claims aggregation using a range query, parsing filters from nested query", async () => {
              const parsedQueryTreeToElasticsearchQueriesService =
                createMockInstance(
                  ParsedQueryTreeToElasticsearchQueriesService
                );

              const featureFlagsValue: any = mockFeatureFlagsValue({
                enableBrazilianClaims: true
              });

              const keywordFilterClauseBuilderService = createMockInstance(
                KeywordFilterClauseBuilderService
              );

              const aggregationContainerBuilderService =
                new AggregationContainerBuilderService(
                  parsedQueryTreeToElasticsearchQueriesService,
                  keywordFilterClauseBuilderService
                );

              const aggregations = [
                {
                  filterField: KeywordFilterAutocompleteFilterField.DIAGNOSES,
                  aggregationField: "DRG_diagnoses.codeAndDescription_eng"
                },
                {
                  filterField:
                    KeywordFilterAutocompleteFilterField.DRG_DIAGNOSES,
                  aggregationField: "DRG_diagnoses.codeAndDescription_eng"
                },
                {
                  filterField: KeywordFilterAutocompleteFilterField.PROCEDURES,
                  aggregationField: "DRG_procedures.codeAndDescription_eng"
                },
                {
                  filterField:
                    KeywordFilterAutocompleteFilterField.DRG_PROCEDURES,
                  aggregationField: "DRG_procedures.codeAndDescription_eng"
                },
                {
                  filterField: KeywordFilterAutocompleteFilterField.CCSR,
                  aggregationField: "ccsr.description_eng"
                },
                {
                  filterField: KeywordFilterAutocompleteFilterField.CCSR_PX,
                  aggregationField: "ccsr_px.description_eng"
                }
              ];

              for (const timeframe of [1, 2, 5]) {
                for (const aggregation of aggregations) {
                  const projectId = faker.datatype.string();
                  const projectFeatures = {
                    engagementsV2: faker.datatype.boolean()
                  };
                  const userId = faker.datatype.string();
                  const query = undefined;
                  const filterField = aggregation.filterField;
                  const filterValue = faker.random.alpha();
                  const suppliedFilters = generateFilters();
                  let path =
                    aggregation.filterField ==
                      KeywordFilterAutocompleteFilterField.DIAGNOSES ||
                    aggregation.filterField ==
                      KeywordFilterAutocompleteFilterField.DRG_DIAGNOSES
                      ? "DRG_diagnoses"
                      : "DRG_procedures";
                  path =
                    aggregation.filterField ===
                    KeywordFilterAutocompleteFilterField.CCSR
                      ? "ccsr"
                      : path;

                  path =
                    aggregation.filterField ===
                    KeywordFilterAutocompleteFilterField.CCSR_PX
                      ? "ccsr_px"
                      : path;

                  suppliedFilters.claims.timeFrame!.value = timeframe;

                  const expectedFilters: QueryDslQueryContainer[] = [
                    {
                      match_phrase: {
                        [`${aggregation.aggregationField}.autocomplete_search`]:
                          {
                            query: filterValue,
                            slop: 5
                          }
                      }
                    }
                  ];

                  if (
                    aggregation.filterField !==
                    KeywordFilterAutocompleteFilterField.DIAGNOSES
                  ) {
                    suppliedFilters.claims.diagnosesICD.values = [
                      faker.datatype.string()
                    ];

                    expectedFilters.push({
                      terms: {
                        "DRG_diagnoses.codeAndDescription_eng":
                          suppliedFilters.claims.diagnosesICD.values
                      }
                    });
                  }
                  if (
                    aggregation.filterField !==
                    KeywordFilterAutocompleteFilterField.DRG_DIAGNOSES
                  ) {
                    suppliedFilters.claims.diagnosesICD.values = [
                      faker.datatype.string()
                    ];

                    expectedFilters.push({
                      terms: {
                        "DRG_diagnoses.codeAndDescription_eng":
                          suppliedFilters.claims.diagnosesICD.values
                      }
                    });
                  }
                  if (
                    aggregation.filterField !==
                    KeywordFilterAutocompleteFilterField.PROCEDURES
                  ) {
                    suppliedFilters.claims.proceduresCPT.values = [
                      faker.datatype.string()
                    ];

                    expectedFilters.push({
                      terms: {
                        "DRG_procedures.codeAndDescription_eng":
                          suppliedFilters.claims.proceduresCPT.values
                      }
                    });
                  }
                  if (
                    aggregation.filterField !==
                    KeywordFilterAutocompleteFilterField.DRG_PROCEDURES
                  ) {
                    suppliedFilters.claims.proceduresCPT.values = [
                      faker.datatype.string()
                    ];

                    expectedFilters.push({
                      terms: {
                        "DRG_procedures.codeAndDescription_eng":
                          suppliedFilters.claims.proceduresCPT.values
                      }
                    });
                  }
                  if (
                    aggregation.filterField !==
                    KeywordFilterAutocompleteFilterField.CCSR
                  ) {
                    suppliedFilters.claims.ccsr!.values = [
                      faker.datatype.string()
                    ];

                    expectedFilters.push({
                      terms: {
                        "ccsr.description_eng":
                          suppliedFilters.claims.ccsr!.values
                      }
                    });
                  }

                  const filters: QueryDslQueryContainer[] = [
                    {
                      nested: {
                        path,
                        query: {
                          bool: {
                            filter: [
                              ...expectedFilters,
                              {
                                range: {
                                  [`${path}.internalCount_${timeframe}_year`]: {
                                    gte: 1
                                  }
                                }
                              }
                            ]
                          }
                        }
                      }
                    }
                  ];

                  const aggregationContainer =
                    await aggregationContainerBuilderService.buildAggregationContainer(
                      {
                        projectId,
                        projectFeatures,
                        userId,
                        query,
                        filterField,
                        filterValue,
                        suppliedFilters
                      },
                      LANGUAGE_DETECTOR_ENGLISH,
                      filters,
                      featureFlagsValue,
                      [],
                      undefined
                    );
                  const size = path === "ccsr" ? "icdSize" : "cptSize";

                  const expectedAggregationForCcsr = {
                    matching: {
                      terms: {
                        field: aggregation.aggregationField
                      },
                      aggs: {
                        icd_size: {
                          terms: {
                            field: `${path}.${size}`
                          }
                        }
                      }
                    }
                  };

                  const expectedAggregationForNonCcsr = {
                    matching: {
                      terms: {
                        field: `${aggregation.aggregationField}.keyword`
                      }
                    }
                  };
                  const isCcsr =
                    aggregation.filterField ==
                      KeywordFilterAutocompleteFilterField.CCSR ||
                    aggregation.filterField ==
                      KeywordFilterAutocompleteFilterField.CCSR_PX;
                  expect(aggregationContainer).toMatchObject(
                    expect.objectContaining({
                      nested: {
                        nested: {
                          path
                        },
                        aggs: {
                          filtered_matching: {
                            filter: {
                              bool: {
                                filter: [
                                  ...expectedFilters,
                                  {
                                    range: {
                                      [`${path}.internalCount_${timeframe}_year`]:
                                        {
                                          gte: 1
                                        }
                                    }
                                  }
                                ]
                              }
                            },
                            aggs: isCcsr
                              ? expectedAggregationForCcsr
                              : expectedAggregationForNonCcsr
                          }
                        }
                      }
                    })
                  );
                }
              }
            });
          });

          it("should NOT be used to filter nested claims aggregation when the filter value is of the same type that is being aggregated", async () => {
            const parsedQueryTreeToElasticsearchQueriesService =
              createMockInstance(ParsedQueryTreeToElasticsearchQueriesService);

            const featureFlagsValue: any = mockFeatureFlagsValue({
              enableBrazilianClaims: true
            });

            const keywordFilterClauseBuilderService = createMockInstance(
              KeywordFilterClauseBuilderService
            );

            const aggregationContainerBuilderService =
              new AggregationContainerBuilderService(
                parsedQueryTreeToElasticsearchQueriesService,
                keywordFilterClauseBuilderService
              );

            const aggregations = [
              {
                filterField: KeywordFilterAutocompleteFilterField.DIAGNOSES,
                aggregationField: "DRG_diagnoses.codeAndDescription_eng"
              },
              {
                filterField: KeywordFilterAutocompleteFilterField.DRG_DIAGNOSES,
                aggregationField: "DRG_diagnoses.codeAndDescription_eng"
              },
              {
                filterField: KeywordFilterAutocompleteFilterField.PROCEDURES,
                aggregationField: "DRG_procedures.codeAndDescription_eng"
              },
              {
                filterField:
                  KeywordFilterAutocompleteFilterField.DRG_PROCEDURES,
                aggregationField: "DRG_procedures.codeAndDescription_eng"
              },
              {
                filterField: KeywordFilterAutocompleteFilterField.CCSR,
                aggregationField: "ccsr.description_eng"
              },
              {
                filterField: KeywordFilterAutocompleteFilterField.CCSR_PX,
                aggregationField: "ccsr_px.description_eng"
              }
            ];

            for (const aggregation of aggregations) {
              const projectId = faker.datatype.string();
              const projectFeatures = {
                engagementsV2: faker.datatype.boolean()
              };
              const userId = faker.datatype.string();
              const query = undefined;
              const filterField = aggregation.filterField;
              const filterValue = faker.random.alpha();
              const suppliedFilters = generateFilters();
              let path =
                aggregation.filterField ==
                  KeywordFilterAutocompleteFilterField.DIAGNOSES ||
                aggregation.filterField ==
                  KeywordFilterAutocompleteFilterField.DRG_DIAGNOSES
                  ? "DRG_diagnoses"
                  : "DRG_procedures";
              path =
                aggregation.filterField ===
                KeywordFilterAutocompleteFilterField.CCSR
                  ? "ccsr"
                  : path;

              path =
                aggregation.filterField ===
                KeywordFilterAutocompleteFilterField.CCSR_PX
                  ? "ccsr_px"
                  : path;

              if (
                aggregation.filterField ===
                KeywordFilterAutocompleteFilterField.DIAGNOSES
              ) {
                suppliedFilters.claims.diagnosesICD.values = [
                  faker.datatype.string()
                ];
              }
              if (
                aggregation.filterField ===
                KeywordFilterAutocompleteFilterField.DRG_DIAGNOSES
              ) {
                suppliedFilters.claims.diagnosesICD.values = [
                  faker.datatype.string()
                ];
              }
              if (
                aggregation.filterField ===
                KeywordFilterAutocompleteFilterField.PROCEDURES
              ) {
                suppliedFilters.claims.proceduresCPT.values = [
                  faker.datatype.string()
                ];
              }
              if (
                aggregation.filterField ===
                KeywordFilterAutocompleteFilterField.DRG_PROCEDURES
              ) {
                suppliedFilters.claims.proceduresCPT.values = [
                  faker.datatype.string()
                ];
              }
              if (
                aggregation.filterField ===
                KeywordFilterAutocompleteFilterField.CCSR
              ) {
                suppliedFilters.claims.ccsr!.values = [faker.datatype.string()];
              }
              const filters = [
                {
                  nested: {
                    path,
                    query: {
                      bool: {
                        filter: [
                          {
                            match_phrase: {
                              [`${aggregation.aggregationField}.autocomplete_search`]:
                                {
                                  query: filterValue,
                                  slop: 5
                                }
                            }
                          }
                        ]
                      }
                    }
                  }
                }
              ];

              const aggregationContainer =
                await aggregationContainerBuilderService.buildAggregationContainer(
                  {
                    projectId,
                    projectFeatures,
                    userId,
                    query,
                    filterField,
                    filterValue,
                    suppliedFilters
                  },
                  LANGUAGE_DETECTOR_ENGLISH,
                  filters,
                  featureFlagsValue,
                  [],
                  undefined
                );
              const size = path === "ccsr" ? "icdSize" : "cptSize";
              const expectedAggregationForCcsr = {
                matching: {
                  terms: {
                    field: aggregation.aggregationField
                  },
                  aggs: {
                    icd_size: {
                      terms: {
                        field: `${path}.${size}`
                      }
                    }
                  }
                }
              };

              const expectedAggregationForNonCcsr = {
                matching: {
                  terms: {
                    field: `${aggregation.aggregationField}.keyword`
                  }
                }
              };
              const isCcsr =
                aggregation.filterField ==
                  KeywordFilterAutocompleteFilterField.CCSR ||
                aggregation.filterField ==
                  KeywordFilterAutocompleteFilterField.CCSR_PX;
              expect(aggregationContainer).toMatchObject(
                expect.objectContaining({
                  nested: {
                    nested: {
                      path
                    },
                    aggs: {
                      filtered_matching: {
                        filter: {
                          bool: {
                            filter: [
                              {
                                match_phrase: {
                                  [`${aggregation.aggregationField}.autocomplete_search`]:
                                    {
                                      query: filterValue,
                                      slop: 5
                                    }
                                }
                              }
                            ]
                          }
                        },
                        aggs: isCcsr
                          ? expectedAggregationForCcsr
                          : expectedAggregationForNonCcsr
                      }
                    }
                  }
                })
              );
            }
          });
        });
      });
    });

    describe("payments", () => {
      it("should not include 'include' regex when filterValue is an empty string", async () => {
        const filters = [
          {
            nested: {
              path: "payments",
              query: {
                bool: {
                  filter: []
                }
              }
            }
          }
        ];
        const parsedQueryTreeToElasticsearchQueriesService = createMockInstance(
          ParsedQueryTreeToElasticsearchQueriesService
        );

        const featureFlagsValue: any = mockFeatureFlagsValue();

        const keywordFilterClauseBuilderService = createMockInstance(
          KeywordFilterClauseBuilderService
        );

        const aggregationContainerBuilderService =
          new AggregationContainerBuilderService(
            parsedQueryTreeToElasticsearchQueriesService,
            keywordFilterClauseBuilderService
          );

        const aggregations = [
          {
            filterField:
              KeywordFilterAutocompleteFilterField.PAYMENTS_TYPE_OF_FUNDING,
            aggregationField: "payments.natureOfPayment"
          },
          {
            filterField:
              KeywordFilterAutocompleteFilterField.PAYMENTS_DRUG_OR_DEVICE,
            aggregationField: "payments.associatedDrugOrDevice"
          },
          {
            filterField: KeywordFilterAutocompleteFilterField.PAYMENTS_COMPANY,
            aggregationField: "payments.payerCompany"
          },
          {
            filterField: KeywordFilterAutocompleteFilterField.PAYMENTS_CATEGORY,
            aggregationField: "payments.category"
          }
        ];

        for (const aggregation of aggregations) {
          const projectId = faker.datatype.string();
          const projectFeatures = {
            engagementsV2: faker.datatype.boolean()
          };
          const userId = faker.datatype.string();
          const query = undefined;
          const filterField = aggregation.filterField;
          const filterValue = "";

          const suppliedFilters = generateFilters();

          const aggregationContainer =
            await aggregationContainerBuilderService.buildAggregationContainer(
              {
                projectId,
                projectFeatures,
                userId,
                query,
                filterField,
                filterValue,
                suppliedFilters
              },
              LANGUAGE_DETECTOR_ENGLISH,
              filters,
              featureFlagsValue,
              [],
              undefined
            );

          expect(aggregationContainer).toMatchObject(
            expect.objectContaining({
              nested: {
                nested: {
                  path: "payments"
                },
                aggs: {
                  filtered_matching: {
                    filter: {
                      bool: {
                        filter: []
                      }
                    },
                    aggs: {
                      matching: {
                        terms: {
                          field: aggregation.aggregationField,
                          exclude: "",
                          order: SORT_BY_MATCHING_DOC_COUNT
                        },
                        aggs: {
                          matching: {
                            reverse_nested: {}
                          }
                        }
                      }
                    }
                  }
                }
              }
            })
          );
        }
      });

      it("should use reverse nested aggregation for payments filters when min amount is NOT supplied", async () => {
        const parsedQueryTreeToElasticsearchQueriesService = createMockInstance(
          ParsedQueryTreeToElasticsearchQueriesService
        );

        const featureFlagsValue: any = mockFeatureFlagsValue();

        const keywordFilterClauseBuilderService = createMockInstance(
          KeywordFilterClauseBuilderService
        );

        const aggregationContainerBuilderService =
          new AggregationContainerBuilderService(
            parsedQueryTreeToElasticsearchQueriesService,
            keywordFilterClauseBuilderService
          );

        const aggregations = [
          {
            filterField:
              KeywordFilterAutocompleteFilterField.PAYMENTS_TYPE_OF_FUNDING,
            aggregationField: "payments.natureOfPayment"
          },
          {
            filterField:
              KeywordFilterAutocompleteFilterField.PAYMENTS_DRUG_OR_DEVICE,
            aggregationField: "payments.associatedDrugOrDevice"
          },
          {
            filterField: KeywordFilterAutocompleteFilterField.PAYMENTS_COMPANY,
            aggregationField: "payments.payerCompany"
          },
          {
            filterField: KeywordFilterAutocompleteFilterField.PAYMENTS_CATEGORY,
            aggregationField: "payments.category"
          }
        ];

        for (const aggregation of aggregations) {
          const projectId = faker.datatype.string();
          const projectFeatures = {
            engagementsV2: faker.datatype.boolean()
          };
          const userId = faker.datatype.string();
          const query = undefined;
          const filterField = aggregation.filterField;
          const filterValue = faker.random.alpha();

          const suppliedFilters = generateFilters();

          const filters = [
            {
              nested: {
                path: "payments",
                query: {
                  bool: {
                    filter: [
                      {
                        match_phrase: {
                          [`${aggregation.aggregationField}.autocomplete_search`]:
                            {
                              query: filterValue,
                              slop: 5
                            }
                        }
                      }
                    ]
                  }
                }
              }
            }
          ];

          const aggregationContainer =
            await aggregationContainerBuilderService.buildAggregationContainer(
              {
                projectId,
                projectFeatures,
                userId,
                query,
                filterField,
                filterValue,
                suppliedFilters
              },
              LANGUAGE_DETECTOR_ENGLISH,
              filters,
              featureFlagsValue,
              [],
              undefined
            );

          expect(aggregationContainer).toMatchObject(
            expect.objectContaining({
              nested: {
                nested: {
                  path: "payments"
                },
                aggs: {
                  filtered_matching: {
                    filter: {
                      bool: {
                        filter: [
                          {
                            match_phrase: {
                              [`${aggregation.aggregationField}.autocomplete_search`]:
                                {
                                  query: filterValue,
                                  slop: 5
                                }
                            }
                          }
                        ]
                      }
                    },
                    aggs: {
                      matching: {
                        terms: {
                          field: `${aggregation.aggregationField}`,
                          order: SORT_BY_MATCHING_DOC_COUNT
                        },
                        aggs: {
                          matching: {
                            reverse_nested: {}
                          }
                        }
                      }
                    }
                  }
                }
              }
            })
          );
        }
      });

      it("should use scripted metric aggregation for payments filters when min amount is supplied", async () => {
        const minAmount = 9000;

        const parsedQueryTreeToElasticsearchQueriesService = createMockInstance(
          ParsedQueryTreeToElasticsearchQueriesService
        );

        const featureFlagsValue: any = mockFeatureFlagsValue();

        const keywordFilterClauseBuilderService = createMockInstance(
          KeywordFilterClauseBuilderService
        );

        const aggregationContainerBuilderService =
          new AggregationContainerBuilderService(
            parsedQueryTreeToElasticsearchQueriesService,
            keywordFilterClauseBuilderService
          );

        const aggregations = [
          {
            filterField:
              KeywordFilterAutocompleteFilterField.PAYMENTS_TYPE_OF_FUNDING,
            aggregationField: "payments.natureOfPayment"
          },
          {
            filterField:
              KeywordFilterAutocompleteFilterField.PAYMENTS_DRUG_OR_DEVICE,
            aggregationField: "payments.associatedDrugOrDevice"
          },
          {
            filterField: KeywordFilterAutocompleteFilterField.PAYMENTS_COMPANY,
            aggregationField: "payments.payerCompany"
          },
          {
            filterField: KeywordFilterAutocompleteFilterField.PAYMENTS_CATEGORY,
            aggregationField: "payments.category"
          }
        ];

        for (const aggregation of aggregations) {
          const projectId = faker.datatype.string();
          const projectFeatures = {
            engagementsV2: faker.datatype.boolean()
          };
          const userId = faker.datatype.string();
          const query = undefined;
          const filterField = aggregation.filterField;
          const filterValue = faker.random.alpha();
          const suppliedFilters = generateFilters();
          suppliedFilters.payments.minAmount.value = minAmount;

          const filters: QueryDslQueryContainer[] = [
            {
              function_score: {
                query: {
                  nested: {
                    path: "payments",
                    score_mode: "sum",
                    query: {
                      bool: {
                        must: [
                          {
                            function_score: {
                              functions: [
                                {
                                  field_value_factor: {
                                    field: "payments.amount",
                                    missing: 0
                                  }
                                }
                              ]
                            }
                          }
                        ],
                        filter: [
                          {
                            match_phrase: {
                              [`${aggregation.aggregationField}.autocomplete_search`]:
                                {
                                  query: filterValue,
                                  slop: 5
                                }
                            }
                          }
                        ]
                      }
                    }
                  }
                },
                min_score: minAmount
              }
            }
          ];

          const aggregationContainer =
            await aggregationContainerBuilderService.buildAggregationContainer(
              {
                projectId,
                projectFeatures,
                userId,
                query,
                filterField,
                filterValue,
                suppliedFilters
              },
              LANGUAGE_DETECTOR_ENGLISH,
              filters,
              featureFlagsValue,
              [],
              undefined
            );

          expect(aggregationContainer).toMatchObject(
            expect.objectContaining({
              nested: {
                nested: {
                  path: "payments"
                },
                aggs: {
                  filtered_matching: {
                    filter: {
                      bool: {
                        filter: [
                          {
                            match_phrase: {
                              [`${aggregation.aggregationField}.autocomplete_search`]:
                                {
                                  query: filterValue,
                                  slop: 5
                                }
                            }
                          }
                        ]
                      }
                    },
                    aggs: {
                      matching: {
                        terms: {
                          field: `${aggregation.aggregationField}`
                        },
                        aggs: {
                          matching: {
                            scripted_metric: {
                              params: {
                                minAmount
                              },
                              init_script: expect.any(String),
                              map_script: expect.any(String),
                              combine_script: expect.any(String),
                              reduce_script: expect.any(String)
                            }
                          }
                        }
                      }
                    }
                  }
                }
              }
            })
          );
        }
      });

      it("should use scripted metric aggregation for payments filters when max amount is supplied", async () => {
        const maxAmount = 9000;

        const parsedQueryTreeToElasticsearchQueriesService = createMockInstance(
          ParsedQueryTreeToElasticsearchQueriesService
        );

        const featureFlagsValue: any = mockFeatureFlagsValue();

        const keywordFilterClauseBuilderService = createMockInstance(
          KeywordFilterClauseBuilderService
        );

        const aggregationContainerBuilderService =
          new AggregationContainerBuilderService(
            parsedQueryTreeToElasticsearchQueriesService,
            keywordFilterClauseBuilderService
          );

        const aggregations = [
          {
            filterField:
              KeywordFilterAutocompleteFilterField.PAYMENTS_TYPE_OF_FUNDING,
            aggregationField: "payments.natureOfPayment"
          },
          {
            filterField:
              KeywordFilterAutocompleteFilterField.PAYMENTS_DRUG_OR_DEVICE,
            aggregationField: "payments.associatedDrugOrDevice"
          },
          {
            filterField: KeywordFilterAutocompleteFilterField.PAYMENTS_COMPANY,
            aggregationField: "payments.payerCompany"
          },
          {
            filterField: KeywordFilterAutocompleteFilterField.PAYMENTS_CATEGORY,
            aggregationField: "payments.category"
          }
        ];

        for (const aggregation of aggregations) {
          const projectId = faker.datatype.string();
          const projectFeatures = {
            engagementsV2: faker.datatype.boolean()
          };
          const userId = faker.datatype.string();
          const query = undefined;
          const filterField = aggregation.filterField;
          const filterValue = faker.random.alpha();
          const suppliedFilters = generateFilters();
          suppliedFilters.payments.maxAmount!.value = maxAmount;

          const filters: QueryDslQueryContainer[] = [
            {
              function_score: {
                query: {
                  nested: {
                    path: "payments",
                    score_mode: "sum",
                    query: {
                      bool: {
                        must: [
                          {
                            function_score: {
                              functions: [
                                {
                                  field_value_factor: {
                                    field: "payments.amount",
                                    missing: 0
                                  }
                                }
                              ]
                            }
                          }
                        ],
                        filter: [
                          {
                            match_phrase: {
                              [`${aggregation.aggregationField}.autocomplete_search`]:
                                {
                                  query: filterValue,
                                  slop: 5
                                }
                            }
                          }
                        ]
                      }
                    }
                  }
                },
                min_score: maxAmount
              }
            }
          ];

          const aggregationContainer =
            await aggregationContainerBuilderService.buildAggregationContainer(
              {
                projectId,
                projectFeatures,
                userId,
                query,
                filterField,
                filterValue,
                suppliedFilters
              },
              LANGUAGE_DETECTOR_ENGLISH,
              filters,
              featureFlagsValue,
              [],
              undefined
            );

          expect(aggregationContainer).toMatchObject(
            expect.objectContaining({
              nested: {
                nested: {
                  path: "payments"
                },
                aggs: {
                  filtered_matching: {
                    filter: {
                      bool: {
                        filter: [
                          {
                            match_phrase: {
                              [`${aggregation.aggregationField}.autocomplete_search`]:
                                {
                                  query: filterValue,
                                  slop: 5
                                }
                            }
                          }
                        ]
                      }
                    },
                    aggs: {
                      matching: {
                        terms: {
                          field: `${aggregation.aggregationField}`
                        },
                        aggs: {
                          matching: {
                            scripted_metric: {
                              params: {
                                maxAmount
                              },
                              init_script: expect.any(String),
                              map_script: expect.any(String),
                              combine_script: expect.any(String),
                              reduce_script: expect.any(String)
                            }
                          }
                        }
                      }
                    }
                  }
                }
              }
            })
          );
        }
      });

      it("should use scripted metric aggregation for payments filters when min amount is supplied", async () => {
        const minAmount = faker.datatype.number();
        const maxAmount = faker.datatype.number();

        const parsedQueryTreeToElasticsearchQueriesService = createMockInstance(
          ParsedQueryTreeToElasticsearchQueriesService
        );

        const featureFlagsValue: any = mockFeatureFlagsValue();

        const keywordFilterClauseBuilderService = createMockInstance(
          KeywordFilterClauseBuilderService
        );

        const aggregationContainerBuilderService =
          new AggregationContainerBuilderService(
            parsedQueryTreeToElasticsearchQueriesService,
            keywordFilterClauseBuilderService
          );

        const aggregations = [
          {
            filterField:
              KeywordFilterAutocompleteFilterField.PAYMENTS_TYPE_OF_FUNDING,
            aggregationField: "payments.natureOfPayment"
          },
          {
            filterField:
              KeywordFilterAutocompleteFilterField.PAYMENTS_DRUG_OR_DEVICE,
            aggregationField: "payments.associatedDrugOrDevice"
          },
          {
            filterField: KeywordFilterAutocompleteFilterField.PAYMENTS_COMPANY,
            aggregationField: "payments.payerCompany"
          },
          {
            filterField: KeywordFilterAutocompleteFilterField.PAYMENTS_CATEGORY,
            aggregationField: "payments.category"
          }
        ];

        for (const aggregation of aggregations) {
          const projectId = faker.datatype.string();
          const projectFeatures = {
            engagementsV2: faker.datatype.boolean()
          };
          const userId = faker.datatype.string();
          const query = undefined;
          const filterField = aggregation.filterField;
          const filterValue = faker.random.alpha();
          const suppliedFilters = generateFilters();
          suppliedFilters.payments.minAmount.value = minAmount;
          suppliedFilters.payments.maxAmount!.value = maxAmount;

          const filters: QueryDslQueryContainer[] = [
            {
              function_score: {
                query: {
                  nested: {
                    path: "payments",
                    score_mode: "sum",
                    query: {
                      bool: {
                        must: [
                          {
                            function_score: {
                              functions: [
                                {
                                  field_value_factor: {
                                    field: "payments.amount",
                                    missing: 0
                                  }
                                }
                              ]
                            }
                          }
                        ],
                        filter: [
                          {
                            match_phrase: {
                              [`${aggregation.aggregationField}.autocomplete_search`]:
                                {
                                  query: filterValue,
                                  slop: 5
                                }
                            }
                          }
                        ]
                      }
                    }
                  }
                },
                min_score: minAmount
              }
            }
          ];

          const aggregationContainer =
            await aggregationContainerBuilderService.buildAggregationContainer(
              {
                projectId,
                projectFeatures,
                userId,
                query,
                filterField,
                filterValue,
                suppliedFilters
              },
              LANGUAGE_DETECTOR_ENGLISH,
              filters,
              featureFlagsValue,
              [],
              undefined
            );

          expect(aggregationContainer).toMatchObject(
            expect.objectContaining({
              nested: {
                nested: {
                  path: "payments"
                },
                aggs: {
                  filtered_matching: {
                    filter: {
                      bool: {
                        filter: [
                          {
                            match_phrase: {
                              [`${aggregation.aggregationField}.autocomplete_search`]:
                                {
                                  query: filterValue,
                                  slop: 5
                                }
                            }
                          }
                        ]
                      }
                    },
                    aggs: {
                      matching: {
                        terms: {
                          field: `${aggregation.aggregationField}`
                        },
                        aggs: {
                          matching: {
                            scripted_metric: {
                              params: {
                                minAmount,
                                maxAmount
                              },
                              init_script: expect.any(String),
                              map_script: expect.any(String),
                              combine_script: expect.any(String),
                              reduce_script: expect.any(String)
                            }
                          }
                        }
                      }
                    }
                  }
                }
              }
            })
          );
        }
      });

      it("should include global search term parsed query if supplied and no other payments filters were supplied", async () => {
        const parsedQueryTreeToElasticsearchQueriesService = createMockInstance(
          ParsedQueryTreeToElasticsearchQueriesService
        );

        const pathSpecificQueries: Record<NestedPath, QueryDslQueryContainer> =
          {
            trials: generateMockElasticsearchTermQuery(),
            publications: generateMockElasticsearchTermQuery(),
            congress: generateMockElasticsearchTermQuery(),
            payments: generateMockElasticsearchTermQuery(),
            DRG_diagnoses: generateMockElasticsearchTermQuery(),
            DRG_procedures: generateMockElasticsearchTermQuery(),
            prescriptions: generateMockElasticsearchTermQuery(),
            ccsr: generateMockElasticsearchTermQuery(),
            ccsr_px: generateMockElasticsearchTermQuery()
          };

        const mockParseTreeToQueries =
          mockParseTreeToElasticsearchQueries(pathSpecificQueries);

        parsedQueryTreeToElasticsearchQueriesService.parse.mockImplementation(
          mockParseTreeToQueries
        );

        const featureFlagsValue: any = mockFeatureFlagsValue({
          enableQueryContextualPaymentsFiltering: true
        });

        const keywordFilterClauseBuilderService = createMockInstance(
          KeywordFilterClauseBuilderService
        );

        const aggregationContainerBuilderService =
          new AggregationContainerBuilderService(
            parsedQueryTreeToElasticsearchQueriesService,
            keywordFilterClauseBuilderService
          );

        const aggregations = [
          {
            filterField:
              KeywordFilterAutocompleteFilterField.PAYMENTS_TYPE_OF_FUNDING,
            aggregationField: "payments.natureOfPayment"
          },
          {
            filterField:
              KeywordFilterAutocompleteFilterField.PAYMENTS_DRUG_OR_DEVICE,
            aggregationField: "payments.associatedDrugOrDevice"
          },
          {
            filterField: KeywordFilterAutocompleteFilterField.PAYMENTS_COMPANY,
            aggregationField: "payments.payerCompany"
          },
          {
            filterField: KeywordFilterAutocompleteFilterField.PAYMENTS_CATEGORY,
            aggregationField: "payments.category"
          }
        ];

        for (const aggregation of aggregations) {
          const projectId = faker.datatype.string();
          const projectFeatures = {
            engagementsV2: faker.datatype.boolean()
          };
          const userId = faker.datatype.string();
          const query = faker.datatype.string();
          const filterField = aggregation.filterField;
          const filterValue = faker.random.alpha();

          const suppliedFilters = generateFilters();

          const queryWord1 = faker.datatype.string();
          const queryWord2 = faker.datatype.string();
          const queryWord3 = faker.datatype.string();

          const augmentedQuery = `${queryWord1} | ${queryWord2} | ${queryWord3}`;

          const aggregationContainer =
            await aggregationContainerBuilderService.buildAggregationContainer(
              {
                projectId,
                projectFeatures,
                userId,
                query,
                filterField,
                filterValue,
                suppliedFilters
              },
              LANGUAGE_DETECTOR_ENGLISH,
              [],
              featureFlagsValue,
              [],
              augmentedQuery
            );

          expect(aggregationContainer).toMatchObject(
            expect.objectContaining({
              nested: {
                nested: {
                  path: "payments"
                },
                aggs: {
                  filtered_matching: {
                    filter: {
                      bool: {
                        filter: [pathSpecificQueries.payments]
                      }
                    },
                    aggs: {
                      matching: {
                        terms: {
                          field: `${aggregation.aggregationField}`,
                          order: SORT_BY_MATCHING_DOC_COUNT
                        },
                        aggs: {
                          matching: {
                            reverse_nested: {}
                          }
                        }
                      }
                    }
                  }
                }
              }
            })
          );

          expect(
            parsedQueryTreeToElasticsearchQueriesService.parse
          ).toHaveBeenCalledWith(augmentedQuery, [
            "payments.associatedDrugOrDevice",
            "payments.payerCompany"
          ]);
        }
      });

      it("should NOT include global search term parsed query when the enable-query-contextual-payments-filtering feature flag is disabled", async () => {
        const parsedQueryTreeToElasticsearchQueriesService = createMockInstance(
          ParsedQueryTreeToElasticsearchQueriesService
        );

        const pathSpecificQueries: Record<NestedPath, QueryDslQueryContainer> =
          {
            trials: generateMockElasticsearchTermQuery(),
            publications: generateMockElasticsearchTermQuery(),
            congress: generateMockElasticsearchTermQuery(),
            payments: generateMockElasticsearchTermQuery(),
            DRG_diagnoses: generateMockElasticsearchTermQuery(),
            DRG_procedures: generateMockElasticsearchTermQuery(),
            prescriptions: generateMockElasticsearchTermQuery(),
            ccsr: generateMockElasticsearchTermQuery(),
            ccsr_px: generateMockElasticsearchTermQuery()
          };

        const mockParseTreeToQueries =
          mockParseTreeToElasticsearchQueries(pathSpecificQueries);

        parsedQueryTreeToElasticsearchQueriesService.parse.mockImplementation(
          mockParseTreeToQueries
        );

        const featureFlagsValue: any = mockFeatureFlagsValue();

        const keywordFilterClauseBuilderService = createMockInstance(
          KeywordFilterClauseBuilderService
        );

        const aggregationContainerBuilderService =
          new AggregationContainerBuilderService(
            parsedQueryTreeToElasticsearchQueriesService,
            keywordFilterClauseBuilderService
          );

        const aggregations = [
          {
            filterField:
              KeywordFilterAutocompleteFilterField.PAYMENTS_TYPE_OF_FUNDING,
            aggregationField: "payments.natureOfPayment"
          },
          {
            filterField:
              KeywordFilterAutocompleteFilterField.PAYMENTS_DRUG_OR_DEVICE,
            aggregationField: "payments.associatedDrugOrDevice"
          },
          {
            filterField: KeywordFilterAutocompleteFilterField.PAYMENTS_COMPANY,
            aggregationField: "payments.payerCompany"
          },
          {
            filterField: KeywordFilterAutocompleteFilterField.PAYMENTS_CATEGORY,
            aggregationField: "payments.category"
          }
        ];

        for (const aggregation of aggregations) {
          const projectId = faker.datatype.string();
          const projectFeatures = {
            engagementsV2: faker.datatype.boolean()
          };
          const userId = faker.datatype.string();
          const query = faker.datatype.string();
          const filterField = aggregation.filterField;
          const filterValue = faker.random.alpha();

          const suppliedFilters = generateFilters();

          const queryWord1 = faker.datatype.string();
          const queryWord2 = faker.datatype.string();
          const queryWord3 = faker.datatype.string();

          const augmentedQuery = `${queryWord1} OR ${queryWord2} OR ${queryWord3}`;

          const aggregationContainer =
            await aggregationContainerBuilderService.buildAggregationContainer(
              {
                projectId,
                projectFeatures,
                userId,
                query,
                filterField,
                filterValue,
                suppliedFilters
              },
              LANGUAGE_DETECTOR_ENGLISH,
              [],
              featureFlagsValue,
              [],
              augmentedQuery
            );

          expect(aggregationContainer).toMatchObject(
            expect.objectContaining({
              nested: {
                nested: {
                  path: "payments"
                },
                aggs: {
                  filtered_matching: {
                    filter: {
                      bool: {
                        filter: []
                      }
                    },
                    aggs: {
                      matching: {
                        terms: {
                          field: `${aggregation.aggregationField}`,
                          order: SORT_BY_MATCHING_DOC_COUNT
                        },
                        aggs: {
                          matching: {
                            reverse_nested: {}
                          }
                        }
                      }
                    }
                  }
                }
              }
            })
          );
        }
      });

      describe("supplied payments filter values", () => {
        it("should be used to filter nested payments aggregation", async () => {
          const parsedQueryTreeToElasticsearchQueriesService =
            createMockInstance(ParsedQueryTreeToElasticsearchQueriesService);

          const featureFlagsValue: any = mockFeatureFlagsValue({
            enableQueryContextualPaymentsFiltering: true
          });

          const keywordFilterClauseBuilderService = createMockInstance(
            KeywordFilterClauseBuilderService
          );

          const aggregationContainerBuilderService =
            new AggregationContainerBuilderService(
              parsedQueryTreeToElasticsearchQueriesService,
              keywordFilterClauseBuilderService
            );

          const aggregations = [
            {
              filterField:
                KeywordFilterAutocompleteFilterField.PAYMENTS_TYPE_OF_FUNDING,
              aggregationField: "payments.natureOfPayment"
            },
            {
              filterField:
                KeywordFilterAutocompleteFilterField.PAYMENTS_DRUG_OR_DEVICE,
              aggregationField: "payments.associatedDrugOrDevice"
            },
            {
              filterField:
                KeywordFilterAutocompleteFilterField.PAYMENTS_COMPANY,
              aggregationField: "payments.payerCompany"
            },
            {
              filterField:
                KeywordFilterAutocompleteFilterField.PAYMENTS_CATEGORY,
              aggregationField: "payments.category"
            }
          ];

          for (const aggregation of aggregations) {
            const projectId = faker.datatype.string();
            const projectFeatures = {
              engagementsV2: faker.datatype.boolean()
            };
            const userId = faker.datatype.string();
            const query = undefined;
            const filterField = aggregation.filterField;
            const filterValue = faker.random.alpha();
            const suppliedFilters = generateFilters();
            const expectedFilters: QueryDslQueryContainer[] = [
              {
                match_phrase: {
                  [`${aggregation.aggregationField}.autocomplete_search`]: {
                    query: filterValue,
                    slop: 5
                  }
                }
              }
            ];

            if (
              aggregation.filterField !==
              KeywordFilterAutocompleteFilterField.PAYMENTS_DRUG_OR_DEVICE
            ) {
              suppliedFilters.payments.drugOrDevice.values = [
                faker.datatype.string()
              ];

              expectedFilters.push({
                terms: {
                  "payments.associatedDrugOrDevice":
                    suppliedFilters.payments.drugOrDevice.values
                }
              });
            }
            if (
              aggregation.filterField !==
              KeywordFilterAutocompleteFilterField.PAYMENTS_COMPANY
            ) {
              suppliedFilters.payments.company.values = [faker.company.name()];

              expectedFilters.push({
                terms: {
                  "payments.payerCompany":
                    suppliedFilters.payments.company.values
                }
              });
            }
            if (
              aggregation.filterField ===
              KeywordFilterAutocompleteFilterField.PAYMENTS_TYPE_OF_FUNDING
            ) {
              suppliedFilters.payments.fundingType.values = [
                faker.datatype.string()
              ];

              expectedFilters.push({
                terms: {
                  "payments.associatedDrugOrDevice":
                    suppliedFilters.payments.drugOrDevice.values
                }
              });
            }
            if (
              aggregation.filterField ===
              KeywordFilterAutocompleteFilterField.PAYMENTS_CATEGORY
            ) {
              suppliedFilters.payments.category!.values = [
                faker.datatype.string()
              ];

              expectedFilters.push({
                terms: {
                  "payments.category": suppliedFilters.payments.category!.values
                }
              });
            }

            const filters = [
              {
                nested: {
                  path: "payments",
                  query: {
                    bool: {
                      filter: expectedFilters
                    }
                  }
                }
              }
            ];

            const aggregationContainer =
              await aggregationContainerBuilderService.buildAggregationContainer(
                {
                  projectId,
                  projectFeatures,
                  userId,
                  query,
                  filterField,
                  filterValue,
                  suppliedFilters
                },
                LANGUAGE_DETECTOR_ENGLISH,
                filters,
                featureFlagsValue,
                [],
                undefined
              );

            expect(aggregationContainer).toMatchObject(
              expect.objectContaining({
                nested: {
                  nested: {
                    path: "payments"
                  },
                  aggs: {
                    filtered_matching: {
                      filter: {
                        bool: {
                          filter: expect.arrayContaining(expectedFilters)
                        }
                      },
                      aggs: {
                        matching: {
                          terms: {
                            field: `${aggregation.aggregationField}`,
                            order: SORT_BY_MATCHING_DOC_COUNT
                          },
                          aggs: {
                            matching: {
                              reverse_nested: {}
                            }
                          }
                        }
                      }
                    }
                  }
                }
              })
            );
          }
        });

        it("should NOT be used to filter nested payments aggregation when the filter value is of the same type that is being aggregated", async () => {
          const parsedQueryTreeToElasticsearchQueriesService =
            createMockInstance(ParsedQueryTreeToElasticsearchQueriesService);

          const featureFlagsValue: any = mockFeatureFlagsValue({
            enableQueryContextualPaymentsFiltering: true
          });

          const keywordFilterClauseBuilderService = createMockInstance(
            KeywordFilterClauseBuilderService
          );

          const aggregationContainerBuilderService =
            new AggregationContainerBuilderService(
              parsedQueryTreeToElasticsearchQueriesService,
              keywordFilterClauseBuilderService
            );

          const aggregations = [
            {
              filterField:
                KeywordFilterAutocompleteFilterField.PAYMENTS_TYPE_OF_FUNDING,
              aggregationField: "payments.natureOfPayment"
            },
            {
              filterField:
                KeywordFilterAutocompleteFilterField.PAYMENTS_DRUG_OR_DEVICE,
              aggregationField: "payments.associatedDrugOrDevice"
            },
            {
              filterField:
                KeywordFilterAutocompleteFilterField.PAYMENTS_COMPANY,
              aggregationField: "payments.payerCompany"
            },
            {
              filterField:
                KeywordFilterAutocompleteFilterField.PAYMENTS_CATEGORY,
              aggregationField: "payments.category"
            }
          ];

          for (const aggregation of aggregations) {
            const projectId = faker.datatype.string();
            const projectFeatures = {
              engagementsV2: faker.datatype.boolean()
            };
            const userId = faker.datatype.string();
            const query = undefined;
            const filterField = aggregation.filterField;
            const filterValue = faker.random.alpha();
            const suppliedFilters = generateFilters();

            if (
              aggregation.filterField ===
              KeywordFilterAutocompleteFilterField.PAYMENTS_DRUG_OR_DEVICE
            ) {
              suppliedFilters.payments.drugOrDevice.values = [
                faker.datatype.string()
              ];
            } else if (
              aggregation.filterField ===
              KeywordFilterAutocompleteFilterField.PAYMENTS_COMPANY
            ) {
              suppliedFilters.payments.company.values = [faker.company.name()];
            } else if (
              aggregation.filterField ===
              KeywordFilterAutocompleteFilterField.PAYMENTS_TYPE_OF_FUNDING
            ) {
              suppliedFilters.payments.fundingType.values = [
                faker.datatype.string()
              ];
            } else if (
              aggregation.filterField ===
              KeywordFilterAutocompleteFilterField.PAYMENTS_CATEGORY
            ) {
              suppliedFilters.payments.category!.values = [
                faker.datatype.string()
              ];
            }

            const filters = [
              {
                nested: {
                  path: "payments",
                  query: {
                    bool: {
                      filter: [
                        {
                          match_phrase: {
                            [`${aggregation.aggregationField}.autocomplete_search`]:
                              {
                                query: filterValue,
                                slop: 5
                              }
                          }
                        }
                      ]
                    }
                  }
                }
              }
            ];
            const aggregationContainer =
              await aggregationContainerBuilderService.buildAggregationContainer(
                {
                  projectId,
                  projectFeatures,
                  userId,
                  query,
                  filterField,
                  filterValue,
                  suppliedFilters
                },
                LANGUAGE_DETECTOR_ENGLISH,
                filters,
                featureFlagsValue,
                [],
                undefined
              );

            expect(aggregationContainer).toMatchObject(
              expect.objectContaining({
                nested: {
                  nested: {
                    path: "payments"
                  },
                  aggs: {
                    filtered_matching: {
                      filter: {
                        bool: {
                          filter: [
                            {
                              match_phrase: {
                                [`${aggregation.aggregationField}.autocomplete_search`]:
                                  {
                                    query: filterValue,
                                    slop: 5
                                  }
                              }
                            }
                          ]
                        }
                      },
                      aggs: {
                        matching: {
                          terms: {
                            field: `${aggregation.aggregationField}`,
                            order: SORT_BY_MATCHING_DOC_COUNT
                          },
                          aggs: {
                            matching: {
                              reverse_nested: {}
                            }
                          }
                        }
                      }
                    }
                  }
                }
              })
            );
          }
        });
      });
    });

    describe("congress", () => {
      it("should not include 'include' regex when filterValue is an empty string", async () => {
        const filters = [
          {
            nested: {
              path: "congress",
              query: {
                bool: {
                  filter: []
                }
              }
            }
          }
        ];
        const parsedQueryTreeToElasticsearchQueriesService = createMockInstance(
          ParsedQueryTreeToElasticsearchQueriesService
        );

        const featureFlagsValue: any = mockFeatureFlagsValue();

        const keywordFilterClauseBuilderService = createMockInstance(
          KeywordFilterClauseBuilderService
        );

        const aggregationContainerBuilderService =
          new AggregationContainerBuilderService(
            parsedQueryTreeToElasticsearchQueriesService,
            keywordFilterClauseBuilderService
          );

        const aggregations = [
          {
            filterField: KeywordFilterAutocompleteFilterField.CONGRESS_NAME,
            aggregationField: "congress.name_eng"
          },
          {
            filterField:
              KeywordFilterAutocompleteFilterField.CONGRESS_ORGANIZER,
            aggregationField: "congress.organizer_eng"
          }
        ];

        for (const aggregation of aggregations) {
          const projectId = faker.datatype.string();
          const projectFeatures = {
            engagementsV2: faker.datatype.boolean()
          };
          const userId = faker.datatype.string();
          const query = undefined;
          const filterField = aggregation.filterField;
          const filterValue = "";

          const suppliedFilters = generateFilters();

          const aggregationContainer =
            await aggregationContainerBuilderService.buildAggregationContainer(
              {
                projectId,
                projectFeatures,
                userId,
                query,
                filterField,
                filterValue,
                suppliedFilters
              },
              LANGUAGE_DETECTOR_ENGLISH,
              filters,
              featureFlagsValue,
              [],
              undefined
            );

          expect(aggregationContainer).toMatchObject(
            expect.objectContaining({
              nested: {
                nested: {
                  path: "congress"
                },
                aggs: {
                  filtered_matching: {
                    filter: {
                      bool: {
                        filter: []
                      }
                    },
                    aggs: {
                      matching: {
                        terms: {
                          field: aggregation.aggregationField,
                          exclude: "",
                          order: SORT_BY_MATCHING_DOC_COUNT
                        },
                        aggs: {
                          by_congressId: {
                            terms: {
                              field: "congress.id",
                              size: 1
                            }
                          },
                          matching: {
                            reverse_nested: {}
                          }
                        }
                      }
                    }
                  }
                }
              }
            })
          );
        }
      });

      it("should use reverse nested aggregation for congress filters when min count is NOT supplied", async () => {
        const parsedQueryTreeToElasticsearchQueriesService = createMockInstance(
          ParsedQueryTreeToElasticsearchQueriesService
        );

        const featureFlagsValue: any = mockFeatureFlagsValue();

        const keywordFilterClauseBuilderService = createMockInstance(
          KeywordFilterClauseBuilderService
        );

        const aggregationContainerBuilderService =
          new AggregationContainerBuilderService(
            parsedQueryTreeToElasticsearchQueriesService,
            keywordFilterClauseBuilderService
          );

        const aggregations = [
          {
            filterField: KeywordFilterAutocompleteFilterField.CONGRESS_NAME,
            aggregationField: "congress.name_eng"
          },
          {
            filterField:
              KeywordFilterAutocompleteFilterField.CONGRESS_ORGANIZER,
            aggregationField: "congress.organizer_eng"
          }
        ];

        for (const aggregation of aggregations) {
          const projectId = faker.datatype.string();
          const projectFeatures = {
            engagementsV2: faker.datatype.boolean()
          };
          const userId = faker.datatype.string();
          const query = undefined;
          const filterField = aggregation.filterField;
          const filterValue = faker.random.alpha();

          const suppliedFilters = generateFilters();

          const filters = [
            {
              nested: {
                path: "congress",
                query: {
                  bool: {
                    filter: [
                      {
                        match_phrase: {
                          [`${aggregation.aggregationField}.autocomplete_search`]:
                            {
                              query: filterValue,
                              slop: 5
                            }
                        }
                      }
                    ]
                  }
                }
              }
            }
          ];
          const aggregationContainer =
            await aggregationContainerBuilderService.buildAggregationContainer(
              {
                projectId,
                projectFeatures,
                userId,
                query,
                filterField,
                filterValue,
                suppliedFilters
              },
              LANGUAGE_DETECTOR_ENGLISH,
              filters,
              featureFlagsValue,
              [],
              undefined
            );

          expect(aggregationContainer).toMatchObject(
            expect.objectContaining({
              nested: {
                nested: {
                  path: "congress"
                },
                aggs: {
                  filtered_matching: {
                    filter: {
                      bool: {
                        filter: [
                          {
                            match_phrase: {
                              [`${aggregation.aggregationField}.autocomplete_search`]:
                                {
                                  query: filterValue,
                                  slop: 5
                                }
                            }
                          }
                        ]
                      }
                    },
                    aggs: {
                      matching: {
                        terms: {
                          field: `${aggregation.aggregationField}`,
                          order: SORT_BY_MATCHING_DOC_COUNT
                        },
                        aggs: {
                          by_congressId: {
                            terms: {
                              field: "congress.id",
                              size: 1
                            }
                          },
                          matching: {
                            reverse_nested: {}
                          }
                        }
                      }
                    }
                  }
                }
              }
            })
          );
        }
      });

      it("should use reverse nested aggregation for congress filters when min count is supplied", async () => {
        const minCount = faker.datatype.number({ min: 1 });

        const parsedQueryTreeToElasticsearchQueriesService = createMockInstance(
          ParsedQueryTreeToElasticsearchQueriesService
        );

        const featureFlagsValue: any = mockFeatureFlagsValue();

        const keywordFilterClauseBuilderService = createMockInstance(
          KeywordFilterClauseBuilderService
        );

        const aggregationContainerBuilderService =
          new AggregationContainerBuilderService(
            parsedQueryTreeToElasticsearchQueriesService,
            keywordFilterClauseBuilderService
          );

        const aggregations = [
          {
            filterField: KeywordFilterAutocompleteFilterField.CONGRESS_NAME,
            aggregationField: "congress.name_eng"
          },
          {
            filterField:
              KeywordFilterAutocompleteFilterField.CONGRESS_ORGANIZER,
            aggregationField: "congress.organizer_eng"
          }
        ];

        for (const aggregation of aggregations) {
          const projectId = faker.datatype.string();
          const projectFeatures = {
            engagementsV2: faker.datatype.boolean()
          };
          const userId = faker.datatype.string();
          const query = undefined;
          const filterField = aggregation.filterField;
          const filterValue = faker.random.alpha();
          const suppliedFilters = generateFilters();
          suppliedFilters.congresses.minCount.value = minCount;

          const filters: QueryDslQueryContainer[] = [
            {
              function_score: {
                query: {
                  nested: {
                    path: "congress",
                    score_mode: "sum",
                    query: {
                      bool: {
                        must: {
                          match_all: {}
                        },
                        filter: [
                          {
                            match_phrase: {
                              [`${aggregation.aggregationField}.autocomplete_search`]:
                                {
                                  query: filterValue,
                                  slop: 5
                                }
                            }
                          }
                        ]
                      }
                    }
                  }
                },
                min_score: minCount
              }
            }
          ];
          const aggregationContainer =
            await aggregationContainerBuilderService.buildAggregationContainer(
              {
                projectId,
                projectFeatures,
                userId,
                query,
                filterField,
                filterValue,
                suppliedFilters
              },
              LANGUAGE_DETECTOR_ENGLISH,
              filters,
              featureFlagsValue,
              [],
              undefined
            );

          expect(aggregationContainer).toMatchObject(
            expect.objectContaining({
              nested: {
                nested: {
                  path: "congress"
                },
                aggs: {
                  filtered_matching: {
                    filter: {
                      bool: {
                        filter: [
                          {
                            match_phrase: {
                              [`${aggregation.aggregationField}.autocomplete_search`]:
                                {
                                  query: filterValue,
                                  slop: 5
                                }
                            }
                          }
                        ]
                      }
                    },
                    aggs: {
                      matching: {
                        terms: {
                          field: `${aggregation.aggregationField}`
                        },
                        aggs: {
                          by_congressId: {
                            terms: {
                              field: "congress.id",
                              size: 1
                            }
                          },
                          matching: {
                            reverse_nested: {}
                          }
                        }
                      }
                    }
                  }
                }
              }
            })
          );
        }
      });

      it("should use reverse nested aggregation for congress filters when max count is supplied", async () => {
        const maxCount = faker.datatype.number();

        const parsedQueryTreeToElasticsearchQueriesService = createMockInstance(
          ParsedQueryTreeToElasticsearchQueriesService
        );

        const featureFlagsValue: any = mockFeatureFlagsValue();

        const keywordFilterClauseBuilderService = createMockInstance(
          KeywordFilterClauseBuilderService
        );

        const aggregationContainerBuilderService =
          new AggregationContainerBuilderService(
            parsedQueryTreeToElasticsearchQueriesService,
            keywordFilterClauseBuilderService
          );

        const aggregations = [
          {
            filterField: KeywordFilterAutocompleteFilterField.CONGRESS_NAME,
            aggregationField: "congress.name_eng"
          },
          {
            filterField:
              KeywordFilterAutocompleteFilterField.CONGRESS_ORGANIZER,
            aggregationField: "congress.organizer_eng"
          }
        ];

        for (const aggregation of aggregations) {
          const projectId = faker.datatype.string();
          const projectFeatures = {
            engagementsV2: faker.datatype.boolean()
          };
          const userId = faker.datatype.string();
          const query = undefined;
          const filterField = aggregation.filterField;
          const filterValue = faker.random.alpha();
          const suppliedFilters = generateFilters();
          suppliedFilters.congresses.maxCount!.value = maxCount;

          const filters: QueryDslQueryContainer[] = [
            {
              function_score: {
                query: {
                  nested: {
                    path: "congress",
                    score_mode: "sum",
                    query: {
                      bool: {
                        must: {
                          match_all: {}
                        },
                        filter: [
                          {
                            match_phrase: {
                              [`${aggregation.aggregationField}.autocomplete_search`]:
                                {
                                  query: filterValue,
                                  slop: 5
                                }
                            }
                          }
                        ]
                      }
                    }
                  }
                },
                min_score: maxCount
              }
            }
          ];
          const aggregationContainer =
            await aggregationContainerBuilderService.buildAggregationContainer(
              {
                projectId,
                projectFeatures,
                userId,
                query,
                filterField,
                filterValue,
                suppliedFilters
              },
              LANGUAGE_DETECTOR_ENGLISH,
              filters,
              featureFlagsValue,
              [],
              undefined
            );

          expect(aggregationContainer).toMatchObject(
            expect.objectContaining({
              nested: {
                nested: {
                  path: "congress"
                },
                aggs: {
                  filtered_matching: {
                    filter: {
                      bool: {
                        filter: [
                          {
                            match_phrase: {
                              [`${aggregation.aggregationField}.autocomplete_search`]:
                                {
                                  query: filterValue,
                                  slop: 5
                                }
                            }
                          }
                        ]
                      }
                    },
                    aggs: {
                      matching: {
                        terms: {
                          field: `${aggregation.aggregationField}`
                        },
                        aggs: {
                          by_congressId: {
                            terms: {
                              field: "congress.id",
                              size: 1
                            }
                          },
                          matching: {
                            reverse_nested: {}
                          }
                        }
                      }
                    }
                  }
                }
              }
            })
          );
        }
      });

      it("should use reverse nested aggregation for congress filters when min & max count is supplied", async () => {
        const minCount = faker.datatype.number();
        const maxCount = faker.datatype.number();

        const parsedQueryTreeToElasticsearchQueriesService = createMockInstance(
          ParsedQueryTreeToElasticsearchQueriesService
        );

        const featureFlagsValue: any = mockFeatureFlagsValue();

        const keywordFilterClauseBuilderService = createMockInstance(
          KeywordFilterClauseBuilderService
        );

        const aggregationContainerBuilderService =
          new AggregationContainerBuilderService(
            parsedQueryTreeToElasticsearchQueriesService,
            keywordFilterClauseBuilderService
          );

        const aggregations = [
          {
            filterField: KeywordFilterAutocompleteFilterField.CONGRESS_NAME,
            aggregationField: "congress.name_eng"
          },
          {
            filterField:
              KeywordFilterAutocompleteFilterField.CONGRESS_ORGANIZER,
            aggregationField: "congress.organizer_eng"
          }
        ];

        for (const aggregation of aggregations) {
          const projectId = faker.datatype.string();
          const projectFeatures = {
            engagementsV2: faker.datatype.boolean()
          };
          const userId = faker.datatype.string();
          const query = undefined;
          const filterField = aggregation.filterField;
          const filterValue = faker.random.alpha();
          const suppliedFilters = generateFilters();
          suppliedFilters.congresses.minCount.value = minCount;
          suppliedFilters.congresses.maxCount!.value = maxCount;

          const filters: QueryDslQueryContainer[] = [
            {
              function_score: {
                query: {
                  nested: {
                    path: "congress",
                    score_mode: "sum",
                    query: {
                      bool: {
                        must: {
                          match_all: {}
                        },
                        filter: [
                          {
                            match_phrase: {
                              [`${aggregation.aggregationField}.autocomplete_search`]:
                                {
                                  query: filterValue,
                                  slop: 5
                                }
                            }
                          }
                        ]
                      }
                    }
                  }
                },
                min_score: minCount
              }
            }
          ];
          const aggregationContainer =
            await aggregationContainerBuilderService.buildAggregationContainer(
              {
                projectId,
                projectFeatures,
                userId,
                query,
                filterField,
                filterValue,
                suppliedFilters
              },
              LANGUAGE_DETECTOR_ENGLISH,
              filters,
              featureFlagsValue,
              [],
              undefined
            );

          expect(aggregationContainer).toMatchObject(
            expect.objectContaining({
              nested: {
                nested: {
                  path: "congress"
                },
                aggs: {
                  filtered_matching: {
                    filter: {
                      bool: {
                        filter: [
                          {
                            match_phrase: {
                              [`${aggregation.aggregationField}.autocomplete_search`]:
                                {
                                  query: filterValue,
                                  slop: 5
                                }
                            }
                          }
                        ]
                      }
                    },
                    aggs: {
                      matching: {
                        terms: {
                          field: `${aggregation.aggregationField}`
                        },
                        aggs: {
                          by_congressId: {
                            terms: {
                              field: "congress.id",
                              size: 1
                            }
                          },
                          matching: {
                            reverse_nested: {}
                          }
                        }
                      }
                    }
                  }
                }
              }
            })
          );
        }
      });

      it("should include global search term parsed query if supplied and no other congress filters were supplied", async () => {
        const parsedQueryTreeToElasticsearchQueriesService = createMockInstance(
          ParsedQueryTreeToElasticsearchQueriesService
        );

        const pathSpecificQueries: Record<NestedPath, QueryDslQueryContainer> =
          {
            trials: generateMockElasticsearchTermQuery(),
            publications: generateMockElasticsearchTermQuery(),
            congress: generateMockElasticsearchTermQuery(),
            payments: generateMockElasticsearchTermQuery(),
            DRG_diagnoses: generateMockElasticsearchTermQuery(),
            DRG_procedures: generateMockElasticsearchTermQuery(),
            prescriptions: generateMockElasticsearchTermQuery(),
            ccsr: generateMockElasticsearchTermQuery(),
            ccsr_px: generateMockElasticsearchTermQuery()
          };

        const mockParseTreeToQueries =
          mockParseTreeToElasticsearchQueries(pathSpecificQueries);

        parsedQueryTreeToElasticsearchQueriesService.parse.mockImplementation(
          mockParseTreeToQueries
        );

        const featureFlagsValue: any = mockFeatureFlagsValue();

        const keywordFilterClauseBuilderService = createMockInstance(
          KeywordFilterClauseBuilderService
        );

        const aggregationContainerBuilderService =
          new AggregationContainerBuilderService(
            parsedQueryTreeToElasticsearchQueriesService,
            keywordFilterClauseBuilderService
          );

        const aggregations = [
          {
            filterField: KeywordFilterAutocompleteFilterField.CONGRESS_NAME,
            aggregationField: "congress.name_eng"
          },
          {
            filterField:
              KeywordFilterAutocompleteFilterField.CONGRESS_ORGANIZER,
            aggregationField: "congress.organizer_eng"
          }
        ];

        for (const aggregation of aggregations) {
          const projectId = faker.datatype.string();
          const projectFeatures = {
            engagementsV2: faker.datatype.boolean()
          };
          const userId = faker.datatype.string();
          const query = faker.datatype.string();
          const filterField = aggregation.filterField;
          const filterValue = faker.random.alpha();

          const suppliedFilters = generateFilters();

          const queryWord1 = faker.datatype.string();
          const queryWord2 = faker.datatype.string();
          const queryWord3 = faker.datatype.string();

          const augmentedQuery = `${queryWord1} | ${queryWord2} | ${queryWord3}`;

          const aggregationContainer =
            await aggregationContainerBuilderService.buildAggregationContainer(
              {
                projectId,
                projectFeatures,
                userId,
                query,
                filterField,
                filterValue,
                suppliedFilters
              },
              LANGUAGE_DETECTOR_ENGLISH,
              [],
              featureFlagsValue,
              [],
              augmentedQuery
            );

          expect(aggregationContainer).toMatchObject(
            expect.objectContaining({
              nested: {
                nested: {
                  path: "congress"
                },
                aggs: {
                  filtered_matching: {
                    filter: {
                      bool: {
                        filter: [pathSpecificQueries.congress]
                      }
                    },
                    aggs: {
                      matching: {
                        terms: {
                          field: `${aggregation.aggregationField}`,
                          order: SORT_BY_MATCHING_DOC_COUNT
                        },
                        aggs: {
                          by_congressId: {
                            terms: {
                              field: "congress.id",
                              size: 1
                            }
                          },
                          matching: {
                            reverse_nested: {}
                          }
                        }
                      }
                    }
                  }
                }
              }
            })
          );

          expect(
            parsedQueryTreeToElasticsearchQueriesService.parse
          ).toHaveBeenCalledWith(augmentedQuery, [
            "congress.keywords_eng",
            "congress.title_eng",
            "congress.organizer_eng.search",
            "congress.name_eng.search",
            "congress.role.search"
          ]);
        }
      });

      describe("supplied congress filter values", () => {
        it("should be used to filter nested congress aggregation", async () => {
          const parsedQueryTreeToElasticsearchQueriesService =
            createMockInstance(ParsedQueryTreeToElasticsearchQueriesService);

          const featureFlagsValue: any = mockFeatureFlagsValue();

          const keywordFilterClauseBuilderService = createMockInstance(
            KeywordFilterClauseBuilderService
          );

          const aggregationContainerBuilderService =
            new AggregationContainerBuilderService(
              parsedQueryTreeToElasticsearchQueriesService,
              keywordFilterClauseBuilderService
            );

          const aggregations = [
            {
              filterField: KeywordFilterAutocompleteFilterField.CONGRESS_NAME,
              aggregationField: "congress.name_eng"
            },
            {
              filterField:
                KeywordFilterAutocompleteFilterField.CONGRESS_ORGANIZER,
              aggregationField: "congress.organizer_eng"
            }
          ];

          for (const aggregation of aggregations) {
            const projectId = faker.datatype.string();
            const projectFeatures = {
              engagementsV2: faker.datatype.boolean()
            };
            const userId = faker.datatype.string();
            const query = undefined;
            const filterField = aggregation.filterField;
            const filterValue = faker.random.alpha();
            const suppliedFilters = generateFilters();
            const expectedFilters: QueryDslQueryContainer[] = [
              {
                match_phrase: {
                  [`${aggregation.aggregationField}.autocomplete_search`]: {
                    query: filterValue,
                    slop: 5
                  }
                }
              }
            ];

            if (
              aggregation.filterField !==
              KeywordFilterAutocompleteFilterField.CONGRESS_NAME
            ) {
              suppliedFilters.congresses.name.values = [
                faker.datatype.string()
              ];

              expectedFilters.push({
                terms: {
                  "congress.name_eng": suppliedFilters.congresses.name.values
                }
              });
            }
            if (
              aggregation.filterField !==
              KeywordFilterAutocompleteFilterField.CONGRESS_ORGANIZER
            ) {
              suppliedFilters.congresses.organizerName.values = [
                faker.datatype.string()
              ];

              expectedFilters.push({
                terms: {
                  "congress.organizer_eng":
                    suppliedFilters.congresses.organizerName.values
                }
              });
            }

            const filters = [
              {
                nested: {
                  path: "congress",
                  query: {
                    bool: {
                      filter: expectedFilters
                    }
                  }
                }
              }
            ];
            const aggregationContainer =
              await aggregationContainerBuilderService.buildAggregationContainer(
                {
                  projectId,
                  projectFeatures,
                  userId,
                  query,
                  filterField,
                  filterValue,
                  suppliedFilters
                },
                LANGUAGE_DETECTOR_ENGLISH,
                filters,
                featureFlagsValue,
                [],
                undefined
              );

            expect(aggregationContainer).toMatchObject(
              expect.objectContaining({
                nested: {
                  nested: {
                    path: "congress"
                  },
                  aggs: {
                    filtered_matching: {
                      filter: {
                        bool: {
                          filter: expect.arrayContaining(expectedFilters)
                        }
                      },
                      aggs: {
                        matching: {
                          terms: {
                            field: `${aggregation.aggregationField}`,
                            order: SORT_BY_MATCHING_DOC_COUNT
                          },
                          aggs: {
                            by_congressId: {
                              terms: {
                                field: "congress.id",
                                size: 1
                              }
                            },
                            matching: {
                              reverse_nested: {}
                            }
                          }
                        }
                      }
                    }
                  }
                }
              })
            );
          }
        });

        it("should NOT be used to filter nested congress aggregation when the filter value is of the same type that is being aggregated", async () => {
          const parsedQueryTreeToElasticsearchQueriesService =
            createMockInstance(ParsedQueryTreeToElasticsearchQueriesService);

          const featureFlagsValue: any = mockFeatureFlagsValue();

          const keywordFilterClauseBuilderService = createMockInstance(
            KeywordFilterClauseBuilderService
          );

          const aggregationContainerBuilderService =
            new AggregationContainerBuilderService(
              parsedQueryTreeToElasticsearchQueriesService,
              keywordFilterClauseBuilderService
            );

          const aggregations = [
            {
              filterField: KeywordFilterAutocompleteFilterField.CONGRESS_NAME,
              aggregationField: "congress.name_eng"
            },
            {
              filterField:
                KeywordFilterAutocompleteFilterField.CONGRESS_ORGANIZER,
              aggregationField: "congress.organizer_eng"
            }
          ];

          for (const aggregation of aggregations) {
            const projectId = faker.datatype.string();
            const projectFeatures = {
              engagementsV2: faker.datatype.boolean()
            };
            const userId = faker.datatype.string();
            const query = undefined;
            const filterField = aggregation.filterField;
            const filterValue = faker.random.alpha();
            const suppliedFilters = generateFilters();

            if (
              aggregation.filterField ===
              KeywordFilterAutocompleteFilterField.CONGRESS_NAME
            ) {
              suppliedFilters.congresses.name.values = [
                faker.datatype.string()
              ];
            } else if (
              aggregation.filterField ===
              KeywordFilterAutocompleteFilterField.CONGRESS_ORGANIZER
            ) {
              suppliedFilters.congresses.organizerName.values = [
                faker.datatype.string()
              ];
            }

            const filters = [
              {
                nested: {
                  path: "congress",
                  query: {
                    bool: {
                      filter: [
                        {
                          match_phrase: {
                            [`${aggregation.aggregationField}.autocomplete_search`]:
                              {
                                query: filterValue,
                                slop: 5
                              }
                          }
                        }
                      ]
                    }
                  }
                }
              }
            ];
            const aggregationContainer =
              await aggregationContainerBuilderService.buildAggregationContainer(
                {
                  projectId,
                  projectFeatures,
                  userId,
                  query,
                  filterField,
                  filterValue,
                  suppliedFilters
                },
                LANGUAGE_DETECTOR_ENGLISH,
                filters,
                featureFlagsValue,
                [],
                undefined
              );

            expect(aggregationContainer).toMatchObject(
              expect.objectContaining({
                nested: {
                  nested: {
                    path: "congress"
                  },
                  aggs: {
                    filtered_matching: {
                      filter: {
                        bool: {
                          filter: [
                            {
                              match_phrase: {
                                [`${aggregation.aggregationField}.autocomplete_search`]:
                                  {
                                    query: filterValue,
                                    slop: 5
                                  }
                              }
                            }
                          ]
                        }
                      },
                      aggs: {
                        matching: {
                          terms: {
                            field: `${aggregation.aggregationField}`,
                            order: SORT_BY_MATCHING_DOC_COUNT
                          },
                          aggs: {
                            by_congressId: {
                              terms: {
                                field: "congress.id",
                                size: 1
                              }
                            },
                            matching: {
                              reverse_nested: {}
                            }
                          }
                        }
                      }
                    }
                  }
                }
              })
            );
          }
        });
      });
    });

    describe("publications", () => {
      it("should not include 'include' regex when filterValue is an empty string", async () => {
        const filters = [
          {
            nested: {
              path: "publications",
              query: {
                bool: {
                  filter: []
                }
              }
            }
          }
        ];
        const parsedQueryTreeToElasticsearchQueriesService = createMockInstance(
          ParsedQueryTreeToElasticsearchQueriesService
        );

        const featureFlagsValue: any = mockFeatureFlagsValue();

        const keywordFilterClauseBuilderService = createMockInstance(
          KeywordFilterClauseBuilderService
        );

        const aggregationContainerBuilderService =
          new AggregationContainerBuilderService(
            parsedQueryTreeToElasticsearchQueriesService,
            keywordFilterClauseBuilderService
          );

        const aggregations = [
          {
            filterField: KeywordFilterAutocompleteFilterField.JOURNAL_NAME,
            aggregationField: "publications.journalName_eng"
          },
          {
            filterField: KeywordFilterAutocompleteFilterField.PUBLICATION_TYPE,
            aggregationField: "publications.type_eng"
          }
        ];

        for (const aggregation of aggregations) {
          const projectId = faker.datatype.string();
          const projectFeatures = {
            engagementsV2: faker.datatype.boolean()
          };
          const userId = faker.datatype.string();
          const query = undefined;
          const filterField = aggregation.filterField;
          const filterValue = "";

          const suppliedFilters = generateFilters();

          const aggregationContainer =
            await aggregationContainerBuilderService.buildAggregationContainer(
              {
                projectId,
                projectFeatures,
                userId,
                query,
                filterField,
                filterValue,
                suppliedFilters
              },
              LANGUAGE_DETECTOR_ENGLISH,
              filters,
              featureFlagsValue,
              [],
              undefined
            );

          expect(aggregationContainer).toMatchObject(
            expect.objectContaining({
              nested: {
                nested: {
                  path: "publications"
                },
                aggs: {
                  filtered_matching: {
                    filter: {
                      bool: {
                        filter: []
                      }
                    },
                    aggs: {
                      matching: {
                        terms: {
                          field: aggregation.aggregationField,
                          exclude: "",
                          order: SORT_BY_MATCHING_DOC_COUNT
                        },
                        aggs: {
                          matching: {
                            reverse_nested: {}
                          }
                        }
                      }
                    }
                  }
                }
              }
            })
          );
        }
      });

      it("should use reverse nested aggregation for publication filters when min filters are NOT supplied", async () => {
        const parsedQueryTreeToElasticsearchQueriesService = createMockInstance(
          ParsedQueryTreeToElasticsearchQueriesService
        );

        const featureFlagsValue: any = mockFeatureFlagsValue();

        const keywordFilterClauseBuilderService = createMockInstance(
          KeywordFilterClauseBuilderService
        );

        const aggregationContainerBuilderService =
          new AggregationContainerBuilderService(
            parsedQueryTreeToElasticsearchQueriesService,
            keywordFilterClauseBuilderService
          );

        const aggregations = [
          {
            filterField: KeywordFilterAutocompleteFilterField.JOURNAL_NAME,
            aggregationField: "publications.journalName_eng"
          },
          {
            filterField: KeywordFilterAutocompleteFilterField.PUBLICATION_TYPE,
            aggregationField: "publications.type_eng"
          }
        ];

        for (const aggregation of aggregations) {
          const projectId = faker.datatype.string();
          const projectFeatures = {
            engagementsV2: faker.datatype.boolean()
          };
          const userId = faker.datatype.string();
          const query = undefined;
          const filterField = aggregation.filterField;
          const filterValue = faker.random.alpha();

          const suppliedFilters = generateFilters();

          const filters = [
            {
              nested: {
                path: "publications",
                query: {
                  bool: {
                    filter: [
                      {
                        match_phrase: {
                          [`${aggregation.aggregationField}.autocomplete_search`]:
                            {
                              query: filterValue,
                              slop: 5
                            }
                        }
                      }
                    ]
                  }
                }
              }
            }
          ];

          const aggregationContainer =
            await aggregationContainerBuilderService.buildAggregationContainer(
              {
                projectId,
                projectFeatures,
                userId,
                query,
                filterField,
                filterValue,
                suppliedFilters
              },
              LANGUAGE_DETECTOR_ENGLISH,
              filters,
              featureFlagsValue,
              [],
              undefined
            );

          expect(aggregationContainer).toMatchObject(
            expect.objectContaining({
              nested: {
                nested: {
                  path: "publications"
                },
                aggs: {
                  filtered_matching: {
                    filter: {
                      bool: {
                        filter: [
                          {
                            match_phrase: {
                              [`${aggregation.aggregationField}.autocomplete_search`]:
                                {
                                  query: filterValue,
                                  slop: 5
                                }
                            }
                          }
                        ]
                      }
                    },
                    aggs: {
                      matching: {
                        terms: {
                          field: `${aggregation.aggregationField}`,
                          order: SORT_BY_MATCHING_DOC_COUNT
                        },
                        aggs: {
                          matching: {
                            reverse_nested: {}
                          }
                        }
                      }
                    }
                  }
                }
              }
            })
          );
        }
      });

      it("should use scripted metric aggregation for publication filters when min filters are supplied", async () => {
        const min_count = faker.datatype.number();
        const min_microblogging_count = faker.datatype.number();

        const parsedQueryTreeToElasticsearchQueriesService = createMockInstance(
          ParsedQueryTreeToElasticsearchQueriesService
        );

        const featureFlagsValue: any = mockFeatureFlagsValue();

        const keywordFilterClauseBuilderService = createMockInstance(
          KeywordFilterClauseBuilderService
        );

        const aggregationContainerBuilderService =
          new AggregationContainerBuilderService(
            parsedQueryTreeToElasticsearchQueriesService,
            keywordFilterClauseBuilderService
          );

        const aggregations = [
          {
            filterField: KeywordFilterAutocompleteFilterField.JOURNAL_NAME,
            aggregationField: "publications.journalName_eng"
          },
          {
            filterField: KeywordFilterAutocompleteFilterField.PUBLICATION_TYPE,
            aggregationField: "publications.type_eng"
          }
        ];

        for (const aggregation of aggregations) {
          const projectId = faker.datatype.string();
          const projectFeatures = {
            engagementsV2: faker.datatype.boolean()
          };
          const userId = faker.datatype.string();
          const query = undefined;
          const filterField = aggregation.filterField;
          const filterValue = faker.random.alpha();
          const suppliedFilters = generateFilters();
          suppliedFilters.publications.minCount.value = min_count;
          suppliedFilters.publications.socialMediaMinCount.value =
            min_microblogging_count;

          const filters: QueryDslQueryContainer[] = [
            {
              function_score: {
                query: {
                  nested: {
                    path: "publications",
                    score_mode: "sum",
                    query: {
                      bool: {
                        must: {
                          match_all: {}
                        },
                        filter: [
                          {
                            match_phrase: {
                              [`${aggregation.aggregationField}.autocomplete_search`]:
                                {
                                  query: filterValue,
                                  slop: 5
                                }
                            }
                          }
                        ]
                      }
                    }
                  }
                },
                min_score: min_count
              }
            },
            {
              function_score: {
                query: {
                  nested: {
                    path: "publications",
                    score_mode: "sum",
                    query: {
                      bool: {
                        must: [
                          {
                            function_score: {
                              functions: [
                                {
                                  field_value_factor: {
                                    field: "publications.microBloggingCount",
                                    missing: 0
                                  }
                                }
                              ]
                            }
                          }
                        ],
                        filter: [
                          {
                            match_phrase: {
                              [`${aggregation.aggregationField}.autocomplete_search`]:
                                {
                                  query: filterValue,
                                  slop: 5
                                }
                            }
                          }
                        ]
                      }
                    }
                  }
                },
                min_score: min_microblogging_count
              }
            }
          ];
          const aggregationContainer =
            await aggregationContainerBuilderService.buildAggregationContainer(
              {
                projectId,
                projectFeatures,
                userId,
                query,
                filterField,
                filterValue,
                suppliedFilters
              },
              LANGUAGE_DETECTOR_ENGLISH,
              filters,
              featureFlagsValue,
              [],
              undefined
            );

          expect(aggregationContainer).toMatchObject(
            expect.objectContaining({
              nested: {
                nested: {
                  path: "publications"
                },
                aggs: {
                  filtered_matching: {
                    filter: {
                      bool: {
                        filter: [
                          {
                            match_phrase: {
                              [`${aggregation.aggregationField}.autocomplete_search`]:
                                {
                                  query: filterValue,
                                  slop: 5
                                }
                            }
                          }
                        ]
                      }
                    },
                    aggs: {
                      matching: {
                        terms: {
                          field: `${aggregation.aggregationField}`
                        },
                        aggs: {
                          matching: {
                            scripted_metric: {
                              params: {
                                minCount: min_count,
                                minAmount: min_microblogging_count
                              },
                              init_script: expect.any(String),
                              map_script: expect.any(String),
                              combine_script: expect.any(String),
                              reduce_script: expect.any(String)
                            }
                          }
                        }
                      }
                    }
                  }
                }
              }
            })
          );
        }
      });

      it("should use scripted metric aggregation for publication filters when max filters are supplied", async () => {
        const max_count = faker.datatype.number();
        const max_microblogging_count = faker.datatype.number();

        const parsedQueryTreeToElasticsearchQueriesService = createMockInstance(
          ParsedQueryTreeToElasticsearchQueriesService
        );

        const featureFlagsValue: any = mockFeatureFlagsValue();

        const keywordFilterClauseBuilderService = createMockInstance(
          KeywordFilterClauseBuilderService
        );

        const aggregationContainerBuilderService =
          new AggregationContainerBuilderService(
            parsedQueryTreeToElasticsearchQueriesService,
            keywordFilterClauseBuilderService
          );

        const aggregations = [
          {
            filterField: KeywordFilterAutocompleteFilterField.JOURNAL_NAME,
            aggregationField: "publications.journalName_eng"
          },
          {
            filterField: KeywordFilterAutocompleteFilterField.PUBLICATION_TYPE,
            aggregationField: "publications.type_eng"
          }
        ];

        for (const aggregation of aggregations) {
          const projectId = faker.datatype.string();
          const projectFeatures = {
            engagementsV2: faker.datatype.boolean()
          };
          const userId = faker.datatype.string();
          const query = undefined;
          const filterField = aggregation.filterField;
          const filterValue = faker.random.alpha();
          const suppliedFilters = generateFilters();
          suppliedFilters.publications.maxCount!.value = max_count;
          suppliedFilters.publications.socialMediaMaxCount!.value =
            max_microblogging_count;

          const filters: QueryDslQueryContainer[] = [
            {
              function_score: {
                query: {
                  nested: {
                    path: "publications",
                    score_mode: "sum",
                    query: {
                      bool: {
                        must: {
                          match_all: {}
                        },
                        filter: [
                          {
                            match_phrase: {
                              [`${aggregation.aggregationField}.autocomplete_search`]:
                                {
                                  query: filterValue,
                                  slop: 5
                                }
                            }
                          }
                        ]
                      }
                    }
                  }
                },
                min_score: max_count
              }
            },
            {
              function_score: {
                query: {
                  nested: {
                    path: "publications",
                    score_mode: "sum",
                    query: {
                      bool: {
                        must: [
                          {
                            function_score: {
                              functions: [
                                {
                                  field_value_factor: {
                                    field: "publications.microBloggingCount",
                                    missing: 0
                                  }
                                }
                              ]
                            }
                          }
                        ],
                        filter: [
                          {
                            match_phrase: {
                              [`${aggregation.aggregationField}.autocomplete_search`]:
                                {
                                  query: filterValue,
                                  slop: 5
                                }
                            }
                          }
                        ]
                      }
                    }
                  }
                },
                min_score: max_microblogging_count
              }
            }
          ];
          const aggregationContainer =
            await aggregationContainerBuilderService.buildAggregationContainer(
              {
                projectId,
                projectFeatures,
                userId,
                query,
                filterField,
                filterValue,
                suppliedFilters
              },
              LANGUAGE_DETECTOR_ENGLISH,
              filters,
              featureFlagsValue,
              [],
              undefined
            );

          expect(aggregationContainer).toMatchObject(
            expect.objectContaining({
              nested: {
                nested: {
                  path: "publications"
                },
                aggs: {
                  filtered_matching: {
                    filter: {
                      bool: {
                        filter: [
                          {
                            match_phrase: {
                              [`${aggregation.aggregationField}.autocomplete_search`]:
                                {
                                  query: filterValue,
                                  slop: 5
                                }
                            }
                          }
                        ]
                      }
                    },
                    aggs: {
                      matching: {
                        terms: {
                          field: `${aggregation.aggregationField}`
                        },
                        aggs: {
                          matching: {
                            scripted_metric: {
                              params: {
                                maxCount: max_count,
                                maxAmount: max_microblogging_count
                              },
                              init_script: expect.any(String),
                              map_script: expect.any(String),
                              combine_script: expect.any(String),
                              reduce_script: expect.any(String)
                            }
                          }
                        }
                      }
                    }
                  }
                }
              }
            })
          );
        }
      });

      it("should use scripted metric aggregation for publication filters when min & max filters are supplied", async () => {
        const min_count = faker.datatype.number();
        const min_microblogging_count = faker.datatype.number();
        const max_count = faker.datatype.number();
        const max_microblogging_count = faker.datatype.number();

        const parsedQueryTreeToElasticsearchQueriesService = createMockInstance(
          ParsedQueryTreeToElasticsearchQueriesService
        );

        const featureFlagsValue: any = mockFeatureFlagsValue();

        const keywordFilterClauseBuilderService = createMockInstance(
          KeywordFilterClauseBuilderService
        );

        const aggregationContainerBuilderService =
          new AggregationContainerBuilderService(
            parsedQueryTreeToElasticsearchQueriesService,
            keywordFilterClauseBuilderService
          );

        const aggregations = [
          {
            filterField: KeywordFilterAutocompleteFilterField.JOURNAL_NAME,
            aggregationField: "publications.journalName_eng"
          },
          {
            filterField: KeywordFilterAutocompleteFilterField.PUBLICATION_TYPE,
            aggregationField: "publications.type_eng"
          }
        ];

        for (const aggregation of aggregations) {
          const projectId = faker.datatype.string();
          const projectFeatures = {
            engagementsV2: faker.datatype.boolean()
          };
          const userId = faker.datatype.string();
          const query = undefined;
          const filterField = aggregation.filterField;
          const filterValue = faker.random.alpha();
          const suppliedFilters = generateFilters();
          suppliedFilters.publications.minCount.value = min_count;
          suppliedFilters.publications.socialMediaMinCount.value =
            min_microblogging_count;
          suppliedFilters.publications.maxCount!.value = max_count;
          suppliedFilters.publications.socialMediaMaxCount!.value =
            max_microblogging_count;

          const filters: QueryDslQueryContainer[] = [
            {
              function_score: {
                query: {
                  nested: {
                    path: "publications",
                    score_mode: "sum",
                    query: {
                      bool: {
                        must: {
                          match_all: {}
                        },
                        filter: [
                          {
                            match_phrase: {
                              [`${aggregation.aggregationField}.autocomplete_search`]:
                                {
                                  query: filterValue,
                                  slop: 5
                                }
                            }
                          }
                        ]
                      }
                    }
                  }
                },
                min_score: min_count
              }
            },
            {
              function_score: {
                query: {
                  nested: {
                    path: "publications",
                    score_mode: "sum",
                    query: {
                      bool: {
                        must: [
                          {
                            function_score: {
                              functions: [
                                {
                                  field_value_factor: {
                                    field: "publications.microBloggingCount",
                                    missing: 0
                                  }
                                }
                              ]
                            }
                          }
                        ],
                        filter: [
                          {
                            match_phrase: {
                              [`${aggregation.aggregationField}.autocomplete_search`]:
                                {
                                  query: filterValue,
                                  slop: 5
                                }
                            }
                          }
                        ]
                      }
                    }
                  }
                },
                min_score: min_microblogging_count
              }
            }
          ];
          const aggregationContainer =
            await aggregationContainerBuilderService.buildAggregationContainer(
              {
                projectId,
                projectFeatures,
                userId,
                query,
                filterField,
                filterValue,
                suppliedFilters
              },
              LANGUAGE_DETECTOR_ENGLISH,
              filters,
              featureFlagsValue,
              [],
              undefined
            );

          expect(aggregationContainer).toMatchObject(
            expect.objectContaining({
              nested: {
                nested: {
                  path: "publications"
                },
                aggs: {
                  filtered_matching: {
                    filter: {
                      bool: {
                        filter: [
                          {
                            match_phrase: {
                              [`${aggregation.aggregationField}.autocomplete_search`]:
                                {
                                  query: filterValue,
                                  slop: 5
                                }
                            }
                          }
                        ]
                      }
                    },
                    aggs: {
                      matching: {
                        terms: {
                          field: `${aggregation.aggregationField}`
                        },
                        aggs: {
                          matching: {
                            scripted_metric: {
                              params: {
                                minCount: min_count,
                                minAmount: min_microblogging_count,
                                maxCount: max_count,
                                maxAmount: max_microblogging_count
                              },
                              init_script: expect.any(String),
                              map_script: expect.any(String),
                              combine_script: expect.any(String),
                              reduce_script: expect.any(String)
                            }
                          }
                        }
                      }
                    }
                  }
                }
              }
            })
          );
        }
      });

      it("should include global search term parsed query if supplied and no other publications filters were supplied", async () => {
        const parsedQueryTreeToElasticsearchQueriesService = createMockInstance(
          ParsedQueryTreeToElasticsearchQueriesService
        );

        const pathSpecificQueries: Record<NestedPath, QueryDslQueryContainer> =
          {
            trials: generateMockElasticsearchTermQuery(),
            publications: generateMockElasticsearchTermQuery(),
            congress: generateMockElasticsearchTermQuery(),
            payments: generateMockElasticsearchTermQuery(),
            DRG_diagnoses: generateMockElasticsearchTermQuery(),
            DRG_procedures: generateMockElasticsearchTermQuery(),
            prescriptions: generateMockElasticsearchTermQuery(),
            ccsr: generateMockElasticsearchTermQuery(),
            ccsr_px: generateMockElasticsearchTermQuery()
          };

        const mockParseTreeToQueries =
          mockParseTreeToElasticsearchQueries(pathSpecificQueries);

        parsedQueryTreeToElasticsearchQueriesService.parse.mockImplementation(
          mockParseTreeToQueries
        );

        const featureFlagsValue: any = mockFeatureFlagsValue();

        const keywordFilterClauseBuilderService = createMockInstance(
          KeywordFilterClauseBuilderService
        );

        const aggregationContainerBuilderService =
          new AggregationContainerBuilderService(
            parsedQueryTreeToElasticsearchQueriesService,
            keywordFilterClauseBuilderService
          );

        const aggregations = [
          {
            filterField: KeywordFilterAutocompleteFilterField.JOURNAL_NAME,
            aggregationField: "publications.journalName_eng"
          },
          {
            filterField: KeywordFilterAutocompleteFilterField.PUBLICATION_TYPE,
            aggregationField: "publications.type_eng"
          }
        ];

        for (const aggregation of aggregations) {
          const projectId = faker.datatype.string();
          const projectFeatures = {
            engagementsV2: faker.datatype.boolean()
          };
          const userId = faker.datatype.string();
          const query = faker.datatype.string();
          const filterField = aggregation.filterField;
          const filterValue = faker.random.alpha();

          const suppliedFilters = generateFilters();

          const queryWord1 = faker.datatype.string();
          const queryWord2 = faker.datatype.string();
          const queryWord3 = faker.datatype.string();

          const augmentedQuery = `${queryWord1} | ${queryWord2} | ${queryWord3}`;

          const aggregationContainer =
            await aggregationContainerBuilderService.buildAggregationContainer(
              {
                projectId,
                projectFeatures,
                userId,
                query,
                filterField,
                filterValue,
                suppliedFilters
              },
              LANGUAGE_DETECTOR_ENGLISH,
              [],
              featureFlagsValue,
              [],
              augmentedQuery
            );

          expect(aggregationContainer).toMatchObject(
            expect.objectContaining({
              nested: {
                nested: {
                  path: "publications"
                },
                aggs: {
                  filtered_matching: {
                    filter: {
                      bool: {
                        filter: [pathSpecificQueries.publications]
                      }
                    },
                    aggs: {
                      matching: {
                        terms: {
                          field: `${aggregation.aggregationField}`,
                          order: SORT_BY_MATCHING_DOC_COUNT
                        },
                        aggs: {
                          matching: {
                            reverse_nested: {}
                          }
                        }
                      }
                    }
                  }
                }
              }
            })
          );

          expect(
            parsedQueryTreeToElasticsearchQueriesService.parse
          ).toHaveBeenCalledWith(
            augmentedQuery,
            [
              "publications.publicationAbstract",
              "publications.keywords",
              "publications.title"
            ],
            expect.any(Function)
          );
        }
      });

      describe("supplied publication filter values", () => {
        it("should be used to filter nested publications aggregation", async () => {
          const parsedQueryTreeToElasticsearchQueriesService =
            createMockInstance(ParsedQueryTreeToElasticsearchQueriesService);

          const featureFlagsValue: any = mockFeatureFlagsValue();

          const keywordFilterClauseBuilderService = createMockInstance(
            KeywordFilterClauseBuilderService
          );

          const aggregationContainerBuilderService =
            new AggregationContainerBuilderService(
              parsedQueryTreeToElasticsearchQueriesService,
              keywordFilterClauseBuilderService
            );

          const aggregations = [
            {
              filterField: KeywordFilterAutocompleteFilterField.JOURNAL_NAME,
              aggregationField: "publications.journalName_eng"
            },
            {
              filterField:
                KeywordFilterAutocompleteFilterField.PUBLICATION_TYPE,
              aggregationField: "publications.type_eng"
            }
          ];

          for (const aggregation of aggregations) {
            const projectId = faker.datatype.string();
            const projectFeatures = {
              engagementsV2: faker.datatype.boolean()
            };
            const userId = faker.datatype.string();
            const query = undefined;
            const filterField = aggregation.filterField;
            const filterValue = faker.random.alpha();
            const suppliedFilters = generateFilters();
            const expectedFilters: QueryDslQueryContainer[] = [
              {
                match_phrase: {
                  [`${aggregation.aggregationField}.autocomplete_search`]: {
                    query: filterValue,
                    slop: 5
                  }
                }
              }
            ];

            if (
              aggregation.filterField !==
              KeywordFilterAutocompleteFilterField.JOURNAL_NAME
            ) {
              suppliedFilters.publications.journal.values = [
                faker.datatype.string()
              ];

              expectedFilters.push({
                terms: {
                  "publications.journalName_eng":
                    suppliedFilters.publications.journal.values
                }
              });
            }
            if (
              aggregation.filterField !==
              KeywordFilterAutocompleteFilterField.PUBLICATION_TYPE
            ) {
              suppliedFilters.publications.type.values = [
                faker.datatype.string()
              ];

              expectedFilters.push({
                terms: {
                  "publications.type_eng":
                    suppliedFilters.publications.type.values
                }
              });
            }

            const filters = [
              {
                nested: {
                  path: "publications",
                  query: {
                    bool: {
                      filter: expectedFilters
                    }
                  }
                }
              }
            ];
            const aggregationContainer =
              await aggregationContainerBuilderService.buildAggregationContainer(
                {
                  projectId,
                  projectFeatures,
                  userId,
                  query,
                  filterField,
                  filterValue,
                  suppliedFilters
                },
                LANGUAGE_DETECTOR_ENGLISH,
                filters,
                featureFlagsValue,
                [],
                undefined
              );

            expect(aggregationContainer).toMatchObject(
              expect.objectContaining({
                nested: {
                  nested: {
                    path: "publications"
                  },
                  aggs: {
                    filtered_matching: {
                      filter: {
                        bool: {
                          filter: expect.arrayContaining(expectedFilters)
                        }
                      },
                      aggs: {
                        matching: {
                          terms: {
                            field: `${aggregation.aggregationField}`,
                            order: SORT_BY_MATCHING_DOC_COUNT
                          },
                          aggs: {
                            matching: {
                              reverse_nested: {}
                            }
                          }
                        }
                      }
                    }
                  }
                }
              })
            );
          }
        });

        it("should NOT be used to filter nested publications aggregation when the filter value is of the same type that is being aggregated", async () => {
          const parsedQueryTreeToElasticsearchQueriesService =
            createMockInstance(ParsedQueryTreeToElasticsearchQueriesService);

          const featureFlagsValue: any = mockFeatureFlagsValue();

          const keywordFilterClauseBuilderService = createMockInstance(
            KeywordFilterClauseBuilderService
          );

          const aggregationContainerBuilderService =
            new AggregationContainerBuilderService(
              parsedQueryTreeToElasticsearchQueriesService,
              keywordFilterClauseBuilderService
            );

          const aggregations = [
            {
              filterField: KeywordFilterAutocompleteFilterField.JOURNAL_NAME,
              aggregationField: "publications.journalName_eng"
            },
            {
              filterField:
                KeywordFilterAutocompleteFilterField.PUBLICATION_TYPE,
              aggregationField: "publications.type_eng"
            }
          ];

          for (const aggregation of aggregations) {
            const projectId = faker.datatype.string();
            const projectFeatures = {
              engagementsV2: faker.datatype.boolean()
            };
            const userId = faker.datatype.string();
            const query = undefined;
            const filterField = aggregation.filterField;
            const filterValue = faker.random.alpha();
            const suppliedFilters = generateFilters();

            if (
              aggregation.filterField !==
              KeywordFilterAutocompleteFilterField.JOURNAL_NAME
            ) {
              suppliedFilters.publications.journal.values = [
                faker.datatype.string()
              ];
            } else {
              suppliedFilters.publications.type.values = [
                faker.datatype.string()
              ];
            }

            const filters = [
              {
                nested: {
                  path: "publications",
                  query: {
                    bool: {
                      filter: [
                        {
                          match_phrase: {
                            [`${aggregation.aggregationField}.autocomplete_search`]:
                              {
                                query: filterValue,
                                slop: 5
                              }
                          }
                        }
                      ]
                    }
                  }
                }
              }
            ];
            const aggregationContainer =
              await aggregationContainerBuilderService.buildAggregationContainer(
                {
                  projectId,
                  projectFeatures,
                  userId,
                  query,
                  filterField,
                  filterValue,
                  suppliedFilters
                },
                LANGUAGE_DETECTOR_ENGLISH,
                filters,
                featureFlagsValue,
                [],
                undefined
              );

            expect(aggregationContainer).toMatchObject(
              expect.objectContaining({
                nested: {
                  nested: {
                    path: "publications"
                  },
                  aggs: {
                    filtered_matching: {
                      filter: {
                        bool: {
                          filter: [
                            {
                              match_phrase: {
                                [`${aggregation.aggregationField}.autocomplete_search`]:
                                  {
                                    query: filterValue,
                                    slop: 5
                                  }
                              }
                            }
                          ]
                        }
                      },
                      aggs: {
                        matching: {
                          terms: {
                            field: `${aggregation.aggregationField}`,
                            order: SORT_BY_MATCHING_DOC_COUNT
                          },
                          aggs: {
                            matching: {
                              reverse_nested: {}
                            }
                          }
                        }
                      }
                    }
                  }
                }
              })
            );
          }
        });
      });

      describe("multi-lang", () => {
        it('should use _cmn fields for publication aggregation fields when detected language is "cmn"', async () => {
          const parsedQueryTreeToElasticsearchQueriesService =
            createMockInstance(ParsedQueryTreeToElasticsearchQueriesService);

          const keywordFilterClauseBuilderService = createMockInstance(
            KeywordFilterClauseBuilderService
          );

          const aggregationContainerBuilderService =
            new AggregationContainerBuilderService(
              parsedQueryTreeToElasticsearchQueriesService,
              keywordFilterClauseBuilderService
            );

          const featureFlagsValue: any = mockFeatureFlagsValue();

          const aggregations = [
            {
              filterField:
                KeywordFilterAutocompleteFilterField.PUBLICATION_TYPE,
              aggregationField: "publications.type_cmn"
            },
            {
              filterField: KeywordFilterAutocompleteFilterField.JOURNAL_NAME,
              aggregationField: "publications.journalName_cmn"
            }
          ];

          for (const aggregation of aggregations) {
            const projectId = faker.datatype.string();
            const projectFeatures = {
              engagementsV2: faker.datatype.boolean()
            };
            const userId = faker.datatype.string();
            const query = undefined;
            const filterField = aggregation.filterField;
            const filterValue = COMPUTER_CMN;

            const suppliedFilters = generateFilters();

            const aggregationContainer =
              await aggregationContainerBuilderService.buildAggregationContainer(
                {
                  projectId,
                  projectFeatures,
                  userId,
                  query,
                  filterField,
                  filterValue,
                  suppliedFilters
                },
                LANGUAGE_DETECTOR_CMN,
                [],
                featureFlagsValue,
                [],
                undefined
              );

            expect(aggregationContainer).toMatchObject(
              expect.objectContaining({
                nested: {
                  nested: {
                    path: "publications"
                  },
                  aggs: {
                    filtered_matching: {
                      filter: expect.anything(),
                      aggs: {
                        matching: {
                          terms: {
                            field: aggregation.aggregationField,
                            order: SORT_BY_MATCHING_DOC_COUNT
                          },
                          aggs: expect.anything()
                        }
                      }
                    }
                  }
                }
              })
            );
          }
        });

        it('should use _cmn fields for publication aggregation fields when supplied language is "cmn" and filterValue is empty', async () => {
          const parsedQueryTreeToElasticsearchQueriesService =
            createMockInstance(ParsedQueryTreeToElasticsearchQueriesService);

          const featureFlagsValue: any = mockFeatureFlagsValue();

          const keywordFilterClauseBuilderService = createMockInstance(
            KeywordFilterClauseBuilderService
          );

          const aggregationContainerBuilderService =
            new AggregationContainerBuilderService(
              parsedQueryTreeToElasticsearchQueriesService,
              keywordFilterClauseBuilderService
            );

          const aggregations = [
            {
              filterField:
                KeywordFilterAutocompleteFilterField.PUBLICATION_TYPE,
              aggregationField: "publications.type_cmn"
            },
            {
              filterField: KeywordFilterAutocompleteFilterField.JOURNAL_NAME,
              aggregationField: "publications.journalName_cmn"
            }
          ];

          for (const aggregation of aggregations) {
            const projectId = faker.datatype.string();
            const projectFeatures = {
              engagementsV2: faker.datatype.boolean()
            };
            const userId = faker.datatype.string();
            const query = undefined;
            const filterField = aggregation.filterField;

            const suppliedFilters = generateFilters();

            const aggregationContainer =
              await aggregationContainerBuilderService.buildAggregationContainer(
                {
                  projectId,
                  projectFeatures,
                  userId,
                  query,
                  filterField,
                  suppliedFilters,
                  language: CHINESE
                },
                LANGUAGE_DETECTOR_ENGLISH,
                [],
                featureFlagsValue,
                [],
                undefined
              );

            expect(aggregationContainer).toMatchObject(
              expect.objectContaining({
                nested: {
                  nested: {
                    path: "publications"
                  },
                  aggs: {
                    filtered_matching: {
                      filter: expect.anything(),
                      aggs: {
                        matching: {
                          terms: {
                            field: aggregation.aggregationField,
                            exclude: "",
                            order: SORT_BY_MATCHING_DOC_COUNT
                          },
                          aggs: expect.anything()
                        }
                      }
                    }
                  }
                }
              })
            );
          }
        });

        it('should use _jpn fields for publication aggregation fields when detected language is "jpn"', async () => {
          const parsedQueryTreeToElasticsearchQueriesService =
            createMockInstance(ParsedQueryTreeToElasticsearchQueriesService);

          const featureFlagsValue: any = mockFeatureFlagsValue();

          const keywordFilterClauseBuilderService = createMockInstance(
            KeywordFilterClauseBuilderService
          );

          const aggregationContainerBuilderService =
            new AggregationContainerBuilderService(
              parsedQueryTreeToElasticsearchQueriesService,
              keywordFilterClauseBuilderService
            );

          const aggregations = [
            {
              filterField:
                KeywordFilterAutocompleteFilterField.PUBLICATION_TYPE,
              aggregationField: "publications.type_jpn"
            },
            {
              filterField: KeywordFilterAutocompleteFilterField.JOURNAL_NAME,
              aggregationField: "publications.journalName_jpn"
            }
          ];

          for (const aggregation of aggregations) {
            const projectId = faker.datatype.string();
            const projectFeatures = {
              engagementsV2: faker.datatype.boolean()
            };
            const userId = faker.datatype.string();
            const query = undefined;
            const filterField = aggregation.filterField;
            const filterValue = COMPUTER_JPN;

            const suppliedFilters = generateFilters();

            const aggregationContainer =
              await aggregationContainerBuilderService.buildAggregationContainer(
                {
                  projectId,
                  projectFeatures,
                  userId,
                  query,
                  filterField,
                  filterValue,
                  suppliedFilters
                },
                LANGUAGE_DETECTOR_JAPANESE,
                [],
                featureFlagsValue,
                [],
                undefined
              );

            expect(aggregationContainer).toMatchObject(
              expect.objectContaining({
                nested: {
                  nested: {
                    path: "publications"
                  },
                  aggs: {
                    filtered_matching: {
                      filter: expect.anything(),
                      aggs: {
                        matching: {
                          terms: {
                            field: aggregation.aggregationField,
                            exclude: undefined,
                            order: SORT_BY_MATCHING_DOC_COUNT
                          },
                          aggs: expect.anything()
                        }
                      }
                    }
                  }
                }
              })
            );
          }
        });

        it('should use _jpn fields for publication aggregation fields when supplied language is "jpn" and filterValue is empty', async () => {
          const parsedQueryTreeToElasticsearchQueriesService =
            createMockInstance(ParsedQueryTreeToElasticsearchQueriesService);

          const featureFlagsValue: any = mockFeatureFlagsValue();

          const keywordFilterClauseBuilderService = createMockInstance(
            KeywordFilterClauseBuilderService
          );

          const aggregationContainerBuilderService =
            new AggregationContainerBuilderService(
              parsedQueryTreeToElasticsearchQueriesService,
              keywordFilterClauseBuilderService
            );

          const aggregations = [
            {
              filterField:
                KeywordFilterAutocompleteFilterField.PUBLICATION_TYPE,
              aggregationField: "publications.type_jpn"
            },
            {
              filterField: KeywordFilterAutocompleteFilterField.JOURNAL_NAME,
              aggregationField: "publications.journalName_jpn"
            }
          ];

          for (const aggregation of aggregations) {
            const projectId = faker.datatype.string();
            const projectFeatures = {
              engagementsV2: faker.datatype.boolean()
            };
            const userId = faker.datatype.string();
            const query = undefined;
            const filterField = aggregation.filterField;

            const suppliedFilters = generateFilters();
            const aggregationContainer =
              await aggregationContainerBuilderService.buildAggregationContainer(
                {
                  projectId,
                  projectFeatures,
                  userId,
                  query,
                  filterField,
                  suppliedFilters,
                  language: JAPANESE
                },
                LANGUAGE_DETECTOR_ENGLISH,
                [],
                featureFlagsValue,
                [],
                undefined
              );

            expect(aggregationContainer).toMatchObject(
              expect.objectContaining({
                nested: {
                  nested: {
                    path: "publications"
                  },
                  aggs: {
                    filtered_matching: {
                      filter: expect.anything(),
                      aggs: {
                        matching: {
                          terms: {
                            field: aggregation.aggregationField,
                            exclude: "",
                            order: SORT_BY_MATCHING_DOC_COUNT
                          },
                          aggs: expect.anything()
                        }
                      }
                    }
                  }
                }
              })
            );
          }
        });
      });
    });

    describe("trials", () => {
      it("should not include 'include' regex when filterValue is an empty string", async () => {
        const featureFlagsValue: any = mockFeatureFlagsValue();
        const filters = [
          {
            nested: {
              path: "trials",
              query: {
                bool: {
                  filter: []
                }
              }
            }
          }
        ];
        const parsedQueryTreeToElasticsearchQueriesService = createMockInstance(
          ParsedQueryTreeToElasticsearchQueriesService
        );

        const keywordFilterClauseBuilderService = createMockInstance(
          KeywordFilterClauseBuilderService
        );

        const aggregationContainerBuilderService =
          new AggregationContainerBuilderService(
            parsedQueryTreeToElasticsearchQueriesService,
            keywordFilterClauseBuilderService
          );

        const aggregations = [
          {
            filterField: KeywordFilterAutocompleteFilterField.TRIAL_PHASE,
            aggregationField: "trials.phase_eng"
          },
          {
            filterField: KeywordFilterAutocompleteFilterField.TRIAL_STATUS,
            aggregationField: "trials.status_eng"
          },
          {
            filterField: KeywordFilterAutocompleteFilterField.TRIAL_STUDY_TYPE,

            aggregationField: "trials.type_eng"
          },
          {
            filterField: KeywordFilterAutocompleteFilterField.TRIAL_SPONSOR,

            aggregationField: "trials.sponsor_eng"
          },
          {
            filterField:
              KeywordFilterAutocompleteFilterField.TRIAL_SPONSOR_TYPE,
            aggregationField: "trials.sponsorType_eng"
          }
        ];

        for (const aggregation of aggregations) {
          const projectId = faker.datatype.string();
          const projectFeatures = {
            engagementsV2: faker.datatype.boolean()
          };
          const userId = faker.datatype.string();
          const query = undefined;
          const filterField = aggregation.filterField;
          const filterValue = "";

          const suppliedFilters = generateFilters();

          const aggregationContainer =
            await aggregationContainerBuilderService.buildAggregationContainer(
              {
                projectId,
                projectFeatures,
                userId,
                query,
                filterField,
                filterValue,
                suppliedFilters
              },
              LANGUAGE_DETECTOR_ENGLISH,
              filters,
              featureFlagsValue,
              [],
              undefined
            );

          expect(aggregationContainer).toMatchObject(
            expect.objectContaining({
              nested: {
                nested: {
                  path: "trials"
                },
                aggs: {
                  filtered_matching: {
                    filter: {
                      bool: {
                        filter: []
                      }
                    },
                    aggs: {
                      matching: {
                        terms: {
                          field: aggregation.aggregationField,
                          exclude: "",
                          order: SORT_BY_MATCHING_DOC_COUNT
                        },
                        aggs: {
                          matching: {
                            reverse_nested: {}
                          }
                        }
                      }
                    }
                  }
                }
              }
            })
          );
        }
      });

      it("should use reverse nested aggregation for trials filters when min amount is NOT supplied", async () => {
        const parsedQueryTreeToElasticsearchQueriesService = createMockInstance(
          ParsedQueryTreeToElasticsearchQueriesService
        );

        const featureFlagsValue: any = mockFeatureFlagsValue();

        const keywordFilterClauseBuilderService = createMockInstance(
          KeywordFilterClauseBuilderService
        );

        const aggregationContainerBuilderService =
          new AggregationContainerBuilderService(
            parsedQueryTreeToElasticsearchQueriesService,
            keywordFilterClauseBuilderService
          );

        const aggregations = [
          {
            filterField: KeywordFilterAutocompleteFilterField.TRIAL_PHASE,
            aggregationField: "trials.phase_eng"
          },
          {
            filterField: KeywordFilterAutocompleteFilterField.TRIAL_STATUS,
            aggregationField: "trials.status_eng"
          },
          {
            filterField: KeywordFilterAutocompleteFilterField.TRIAL_STUDY_TYPE,

            aggregationField: "trials.type_eng"
          },
          {
            filterField: KeywordFilterAutocompleteFilterField.TRIAL_SPONSOR,

            aggregationField: "trials.sponsor_eng"
          },
          {
            filterField:
              KeywordFilterAutocompleteFilterField.TRIAL_SPONSOR_TYPE,
            aggregationField: "trials.sponsorType_eng"
          }
        ];

        for (const aggregation of aggregations) {
          const projectId = faker.datatype.string();
          const projectFeatures = {
            engagementsV2: faker.datatype.boolean()
          };
          const userId = faker.datatype.string();
          const query = undefined;
          const filterField = aggregation.filterField;
          const filterValue = faker.random.alpha();

          const suppliedFilters = generateFilters();

          const filters = [
            {
              nested: {
                path: "trials",
                query: {
                  bool: {
                    filter: [
                      {
                        match_phrase: {
                          [`${aggregation.aggregationField}.autocomplete_search`]:
                            {
                              query: filterValue,
                              slop: 5
                            }
                        }
                      }
                    ]
                  }
                }
              }
            }
          ];
          const aggregationContainer =
            await aggregationContainerBuilderService.buildAggregationContainer(
              {
                projectId,
                projectFeatures,
                userId,
                query,
                filterField,
                filterValue,
                suppliedFilters
              },
              LANGUAGE_DETECTOR_ENGLISH,
              filters,
              featureFlagsValue,
              [],
              undefined
            );

          expect(aggregationContainer).toMatchObject(
            expect.objectContaining({
              nested: {
                nested: {
                  path: "trials"
                },
                aggs: {
                  filtered_matching: {
                    filter: {
                      bool: {
                        filter: [
                          {
                            match_phrase: {
                              [`${aggregation.aggregationField}.autocomplete_search`]:
                                {
                                  query: filterValue,
                                  slop: 5
                                }
                            }
                          }
                        ]
                      }
                    },
                    aggs: {
                      matching: {
                        terms: {
                          field: `${aggregation.aggregationField}`,
                          order: SORT_BY_MATCHING_DOC_COUNT
                        },
                        aggs: {
                          matching: {
                            reverse_nested: {}
                          }
                        }
                      }
                    }
                  }
                }
              }
            })
          );
        }
      });

      it("should use reverse nested aggregation for trials filters when min count is supplied", async () => {
        const featureFlagsValue: any = mockFeatureFlagsValue();

        const parsedQueryTreeToElasticsearchQueriesService = createMockInstance(
          ParsedQueryTreeToElasticsearchQueriesService
        );

        const minCount = faker.datatype.number();

        const keywordFilterClauseBuilderService = createMockInstance(
          KeywordFilterClauseBuilderService
        );

        const aggregationContainerBuilderService =
          new AggregationContainerBuilderService(
            parsedQueryTreeToElasticsearchQueriesService,
            keywordFilterClauseBuilderService
          );

        const aggregations = [
          {
            filterField: KeywordFilterAutocompleteFilterField.TRIAL_PHASE,
            aggregationField: "trials.phase_eng"
          },
          {
            filterField: KeywordFilterAutocompleteFilterField.TRIAL_STATUS,
            aggregationField: "trials.status_eng"
          },
          {
            filterField: KeywordFilterAutocompleteFilterField.TRIAL_STUDY_TYPE,

            aggregationField: "trials.type_eng"
          },
          {
            filterField: KeywordFilterAutocompleteFilterField.TRIAL_SPONSOR,

            aggregationField: "trials.sponsor_eng"
          },
          {
            filterField:
              KeywordFilterAutocompleteFilterField.TRIAL_SPONSOR_TYPE,
            aggregationField: "trials.sponsorType_eng"
          }
        ];

        for (const aggregation of aggregations) {
          const projectId = faker.datatype.string();
          const projectFeatures = {
            engagementsV2: faker.datatype.boolean()
          };
          const userId = faker.datatype.string();
          const query = undefined;
          const filterField = aggregation.filterField;
          const filterValue = faker.random.alpha();
          const suppliedFilters = generateFilters();
          suppliedFilters.trials.minCount.value = minCount;

          const filters: QueryDslQueryContainer[] = [
            {
              function_score: {
                query: {
                  nested: {
                    path: "trials",
                    score_mode: "sum",
                    query: {
                      bool: {
                        must: {
                          match_all: {}
                        },
                        filter: [
                          {
                            match_phrase: {
                              [`${aggregation.aggregationField}.autocomplete_search`]:
                                {
                                  query: filterValue,
                                  slop: 5
                                }
                            }
                          }
                        ]
                      }
                    }
                  }
                },
                min_score: minCount
              }
            }
          ];
          const aggregationContainer =
            await aggregationContainerBuilderService.buildAggregationContainer(
              {
                projectId,
                projectFeatures,
                userId,
                query,
                filterField,
                filterValue,
                suppliedFilters
              },
              LANGUAGE_DETECTOR_ENGLISH,
              filters,
              featureFlagsValue,
              [],
              undefined
            );

          expect(aggregationContainer).toMatchObject(
            expect.objectContaining({
              nested: {
                nested: {
                  path: "trials"
                },
                aggs: {
                  filtered_matching: {
                    filter: {
                      bool: {
                        filter: [
                          {
                            match_phrase: {
                              [`${aggregation.aggregationField}.autocomplete_search`]:
                                {
                                  query: filterValue,
                                  slop: 5
                                }
                            }
                          }
                        ]
                      }
                    },
                    aggs: {
                      matching: {
                        terms: {
                          field: `${aggregation.aggregationField}`
                        },
                        aggs: {
                          matching: {
                            reverse_nested: {}
                          }
                        }
                      }
                    }
                  }
                }
              }
            })
          );
        }
      });

      it("should use reverse nested aggregation for trials filters when max count is supplied", async () => {
        const featureFlagsValue: any = mockFeatureFlagsValue();

        const parsedQueryTreeToElasticsearchQueriesService = createMockInstance(
          ParsedQueryTreeToElasticsearchQueriesService
        );

        const maxCount = faker.datatype.number();

        const keywordFilterClauseBuilderService = createMockInstance(
          KeywordFilterClauseBuilderService
        );

        const aggregationContainerBuilderService =
          new AggregationContainerBuilderService(
            parsedQueryTreeToElasticsearchQueriesService,
            keywordFilterClauseBuilderService
          );

        const aggregations = [
          {
            filterField: KeywordFilterAutocompleteFilterField.TRIAL_PHASE,
            aggregationField: "trials.phase_eng"
          },
          {
            filterField: KeywordFilterAutocompleteFilterField.TRIAL_STATUS,
            aggregationField: "trials.status_eng"
          },
          {
            filterField: KeywordFilterAutocompleteFilterField.TRIAL_STUDY_TYPE,

            aggregationField: "trials.type_eng"
          },
          {
            filterField: KeywordFilterAutocompleteFilterField.TRIAL_SPONSOR,

            aggregationField: "trials.sponsor_eng"
          },
          {
            filterField:
              KeywordFilterAutocompleteFilterField.TRIAL_SPONSOR_TYPE,
            aggregationField: "trials.sponsorType_eng"
          }
        ];

        for (const aggregation of aggregations) {
          const projectId = faker.datatype.string();
          const projectFeatures = {
            engagementsV2: faker.datatype.boolean()
          };
          const userId = faker.datatype.string();
          const query = undefined;
          const filterField = aggregation.filterField;
          const filterValue = faker.random.alpha();
          const suppliedFilters = generateFilters();
          suppliedFilters.trials.maxCount!.value = maxCount;

          const aggregationContainer =
            await aggregationContainerBuilderService.buildAggregationContainer(
              {
                projectId,
                projectFeatures,
                userId,
                query,
                filterField,
                filterValue,
                suppliedFilters
              },
              LANGUAGE_DETECTOR_ENGLISH,
              [],
              featureFlagsValue,
              [],
              undefined
            );

          expect(aggregationContainer).toMatchObject(
            expect.objectContaining({
              nested: {
                nested: {
                  path: "trials"
                },
                aggs: {
                  filtered_matching: {
                    filter: {
                      bool: {
                        filter: []
                      }
                    },
                    aggs: {
                      matching: {
                        terms: {
                          field: `${aggregation.aggregationField}`
                        },
                        aggs: {
                          matching: {
                            reverse_nested: {}
                          }
                        }
                      }
                    }
                  }
                }
              }
            })
          );
        }
      });

      it("should use reverse nested aggregation for trials filters when min & max count is supplied", async () => {
        const featureFlagsValue: any = mockFeatureFlagsValue();
        const parsedQueryTreeToElasticsearchQueriesService = createMockInstance(
          ParsedQueryTreeToElasticsearchQueriesService
        );

        const maxCount = faker.datatype.number();
        const minCount = faker.datatype.number();

        const keywordFilterClauseBuilderService = createMockInstance(
          KeywordFilterClauseBuilderService
        );

        const aggregationContainerBuilderService =
          new AggregationContainerBuilderService(
            parsedQueryTreeToElasticsearchQueriesService,
            keywordFilterClauseBuilderService
          );

        const aggregations = [
          {
            filterField: KeywordFilterAutocompleteFilterField.TRIAL_PHASE,
            aggregationField: "trials.phase_eng"
          },
          {
            filterField: KeywordFilterAutocompleteFilterField.TRIAL_STATUS,
            aggregationField: "trials.status_eng"
          },
          {
            filterField: KeywordFilterAutocompleteFilterField.TRIAL_STUDY_TYPE,

            aggregationField: "trials.type_eng"
          },
          {
            filterField: KeywordFilterAutocompleteFilterField.TRIAL_SPONSOR,

            aggregationField: "trials.sponsor_eng"
          },
          {
            filterField:
              KeywordFilterAutocompleteFilterField.TRIAL_SPONSOR_TYPE,
            aggregationField: "trials.sponsorType_eng"
          }
        ];

        for (const aggregation of aggregations) {
          const projectId = faker.datatype.string();
          const projectFeatures = {
            engagementsV2: faker.datatype.boolean()
          };
          const userId = faker.datatype.string();
          const query = undefined;
          const filterField = aggregation.filterField;
          const filterValue = faker.random.alpha();
          const suppliedFilters = generateFilters();
          suppliedFilters.trials.maxCount!.value = maxCount;
          suppliedFilters.trials.minCount.value = minCount;

          const aggregationContainer =
            await aggregationContainerBuilderService.buildAggregationContainer(
              {
                projectId,
                projectFeatures,
                userId,
                query,
                filterField,
                filterValue,
                suppliedFilters
              },
              LANGUAGE_DETECTOR_ENGLISH,
              [],
              featureFlagsValue,
              [],
              undefined
            );

          expect(aggregationContainer).toMatchObject(
            expect.objectContaining({
              nested: {
                nested: {
                  path: "trials"
                },
                aggs: {
                  filtered_matching: {
                    filter: {
                      bool: {
                        filter: []
                      }
                    },
                    aggs: {
                      matching: {
                        terms: {
                          field: `${aggregation.aggregationField}`
                        },
                        aggs: {
                          matching: {
                            reverse_nested: {}
                          }
                        }
                      }
                    }
                  }
                }
              }
            })
          );
        }
      });

      it("should include global search term parsed query if supplied and no other trials filters were supplied", async () => {
        const featureFlagsValue: any = mockFeatureFlagsValue();

        const parsedQueryTreeToElasticsearchQueriesService = createMockInstance(
          ParsedQueryTreeToElasticsearchQueriesService
        );

        const pathSpecificQueries: Record<NestedPath, QueryDslQueryContainer> =
          {
            trials: generateMockElasticsearchTermQuery(),
            publications: generateMockElasticsearchTermQuery(),
            congress: generateMockElasticsearchTermQuery(),
            payments: generateMockElasticsearchTermQuery(),
            DRG_diagnoses: generateMockElasticsearchTermQuery(),
            DRG_procedures: generateMockElasticsearchTermQuery(),
            prescriptions: generateMockElasticsearchTermQuery(),
            ccsr: generateMockElasticsearchTermQuery(),
            ccsr_px: generateMockElasticsearchTermQuery()
          };

        const mockParseTreeToQueries =
          mockParseTreeToElasticsearchQueries(pathSpecificQueries);

        parsedQueryTreeToElasticsearchQueriesService.parse.mockImplementation(
          mockParseTreeToQueries
        );

        const keywordFilterClauseBuilderService = createMockInstance(
          KeywordFilterClauseBuilderService
        );

        const aggregationContainerBuilderService =
          new AggregationContainerBuilderService(
            parsedQueryTreeToElasticsearchQueriesService,
            keywordFilterClauseBuilderService
          );

        const aggregations = [
          {
            filterField: KeywordFilterAutocompleteFilterField.TRIAL_PHASE,
            aggregationField: "trials.phase_eng"
          },
          {
            filterField: KeywordFilterAutocompleteFilterField.TRIAL_STATUS,
            aggregationField: "trials.status_eng"
          },
          {
            filterField: KeywordFilterAutocompleteFilterField.TRIAL_STUDY_TYPE,

            aggregationField: "trials.type_eng"
          },
          {
            filterField: KeywordFilterAutocompleteFilterField.TRIAL_SPONSOR,

            aggregationField: "trials.sponsor_eng"
          },
          {
            filterField:
              KeywordFilterAutocompleteFilterField.TRIAL_SPONSOR_TYPE,
            aggregationField: "trials.sponsorType_eng"
          }
        ];

        for (const aggregation of aggregations) {
          const projectId = faker.datatype.string();
          const projectFeatures = {
            engagementsV2: faker.datatype.boolean()
          };
          const userId = faker.datatype.string();
          const query = faker.datatype.string();
          const filterField = aggregation.filterField;
          const filterValue = faker.random.alpha();

          const suppliedFilters = generateFilters();

          const queryWord1 = faker.datatype.string();
          const queryWord2 = faker.datatype.string();
          const queryWord3 = faker.datatype.string();

          const augmentedQuery = `${queryWord1} | ${queryWord2} | ${queryWord3}`;

          const aggregationContainer =
            await aggregationContainerBuilderService.buildAggregationContainer(
              {
                projectId,
                projectFeatures,
                userId,
                query,
                filterField,
                filterValue,
                suppliedFilters
              },
              LANGUAGE_DETECTOR_ENGLISH,
              [],
              featureFlagsValue,
              [],
              augmentedQuery
            );

          expect(aggregationContainer).toMatchObject(
            expect.objectContaining({
              nested: {
                nested: {
                  path: "trials"
                },
                aggs: {
                  filtered_matching: {
                    filter: {
                      bool: {
                        filter: [pathSpecificQueries.trials]
                      }
                    },
                    aggs: {
                      matching: {
                        terms: {
                          field: `${aggregation.aggregationField}`,
                          order: SORT_BY_MATCHING_DOC_COUNT
                        },
                        aggs: {
                          matching: {
                            reverse_nested: {}
                          }
                        }
                      }
                    }
                  }
                }
              }
            })
          );

          expect(
            parsedQueryTreeToElasticsearchQueriesService.parse
          ).toHaveBeenCalledWith(
            augmentedQuery,
            [
              "trials.officialTitle",
              "trials.briefTitle",
              "trials.conditions",
              "trials.interventions",
              "trials.keywords",
              "trials.summary"
            ],
            ENGLISH
          );
        }
      });

      describe("supplied trials filter values", () => {
        it("should be used to filter nested trials aggregation", async () => {
          const featureFlagsValue: any = mockFeatureFlagsValue();

          const parsedQueryTreeToElasticsearchQueriesService =
            createMockInstance(ParsedQueryTreeToElasticsearchQueriesService);

          const keywordFilterClauseBuilderService = createMockInstance(
            KeywordFilterClauseBuilderService
          );

          const aggregationContainerBuilderService =
            new AggregationContainerBuilderService(
              parsedQueryTreeToElasticsearchQueriesService,
              keywordFilterClauseBuilderService
            );

          const aggregations = [
            {
              filterField: KeywordFilterAutocompleteFilterField.TRIAL_PHASE,
              aggregationField: "trials.phase_eng"
            },
            {
              filterField: KeywordFilterAutocompleteFilterField.TRIAL_STATUS,
              aggregationField: "trials.status_eng"
            },
            {
              filterField:
                KeywordFilterAutocompleteFilterField.TRIAL_STUDY_TYPE,

              aggregationField: "trials.type_eng"
            },
            {
              filterField: KeywordFilterAutocompleteFilterField.TRIAL_SPONSOR,

              aggregationField: "trials.sponsor_eng"
            },
            {
              filterField:
                KeywordFilterAutocompleteFilterField.TRIAL_SPONSOR_TYPE,
              aggregationField: "trials.sponsorType_eng"
            }
          ];

          for (const aggregation of aggregations) {
            const projectId = faker.datatype.string();
            const projectFeatures = {
              engagementsV2: faker.datatype.boolean()
            };
            const userId = faker.datatype.string();
            const query = undefined;
            const filterField = aggregation.filterField;
            const filterValue = faker.random.alpha();
            const suppliedFilters = generateFilters();
            const expectedFilters: QueryDslQueryContainer[] = [
              {
                match_phrase: {
                  [`${aggregation.aggregationField}.autocomplete_search`]: {
                    query: filterValue,
                    slop: 5
                  }
                }
              }
            ];

            if (
              aggregation.filterField !==
              KeywordFilterAutocompleteFilterField.TRIAL_PHASE
            ) {
              suppliedFilters.trials.phase.values = [faker.datatype.string()];

              expectedFilters.push({
                terms: {
                  "trials.phase_eng": suppliedFilters.trials.phase.values
                }
              });
            }
            if (
              aggregation.filterField !==
              KeywordFilterAutocompleteFilterField.TRIAL_STATUS
            ) {
              suppliedFilters.trials.status.values = [faker.datatype.string()];

              expectedFilters.push({
                terms: {
                  "trials.status_eng": suppliedFilters.trials.status.values
                }
              });
            }
            if (
              aggregation.filterField !==
              KeywordFilterAutocompleteFilterField.TRIAL_STUDY_TYPE
            ) {
              suppliedFilters.trials.studyType.values = [
                faker.datatype.string()
              ];

              expectedFilters.push({
                terms: {
                  "trials.studyType_eng":
                    suppliedFilters.trials.studyType.values
                }
              });
            }
            if (
              aggregation.filterField !==
              KeywordFilterAutocompleteFilterField.TRIAL_SPONSOR
            ) {
              suppliedFilters.trials.sponsor.values = [faker.datatype.string()];

              expectedFilters.push({
                terms: {
                  "trials.sponsor_eng": suppliedFilters.trials.sponsor.values
                }
              });
            }
            if (
              aggregation.filterField !==
              KeywordFilterAutocompleteFilterField.TRIAL_SPONSOR_TYPE
            ) {
              suppliedFilters.trials.sponsorType.values = [
                faker.datatype.string()
              ];

              expectedFilters.push({
                terms: {
                  "trials.sponsorType_eng":
                    suppliedFilters.trials.sponsorType.values
                }
              });
            }

            const filters = [
              {
                nested: {
                  path: "trials",
                  query: {
                    bool: {
                      filter: expectedFilters
                    }
                  }
                }
              }
            ];
            const aggregationContainer =
              await aggregationContainerBuilderService.buildAggregationContainer(
                {
                  projectId,
                  projectFeatures,
                  userId,
                  query,
                  filterField,
                  filterValue,
                  suppliedFilters
                },
                LANGUAGE_DETECTOR_ENGLISH,
                filters,
                featureFlagsValue,
                [],
                undefined
              );

            expect(aggregationContainer).toMatchObject(
              expect.objectContaining({
                nested: {
                  nested: {
                    path: "trials"
                  },
                  aggs: {
                    filtered_matching: {
                      filter: {
                        bool: {
                          filter: expect.arrayContaining(expectedFilters)
                        }
                      },
                      aggs: {
                        matching: {
                          terms: {
                            field: `${aggregation.aggregationField}`,
                            order: SORT_BY_MATCHING_DOC_COUNT
                          },
                          aggs: {
                            matching: {
                              reverse_nested: {}
                            }
                          }
                        }
                      }
                    }
                  }
                }
              })
            );
          }
        });

        it("should NOT be used to filter nested trials aggregation when the filter value is of the same type that is being aggregated", async () => {
          const featureFlagsValue: any = mockFeatureFlagsValue();

          const parsedQueryTreeToElasticsearchQueriesService =
            createMockInstance(ParsedQueryTreeToElasticsearchQueriesService);

          const keywordFilterClauseBuilderService = createMockInstance(
            KeywordFilterClauseBuilderService
          );

          const aggregationContainerBuilderService =
            new AggregationContainerBuilderService(
              parsedQueryTreeToElasticsearchQueriesService,
              keywordFilterClauseBuilderService
            );

          const aggregations = [
            {
              filterField: KeywordFilterAutocompleteFilterField.TRIAL_PHASE,
              aggregationField: "trials.phase_eng"
            },
            {
              filterField: KeywordFilterAutocompleteFilterField.TRIAL_STATUS,
              aggregationField: "trials.status_eng"
            },
            {
              filterField:
                KeywordFilterAutocompleteFilterField.TRIAL_STUDY_TYPE,

              aggregationField: "trials.type_eng"
            },
            {
              filterField: KeywordFilterAutocompleteFilterField.TRIAL_SPONSOR,

              aggregationField: "trials.sponsor_eng"
            },
            {
              filterField:
                KeywordFilterAutocompleteFilterField.TRIAL_SPONSOR_TYPE,
              aggregationField: "trials.sponsorType_eng"
            }
          ];

          for (const aggregation of aggregations) {
            const projectId = faker.datatype.string();
            const projectFeatures = {
              engagementsV2: faker.datatype.boolean()
            };
            const userId = faker.datatype.string();
            const query = undefined;
            const filterField = aggregation.filterField;
            const filterValue = faker.random.alpha();
            const suppliedFilters = generateFilters();

            if (
              aggregation.filterField ===
              KeywordFilterAutocompleteFilterField.CONGRESS_NAME
            ) {
              suppliedFilters.congresses.name.values = [
                faker.datatype.string()
              ];
            } else if (
              aggregation.filterField ===
              KeywordFilterAutocompleteFilterField.CONGRESS_ORGANIZER
            ) {
              suppliedFilters.congresses.organizerName.values = [
                faker.datatype.string()
              ];
            }

            if (
              aggregation.filterField ===
              KeywordFilterAutocompleteFilterField.TRIAL_PHASE
            ) {
              suppliedFilters.trials.phase.values = [faker.datatype.string()];
            } else if (
              aggregation.filterField ===
              KeywordFilterAutocompleteFilterField.TRIAL_STATUS
            ) {
              suppliedFilters.trials.status.values = [faker.datatype.string()];
            } else if (
              aggregation.filterField ===
              KeywordFilterAutocompleteFilterField.TRIAL_STUDY_TYPE
            ) {
              suppliedFilters.trials.studyType.values = [
                faker.datatype.string()
              ];
            } else if (
              aggregation.filterField ===
              KeywordFilterAutocompleteFilterField.TRIAL_SPONSOR
            ) {
              suppliedFilters.trials.sponsor.values = [faker.datatype.string()];
            } else if (
              aggregation.filterField ===
              KeywordFilterAutocompleteFilterField.TRIAL_SPONSOR_TYPE
            ) {
              suppliedFilters.trials.sponsorType.values = [
                faker.datatype.string()
              ];
            }

            const filters = [
              {
                nested: {
                  path: "trials",
                  query: {
                    bool: {
                      filter: [
                        {
                          match_phrase: {
                            [`${aggregation.aggregationField}.autocomplete_search`]:
                              {
                                query: filterValue,
                                slop: 5
                              }
                          }
                        }
                      ]
                    }
                  }
                }
              }
            ];
            const aggregationContainer =
              await aggregationContainerBuilderService.buildAggregationContainer(
                {
                  projectId,
                  projectFeatures,
                  userId,
                  query,
                  filterField,
                  filterValue,
                  suppliedFilters
                },
                LANGUAGE_DETECTOR_ENGLISH,
                filters,
                featureFlagsValue,
                [],
                undefined
              );

            expect(aggregationContainer).toMatchObject(
              expect.objectContaining({
                nested: {
                  nested: {
                    path: "trials"
                  },
                  aggs: {
                    filtered_matching: {
                      filter: {
                        bool: {
                          filter: [
                            {
                              match_phrase: {
                                [`${aggregation.aggregationField}.autocomplete_search`]:
                                  {
                                    query: filterValue,
                                    slop: 5
                                  }
                              }
                            }
                          ]
                        }
                      },
                      aggs: {
                        matching: {
                          terms: {
                            field: `${aggregation.aggregationField}`,
                            order: SORT_BY_MATCHING_DOC_COUNT
                          },
                          aggs: {
                            matching: {
                              reverse_nested: {}
                            }
                          }
                        }
                      }
                    }
                  }
                }
              })
            );
          }
        });
      });
    });

    describe("multi-lang", () => {
      it('should use _cmn fields for specific aggregation fields when detected language is "cmn"', async () => {
        const parsedQueryTreeToElasticsearchQueriesService = createMockInstance(
          ParsedQueryTreeToElasticsearchQueriesService
        );

        const featureFlagsValue: any = mockFeatureFlagsValue();

        const keywordFilterClauseBuilderService = createMockInstance(
          KeywordFilterClauseBuilderService
        );

        const aggregationContainerBuilderService =
          new AggregationContainerBuilderService(
            parsedQueryTreeToElasticsearchQueriesService,
            keywordFilterClauseBuilderService
          );

        const aggregations = [
          {
            filterField: KeywordFilterAutocompleteFilterField.COUNTRY,
            aggregationField: "country_cmn"
          },
          {
            filterField: KeywordFilterAutocompleteFilterField.REGION,
            aggregationField: "state_cmn"
          },
          {
            filterField: KeywordFilterAutocompleteFilterField.CITY,
            aggregationField: "city_cmn"
          },
          {
            filterField: KeywordFilterAutocompleteFilterField.MEDICAL_SCHOOL,
            aggregationField: "studentInstitutionNames_cmn"
          }
        ];

        for (const aggregation of aggregations) {
          const projectId = faker.datatype.string();
          const projectFeatures = {
            engagementsV2: faker.datatype.boolean()
          };
          const userId = faker.datatype.string();
          const query = undefined;
          const filterField = aggregation.filterField;
          const filterValue = COMPUTER_CMN;

          const suppliedFilters = generateFilters();

          const aggregationContainer =
            await aggregationContainerBuilderService.buildAggregationContainer(
              {
                projectId,
                projectFeatures,
                userId,
                query,
                filterField,
                filterValue,
                suppliedFilters
              },
              LANGUAGE_DETECTOR_CMN,
              [],
              featureFlagsValue,
              [],
              undefined
            );

          expect(aggregationContainer).toMatchObject(
            expect.objectContaining({
              matching: {
                terms: {
                  field: aggregation.aggregationField,
                  include: `.*${filterValue}.*`
                }
              }
            })
          );
        }
      });

      it('should use _cmn fields for specific aggregation fields when supplied language is "cmn" and filterValue is empty', async () => {
        const parsedQueryTreeToElasticsearchQueriesService = createMockInstance(
          ParsedQueryTreeToElasticsearchQueriesService
        );

        const featureFlagsValue: any = mockFeatureFlagsValue();

        const keywordFilterClauseBuilderService = createMockInstance(
          KeywordFilterClauseBuilderService
        );

        const aggregationContainerBuilderService =
          new AggregationContainerBuilderService(
            parsedQueryTreeToElasticsearchQueriesService,
            keywordFilterClauseBuilderService
          );

        const aggregations = [
          {
            filterField: KeywordFilterAutocompleteFilterField.COUNTRY,
            aggregationField: "country_cmn"
          },
          {
            filterField: KeywordFilterAutocompleteFilterField.REGION,
            aggregationField: "state_cmn"
          },
          {
            filterField: KeywordFilterAutocompleteFilterField.CITY,
            aggregationField: "city_cmn"
          },
          {
            filterField: KeywordFilterAutocompleteFilterField.MEDICAL_SCHOOL,
            aggregationField: "studentInstitutionNames_cmn"
          }
        ];

        for (const aggregation of aggregations) {
          const projectId = faker.datatype.string();
          const projectFeatures = {
            engagementsV2: faker.datatype.boolean()
          };
          const userId = faker.datatype.string();
          const query = undefined;
          const filterField = aggregation.filterField;

          const suppliedFilters = generateFilters();

          const aggregationContainer =
            await aggregationContainerBuilderService.buildAggregationContainer(
              {
                projectId,
                projectFeatures,
                userId,
                query,
                filterField,
                suppliedFilters,
                language: CHINESE
              },
              LANGUAGE_DETECTOR_ENGLISH,
              [],
              featureFlagsValue,
              [],
              undefined
            );

          expect(aggregationContainer).toMatchObject(
            expect.objectContaining({
              matching: {
                terms: {
                  field: aggregation.aggregationField,
                  exclude: "",
                  shard_size: 1000
                }
              }
            })
          );
        }
      });

      it('should use _jpn fields for specific aggregation fields when detected language is "jpn"', async () => {
        const parsedQueryTreeToElasticsearchQueriesService = createMockInstance(
          ParsedQueryTreeToElasticsearchQueriesService
        );

        const featureFlagsValue: any = mockFeatureFlagsValue();

        const keywordFilterClauseBuilderService = createMockInstance(
          KeywordFilterClauseBuilderService
        );

        const aggregationContainerBuilderService =
          new AggregationContainerBuilderService(
            parsedQueryTreeToElasticsearchQueriesService,
            keywordFilterClauseBuilderService
          );

        const aggregations = [
          {
            filterField: KeywordFilterAutocompleteFilterField.COUNTRY,
            aggregationField: "country_jpn"
          },
          {
            filterField: KeywordFilterAutocompleteFilterField.REGION,
            aggregationField: "state_jpn"
          },
          {
            filterField: KeywordFilterAutocompleteFilterField.CITY,
            aggregationField: "city_jpn"
          },
          {
            filterField: KeywordFilterAutocompleteFilterField.MEDICAL_SCHOOL,
            aggregationField: "studentInstitutionNames_jpn"
          }
        ];

        for (const aggregation of aggregations) {
          const projectId = faker.datatype.string();
          const projectFeatures = {
            engagementsV2: faker.datatype.boolean()
          };
          const userId = faker.datatype.string();
          const query = undefined;
          const filterField = aggregation.filterField;
          const filterValue = COMPUTER_JPN;

          const suppliedFilters = generateFilters();

          const aggregationContainer =
            await aggregationContainerBuilderService.buildAggregationContainer(
              {
                projectId,
                projectFeatures,
                userId,
                query,
                filterField,
                filterValue,
                suppliedFilters
              },
              LANGUAGE_DETECTOR_JAPANESE,
              [],
              featureFlagsValue,
              [],
              undefined
            );

          expect(aggregationContainer).toMatchObject(
            expect.objectContaining({
              matching: {
                terms: {
                  field: aggregation.aggregationField,
                  include: `.*${filterValue}.*`
                }
              }
            })
          );
        }
      });

      it('should use _jpn fields for specific aggregation fields when supplied language is "jpn" and filterValue is empty', async () => {
        const parsedQueryTreeToElasticsearchQueriesService = createMockInstance(
          ParsedQueryTreeToElasticsearchQueriesService
        );

        const featureFlagsValue: any = mockFeatureFlagsValue();

        const keywordFilterClauseBuilderService = createMockInstance(
          KeywordFilterClauseBuilderService
        );

        const aggregationContainerBuilderService =
          new AggregationContainerBuilderService(
            parsedQueryTreeToElasticsearchQueriesService,
            keywordFilterClauseBuilderService
          );

        const aggregations = [
          {
            filterField: KeywordFilterAutocompleteFilterField.COUNTRY,
            aggregationField: "country_jpn"
          },
          {
            filterField: KeywordFilterAutocompleteFilterField.REGION,
            aggregationField: "state_jpn"
          },
          {
            filterField: KeywordFilterAutocompleteFilterField.CITY,
            aggregationField: "city_jpn"
          },
          {
            filterField: KeywordFilterAutocompleteFilterField.MEDICAL_SCHOOL,
            aggregationField: "studentInstitutionNames_jpn"
          }
        ];

        for (const aggregation of aggregations) {
          const projectId = faker.datatype.string();
          const projectFeatures = {
            engagementsV2: faker.datatype.boolean()
          };
          const userId = faker.datatype.string();
          const query = undefined;
          const filterField = aggregation.filterField;

          const suppliedFilters = generateFilters();

          const aggregationContainer =
            await aggregationContainerBuilderService.buildAggregationContainer(
              {
                projectId,
                projectFeatures,
                userId,
                query,
                filterField,
                suppliedFilters,
                language: JAPANESE
              },
              LANGUAGE_DETECTOR_ENGLISH,
              [],
              featureFlagsValue,
              [],
              undefined
            );

          expect(aggregationContainer).toMatchObject(
            expect.objectContaining({
              matching: {
                terms: {
                  field: aggregation.aggregationField,
                  exclude: "",
                  shard_size: 1000
                }
              }
            })
          );
        }
      });
    });

    describe("Society affiliations", () => {
      it("should not include 'include' regex when filterValue is an empty string", async () => {
        const featureFlagsValue: any = mockFeatureFlagsValue();
        const filters: QueryDslQueryContainer[] = [];
        const parsedQueryTreeToElasticsearchQueriesService = createMockInstance(
          ParsedQueryTreeToElasticsearchQueriesService
        );

        const keywordFilterClauseBuilderService = createMockInstance(
          KeywordFilterClauseBuilderService
        );

        const aggregationContainerBuilderService =
          new AggregationContainerBuilderService(
            parsedQueryTreeToElasticsearchQueriesService,
            keywordFilterClauseBuilderService
          );

        const aggregation = {
          filterField:
            KeywordFilterAutocompleteFilterField.SOCIETY_AFFILIATIONS,
          aggregationField: "affiliations.institution.name.keyword"
        };
        const projectId = faker.datatype.string();
        const projectFeatures = {
          engagementsV2: faker.datatype.boolean()
        };
        const userId = faker.datatype.string();
        const query = undefined;
        const filterField = aggregation.filterField;
        const filterValue = "";

        const suppliedFilters = generateFilters();

        const aggregationContainer =
          await aggregationContainerBuilderService.buildAggregationContainer(
            {
              projectId,
              projectFeatures,
              userId,
              query,
              filterField,
              filterValue,
              suppliedFilters
            },
            LANGUAGE_DETECTOR_ENGLISH,
            filters,
            featureFlagsValue,
            [],
            undefined
          );

        expect(aggregationContainer).toMatchObject(
          expect.objectContaining({
            nested: {
              nested: {
                path: "affiliations"
              },
              aggs: {
                filtered_matching: {
                  filter: {
                    bool: {
                      filter: [
                        {
                          term: {
                            ["affiliations.type"]: {
                              value: "Society Member"
                            }
                          }
                        }
                      ]
                    }
                  },
                  aggs: {
                    matching: {
                      terms: {
                        field: aggregation.aggregationField,
                        exclude: "",
                        order: SORT_BY_MATCHING_DOC_COUNT
                      },
                      aggs: {
                        matching: {
                          reverse_nested: {}
                        }
                      }
                    }
                  }
                }
              }
            }
          })
        );
      });
      it("should include 'include' regex when filterValue is not an empty string", async () => {
        const featureFlagsValue: any = mockFeatureFlagsValue();
        const filters: QueryDslQueryContainer[] = [];
        const parsedQueryTreeToElasticsearchQueriesService = createMockInstance(
          ParsedQueryTreeToElasticsearchQueriesService
        );

        const keywordFilterClauseBuilderService = createMockInstance(
          KeywordFilterClauseBuilderService
        );

        const aggregationContainerBuilderService =
          new AggregationContainerBuilderService(
            parsedQueryTreeToElasticsearchQueriesService,
            keywordFilterClauseBuilderService
          );

        const aggregation = {
          filterField:
            KeywordFilterAutocompleteFilterField.SOCIETY_AFFILIATIONS,
          aggregationField: "affiliations.institution.name.keyword"
        };
        const projectId = faker.datatype.string();
        const projectFeatures = {
          engagementsV2: faker.datatype.boolean()
        };
        const userId = faker.datatype.string();
        const query = undefined;
        const filterField = aggregation.filterField;
        const filterValue = faker.datatype.string();

        const suppliedFilters = generateFilters();

        const aggregationContainer =
          await aggregationContainerBuilderService.buildAggregationContainer(
            {
              projectId,
              projectFeatures,
              userId,
              query,
              filterField,
              filterValue,
              suppliedFilters
            },
            LANGUAGE_DETECTOR_ENGLISH,
            filters,
            featureFlagsValue,
            [],
            undefined
          );

        expect(aggregationContainer).toMatchObject(
          expect.objectContaining({
            nested: {
              nested: {
                path: "affiliations"
              },
              aggs: {
                filtered_matching: {
                  filter: {
                    bool: {
                      filter: [
                        {
                          term: {
                            ["affiliations.type"]: {
                              value: "Society Member"
                            }
                          }
                        }
                      ]
                    }
                  },
                  aggs: {
                    matching: {
                      terms: {
                        field: aggregation.aggregationField,
                        include: expect.any(String),
                        order: SORT_BY_MATCHING_DOC_COUNT
                      },
                      aggs: {
                        matching: {
                          reverse_nested: {}
                        }
                      }
                    }
                  }
                }
              }
            }
          })
        );
      });
    });

    describe("locations", () => {
      it("should aggregate both matching locations and regions for all filters except postal code", async () => {
        const parsedQueryTreeToElasticsearchQueriesService = createMockInstance(
          ParsedQueryTreeToElasticsearchQueriesService
        );

        const featureFlagsValue: any = mockFeatureFlagsValue({
          enableLocationFilterRegionRollup: true
        });

        const keywordFilterClauseBuilderService = createMockInstance(
          KeywordFilterClauseBuilderService
        );

        const aggregationContainerBuilderService =
          new AggregationContainerBuilderService(
            parsedQueryTreeToElasticsearchQueriesService,
            keywordFilterClauseBuilderService
          );

        const aggregations = [
          {
            filterField: KeywordFilterAutocompleteFilterField.COUNTRY,
            aggregationFields: {
              filterLocationField: `addressesForHCPU.filters.country`,
              filterRegionField: `addressesForHCPU.filters.country_level_regions`,
              addressLocationField: `addressesForHCPU.country`,
              addressRegionField: `addressesForHCPU.country_level_regions`
            }
          },
          {
            filterField: KeywordFilterAutocompleteFilterField.REGION,
            aggregationFields: {
              filterLocationField: `addressesForHCPU.filters.region`,
              filterRegionField: `addressesForHCPU.filters.state_level_regions`,
              addressLocationField: `addressesForHCPU.region`,
              addressRegionField: `addressesForHCPU.state_level_regions`
            }
          },
          {
            filterField: KeywordFilterAutocompleteFilterField.CITY,
            aggregationFields: {
              filterLocationField: `addressesForHCPU.filters.city`,
              filterRegionField: `addressesForHCPU.filters.city_level_regions`,
              addressLocationField: `addressesForHCPU.city`,
              addressRegionField: `addressesForHCPU.city_level_regions`
            }
          }
        ];

        for (const aggregation of aggregations) {
          const projectId = faker.datatype.string();
          const projectFeatures = {
            engagementsV2: faker.datatype.boolean()
          };
          const userId = faker.datatype.string();
          const query = undefined;
          const filterField = aggregation.filterField;
          const filterValue = faker.random.alpha();

          const suppliedFilters = generateFilters();

          const filters = [
            {
              nested: {
                path: "addressesForHCPU",
                query: {
                  bool: {
                    filter: [
                      {
                        bool: {
                          should: [
                            {
                              match_phrase_prefix: {
                                [aggregation.aggregationFields
                                  .addressLocationField]: {
                                  query: filterValue
                                }
                              }
                            },
                            {
                              match_phrase_prefix: {
                                [aggregation.aggregationFields
                                  .addressRegionField]: {
                                  query: filterValue
                                }
                              }
                            }
                          ]
                        }
                      }
                    ]
                  }
                }
              }
            }
          ];

          keywordFilterClauseBuilderService.buildGeoRollupLocationFilters.mockReturnValue(
            filters
          );

          const aggregationContainer =
            await aggregationContainerBuilderService.buildAggregationContainer(
              {
                projectId,
                projectFeatures,
                userId,
                query,
                filterField,
                filterValue,
                suppliedFilters
              },
              LANGUAGE_DETECTOR_ENGLISH,
              filters,
              featureFlagsValue,
              [],
              undefined
            );

          expect(aggregationContainer).toMatchObject(
            expect.objectContaining({
              nested: {
                nested: {
                  path: "addressesForHCPU"
                },
                aggs: {
                  filtered_matching: {
                    filter: {
                      bool: {
                        filter: filters[0].nested.query.bool.filter
                      }
                    },
                    aggs: {
                      filtered_matching_locations: {
                        filter: {
                          bool: {
                            filter:
                              filters[0].nested.query.bool.filter[0].bool
                                .should[0]
                          }
                        },
                        aggs: {
                          matching_locations: {
                            terms: {
                              field:
                                aggregation.aggregationFields
                                  .filterLocationField,
                              order: {
                                "matching.doc_count": "desc"
                              }
                            },
                            aggs: {
                              matching: {
                                reverse_nested: {}
                              },
                              regions_in: {
                                terms: {
                                  field:
                                    aggregation.aggregationFields
                                      .filterRegionField,
                                  exclude: ""
                                }
                              }
                            }
                          }
                        }
                      },
                      filtered_matching_regions: {
                        filter: {
                          bool: {
                            filter: [
                              filters[0].nested.query.bool.filter[0].bool
                                .should[1]
                            ]
                          }
                        },
                        aggs: {
                          matching_regions: {
                            terms: {
                              field:
                                aggregation.aggregationFields.filterRegionField,
                              order: {
                                "matching.doc_count": "desc"
                              },
                              include: expect.any(String)
                            },
                            aggs: {
                              matching: {
                                reverse_nested: {}
                              },
                              locations_in_region: {
                                terms: {
                                  field:
                                    aggregation.aggregationFields
                                      .filterLocationField,
                                  exclude: "",
                                  size: 200
                                },
                                aggs: {
                                  matching: {
                                    reverse_nested: {}
                                  }
                                }
                              }
                            }
                          }
                        }
                      }
                    }
                  }
                }
              }
            })
          );
        }
      });

      it("should aggregate matching locations for postal code for only US addresses", async () => {
        const parsedQueryTreeToElasticsearchQueriesService = createMockInstance(
          ParsedQueryTreeToElasticsearchQueriesService
        );

        const featureFlagsValue: any = mockFeatureFlagsValue({
          enableLocationFilterRegionRollup: true
        });

        const keywordFilterClauseBuilderService = createMockInstance(
          KeywordFilterClauseBuilderService
        );

        const aggregationContainerBuilderService =
          new AggregationContainerBuilderService(
            parsedQueryTreeToElasticsearchQueriesService,
            keywordFilterClauseBuilderService
          );

        const aggregations = [
          {
            filterField: KeywordFilterAutocompleteFilterField.POSTAL_CODE,
            aggregationFields: {
              filterLocationField: `addressesForHCPU.filters.postal_code`,
              filterRegionField: `addressesForHCPU.filters.city_level_regions`,
              addressLocationField: `addressesForHCPU.postal_code`,
              addressRegionField: `addressesForHCPU.city_level_regions`
            }
          }
        ];

        for (const aggregation of aggregations) {
          const projectId = faker.datatype.string();
          const projectFeatures = {
            engagementsV2: faker.datatype.boolean()
          };
          const userId = faker.datatype.string();
          const query = undefined;
          const filterField = aggregation.filterField;
          const filterValue = faker.random.alpha();

          const suppliedFilters = generateFilters();

          const filters = [
            {
              nested: {
                path: "addressesForHCPU",
                query: {
                  bool: {
                    filter: [
                      {
                        regexp: {
                          [aggregation.aggregationFields.filterLocationField]:
                            expandFilterValueToRegex(filterValue)
                        }
                      }
                    ]
                  }
                }
              }
            }
          ];

          keywordFilterClauseBuilderService.buildGeoRollupLocationFilters.mockReturnValue(
            filters
          );

          const aggregationContainer =
            await aggregationContainerBuilderService.buildAggregationContainer(
              {
                projectId,
                projectFeatures,
                userId,
                query,
                filterField,
                filterValue,
                suppliedFilters
              },
              LANGUAGE_DETECTOR_ENGLISH,
              filters,
              featureFlagsValue,
              [],
              undefined
            );

          expect(aggregationContainer).toMatchObject(
            expect.objectContaining({
              nested: {
                nested: {
                  path: "addressesForHCPU"
                },
                aggs: {
                  filtered_matching: {
                    filter: {
                      bool: {
                        filter: [
                          {
                            regexp: {
                              [aggregation.aggregationFields
                                .filterLocationField]:
                                expandFilterValueToRegex(filterValue)
                            }
                          }
                        ]
                      }
                    },
                    aggs: {
                      filtered_matching_locations: {
                        filter: {
                          bool: {
                            filter: []
                          }
                        },
                        aggs: {
                          matching_locations: {
                            terms: {
                              field:
                                aggregation.aggregationFields
                                  .filterLocationField,
                              order: {
                                "matching.doc_count": "desc"
                              },
                              include: "us\\|.*"
                            },
                            aggs: {
                              matching: {
                                reverse_nested: {}
                              },
                              regions_in: {
                                terms: {
                                  field:
                                    aggregation.aggregationFields
                                      .filterRegionField,
                                  exclude: ""
                                }
                              }
                            }
                          }
                        }
                      }
                    }
                  }
                }
              }
            })
          );
        }
      });
    });
  });
});
