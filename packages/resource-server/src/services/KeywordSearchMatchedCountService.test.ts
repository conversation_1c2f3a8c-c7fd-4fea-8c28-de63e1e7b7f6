import {
  QueryDslFunctionBoostMode,
  SearchHit,
  SearchInnerHitsResult,
  QueryDslQueryContainer,
  SearchRequest,
  SearchResponse,
  SearchTotalHits
} from "@elastic/elasticsearch/lib/api/types";
import { faker } from "@faker-js/faker";
import {
  createMockInstance,
  generateKeywordSearchFeatureFlags
} from "../util/TestUtils";
import { ConfigService } from "./ConfigService";
import { ElasticSearchService } from "./ElasticSearchService";
import {
  AssetToESMatchedCountQuery,
  AssetToMatchedCount,
  KeywordSearchMatchedCountService,
  SORT_TIEBREAKER_FOR_INDICATIONS
} from "./KeywordSearchMatchedCountService";
import {
  HCPDocument,
  RootFieldsForScoring
} from "./KeywordSearchResourceServiceRewrite";
import { RulesParserService } from "./RulesParserService";
import {
  generateFilters,
  generateKeywordSearchInput
} from "./KeywordSearchResourceServiceRewrite.test";
import {
  RuleFieldEnum,
  RuleOperatorEnum,
  RuleTypeEnum
} from "@h1nyc/search-sdk";
import { KeywordFilterClauseBuilderService } from "./KeywordFilterClauseBuilderService";

function generateMockElasticsearchHit(
  docId: string = faker.datatype.uuid(),
  id: string = faker.datatype.string()
): SearchHit<HCPDocument> {
  return {
    _index: faker.datatype.string(),
    _id: docId,
    _score: faker.datatype.number(),
    _source: {
      id,
      h1dn_id: faker.datatype.string(),
      name_eng: `${faker.name.firstName()} ${faker.name.lastName()}`,
      name_cmn: `${faker.name.firstName()} ${faker.name.lastName()}`,
      name_jpn: `${faker.name.firstName()} ${faker.name.lastName()}`,
      firstName_eng: faker.name.firstName(),
      firstName_cmn: faker.name.firstName(),
      firstName_jpn: faker.name.firstName(),
      middleName_eng: faker.name.middleName(),
      middleName_cmn: faker.name.middleName(),
      middleName_jpn: faker.name.middleName(),
      lastName_eng: faker.name.lastName(),
      lastName_cmn: faker.name.lastName(),
      lastName_jpn: faker.name.lastName(),
      designations: [faker.datatype.string(), faker.datatype.string()],
      emails: [faker.datatype.string(), faker.datatype.string()],
      specialty_eng: [faker.commerce.department(), faker.commerce.department()],
      specialty_cmn: [faker.commerce.department(), faker.commerce.department()],
      specialty_jpn: [faker.commerce.department(), faker.commerce.department()],
      citationTotal: faker.datatype.number(),
      congressCount: faker.datatype.number(),
      paymentTotal: faker.datatype.number(),
      referralsSentCount: faker.datatype.number(),
      referralsReceivedCount: faker.datatype.number(),
      microBloggingTotal: faker.datatype.number(),
      trialCount: faker.datatype.number(),
      trialEnrollmentRate: faker.datatype.number(),
      publicationCount: faker.datatype.number(),
      presentWorkInstitutionCount: faker.datatype.number(),
      totalWorks: faker.datatype.number(),
      DRG_diagnosesCount: faker.datatype.number(),
      DRG_proceduresCount: faker.datatype.number(),
      num_prescriptions: faker.datatype.number(),
      patientsDiversityCount: faker.datatype.number(),
      patientsDiversity: [],
      patientsDiversityPercentile: faker.datatype.number(),
      patientsDiversityRatio: {
        asian: faker.datatype.number(),
        pacificIslander: faker.datatype.number(),
        asianPacificIslander: faker.datatype.number(),
        blackNonHispanic: faker.datatype.number(),
        hispanic: faker.datatype.number(),
        whiteNonHispanic: faker.datatype.number(),
        americanIndianOrAlaskaNative: faker.datatype.number(),
        mixedBrazil: faker.datatype.number(),
        indigenousBrazil: faker.datatype.number(),
        notIdentified: faker.datatype.number()
      },
      providerDiversity: [],
      totalPatientCount: faker.datatype.number(),
      projectIds: []
    }
  };
}

function generateMockElasticsearchResponse(
  hits: Array<SearchHit<HCPDocument>> = []
): SearchResponse<HCPDocument> {
  return {
    took: faker.datatype.number(),
    timed_out: false,
    _shards: {
      total: faker.datatype.number(),
      successful: faker.datatype.number(),
      skipped: faker.datatype.number(),
      failed: faker.datatype.number()
    },
    hits: {
      total: {
        value: faker.datatype.number(),
        relation: "eq"
      },
      max_score: faker.datatype.number(),
      hits
    }
  };
}

const BOOST_MODE_DEFAULT: QueryDslFunctionBoostMode = "multiply";

function generateMockInputShouldClauseWithQuery(
  field: string
): QueryDslQueryContainer {
  return {
    function_score: {
      query: {
        nested: {
          path: field,
          score_mode: "sum",
          inner_hits: {
            size: 10
          },
          query: {
            bool: {
              filter: []
            }
          }
        }
      },
      boost_mode: BOOST_MODE_DEFAULT,
      functions: [
        {
          weight: faker.datatype.number()
        }
      ]
    }
  };
}

function generateMockInputBooleanQuery(field: string): QueryDslQueryContainer {
  return {
    nested: {
      path: field,
      score_mode: "sum",
      inner_hits: {
        size: 10
      },
      query: {}
    }
  };
}

function generateMockInputShouldClauseWithoutQuery(field: string) {
  return {
    function_score: {
      functions: [
        {
          field_value_factor: {
            field: field,
            factor: faker.datatype.number(),
            missing: 0
          }
        }
      ]
    }
  };
}

function generateMockInputClaimsShouldClauseWithRegionFilter(
  field: string
): QueryDslQueryContainer {
  return {
    bool: {
      must: [
        {
          function_score: {
            query: {
              nested: {
                path: field,
                score_mode: "sum",
                inner_hits: {
                  size: 10
                },
                query: {}
              }
            },
            boost_mode: BOOST_MODE_DEFAULT,
            functions: [
              {
                weight: faker.datatype.number()
              }
            ]
          }
        }
      ],
      must_not: [
        {
          terms: {
            country_multi: ["Brazil"]
          }
        }
      ]
    }
  };
}

function generateMockSearchRequest(): SearchRequest {
  return {
    index: faker.datatype.string(),
    from: 0,
    size: faker.datatype.number(),
    track_total_hits: false,
    _source_includes: ["id"],
    query: {
      bool: {
        filter: generateMockProjectIdFilter(),
        must: generateMockInputBooleanQuery("citationTotal")
      }
    }
  };
}

function generateNestedFilter() {
  return {
    nested: {
      path: "publications",
      query: {
        bool: {
          filter: [
            {
              terms: {
                "publications.type_eng": ["Review"]
              }
            }
          ]
        }
      }
    }
  };
}

function generateMockProjectIdFilter(): QueryDslQueryContainer {
  return {
    term: {
      projectIds: faker.datatype.string()
    }
  };
}
function generateMockIndicationsInnerHit(): SearchInnerHitsResult {
  return {
    hits: {
      total: {
        value: 1,
        relation: "eq"
      },
      hits: [
        {
          _index: faker.datatype.string(),
          _id: faker.datatype.string(),
          _nested: {
            field: "indications",
            offset: 4
          },
          _score: faker.datatype.number(),
          _source: {},
          fields: {
            ["indications.indication.keyword"]: [faker.datatype.string()]
          }
        }
      ]
    }
  };
}

function generateMockFilterInput(): QueryDslQueryContainer[] {
  return [generateMockProjectIdFilter(), generateNestedFilter()];
}

describe("KeywordSearchMatchedCountService", () => {
  it("checks if a filter is a nested filter", () => {
    const configService = createMockInstance(ConfigService);
    configService.elasticPeopleIndex = faker.datatype.string();

    const elasticSearchService = createMockInstance(ElasticSearchService);
    elasticSearchService.query.mockResolvedValue(
      generateMockElasticsearchResponse()
    );
    const keywordFilterClauseBuilderService = createMockInstance(
      KeywordFilterClauseBuilderService
    );

    const keywordSearchMatchedCountService =
      new KeywordSearchMatchedCountService(
        configService,
        elasticSearchService,
        keywordFilterClauseBuilderService
      );
    const nestedFilter = generateNestedFilter();
    const projectIdFilter = generateMockProjectIdFilter();

    expect(
      keywordSearchMatchedCountService.isNotNestedFilter(nestedFilter)
    ).toEqual(false);
    expect(
      keywordSearchMatchedCountService.isNotNestedFilter(projectIdFilter)
    ).toEqual(true);
  });

  it("removes nested filters from given set of input filters", () => {
    const configService = createMockInstance(ConfigService);
    configService.elasticPeopleIndex = faker.datatype.string();

    const elasticSearchService = createMockInstance(ElasticSearchService);
    elasticSearchService.query.mockResolvedValue(
      generateMockElasticsearchResponse()
    );
    const keywordFilterClauseBuilderService = createMockInstance(
      KeywordFilterClauseBuilderService
    );

    const keywordSearchMatchedCountService =
      new KeywordSearchMatchedCountService(
        configService,
        elasticSearchService,
        keywordFilterClauseBuilderService
      );
    const projectIdFilter = generateMockProjectIdFilter();
    const nestedFilter = generateNestedFilter();
    const filters: QueryDslQueryContainer[] = [projectIdFilter, nestedFilter];

    expect(
      keywordSearchMatchedCountService.removeNestedFilters(filters)
    ).toEqual([projectIdFilter]);
  });

  it("builds matchedCount ES query", () => {
    const configService = createMockInstance(ConfigService);
    configService.elasticPeopleIndex = faker.datatype.string();

    const elasticSearchService = createMockInstance(ElasticSearchService);
    elasticSearchService.query.mockResolvedValue(
      generateMockElasticsearchResponse()
    );
    const keywordFilterClauseBuilderService = createMockInstance(
      KeywordFilterClauseBuilderService
    );

    const keywordSearchMatchedCountService =
      new KeywordSearchMatchedCountService(
        configService,
        elasticSearchService,
        keywordFilterClauseBuilderService
      );

    const filters = generateMockFilterInput();
    const pageSize = faker.datatype.number();

    expect(
      keywordSearchMatchedCountService.buildMatchedCountQuery(
        generateMockInputBooleanQuery("citationTotal"),
        filters,
        pageSize
      )
    ).toEqual({
      index: configService.elasticPeopleIndex,
      from: 0,
      size: pageSize,
      track_total_hits: false,
      _source_includes: ["id"],
      query: {
        bool: {
          filter: filters,
          must: {
            nested: expect.objectContaining({
              path: "citationTotal",
              inner_hits: expect.objectContaining({
                size: 0
              })
            })
          }
        }
      }
    });
  });

  it("checks matched count ES queries for different assets when all function queries contains query field", () => {
    const configService = createMockInstance(ConfigService);
    configService.elasticPeopleIndex = faker.datatype.string();

    const elasticSearchService = createMockInstance(ElasticSearchService);
    elasticSearchService.query.mockResolvedValue(
      generateMockElasticsearchResponse()
    );
    const keywordFilterClauseBuilderService = createMockInstance(
      KeywordFilterClauseBuilderService
    );

    const keywordSearchMatchedCountService =
      new KeywordSearchMatchedCountService(
        configService,
        elasticSearchService,
        keywordFilterClauseBuilderService
      );

    const assetToShouldClause: Map<
      RootFieldsForScoring,
      {
        isEmptyClauseQuery: boolean;
        query: QueryDslQueryContainer;
      }
    > = new Map();

    const citationShouldClause =
      generateMockInputShouldClauseWithQuery("citationTotal");
    const microBloggingShouldClause =
      generateMockInputShouldClauseWithQuery("microBloggingTotal");
    const diagnosesShouldClause =
      generateMockInputShouldClauseWithQuery("DRG_diagnosesCount");
    const proceduresShouldClause = generateMockInputShouldClauseWithQuery(
      "DRG_proceduresCount"
    );
    const prescriptionsShouldClause =
      generateMockInputShouldClauseWithQuery("prescriptions");
    const paymentsShouldClause =
      generateMockInputShouldClauseWithQuery("paymentTotal");
    const trialsShouldClause = generateMockInputShouldClauseWithQuery("trials");
    const ccsrMustClause = generateMockInputShouldClauseWithQuery("ccsr");

    assetToShouldClause.set("citationTotal", {
      isEmptyClauseQuery: false,
      query: citationShouldClause
    });
    assetToShouldClause.set("microBloggingTotal", {
      isEmptyClauseQuery: false,
      query: microBloggingShouldClause
    });
    assetToShouldClause.set("DRG_diagnosesCount", {
      isEmptyClauseQuery: false,
      query: diagnosesShouldClause
    });
    assetToShouldClause.set("DRG_proceduresCount", {
      isEmptyClauseQuery: false,
      query: proceduresShouldClause
    });
    assetToShouldClause.set("num_prescriptions", {
      isEmptyClauseQuery: false,
      query: prescriptionsShouldClause
    });
    assetToShouldClause.set("paymentTotal", {
      isEmptyClauseQuery: false,
      query: paymentsShouldClause
    });
    assetToShouldClause.set("trialCount", {
      isEmptyClauseQuery: false,
      query: trialsShouldClause
    });

    assetToShouldClause.set("ccsr", {
      isEmptyClauseQuery: false,
      query: ccsrMustClause
    });

    const projectIdFilter = generateMockProjectIdFilter();
    const filters: QueryDslQueryContainer[] = [projectIdFilter];

    const hit1 = generateMockElasticsearchHit();
    const hit2 = generateMockElasticsearchHit();
    const hits = [hit1, hit2];

    const docIds: string[] = [
      hit1._id ?? faker.datatype.string(),
      hit2._id ?? faker.datatype.string()
    ];

    const pageSize = docIds.length;

    const matchedCountQueries: Array<AssetToESMatchedCountQuery> =
      keywordSearchMatchedCountService.collectSearchCardMatchedCountsQueries(
        hits,
        filters,
        assetToShouldClause
      );

    expect(matchedCountQueries.length).toEqual(11);
    expect(matchedCountQueries).toEqual([
      {
        field: "l1_indications",
        query: {
          index: configService.elasticPeopleIndex,
          from: 0,
          size: pageSize,
          track_total_hits: false,
          _source_includes: ["id"],
          query: {
            bool: {
              filter: expect.arrayContaining([
                ...filters,
                {
                  nested: {
                    path: "indications",
                    query: {
                      bool: {
                        filter: [
                          {
                            terms: {
                              ["indications.indicationType"]: ["L1"]
                            }
                          },
                          {
                            terms: {
                              ["indications.indicationSource"]: ["all"]
                            }
                          }
                        ]
                      }
                    },
                    inner_hits: {
                      _source: false,
                      sort: SORT_TIEBREAKER_FOR_INDICATIONS,
                      size: 1000,
                      name: "l1_indications",
                      docvalue_fields: ["indications.indication.keyword"]
                    }
                  }
                }
              ]),
              must: {
                match_all: {}
              }
            }
          }
        }
      },
      {
        field: "l3_indications",
        query: {
          index: configService.elasticPeopleIndex,
          from: 0,
          size: pageSize,
          track_total_hits: false,
          _source_includes: ["id"],
          query: {
            bool: {
              filter: expect.arrayContaining([
                ...filters,
                {
                  nested: {
                    path: "indications",
                    query: {
                      function_score: {
                        query: {
                          bool: {
                            filter: [
                              {
                                terms: {
                                  ["indications.indicationType"]: ["L3"]
                                }
                              },
                              {
                                terms: {
                                  ["indications.indicationSource"]: ["all"]
                                }
                              }
                            ]
                          }
                        },
                        boost_mode: "replace",
                        functions: [
                          {
                            field_value_factor: {
                              field: "indications.indicationScore",
                              missing: 0
                            }
                          }
                        ]
                      }
                    },
                    inner_hits: {
                      _source: false,
                      sort: SORT_TIEBREAKER_FOR_INDICATIONS,
                      size: 5,
                      name: "l3_indications",
                      docvalue_fields: ["indications.indication.keyword"]
                    }
                  }
                }
              ]),
              must: {
                match_all: {}
              }
            }
          }
        }
      },
      {
        field: "citations",
        query: {
          index: configService.elasticPeopleIndex,
          from: 0,
          size: pageSize,
          track_total_hits: false,
          _source_includes: ["id"],
          query: {
            bool: {
              filter: filters,
              must: {
                nested: expect.objectContaining({
                  path: "citationTotal",
                  inner_hits: expect.objectContaining({
                    size: 0
                  })
                })
              }
            }
          }
        }
      },
      {
        field: "microBlogging",
        query: {
          index: configService.elasticPeopleIndex,
          from: 0,
          size: pageSize,
          track_total_hits: false,
          _source_includes: ["id"],
          query: {
            bool: {
              filter: filters,
              must: {
                nested: expect.objectContaining({
                  path: "microBloggingTotal",
                  inner_hits: expect.objectContaining({
                    size: 0
                  })
                })
              }
            }
          }
        }
      },
      {
        field: "diagnoses_amount",
        query: {
          index: configService.elasticPeopleIndex,
          from: 0,
          size: pageSize,
          track_total_hits: false,
          _source_includes: ["id"],
          query: {
            bool: {
              filter: filters,
              must: {
                nested: expect.objectContaining({
                  path: "DRG_diagnosesCount",
                  inner_hits: expect.objectContaining({
                    size: 0
                  })
                })
              }
            }
          }
        }
      },
      {
        field: "ccsr_amount",
        query: {
          index: configService.elasticPeopleIndex,
          from: 0,
          size: pageSize,
          track_total_hits: false,
          _source_includes: ["id"],
          query: {
            bool: {
              filter: filters,
              must: {
                nested: expect.objectContaining({
                  path: "ccsr",
                  inner_hits: expect.objectContaining({
                    size: 0
                  })
                })
              }
            }
          }
        }
      },
      {
        field: "procedures_amount",
        query: {
          index: configService.elasticPeopleIndex,
          from: 0,
          size: pageSize,
          track_total_hits: false,
          _source_includes: ["id"],
          query: {
            bool: {
              filter: filters,
              must: {
                nested: expect.objectContaining({
                  path: "DRG_proceduresCount",
                  inner_hits: expect.objectContaining({
                    size: 0
                  })
                })
              }
            }
          }
        }
      },
      {
        field: "prescriptions_amount",
        query: {
          index: configService.elasticPeopleIndex,
          from: 0,
          size: pageSize,
          track_total_hits: false,
          _source_includes: ["id"],
          query: {
            bool: {
              filter: filters,
              must: {
                nested: expect.objectContaining({
                  path: "prescriptions",
                  inner_hits: expect.objectContaining({
                    size: 0
                  })
                })
              }
            }
          }
        }
      },
      {
        field: "payments",
        query: {
          index: configService.elasticPeopleIndex,
          from: 0,
          size: pageSize,
          track_total_hits: false,
          _source_includes: ["id"],
          query: {
            bool: {
              filter: filters,
              must: {
                nested: expect.objectContaining({
                  path: "paymentTotal",
                  inner_hits: expect.objectContaining({
                    size: 0
                  })
                })
              }
            }
          }
        }
      },
      {
        field: "trial_ongoing_count",
        query: {
          index: configService.elasticPeopleIndex,
          from: 0,
          size: pageSize,
          track_total_hits: false,
          _source_includes: ["id"],
          query: {
            bool: {
              filter: filters,
              must: {
                nested: expect.objectContaining({
                  path: "trials",
                  inner_hits: expect.objectContaining({
                    size: 0
                  }),
                  query: {
                    bool: {
                      filter: [
                        {
                          terms: {
                            "trials.status_eng": [
                              "Not yet recruiting",
                              "Recruiting",
                              "Enrolling by invitation",
                              "Active, not recruiting",
                              "Active"
                            ]
                          }
                        }
                      ]
                    }
                  }
                })
              }
            }
          }
        }
      },
      {
        field: "trial_actively_recruiting_count",
        query: {
          index: configService.elasticPeopleIndex,
          from: 0,
          size: pageSize,
          track_total_hits: false,
          _source_includes: ["id"],
          query: {
            bool: {
              filter: filters,
              must: {
                nested: expect.objectContaining({
                  path: "trials",
                  inner_hits: expect.objectContaining({
                    size: 0
                  }),
                  query: {
                    bool: {
                      filter: [
                        {
                          terms: {
                            "trials.status_eng": [
                              "Recruiting",
                              "Enrolling by invitation"
                            ]
                          }
                        }
                      ]
                    }
                  }
                })
              }
            }
          }
        }
      }
    ]);
  });

  it("checks matched count ES queries for different assets when some function queries contains only function score", () => {
    const configService = createMockInstance(ConfigService);
    configService.elasticPeopleIndex = faker.datatype.string();
    const elasticSearchService = createMockInstance(ElasticSearchService);
    elasticSearchService.query.mockResolvedValue(
      generateMockElasticsearchResponse()
    );
    const keywordFilterClauseBuilderService = createMockInstance(
      KeywordFilterClauseBuilderService
    );

    const keywordSearchMatchedCountService =
      new KeywordSearchMatchedCountService(
        configService,
        elasticSearchService,
        keywordFilterClauseBuilderService
      );

    const assetToShouldClause: Map<
      RootFieldsForScoring,
      {
        isEmptyClauseQuery: boolean;
        query: QueryDslQueryContainer;
      }
    > = new Map();

    const citationShouldClause =
      generateMockInputShouldClauseWithQuery("citationTotal");
    const microBloggingShouldClause =
      generateMockInputShouldClauseWithQuery("microBloggingTotal");
    const diagnosesShouldClause =
      generateMockInputShouldClauseWithoutQuery("DRG_diagnosesCount");
    const proceduresShouldClause = generateMockInputShouldClauseWithoutQuery(
      "DRG_proceduresCount"
    );
    const prescriptionsShouldClause =
      generateMockInputShouldClauseWithoutQuery("prescriptions");
    const paymentsShouldClause =
      generateMockInputShouldClauseWithQuery("paymentTotal");
    const trialsShouldClause =
      generateMockInputShouldClauseWithoutQuery("trials");
    assetToShouldClause.set("citationTotal", {
      isEmptyClauseQuery: false,
      query: citationShouldClause
    });
    assetToShouldClause.set("microBloggingTotal", {
      isEmptyClauseQuery: false,
      query: microBloggingShouldClause
    });
    assetToShouldClause.set("DRG_diagnosesCount", {
      isEmptyClauseQuery: true,
      query: diagnosesShouldClause
    });
    assetToShouldClause.set("DRG_proceduresCount", {
      isEmptyClauseQuery: true,
      query: proceduresShouldClause
    });
    assetToShouldClause.set("num_prescriptions", {
      isEmptyClauseQuery: true,
      query: prescriptionsShouldClause
    });
    assetToShouldClause.set("paymentTotal", {
      isEmptyClauseQuery: false,
      query: paymentsShouldClause
    });
    assetToShouldClause.set("trialCount", {
      isEmptyClauseQuery: true,
      query: trialsShouldClause
    });

    const projectIdFilter = generateMockProjectIdFilter();
    const filters: QueryDslQueryContainer[] = [projectIdFilter];

    const hit1 = generateMockElasticsearchHit();
    const hit2 = generateMockElasticsearchHit();
    const hits = [hit1, hit2];

    const peopleIds: string[] = [
      hit1._source?.id ?? faker.datatype.string(),
      hit2._source?.id ?? faker.datatype.string()
    ];

    const pageSize = peopleIds.length;
    filters.push({
      terms: {
        id: [...peopleIds]
      }
    });

    const matchedCountQueries: Array<AssetToESMatchedCountQuery> =
      keywordSearchMatchedCountService.collectSearchCardMatchedCountsQueries(
        hits,
        filters,
        assetToShouldClause
      );

    expect(matchedCountQueries.length).toEqual(5);

    expect(matchedCountQueries).toEqual([
      {
        field: "l1_indications",
        query: {
          index: configService.elasticPeopleIndex,
          from: 0,
          size: pageSize,
          track_total_hits: false,
          _source_includes: ["id"],
          query: {
            bool: {
              filter: expect.arrayContaining([
                ...filters,
                {
                  nested: {
                    path: "indications",
                    query: {
                      bool: {
                        filter: [
                          {
                            terms: {
                              ["indications.indicationType"]: ["L1"]
                            }
                          },
                          {
                            terms: {
                              ["indications.indicationSource"]: ["all"]
                            }
                          }
                        ]
                      }
                    },
                    inner_hits: {
                      _source: false,
                      sort: SORT_TIEBREAKER_FOR_INDICATIONS,
                      size: 1000,
                      name: "l1_indications",
                      docvalue_fields: ["indications.indication.keyword"]
                    }
                  }
                }
              ]),
              must: {
                match_all: {}
              }
            }
          }
        }
      },
      {
        field: "l3_indications",
        query: {
          index: configService.elasticPeopleIndex,
          from: 0,
          size: pageSize,
          track_total_hits: false,
          _source_includes: ["id"],
          query: {
            bool: {
              filter: expect.arrayContaining([
                ...filters,
                {
                  nested: {
                    path: "indications",
                    query: {
                      function_score: {
                        query: {
                          bool: {
                            filter: [
                              {
                                terms: {
                                  ["indications.indicationType"]: ["L3"]
                                }
                              },
                              {
                                terms: {
                                  ["indications.indicationSource"]: ["all"]
                                }
                              }
                            ]
                          }
                        },
                        boost_mode: "replace",
                        functions: [
                          {
                            field_value_factor: {
                              field: "indications.indicationScore",
                              missing: 0
                            }
                          }
                        ]
                      }
                    },
                    inner_hits: {
                      _source: false,
                      sort: SORT_TIEBREAKER_FOR_INDICATIONS,
                      size: 5,
                      name: "l3_indications",
                      docvalue_fields: ["indications.indication.keyword"]
                    }
                  }
                }
              ]),
              must: {
                match_all: {}
              }
            }
          }
        }
      },
      {
        field: "citations",
        query: {
          index: configService.elasticPeopleIndex,
          from: 0,
          size: pageSize,
          track_total_hits: false,
          _source_includes: ["id"],
          query: {
            bool: {
              filter: filters,
              must: {
                nested: expect.objectContaining({
                  path: "citationTotal",
                  inner_hits: expect.objectContaining({
                    size: 0
                  })
                })
              }
            }
          }
        }
      },
      {
        field: "microBlogging",
        query: {
          index: configService.elasticPeopleIndex,
          from: 0,
          size: pageSize,
          track_total_hits: false,
          _source_includes: ["id"],
          query: {
            bool: {
              filter: filters,
              must: {
                nested: expect.objectContaining({
                  path: "microBloggingTotal",
                  inner_hits: expect.objectContaining({
                    size: 0
                  })
                })
              }
            }
          }
        }
      },
      {
        field: "payments",
        query: {
          index: configService.elasticPeopleIndex,
          from: 0,
          size: pageSize,
          track_total_hits: false,
          _source_includes: ["id"],
          query: {
            bool: {
              filter: filters,
              must: {
                nested: expect.objectContaining({
                  path: "paymentTotal",
                  inner_hits: expect.objectContaining({
                    size: 0
                  })
                })
              }
            }
          }
        }
      }
    ]);
  });

  it("checks mapped response for ES response for Matched count queries", () => {
    const configService = createMockInstance(ConfigService);
    configService.elasticPeopleIndex = faker.datatype.string();
    const elasticSearchService = createMockInstance(ElasticSearchService);
    const peopleId1 = "12345";
    const peopleId2 = "98765";
    const docId1 = faker.datatype.string();
    const docId2 = faker.datatype.string();

    const hits: Array<SearchHit<HCPDocument>> = [
      {
        _index: configService.elasticPeopleIndex,
        _id: docId1,
        _score: faker.datatype.number()
      },
      {
        _index: configService.elasticPeopleIndex,
        _id: docId2,
        _score: faker.datatype.number()
      }
    ];
    elasticSearchService.query.mockResolvedValue(
      generateMockElasticsearchResponse(hits)
    );
    const keywordFilterClauseBuilderService = createMockInstance(
      KeywordFilterClauseBuilderService
    );

    const keywordSearchMatchedCountService =
      new KeywordSearchMatchedCountService(
        configService,
        elasticSearchService,
        keywordFilterClauseBuilderService
      );

    const matchedCountQueries: Array<AssetToESMatchedCountQuery> = [];
    matchedCountQueries.push({
      field: "citations",
      query: generateMockSearchRequest()
    });
    matchedCountQueries.push({
      field: "payments",
      query: generateMockSearchRequest()
    });

    const matchedQueryResponses: SearchResponse<HCPDocument>[] = [];

    const citationHit = generateMockElasticsearchHit(docId1, peopleId1);
    const citationResponse = generateMockElasticsearchResponse([citationHit]);
    matchedQueryResponses.push(citationResponse);

    const paymentHit = generateMockElasticsearchHit(docId2, peopleId2);
    const paymentResponse = generateMockElasticsearchResponse([paymentHit]);
    matchedQueryResponses.push(paymentResponse);

    const docIds = [docId1, docId2];
    const peopleIdToAssetMatchedCount: Readonly<
      Record<string, AssetToMatchedCount>
    > =
      keywordSearchMatchedCountService.mapSearchResponsesToMatchedCountsResults(
        matchedCountQueries,
        matchedQueryResponses,
        docIds
      );

    expect(peopleIdToAssetMatchedCount).toEqual({
      [docId1]: { citations: citationHit._score, payments: 0 },
      [docId2]: { citations: 0, payments: paymentHit._score }
    });
  });

  it("checks l1 and l3 indications are present in response", async () => {
    const configService = createMockInstance(ConfigService);
    const elasticSearchService = createMockInstance(ElasticSearchService);
    const keywordFilterClauseBuilderService = createMockInstance(
      KeywordFilterClauseBuilderService
    );

    const keywordSearchMatchedCountService =
      new KeywordSearchMatchedCountService(
        configService,
        elasticSearchService,
        keywordFilterClauseBuilderService
      );

    const docId1 = faker.datatype.string();
    const docId2 = faker.datatype.string();

    const hitsForL1Indication: Array<SearchHit<HCPDocument>> = [
      {
        _index: configService.elasticPeopleIndex,
        _id: docId1,
        _score: faker.datatype.number(),
        inner_hits: {
          l1_indications: generateMockIndicationsInnerHit()
        }
      }
    ];
    const hitsForL3Indication: Array<SearchHit<HCPDocument>> = [
      {
        _index: configService.elasticPeopleIndex,
        _id: docId2,
        _score: faker.datatype.number(),
        inner_hits: {
          l3_indications: generateMockIndicationsInnerHit()
        }
      }
    ];
    const searchResponseForL1Indications =
      generateMockElasticsearchResponse(hitsForL1Indication);
    const searchResponseForL3Indications =
      generateMockElasticsearchResponse(hitsForL3Indication);

    elasticSearchService.query.mockResolvedValue(
      generateMockElasticsearchResponse(hitsForL1Indication)
    );

    const response =
      await keywordSearchMatchedCountService.createIndicationsResponse(
        [searchResponseForL1Indications, searchResponseForL3Indications],
        0,
        "l1_indications"
      );
    expect(response.get(docId1)).toEqual(
      hitsForL1Indication[0].inner_hits!.l1_indications.hits.hits.map(
        keywordSearchMatchedCountService.extractIndicationNameFromInnerHit
      )
    );
    expect(response.get(docId2)).toBeUndefined();

    const response2 =
      await keywordSearchMatchedCountService.createIndicationsResponse(
        [searchResponseForL1Indications, searchResponseForL3Indications],
        1,
        "l3_indications"
      );
    expect(response2.get(docId2)).toEqual(
      hitsForL3Indication[0].inner_hits!.l3_indications.hits.hits.map(
        keywordSearchMatchedCountService.extractIndicationNameFromInnerHit
      )
    );
    expect(response2.get(docId1)).toBeUndefined();
  });

  it("checks matched count ES queries for different assets when some claims queries contain region level filter", () => {
    const configService = createMockInstance(ConfigService);
    configService.elasticPeopleIndex = faker.datatype.string();
    const elasticSearchService = createMockInstance(ElasticSearchService);
    elasticSearchService.query.mockResolvedValue(
      generateMockElasticsearchResponse()
    );
    const keywordFilterClauseBuilderService = createMockInstance(
      KeywordFilterClauseBuilderService
    );

    const keywordSearchMatchedCountService =
      new KeywordSearchMatchedCountService(
        configService,
        elasticSearchService,
        keywordFilterClauseBuilderService
      );

    const assetToShouldClause: Map<
      RootFieldsForScoring,
      {
        isEmptyClauseQuery: boolean;
        query: QueryDslQueryContainer;
      }
    > = new Map();

    const citationShouldClause =
      generateMockInputShouldClauseWithQuery("citationTotal");
    const microBloggingShouldClause =
      generateMockInputShouldClauseWithQuery("microBloggingTotal");
    const diagnosesShouldClause =
      generateMockInputClaimsShouldClauseWithRegionFilter("DRG_diagnoses");
    const proceduresShouldClause =
      generateMockInputClaimsShouldClauseWithRegionFilter("DRG_procedures");
    const prescriptionsShouldClause =
      generateMockInputClaimsShouldClauseWithRegionFilter("prescriptions");
    const paymentsShouldClause =
      generateMockInputShouldClauseWithQuery("paymentTotal");

    assetToShouldClause.set("citationTotal", {
      isEmptyClauseQuery: false,
      query: citationShouldClause
    });
    assetToShouldClause.set("microBloggingTotal", {
      isEmptyClauseQuery: false,
      query: microBloggingShouldClause
    });
    assetToShouldClause.set("DRG_diagnosesCount", {
      isEmptyClauseQuery: false,
      query: diagnosesShouldClause
    });
    assetToShouldClause.set("DRG_proceduresCount", {
      isEmptyClauseQuery: false,
      query: proceduresShouldClause
    });
    assetToShouldClause.set("num_prescriptions", {
      isEmptyClauseQuery: false,
      query: prescriptionsShouldClause
    });
    assetToShouldClause.set("paymentTotal", {
      isEmptyClauseQuery: false,
      query: paymentsShouldClause
    });

    const projectIdFilter = generateMockProjectIdFilter();
    const filters: QueryDslQueryContainer[] = [projectIdFilter];

    const hit1 = generateMockElasticsearchHit();
    const hit2 = generateMockElasticsearchHit();
    const hits = [hit1, hit2];

    const peopleIds: string[] = [
      hit1._source?.id ?? faker.datatype.string(),
      hit2._source?.id ?? faker.datatype.string()
    ];

    const pageSize = peopleIds.length;
    filters.push({
      terms: {
        id: [...peopleIds]
      }
    });

    const matchedCountQueries: Array<AssetToESMatchedCountQuery> =
      keywordSearchMatchedCountService.collectSearchCardMatchedCountsQueries(
        hits,
        filters,
        assetToShouldClause
      );

    expect(matchedCountQueries.length).toEqual(8);

    expect(matchedCountQueries).toEqual([
      {
        field: "l1_indications",
        query: {
          index: configService.elasticPeopleIndex,
          from: 0,
          size: pageSize,
          track_total_hits: false,
          _source_includes: ["id"],
          query: {
            bool: {
              filter: expect.arrayContaining([
                ...filters,
                {
                  nested: {
                    path: "indications",
                    query: {
                      bool: {
                        filter: [
                          {
                            terms: {
                              ["indications.indicationType"]: ["L1"]
                            }
                          },
                          {
                            terms: {
                              ["indications.indicationSource"]: ["all"]
                            }
                          }
                        ]
                      }
                    },
                    inner_hits: {
                      _source: false,
                      sort: SORT_TIEBREAKER_FOR_INDICATIONS,
                      size: 1000,
                      name: "l1_indications",
                      docvalue_fields: ["indications.indication.keyword"]
                    }
                  }
                }
              ]),
              must: {
                match_all: {}
              }
            }
          }
        }
      },
      {
        field: "l3_indications",
        query: {
          index: configService.elasticPeopleIndex,
          from: 0,
          size: pageSize,
          track_total_hits: false,
          _source_includes: ["id"],
          query: {
            bool: {
              filter: expect.arrayContaining([
                ...filters,
                {
                  nested: {
                    path: "indications",
                    query: {
                      function_score: {
                        query: {
                          bool: {
                            filter: [
                              {
                                terms: {
                                  ["indications.indicationType"]: ["L3"]
                                }
                              },
                              {
                                terms: {
                                  ["indications.indicationSource"]: ["all"]
                                }
                              }
                            ]
                          }
                        },
                        boost_mode: "replace",
                        functions: [
                          {
                            field_value_factor: {
                              field: "indications.indicationScore",
                              missing: 0
                            }
                          }
                        ]
                      }
                    },
                    inner_hits: {
                      _source: false,
                      sort: SORT_TIEBREAKER_FOR_INDICATIONS,
                      size: 5,
                      name: "l3_indications",
                      docvalue_fields: ["indications.indication.keyword"]
                    }
                  }
                }
              ]),
              must: {
                match_all: {}
              }
            }
          }
        }
      },
      {
        field: "citations",
        query: {
          index: configService.elasticPeopleIndex,
          from: 0,
          size: pageSize,
          track_total_hits: false,
          _source_includes: ["id"],
          query: {
            bool: {
              filter: filters,
              must: {
                nested: expect.objectContaining({
                  path: "citationTotal",
                  inner_hits: expect.objectContaining({
                    size: 0
                  })
                })
              }
            }
          }
        }
      },
      {
        field: "microBlogging",
        query: {
          index: configService.elasticPeopleIndex,
          from: 0,
          size: pageSize,
          track_total_hits: false,
          _source_includes: ["id"],
          query: {
            bool: {
              filter: filters,
              must: {
                nested: expect.objectContaining({
                  path: "microBloggingTotal",
                  inner_hits: expect.objectContaining({
                    size: 0
                  })
                })
              }
            }
          }
        }
      },
      {
        field: "diagnoses_amount",
        query: {
          index: configService.elasticPeopleIndex,
          from: 0,
          size: pageSize,
          track_total_hits: false,
          _source_includes: ["id"],
          query: {
            bool: {
              filter: filters,
              must: {
                nested: expect.objectContaining({
                  path: "DRG_diagnoses",
                  inner_hits: expect.objectContaining({
                    size: 0
                  })
                })
              },
              must_not: {
                terms: {
                  country_multi: ["Brazil"]
                }
              }
            }
          }
        }
      },
      {
        field: "procedures_amount",
        query: {
          index: configService.elasticPeopleIndex,
          from: 0,
          size: pageSize,
          track_total_hits: false,
          _source_includes: ["id"],
          query: {
            bool: {
              filter: filters,
              must: {
                nested: expect.objectContaining({
                  path: "DRG_procedures",
                  inner_hits: expect.objectContaining({
                    size: 0
                  })
                })
              },
              must_not: {
                terms: {
                  country_multi: ["Brazil"]
                }
              }
            }
          }
        }
      },
      {
        field: "prescriptions_amount",
        query: {
          index: configService.elasticPeopleIndex,
          from: 0,
          size: pageSize,
          track_total_hits: false,
          _source_includes: ["id"],
          query: {
            bool: {
              filter: filters,
              must: {
                nested: expect.objectContaining({
                  path: "prescriptions",
                  inner_hits: expect.objectContaining({
                    size: 0
                  })
                })
              }
            }
          }
        }
      },
      {
        field: "payments",
        query: {
          index: configService.elasticPeopleIndex,
          from: 0,
          size: pageSize,
          track_total_hits: false,
          _source_includes: ["id"],
          query: {
            bool: {
              filter: filters,
              must: {
                nested: expect.objectContaining({
                  path: "paymentTotal",
                  inner_hits: expect.objectContaining({
                    size: 0
                  })
                })
              }
            }
          }
        }
      }
    ]);
  });

  it("should get matched patient counts for HCPs if patientClaimsFilter is present", async () => {
    const configService = createMockInstance(ConfigService);
    configService.elasticPeopleIndex = faker.datatype.string();
    const elasticSearchService = createMockInstance(ElasticSearchService);
    const esResponse = generateMockElasticsearchResponse();
    elasticSearchService.query.mockResolvedValue(esResponse);
    const keywordFilterClauseBuilderService = createMockInstance(
      KeywordFilterClauseBuilderService
    );
    const rulesParserService = createMockInstance(RulesParserService);

    const keywordSearchMatchedCountService =
      new KeywordSearchMatchedCountService(
        configService,
        elasticSearchService,
        keywordFilterClauseBuilderService
      );

    const assetToShouldClause: Map<
      RootFieldsForScoring,
      {
        isEmptyClauseQuery: boolean;
        query: QueryDslQueryContainer;
      }
    > = new Map();

    const citationShouldClause =
      generateMockInputShouldClauseWithQuery("citationTotal");
    const microBloggingShouldClause =
      generateMockInputShouldClauseWithQuery("microBloggingTotal");
    const diagnosesShouldClause =
      generateMockInputClaimsShouldClauseWithRegionFilter("DRG_diagnoses");
    const proceduresShouldClause =
      generateMockInputClaimsShouldClauseWithRegionFilter("DRG_procedures");
    const paymentsShouldClause =
      generateMockInputShouldClauseWithQuery("paymentTotal");

    assetToShouldClause.set("citationTotal", {
      isEmptyClauseQuery: false,
      query: citationShouldClause
    });
    assetToShouldClause.set("microBloggingTotal", {
      isEmptyClauseQuery: false,
      query: microBloggingShouldClause
    });
    assetToShouldClause.set("DRG_diagnosesCount", {
      isEmptyClauseQuery: false,
      query: diagnosesShouldClause
    });
    assetToShouldClause.set("DRG_proceduresCount", {
      isEmptyClauseQuery: false,
      query: proceduresShouldClause
    });
    assetToShouldClause.set("paymentTotal", {
      isEmptyClauseQuery: false,
      query: paymentsShouldClause
    });

    const projectIdFilter = generateMockProjectIdFilter();
    const filters: QueryDslQueryContainer[] = [
      projectIdFilter,
      {
        has_child: {
          type: "claim",
          query: {
            term: {
              [faker.datatype.string()]: faker.datatype.string()
            }
          }
        }
      }
    ];
    rulesParserService.parseRulesToEsQueries.mockReturnValue([
      {
        term: {
          [faker.datatype.string()]: faker.datatype.string()
        }
      }
    ]);
    keywordFilterClauseBuilderService.buildPatientPopulationFilter.mockReturnValue(
      [
        {
          term: {
            [faker.datatype.string()]: faker.datatype.string()
          }
        }
      ]
    );
    const hit1 = generateMockElasticsearchHit();
    const hit2 = generateMockElasticsearchHit();
    const hits = [hit1, hit2];
    const response = generateMockElasticsearchResponse(hits);
    const peopleIds: string[] = [
      hit1._source?.id ?? faker.datatype.string(),
      hit2._source?.id ?? faker.datatype.string()
    ];

    filters.push({
      terms: {
        id: [...peopleIds]
      }
    });
    const defaultFilters = generateFilters();
    const suppliedFilters = generateFilters({
      patientClaimsFilter: {
        type: RuleTypeEnum.RULE,
        rule: {
          field: RuleFieldEnum.DIAGNOSES_CODE,
          operator: RuleOperatorEnum.EQUAL,
          value: [faker.datatype.string()]
        }
      },
      claims: {
        ...defaultFilters.claims,
        showUniquePatients: {
          value: true
        }
      }
    });
    const input = generateKeywordSearchInput({ suppliedFilters });
    const keywordSearchFeatureFlags = generateKeywordSearchFeatureFlags();
    const [matchedCountResults] =
      await keywordSearchMatchedCountService.getMatchedCountForAssets(
        input,
        response.hits,
        filters,
        assetToShouldClause,
        keywordSearchFeatureFlags
      );

    expect(
      matchedCountResults[hit1._id]["patient_claims_matching_count"]
    ).toEqual((esResponse.hits.total as SearchTotalHits).value);
    expect(
      matchedCountResults[hit2._id]["patient_claims_matching_count"]
    ).toEqual((esResponse.hits.total as SearchTotalHits).value);
  });

  it("should get patient diversity stats from child patient docs", async () => {
    const configService = createMockInstance(ConfigService);
    configService.elasticPeopleIndex = faker.datatype.string();
    const elasticSearchService = createMockInstance(ElasticSearchService);
    const esResponse = generateMockElasticsearchResponse();
    elasticSearchService.query.mockResolvedValue(esResponse);
    const keywordFilterClauseBuilderService = createMockInstance(
      KeywordFilterClauseBuilderService
    );
    const rulesParserService = createMockInstance(RulesParserService);

    const keywordSearchMatchedCountService =
      new KeywordSearchMatchedCountService(
        configService,
        elasticSearchService,
        keywordFilterClauseBuilderService
      );

    const assetToShouldClause: Map<
      RootFieldsForScoring,
      {
        isEmptyClauseQuery: boolean;
        query: QueryDslQueryContainer;
      }
    > = new Map();

    const citationShouldClause =
      generateMockInputShouldClauseWithQuery("citationTotal");
    const microBloggingShouldClause =
      generateMockInputShouldClauseWithQuery("microBloggingTotal");
    const diagnosesShouldClause =
      generateMockInputClaimsShouldClauseWithRegionFilter("DRG_diagnoses");
    const proceduresShouldClause =
      generateMockInputClaimsShouldClauseWithRegionFilter("DRG_procedures");
    const paymentsShouldClause =
      generateMockInputShouldClauseWithQuery("paymentTotal");

    assetToShouldClause.set("citationTotal", {
      isEmptyClauseQuery: false,
      query: citationShouldClause
    });
    assetToShouldClause.set("microBloggingTotal", {
      isEmptyClauseQuery: false,
      query: microBloggingShouldClause
    });
    assetToShouldClause.set("DRG_diagnosesCount", {
      isEmptyClauseQuery: false,
      query: diagnosesShouldClause
    });
    assetToShouldClause.set("DRG_proceduresCount", {
      isEmptyClauseQuery: false,
      query: proceduresShouldClause
    });
    assetToShouldClause.set("paymentTotal", {
      isEmptyClauseQuery: false,
      query: paymentsShouldClause
    });

    const projectIdFilter = generateMockProjectIdFilter();
    const filters: QueryDslQueryContainer[] = [
      projectIdFilter,
      {
        has_child: {
          type: "claim",
          query: {
            term: {
              [faker.datatype.string()]: faker.datatype.string()
            }
          }
        }
      }
    ];
    rulesParserService.parseRulesToEsQueries.mockReturnValue([
      {
        term: {
          [faker.datatype.string()]: faker.datatype.string()
        }
      }
    ]);
    keywordFilterClauseBuilderService.buildPatientPopulationFilter.mockReturnValue(
      [
        {
          term: {
            [faker.datatype.string()]: faker.datatype.string()
          }
        }
      ]
    );
    const hit1 = generateMockElasticsearchHit();
    const hit2 = generateMockElasticsearchHit();
    const hits = [hit1, hit2];
    const response = generateMockElasticsearchResponse(hits);
    const peopleIds: string[] = [
      hit1._source?.id ?? faker.datatype.string(),
      hit2._source?.id ?? faker.datatype.string()
    ];

    filters.push({
      terms: {
        id: [...peopleIds]
      }
    });
    const defaultFilters = generateFilters();
    const suppliedFilters = generateFilters({
      patientClaimsFilter: {
        type: RuleTypeEnum.RULE,
        rule: {
          field: RuleFieldEnum.DIAGNOSES_CODE,
          operator: RuleOperatorEnum.EQUAL,
          value: [faker.datatype.string()]
        }
      },
      claims: {
        ...defaultFilters.claims,
        showUniquePatients: {
          value: true
        }
      }
    });
    const input = generateKeywordSearchInput({ suppliedFilters });
    const keywordSearchFeatureFlags = generateKeywordSearchFeatureFlags();
    const [, , , patientDiversityDistribution] =
      await keywordSearchMatchedCountService.getMatchedCountForAssets(
        input,
        response.hits,
        filters,
        assetToShouldClause,
        keywordSearchFeatureFlags
      );

    hits.forEach((hit) => {
      expect(patientDiversityDistribution[hit._id].race).toBeTruthy();
      expect(patientDiversityDistribution[hit._id].age).toBeTruthy();
      expect(patientDiversityDistribution[hit._id].gender).toBeTruthy();
    });
  });

  it("should get matched ongoing and actively-recruiting trial counts", async () => {
    const configService = createMockInstance(ConfigService);
    configService.elasticPeopleIndex = faker.datatype.string();
    const elasticSearchService = createMockInstance(ElasticSearchService);
    const esResponse = generateMockElasticsearchResponse();
    elasticSearchService.query.mockResolvedValue(esResponse);
    const keywordFilterClauseBuilderService = createMockInstance(
      KeywordFilterClauseBuilderService
    );
    const rulesParserService = createMockInstance(RulesParserService);

    const keywordSearchMatchedCountService =
      new KeywordSearchMatchedCountService(
        configService,
        elasticSearchService,
        keywordFilterClauseBuilderService
      );

    const assetToShouldClause: Map<
      RootFieldsForScoring,
      {
        isEmptyClauseQuery: boolean;
        query: QueryDslQueryContainer;
      }
    > = new Map();

    const citationShouldClause =
      generateMockInputShouldClauseWithQuery("citationTotal");
    const microBloggingShouldClause =
      generateMockInputShouldClauseWithQuery("microBloggingTotal");
    const diagnosesShouldClause =
      generateMockInputClaimsShouldClauseWithRegionFilter("DRG_diagnoses");
    const proceduresShouldClause =
      generateMockInputClaimsShouldClauseWithRegionFilter("DRG_procedures");
    const paymentsShouldClause =
      generateMockInputShouldClauseWithQuery("paymentTotal");
    const trialsShouldClause = generateMockInputShouldClauseWithQuery("trials");

    assetToShouldClause.set("citationTotal", {
      isEmptyClauseQuery: false,
      query: citationShouldClause
    });
    assetToShouldClause.set("microBloggingTotal", {
      isEmptyClauseQuery: false,
      query: microBloggingShouldClause
    });
    assetToShouldClause.set("DRG_diagnosesCount", {
      isEmptyClauseQuery: false,
      query: diagnosesShouldClause
    });
    assetToShouldClause.set("DRG_proceduresCount", {
      isEmptyClauseQuery: false,
      query: proceduresShouldClause
    });
    assetToShouldClause.set("paymentTotal", {
      isEmptyClauseQuery: false,
      query: paymentsShouldClause
    });
    assetToShouldClause.set("trialCount", {
      isEmptyClauseQuery: false,
      query: trialsShouldClause
    });

    const projectIdFilter = generateMockProjectIdFilter();
    const filters: QueryDslQueryContainer[] = [
      projectIdFilter,
      {
        has_child: {
          type: "claim",
          query: {
            term: {
              [faker.datatype.string()]: faker.datatype.string()
            }
          }
        }
      }
    ];
    rulesParserService.parseRulesToEsQueries.mockReturnValue([
      {
        term: {
          [faker.datatype.string()]: faker.datatype.string()
        }
      }
    ]);
    keywordFilterClauseBuilderService.buildPatientPopulationFilter.mockReturnValue(
      [
        {
          term: {
            [faker.datatype.string()]: faker.datatype.string()
          }
        }
      ]
    );
    const hit1 = generateMockElasticsearchHit();
    const hit2 = generateMockElasticsearchHit();
    const hits = [hit1, hit2];
    const response = generateMockElasticsearchResponse(hits);
    const peopleIds: string[] = [
      hit1._source?.id ?? faker.datatype.string(),
      hit2._source?.id ?? faker.datatype.string()
    ];

    filters.push({
      terms: {
        id: [...peopleIds]
      }
    });
    const defaultFilters = generateFilters();
    const suppliedFilters = generateFilters({
      patientClaimsFilter: {
        type: RuleTypeEnum.RULE,
        rule: {
          field: RuleFieldEnum.DIAGNOSES_CODE,
          operator: RuleOperatorEnum.EQUAL,
          value: [faker.datatype.string()]
        }
      },
      claims: {
        ...defaultFilters.claims,
        showUniquePatients: {
          value: true
        }
      }
    });
    const input = generateKeywordSearchInput({ suppliedFilters });
    const keywordSearchFeatureFlags = generateKeywordSearchFeatureFlags();
    const [matchedCountResults] =
      await keywordSearchMatchedCountService.getMatchedCountForAssets(
        input,
        response.hits,
        filters,
        assetToShouldClause,
        keywordSearchFeatureFlags
      );

    expect(
      matchedCountResults[hit1._id]["patient_claims_matching_count"]
    ).toEqual((esResponse.hits.total as SearchTotalHits).value);
    expect(
      matchedCountResults[hit2._id]["patient_claims_matching_count"]
    ).toEqual((esResponse.hits.total as SearchTotalHits).value);
  });

  it("should get matched patient counts for HCPs if ccsr filter is present", async () => {
    const configService = createMockInstance(ConfigService);
    configService.elasticPeopleIndex = faker.datatype.string();
    const elasticSearchService = createMockInstance(ElasticSearchService);
    const esResponse = generateMockElasticsearchResponse();
    elasticSearchService.query.mockResolvedValue(esResponse);
    const keywordFilterClauseBuilderService = createMockInstance(
      KeywordFilterClauseBuilderService
    );

    const keywordSearchMatchedCountService =
      new KeywordSearchMatchedCountService(
        configService,
        elasticSearchService,
        keywordFilterClauseBuilderService
      );

    const assetToShouldClause: Map<
      RootFieldsForScoring,
      {
        isEmptyClauseQuery: boolean;
        query: QueryDslQueryContainer;
      }
    > = new Map();

    const citationShouldClause =
      generateMockInputShouldClauseWithQuery("citationTotal");
    const microBloggingShouldClause =
      generateMockInputShouldClauseWithQuery("microBloggingTotal");
    const diagnosesShouldClause =
      generateMockInputClaimsShouldClauseWithRegionFilter("DRG_diagnoses");
    const proceduresShouldClause =
      generateMockInputClaimsShouldClauseWithRegionFilter("DRG_procedures");
    const paymentsShouldClause =
      generateMockInputShouldClauseWithQuery("paymentTotal");

    assetToShouldClause.set("citationTotal", {
      isEmptyClauseQuery: false,
      query: citationShouldClause
    });
    assetToShouldClause.set("microBloggingTotal", {
      isEmptyClauseQuery: false,
      query: microBloggingShouldClause
    });
    assetToShouldClause.set("DRG_diagnosesCount", {
      isEmptyClauseQuery: false,
      query: diagnosesShouldClause
    });
    assetToShouldClause.set("DRG_proceduresCount", {
      isEmptyClauseQuery: false,
      query: proceduresShouldClause
    });
    assetToShouldClause.set("paymentTotal", {
      isEmptyClauseQuery: false,
      query: paymentsShouldClause
    });

    const projectIdFilter = generateMockProjectIdFilter();
    const filters: QueryDslQueryContainer[] = [
      projectIdFilter,
      {
        has_child: {
          type: "claim",
          query: {
            term: {
              [faker.datatype.string()]: faker.datatype.string()
            }
          }
        }
      }
    ];

    keywordFilterClauseBuilderService.buildPatientPopulationFilter.mockReturnValue(
      [
        {
          term: {
            [faker.datatype.string()]: faker.datatype.string()
          }
        }
      ]
    );
    const hit1 = generateMockElasticsearchHit();
    const hit2 = generateMockElasticsearchHit();
    const hits = [hit1, hit2];
    const response = generateMockElasticsearchResponse(hits);
    const peopleIds: string[] = [
      hit1._source?.id ?? faker.datatype.string(),
      hit2._source?.id ?? faker.datatype.string()
    ];

    filters.push({
      terms: {
        id: [...peopleIds]
      }
    });
    const emptyFilters = generateFilters();
    const suppliedFilters = generateFilters({
      claims: {
        ...emptyFilters.claims,
        showUniquePatients: {
          value: true
        },
        ccsr: {
          values: [faker.datatype.string()]
        }
      }
    });
    const input = generateKeywordSearchInput({ suppliedFilters });
    const keywordSearchFeatureFlags = generateKeywordSearchFeatureFlags();
    const [matchedCountResults] =
      await keywordSearchMatchedCountService.getMatchedCountForAssets(
        input,
        response.hits,
        filters,
        assetToShouldClause,
        keywordSearchFeatureFlags
      );

    expect(
      matchedCountResults[hit1._id]["patient_claims_matching_count"]
    ).toEqual((esResponse.hits.total as SearchTotalHits).value);
    expect(
      matchedCountResults[hit2._id]["patient_claims_matching_count"]
    ).toEqual((esResponse.hits.total as SearchTotalHits).value);
  });
});
