import {
  createMockInstance,
  generateKeywordSearchFeatureFlags,
  getEmptyKeywordSearchFilters
} from "../util/TestUtils";
import { QueryUnderstandingServiceClient } from "./QueryUnderstandingServiceClient";
import { EntitySearchProcessorService } from "./EntitySearchProcessorService";

import { WeightedSortBy } from "@h1nyc/search-sdk";
import {
  Entities,
  Locations,
  NameTypes,
  EntitiesV2,
  LocationsV2,
  Claims,
  TrialInfo,
  QueryUnderstandingServiceResponse
} from "../proto/query_understanding_service_pb";
import { KeywordSearchFeatureFlags } from "./KeywordSearchResourceServiceRewrite";
import { generateKeywordSearchInput } from "./KeywordSearchResourceServiceRewrite.test";
import { ElasticSearchService } from "./ElasticSearchService";
import { ConfigService } from "./ConfigService";

describe("EntitySearchProcessorService", () => {
  let queryUnderstandingServiceClient: jest.Mocked<QueryUnderstandingServiceClient>;
  let entitySearchProcessorService: EntitySearchProcessorService;
  let keywordSearchFeatureFlags: KeywordSearchFeatureFlags;

  beforeEach(() => {
    const configService = createMockInstance(ConfigService);
    queryUnderstandingServiceClient = createMockInstance(
      QueryUnderstandingServiceClient
    );
    const elasticSearchService = createMockInstance(ElasticSearchService);
    entitySearchProcessorService = new EntitySearchProcessorService(
      configService,
      queryUnderstandingServiceClient,
      elasticSearchService
    );

    keywordSearchFeatureFlags = generateKeywordSearchFeatureFlags({
      enableLlmQueryParserForSearch: true
    });
  });

  function createMockEntitiesResponse(
    searchFor = "",
    indication = "",
    rankBy = "",
    peopleNames = "",
    institutionNames = "",
    country = "",
    state = "",
    city = "",
    zipcode = ""
  ): QueryUnderstandingServiceResponse {
    const response = new QueryUnderstandingServiceResponse();

    const entities = new Entities();

    entities.setSearchFor(searchFor);
    entities.setIndication(indication);
    entities.setRankBy(rankBy);

    if (peopleNames || institutionNames) {
      const names = new NameTypes();
      names.setPeople(peopleNames);
      names.setInstitutions(institutionNames);
      entities.setNames(names);
    }

    if (country || state || city || zipcode) {
      const locations = new Locations();
      locations.setCountry(country);
      locations.setState(state);
      locations.setCity(city);
      locations.setZipcode(zipcode);
      entities.setLocations(locations);
    }

    response.setEntities(entities);
    return response;
  }

  function createMockEntitiesV2Response(
    searchFor = "",
    indicationList: string[] = [],
    specialtyList: string[] = [],
    workInstitutionList: string[] = [],
    countryList: string[] = [],
    stateList: string[] = [],
    cityList: string[] = [],
    diagnosesClaimsList: string[] = [],
    proceduresClaimsList: string[] = [],
    minDiagnosesClaimCount: number | null = null,
    maxDiagnosesClaimCount: number | null = null,
    minProceduresClaimCount: number | null = null,
    maxProceduresClaimCount: number | null = null,
    claimsTimeframe: string | null = null,
    trialMinCount: number | null = null,
    trialMaxCount: number | null = null,
    trialStatus: string | null = null,
    trialPhase: string | null = null
  ): QueryUnderstandingServiceResponse {
    const response = new QueryUnderstandingServiceResponse();

    const entitiesV2 = new EntitiesV2();

    entitiesV2.setSearchFor(searchFor);
    entitiesV2.setIndicationList(indicationList);
    entitiesV2.setSpecialtyList(specialtyList);
    entitiesV2.setWorkInstitutionList(workInstitutionList);

    // Set locations if any location data is provided
    if (countryList.length > 0 || stateList.length > 0 || cityList.length > 0) {
      const locationsV2 = new LocationsV2();
      locationsV2.setCountryList(countryList);
      locationsV2.setStateList(stateList);
      locationsV2.setCityList(cityList);
      entitiesV2.setLocations(locationsV2);
    }

    // Set claims if any claims data is provided
    if (
      diagnosesClaimsList.length > 0 ||
      proceduresClaimsList.length > 0 ||
      minDiagnosesClaimCount !== null ||
      maxDiagnosesClaimCount !== null ||
      minProceduresClaimCount !== null ||
      maxProceduresClaimCount !== null ||
      claimsTimeframe !== null
    ) {
      const claims = new Claims();
      claims.setDiagnosesClaimsList(diagnosesClaimsList);
      claims.setProceduresClaimsList(proceduresClaimsList);

      if (minDiagnosesClaimCount !== null) {
        claims.setMinDiagnosesClaimCount(minDiagnosesClaimCount);
      }
      if (maxDiagnosesClaimCount !== null) {
        claims.setMaxDiagnosesClaimCount(maxDiagnosesClaimCount);
      }
      if (minProceduresClaimCount !== null) {
        claims.setMinProceduresClaimCount(minProceduresClaimCount);
      }
      if (maxProceduresClaimCount !== null) {
        claims.setMaxProceduresClaimCount(maxProceduresClaimCount);
      }
      if (claimsTimeframe !== null) {
        claims.setTimeframe(claimsTimeframe);
      }

      entitiesV2.setClaims(claims);
    }

    // Set trial info if any trial data is provided
    if (
      trialMinCount !== null ||
      trialMaxCount !== null ||
      trialStatus !== null ||
      trialPhase !== null
    ) {
      const trialInfo = new TrialInfo();

      if (trialMinCount !== null) {
        trialInfo.setMinCount(trialMinCount);
      }
      if (trialMaxCount !== null) {
        trialInfo.setMaxCount(trialMaxCount);
      }
      if (trialStatus !== null) {
        trialInfo.setStatus(trialStatus);
      }
      if (trialPhase !== null) {
        trialInfo.setPhase(trialPhase);
      }

      entitiesV2.setTrial(trialInfo);
    }

    response.setEntitiesV2(entitiesV2);
    return response;
  }

  describe("processSearchWithEntityDetection", () => {
    it("should return original input and shouldExecuteSearch=true when feature flag is disabled", async () => {
      const input = generateKeywordSearchInput();
      const flagsWithDisabledLlm = {
        ...keywordSearchFeatureFlags,
        enableLlmQueryParserForSearch: false
      };

      const result =
        await entitySearchProcessorService.processSearchWithEntityDetection(
          input,
          flagsWithDisabledLlm
        );

      expect(result.shouldExecuteSearch).toBe(true);
      expect(result.modifiedInput).toEqual(input);
      expect(result.detectedEntities).toBeUndefined();
      expect(
        queryUnderstandingServiceClient.getEntitiesForQuery
      ).not.toHaveBeenCalled();
    });

    it("should return original input and shouldExecuteSearch=true when query is empty", async () => {
      const input = generateKeywordSearchInput({
        query: ""
      });

      const result =
        await entitySearchProcessorService.processSearchWithEntityDetection(
          input,
          keywordSearchFeatureFlags
        );

      expect(result.shouldExecuteSearch).toBe(true);
      expect(result.modifiedInput).toEqual(input);
      expect(result.detectedEntities).toBeUndefined();
      expect(
        queryUnderstandingServiceClient.getEntitiesForQuery
      ).not.toHaveBeenCalled();
    });

    it("should return original input and shouldExecuteSearch=true when advanced operators (OR) are present", async () => {
      const input = generateKeywordSearchInput({
        query: "oncology OR cardiology"
      });
      const result =
        await entitySearchProcessorService.processSearchWithEntityDetection(
          input,
          keywordSearchFeatureFlags
        );

      expect(result.shouldExecuteSearch).toBe(true);
      expect(result.modifiedInput).toEqual(input);
      expect(result.detectedEntities).toBeUndefined();
      expect(
        queryUnderstandingServiceClient.getEntitiesForQuery
      ).not.toHaveBeenCalled();
    });

    it("should return original input and shouldExecuteSearch=true when advanced operators (AND) are present", async () => {
      const input = generateKeywordSearchInput({
        query: "oncology AND New York"
      });
      const result =
        await entitySearchProcessorService.processSearchWithEntityDetection(
          input,
          keywordSearchFeatureFlags
        );

      expect(result.shouldExecuteSearch).toBe(true);
      expect(result.modifiedInput).toEqual(input);
      expect(result.detectedEntities).toBeUndefined();
      expect(
        queryUnderstandingServiceClient.getEntitiesForQuery
      ).not.toHaveBeenCalled();
    });

    it("should return original input and shouldExecuteSearch=true when query has less than 5 words", async () => {
      const input = generateKeywordSearchInput({
        query: "short query test"
      });
      const result =
        await entitySearchProcessorService.processSearchWithEntityDetection(
          input,
          keywordSearchFeatureFlags
        );

      expect(result.shouldExecuteSearch).toBe(true);
      expect(result.modifiedInput).toEqual(input);
      expect(result.detectedEntities).toBeUndefined();
      expect(
        queryUnderstandingServiceClient.getEntitiesForQuery
      ).not.toHaveBeenCalled();
    });

    it("should return original input and shouldExecuteSearch=true when query has a BOTH operator", async () => {
      const input = generateKeywordSearchInput({
        query: "BOTH( cardiology WITH diabetes )"
      });
      const result =
        await entitySearchProcessorService.processSearchWithEntityDetection(
          input,
          keywordSearchFeatureFlags
        );

      expect(result.shouldExecuteSearch).toBe(true);
      expect(result.modifiedInput).toEqual(input);
      expect(result.detectedEntities).toBeUndefined();
      expect(
        queryUnderstandingServiceClient.getEntitiesForQuery
      ).not.toHaveBeenCalled();
    });

    it("should extract entities and set shouldExecuteSearch=true when searchFor='people'", async () => {
      const input = generateKeywordSearchInput({
        query:
          "find oncologists in Boston Massachusetts with publications on lung cancer"
      });
      const mockResponse = createMockEntitiesResponse(
        "people",
        "lung cancer",
        "publications",
        "",
        "",
        "US",
        "Massachusetts",
        "Boston",
        ""
      );

      queryUnderstandingServiceClient.getEntitiesForQuery.mockResolvedValue(
        mockResponse
      );

      const result =
        await entitySearchProcessorService.processSearchWithEntityDetection(
          input,
          keywordSearchFeatureFlags
        );

      expect(
        queryUnderstandingServiceClient.getEntitiesForQuery
      ).toHaveBeenCalledWith(input.query, "eng");
      expect(result.shouldExecuteSearch).toBe(true);
      expect(result.detectedEntities).toBeDefined();
      expect(result.detectedEntities?.searchFor).toBe("people");
      expect(result.detectedEntities?.indication).toBe("lung cancer");
      expect(result.detectedEntities?.rankBy).toBe("publications");
      expect(result.detectedEntities?.locations).toBeDefined();
      expect(result.detectedEntities?.locations?.country).toBe("US");
      expect(result.detectedEntities?.locations?.state).toBe("Massachusetts");
      expect(result.detectedEntities?.locations?.city).toBe("Boston");

      expect(result.modifiedInput.query).toBe("lung cancer");

      expect(result.modifiedInput.suppliedFilters.country.values).toContain(
        "US"
      );
      expect(result.modifiedInput.suppliedFilters.state.values).toContain(
        "Massachusetts"
      );
      expect(result.modifiedInput.suppliedFilters.city.values).toContain(
        "Boston"
      );

      expect(result.modifiedInput.sortBy.publication).toBeGreaterThan(0);
    });

    it("should extract entities and set shouldExecuteSearch=false when searchFor is not 'people'", async () => {
      const input = generateKeywordSearchInput({
        query: "find hospitals in New York with expertise in oncology"
      });
      const mockResponse = createMockEntitiesResponse(
        "institutions",
        "oncology",
        "trials",
        "",
        "",
        "US",
        "New York",
        "",
        ""
      );

      queryUnderstandingServiceClient.getEntitiesForQuery.mockResolvedValue(
        mockResponse
      );

      const result =
        await entitySearchProcessorService.processSearchWithEntityDetection(
          input,
          keywordSearchFeatureFlags
        );

      expect(
        queryUnderstandingServiceClient.getEntitiesForQuery
      ).toHaveBeenCalledWith(input.query, "eng");
      expect(result.shouldExecuteSearch).toBe(false);
      expect(result.detectedEntities).toBeDefined();
      expect(result.detectedEntities?.searchFor).toBe("institutions");
    });

    it("should apply institution name as a filter when detected", async () => {
      const input = generateKeywordSearchInput({
        query:
          "find doctors at Massachusetts General Hospital who specialize in cardiology"
      });
      const mockResponse = createMockEntitiesResponse(
        "people",
        "cardiology",
        "publications",
        "",
        "Massachusetts General Hospital",
        "US",
        "Massachusetts",
        "",
        ""
      );

      queryUnderstandingServiceClient.getEntitiesForQuery.mockResolvedValue(
        mockResponse
      );

      const result =
        await entitySearchProcessorService.processSearchWithEntityDetection(
          input,
          keywordSearchFeatureFlags
        );

      expect(
        queryUnderstandingServiceClient.getEntitiesForQuery
      ).toHaveBeenCalledWith(input.query, "eng");
      expect(result.shouldExecuteSearch).toBe(true);
      expect(result.detectedEntities?.names?.institutions).toBe(
        "Massachusetts General Hospital"
      );

      expect(result.modifiedInput.suppliedFilters.institution.values).toContain(
        "Massachusetts General Hospital"
      );

      expect(result.modifiedInput.query).toBe("cardiology");
    });

    it("should handle multiple rankBy values with decreasing weights", async () => {
      const input = generateKeywordSearchInput({
        query: "find oncologists ranked by publications, trials and congresses"
      });
      const mockResponse = createMockEntitiesResponse(
        "people",
        "oncology",
        "publications,trials,congresses"
      );

      queryUnderstandingServiceClient.getEntitiesForQuery.mockResolvedValue(
        mockResponse
      );

      const result =
        await entitySearchProcessorService.processSearchWithEntityDetection(
          input,
          keywordSearchFeatureFlags
        );

      expect(
        queryUnderstandingServiceClient.getEntitiesForQuery
      ).toHaveBeenCalledWith(input.query, "eng");
      expect(result.shouldExecuteSearch).toBe(true);

      expect(result.modifiedInput.sortBy.publication).toBeGreaterThan(0);
      expect(result.modifiedInput.sortBy.trial).toBeGreaterThan(0);
      expect(result.modifiedInput.sortBy.congress).toBeGreaterThan(0);

      expect(result.modifiedInput.sortBy.publication).toBeGreaterThan(
        result.modifiedInput.sortBy.trial
      );

      expect(result.modifiedInput.sortBy.trial).toBeGreaterThan(
        result.modifiedInput.sortBy.congress
      );
    });

    it("should handle errors during entity detection", async () => {
      const input = generateKeywordSearchInput({
        query:
          "find oncologists in Boston Massachusetts with publications on lung cancer"
      });
      queryUnderstandingServiceClient.getEntitiesForQuery.mockRejectedValue(
        new Error("Service error")
      );

      const result =
        await entitySearchProcessorService.processSearchWithEntityDetection(
          input,
          keywordSearchFeatureFlags
        );

      expect(
        queryUnderstandingServiceClient.getEntitiesForQuery
      ).toHaveBeenCalledWith(input.query, "eng");
      expect(result.shouldExecuteSearch).toBe(true);
      expect(result.modifiedInput).toEqual(input);
      expect(result.detectedEntities).toBeDefined();
      expect(result.detectedEntities?.searchFor).toBe("");
      expect(result.detectedEntities?.indication).toBe("");
      expect(result.detectedEntities?.rankBy).toBe("");
    });

    it("should set empty query when shouldExecuteSearch is true but no indication exists", async () => {
      const input = generateKeywordSearchInput({
        query: "find top researchers in Boston"
      });
      const mockResponse = createMockEntitiesResponse(
        "people",
        "",
        "publications",
        "",
        "",
        "US",
        "",
        "Boston",
        ""
      );

      queryUnderstandingServiceClient.getEntitiesForQuery.mockResolvedValue(
        mockResponse
      );

      const result =
        await entitySearchProcessorService.processSearchWithEntityDetection(
          input,
          keywordSearchFeatureFlags
        );

      expect(
        queryUnderstandingServiceClient.getEntitiesForQuery
      ).toHaveBeenCalledWith(input.query, "eng");
      expect(result.shouldExecuteSearch).toBe(true);
      expect(result.modifiedInput.query).toBe("");

      expect(result.modifiedInput.suppliedFilters.country.values).toContain(
        "US"
      );
      expect(result.modifiedInput.suppliedFilters.city.values).toContain(
        "Boston"
      );
    });
  });

  describe("extractEntitiesFromQuery", () => {
    it("should return empty entities when query is empty", async () => {
      queryUnderstandingServiceClient.getEntitiesForQuery.mockResolvedValue(
        createMockEntitiesResponse()
      );

      const result = await (
        entitySearchProcessorService as any
      ).extractEntitiesFromQuery("", "eng");

      expect(result).toEqual({
        searchFor: "",
        indication: "",
        rankBy: ""
      });
      expect(
        queryUnderstandingServiceClient.getEntitiesForQuery
      ).not.toHaveBeenCalled();
    });

    it("should extract all possible entities from a complex query", async () => {
      const query =
        "find oncologists in Boston Massachusetts with publications on lung cancer";
      const mockResponse = createMockEntitiesResponse(
        "people",
        "lung cancer",
        "publications",
        "Dr. Smith",
        "Massachusetts General Hospital",
        "US",
        "Massachusetts",
        "Boston",
        "02114"
      );

      queryUnderstandingServiceClient.getEntitiesForQuery.mockResolvedValue(
        mockResponse
      );

      const result = await (
        entitySearchProcessorService as any
      ).extractEntitiesFromQuery(query, "eng");

      expect(result).toEqual({
        searchFor: "people",
        indication: "lung cancer",
        rankBy: "publications",
        names: {
          people: "Dr. Smith",
          institutions: "Massachusetts General Hospital"
        },
        locations: {
          country: "US",
          state: "Massachusetts",
          city: "Boston",
          zipcode: "02114"
        }
      });
      expect(
        queryUnderstandingServiceClient.getEntitiesForQuery
      ).toHaveBeenCalledWith(query, "eng");
    });
  });

  describe("applyRankByToSortWeights", () => {
    it("should apply weights based on rank priority", () => {
      const sortBy: WeightedSortBy = {
        publication: 0,
        citation: 0,
        microBloggingCount: 0,
        trial: 0,
        payment: 0,
        congress: 0,
        grant: 0,
        diagnoses: 0,
        procedures: 0,
        prescriptions: 0,
        referralsReceived: 0,
        referralsSent: 0,
        patientsDiversityRank: 0,
        h1DefaultRank: 0
      };

      (entitySearchProcessorService as any).applyRankByToSortWeights(
        sortBy,
        "publications,trials,congresses"
      );

      expect(sortBy.publication).toBe(1);
      expect(sortBy.trial).toBe(0.5);
      expect(sortBy.congress).toBe(0.25);

      expect(sortBy.citation).toBe(0);
      expect(sortBy.microBloggingCount).toBe(0);
      expect(sortBy.payment).toBe(0);
      expect(sortBy.grant).toBe(0);
      expect(sortBy.diagnoses).toBe(0);
      expect(sortBy.procedures).toBe(0);
    });

    it("should apply default weights when rankBy is not recognized", () => {
      const originalSortBy: WeightedSortBy = {
        publication: 0.5,
        citation: 0.3,
        microBloggingCount: 0,
        trial: 0.7,
        payment: 0,
        congress: 0.1,
        grant: 0,
        diagnoses: 0,
        procedures: 0,
        prescriptions: 0,
        referralsReceived: 0,
        referralsSent: 0,
        patientsDiversityRank: 0,
        h1DefaultRank: 0
      };

      const sortBy = { ...originalSortBy };

      (entitySearchProcessorService as any).applyRankByToSortWeights(
        sortBy,
        "unknown_rank"
      );

      expect(sortBy).toEqual(originalSortBy);
    });

    it("should handle patients rankBy for diagnoses weight", () => {
      const sortBy: WeightedSortBy = {
        publication: 0,
        citation: 0,
        microBloggingCount: 0,
        trial: 0,
        payment: 0,
        congress: 0,
        grant: 0,
        diagnoses: 0,
        procedures: 0,
        prescriptions: 0,
        referralsReceived: 0,
        referralsSent: 0,
        patientsDiversityRank: 0,
        h1DefaultRank: 0
      };

      (entitySearchProcessorService as any).applyRankByToSortWeights(
        sortBy,
        "patients"
      );

      expect(sortBy.diagnoses).toBe(1);

      expect(sortBy.publication).toBe(0);
      expect(sortBy.trial).toBe(0);
      expect(sortBy.congress).toBe(0);
    });
  });

  describe("applyLocationFilters", () => {
    it("should apply all available location filters", () => {
      const filters = getEmptyKeywordSearchFilters();
      const locations = {
        country: "US",
        state: "California",
        city: "San Francisco",
        zipcode: "94105"
      };

      // Call the private method using any trick
      (entitySearchProcessorService as any).applyLocationFilters(
        filters,
        locations
      );

      expect(filters.country.values).toContain("US");
      expect(filters.state.values).toContain("California");
      expect(filters.city.values).toContain("San Francisco");
      expect(filters.zipCode.values).toContain("94105");
    });

    it("should append to existing location filters", () => {
      const filters = getEmptyKeywordSearchFilters();
      filters.country.values = ["Canada"];
      filters.state.values = ["Ontario"];
      filters.city.values = ["Toronto"];
      filters.zipCode.values = ["M5V 2A8"];

      const locations = {
        country: "US",
        state: "California",
        city: "San Francisco",
        zipcode: "94105"
      };

      // Call the private method using any trick
      (entitySearchProcessorService as any).applyLocationFilters(
        filters,
        locations
      );

      expect(filters.country.values).toContain("Canada");
      expect(filters.country.values).toContain("US");
      expect(filters.state.values).toContain("Ontario");
      expect(filters.state.values).toContain("California");
      expect(filters.city.values).toContain("Toronto");
      expect(filters.city.values).toContain("San Francisco");
      expect(filters.zipCode.values).toContain("M5V 2A8");
      expect(filters.zipCode.values).toContain("94105");
    });

    it("should not add duplicate values to location filters", () => {
      const filters = getEmptyKeywordSearchFilters();
      filters.country.values = ["US"];
      filters.state.values = ["California"];
      filters.city.values = ["San Francisco"];
      filters.zipCode.values = ["94105"];

      const locations = {
        country: "US",
        state: "California",
        city: "San Francisco",
        zipcode: "94105"
      };

      // Call the private method using any trick
      (entitySearchProcessorService as any).applyLocationFilters(
        filters,
        locations
      );

      expect(filters.country.values).toEqual(["US"]);
      expect(filters.state.values).toEqual(["California"]);
      expect(filters.city.values).toEqual(["San Francisco"]);
      expect(filters.zipCode.values).toEqual(["94105"]);
    });
  });
});
