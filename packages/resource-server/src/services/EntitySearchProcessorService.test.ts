import {
  createMockInstance,
  generateKeywordSearchFeatureFlags
} from "../util/TestUtils";
import { QueryUnderstandingServiceClient } from "./QueryUnderstandingServiceClient";
import { EntitySearchProcessorService } from "./EntitySearchProcessorService";

import {
  EntitiesV2,
  LocationsV2,
  Claims,
  TrialInfo,
  QueryUnderstandingServiceResponse
} from "../proto/query_understanding_service_pb";
import { KeywordSearchFeatureFlags } from "./KeywordSearchResourceServiceRewrite";
import { generateKeywordSearchInput } from "./KeywordSearchResourceServiceRewrite.test";
import { ElasticSearchService } from "./ElasticSearchService";
import { ConfigService } from "./ConfigService";

describe("EntitySearchProcessorService", () => {
  let queryUnderstandingServiceClient: jest.Mocked<QueryUnderstandingServiceClient>;
  let elasticSearchService: jest.Mocked<ElasticSearchService>;
  let entitySearchProcessorService: EntitySearchProcessorService;
  let keywordSearchFeatureFlags: KeywordSearchFeatureFlags;

  beforeEach(() => {
    const configService = createMockInstance(ConfigService);
    queryUnderstandingServiceClient = createMockInstance(
      QueryUnderstandingServiceClient
    );
    elasticSearchService = createMockInstance(ElasticSearchService);

    // Mock the msearch method to return empty responses by default
    elasticSearchService.msearch.mockResolvedValue({
      took: 5,
      responses: []
    });

    entitySearchProcessorService = new EntitySearchProcessorService(
      configService,
      queryUnderstandingServiceClient,
      elasticSearchService
    );

    keywordSearchFeatureFlags = generateKeywordSearchFeatureFlags({
      enableLlmQueryParserForSearch: true
    });
  });

  // Helper function to create mock msearch responses
  function createMockMsearchResponse(responses: any[] = []) {
    return {
      took: 5,
      responses: responses.map((response) => {
        if (response.error) {
          return response; // Return error responses as-is
        }
        return {
          hits: {
            total: { value: response.hits?.length || 0, relation: "eq" },
            hits: response.hits || []
          }
        };
      })
    };
  }

  // Helper function to create mock nested query response
  function createMockNestedHit(path: string, field: string, values: string[]) {
    const fieldName = field.split(".").pop();
    return {
      _id: "test-id",
      _source: {},
      inner_hits: {
        [path]: {
          hits: {
            hits: values.map((value) => ({
              _id: `inner-${value}`,
              _source: { [fieldName!]: value }
            }))
          }
        }
      }
    };
  }

  // Helper function to create mock non-nested query response
  function createMockNonNestedHit(field: string, values: string[]) {
    return {
      _id: "test-id",
      _source: { [field]: values }
    };
  }

  function createMockEntitiesV2Response(
    searchFor = "",
    indicationList: string[] = [],
    specialtyList: string[] = [],
    workInstitutionList: string[] = [],
    countryList: string[] = [],
    stateList: string[] = [],
    cityList: string[] = [],
    diagnosesClaimsList: string[] = [],
    proceduresClaimsList: string[] = [],
    minDiagnosesClaimCount: number | null = null,
    maxDiagnosesClaimCount: number | null = null,
    minProceduresClaimCount: number | null = null,
    maxProceduresClaimCount: number | null = null,
    claimsTimeframe: string | null = null,
    trialMinCount: number | null = null,
    trialMaxCount: number | null = null,
    trialStatus: string | null = null,
    trialPhase: string | null = null
  ): QueryUnderstandingServiceResponse {
    const response = new QueryUnderstandingServiceResponse();

    const entitiesV2 = new EntitiesV2();

    entitiesV2.setSearchFor(searchFor);
    entitiesV2.setIndicationList(indicationList);
    entitiesV2.setSpecialtyList(specialtyList);
    entitiesV2.setWorkInstitutionList(workInstitutionList);

    // Set locations if any location data is provided
    if (countryList.length > 0 || stateList.length > 0 || cityList.length > 0) {
      const locationsV2 = new LocationsV2();
      locationsV2.setCountryList(countryList);
      locationsV2.setStateList(stateList);
      locationsV2.setCityList(cityList);
      entitiesV2.setLocations(locationsV2);
    }

    // Set claims if any claims data is provided
    if (
      diagnosesClaimsList.length > 0 ||
      proceduresClaimsList.length > 0 ||
      minDiagnosesClaimCount !== null ||
      maxDiagnosesClaimCount !== null ||
      minProceduresClaimCount !== null ||
      maxProceduresClaimCount !== null ||
      claimsTimeframe !== null
    ) {
      const claims = new Claims();
      claims.setDiagnosesList(diagnosesClaimsList);
      claims.setProceduresList(proceduresClaimsList);

      if (minDiagnosesClaimCount !== null) {
        claims.setMinDiagnosesClaimCount(minDiagnosesClaimCount);
      }
      if (maxDiagnosesClaimCount !== null) {
        claims.setMaxDiagnosesClaimCount(maxDiagnosesClaimCount);
      }
      if (minProceduresClaimCount !== null) {
        claims.setMinProceduresClaimCount(minProceduresClaimCount);
      }
      if (maxProceduresClaimCount !== null) {
        claims.setMaxProceduresClaimCount(maxProceduresClaimCount);
      }
      if (claimsTimeframe !== null) {
        claims.setTimeframe(claimsTimeframe);
      }

      entitiesV2.setClaims(claims);
    }

    // Set trial info if any trial data is provided
    if (
      trialMinCount !== null ||
      trialMaxCount !== null ||
      trialStatus !== null ||
      trialPhase !== null
    ) {
      const trialInfo = new TrialInfo();

      if (trialMinCount !== null) {
        trialInfo.setMinCount(trialMinCount);
      }
      if (trialMaxCount !== null) {
        trialInfo.setMaxCount(trialMaxCount);
      }
      if (trialStatus !== null) {
        trialInfo.setStatus(trialStatus);
      }
      if (trialPhase !== null) {
        trialInfo.setPhase(trialPhase);
      }

      entitiesV2.setTrial(trialInfo);
    }

    response.setEntitiesV2(entitiesV2);
    return response;
  }

  describe("processSearchWithEntityDetection", () => {
    it("should return original input and shouldExecuteSearch=true when feature flag is disabled", async () => {
      const input = generateKeywordSearchInput();
      const flagsWithDisabledLlm = {
        ...keywordSearchFeatureFlags,
        enableLlmQueryParserForSearch: false
      };

      const result =
        await entitySearchProcessorService.processSearchWithEntityDetection(
          input,
          flagsWithDisabledLlm
        );

      expect(result.shouldExecuteSearch).toBe(true);
      expect(result.modifiedInput).toEqual(input);
      expect(result.detectedEntities).toBeUndefined();
      expect(
        queryUnderstandingServiceClient.getEntitiesForQuery
      ).not.toHaveBeenCalled();
    });

    it("should return original input and shouldExecuteSearch=true when query is empty", async () => {
      const input = generateKeywordSearchInput({
        query: ""
      });

      const result =
        await entitySearchProcessorService.processSearchWithEntityDetection(
          input,
          keywordSearchFeatureFlags
        );

      expect(result.shouldExecuteSearch).toBe(true);
      expect(result.modifiedInput).toEqual(input);
      expect(result.detectedEntities).toBeUndefined();
      expect(
        queryUnderstandingServiceClient.getEntitiesForQuery
      ).not.toHaveBeenCalled();
    });

    it("should return original input and shouldExecuteSearch=true when advanced operators (OR) are present", async () => {
      const input = generateKeywordSearchInput({
        query: "oncology OR cardiology"
      });
      const result =
        await entitySearchProcessorService.processSearchWithEntityDetection(
          input,
          keywordSearchFeatureFlags
        );

      expect(result.shouldExecuteSearch).toBe(true);
      expect(result.modifiedInput).toEqual(input);
      expect(result.detectedEntities).toBeUndefined();
      expect(
        queryUnderstandingServiceClient.getEntitiesForQuery
      ).not.toHaveBeenCalled();
    });

    it("should return original input and shouldExecuteSearch=true when advanced operators (AND) are present", async () => {
      const input = generateKeywordSearchInput({
        query: "oncology AND New York"
      });
      const result =
        await entitySearchProcessorService.processSearchWithEntityDetection(
          input,
          keywordSearchFeatureFlags
        );

      expect(result.shouldExecuteSearch).toBe(true);
      expect(result.modifiedInput).toEqual(input);
      expect(result.detectedEntities).toBeUndefined();
      expect(
        queryUnderstandingServiceClient.getEntitiesForQuery
      ).not.toHaveBeenCalled();
    });

    it("should return original input and shouldExecuteSearch=true when query has less than 2 words", async () => {
      const input = generateKeywordSearchInput({
        query: "shortQuery"
      });
      const result =
        await entitySearchProcessorService.processSearchWithEntityDetection(
          input,
          keywordSearchFeatureFlags
        );

      expect(result.shouldExecuteSearch).toBe(true);
      expect(result.modifiedInput).toEqual(input);
      expect(result.detectedEntities).toBeUndefined();
      expect(
        queryUnderstandingServiceClient.getEntitiesForQuery
      ).not.toHaveBeenCalled();
    });

    it("should return original input and shouldExecuteSearch=true when query has a BOTH operator", async () => {
      const input = generateKeywordSearchInput({
        query: "BOTH( cardiology WITH diabetes )"
      });
      const result =
        await entitySearchProcessorService.processSearchWithEntityDetection(
          input,
          keywordSearchFeatureFlags
        );

      expect(result.shouldExecuteSearch).toBe(true);
      expect(result.modifiedInput).toEqual(input);
      expect(result.detectedEntities).toBeUndefined();
      expect(
        queryUnderstandingServiceClient.getEntitiesForQuery
      ).not.toHaveBeenCalled();
    });

    it("should extract entities and set shouldExecuteSearch=true when searchFor='people'", async () => {
      const input = generateKeywordSearchInput({
        query:
          "find oncologists in Boston Massachusetts with publications on lung cancer"
      });
      const mockResponse = createMockEntitiesV2Response(
        "people",
        ["lung cancer"],
        [],
        [],
        ["US"],
        ["Massachusetts"],
        ["Boston"]
      );

      queryUnderstandingServiceClient.getEntitiesForQuery.mockResolvedValue(
        mockResponse
      );

      // Mock msearch response for location validation
      const mockMsearchResponse = createMockMsearchResponse([
        {
          hits: [
            createMockNestedHit(
              "addressesForHCPU",
              "addressesForHCPU.country",
              ["US"]
            )
          ]
        },
        {
          hits: [
            createMockNestedHit("addressesForHCPU", "addressesForHCPU.region", [
              "Massachusetts"
            ])
          ]
        },
        {
          hits: [
            createMockNestedHit("addressesForHCPU", "addressesForHCPU.city", [
              "Boston"
            ])
          ]
        }
      ]);

      elasticSearchService.msearch.mockResolvedValue(mockMsearchResponse);

      const result =
        await entitySearchProcessorService.processSearchWithEntityDetection(
          input,
          keywordSearchFeatureFlags
        );

      expect(
        queryUnderstandingServiceClient.getEntitiesForQuery
      ).toHaveBeenCalledWith(input.query, "eng");
      expect(elasticSearchService.msearch).toHaveBeenCalledTimes(1);
      expect(result.shouldExecuteSearch).toBe(true);
      expect(result.detectedEntities).toBeDefined();
      expect(result.detectedEntities?.search_for).toBe("people");
      expect(result.detectedEntities?.indication).toEqual(["lung cancer"]);
      expect(result.detectedEntities?.locations).toBeDefined();
      expect(result.detectedEntities?.locations?.country).toEqual(["US"]);
      expect(result.detectedEntities?.locations?.state).toEqual([
        "Massachusetts"
      ]);
      expect(result.detectedEntities?.locations?.city).toEqual(["Boston"]);

      expect(result.modifiedInput.query).toBe("lung cancer");

      expect(result.modifiedInput.suppliedFilters.country.values).toContain(
        "US"
      );
      expect(result.modifiedInput.suppliedFilters.state.values).toContain(
        "Massachusetts"
      );
      expect(result.modifiedInput.suppliedFilters.city.values).toContain(
        "Boston"
      );

      expect(result.modifiedInput.sortBy.publication).toBeGreaterThan(0);
    });

    it("should extract entities and set shouldExecuteSearch=false when searchFor is not 'people'", async () => {
      const input = generateKeywordSearchInput({
        query: "find hospitals in New York with expertise in oncology"
      });
      const mockResponse = createMockEntitiesV2Response(
        "institution",
        ["oncology"],
        [],
        [],
        ["US"],
        ["New York"],
        []
      );

      const mockMsearchResponse = createMockMsearchResponse([
        {
          hits: [
            createMockNestedHit(
              "addressesForHCPU",
              "addressesForHCPU.country",
              ["US"]
            )
          ]
        },
        {
          hits: [
            createMockNestedHit("addressesForHCPU", "addressesForHCPU.region", [
              "New York"
            ])
          ]
        }
      ]);

      elasticSearchService.msearch.mockResolvedValue(mockMsearchResponse);

      queryUnderstandingServiceClient.getEntitiesForQuery.mockResolvedValue(
        mockResponse
      );

      const result =
        await entitySearchProcessorService.processSearchWithEntityDetection(
          input,
          keywordSearchFeatureFlags
        );

      expect(
        queryUnderstandingServiceClient.getEntitiesForQuery
      ).toHaveBeenCalledWith(input.query, "eng");
      expect(result.shouldExecuteSearch).toBe(false);
      expect(result.detectedEntities).toBeDefined();
      expect(result.detectedEntities?.search_for).toBe("institution");
    });

    it("should apply institution name as a filter when detected", async () => {
      const input = generateKeywordSearchInput({
        query:
          "find doctors at Massachusetts General Hospital who specialize in cardiology"
      });
      const mockResponse = createMockEntitiesV2Response(
        "people",
        ["cardiology"],
        [],
        ["Massachusetts General Hospital"],
        ["US"],
        ["Massachusetts"],
        []
      );

      queryUnderstandingServiceClient.getEntitiesForQuery.mockResolvedValue(
        mockResponse
      );

      // Mock msearch response for available keywords
      const mockMsearchResponse = createMockMsearchResponse([
        {
          hits: [
            createMockNestedHit(
              "addressesForHCPU",
              "addressesForHCPU.country",
              ["US"]
            )
          ]
        },
        {
          hits: [
            createMockNestedHit("addressesForHCPU", "addressesForHCPU.region", [
              "Massachusetts"
            ])
          ]
        },
        {
          hits: [
            createMockNonNestedHit("presentWorkInstitutionNames_eng", [
              "Massachusetts General Hospital"
            ])
          ]
        }
      ]);

      elasticSearchService.msearch.mockResolvedValue(mockMsearchResponse);

      const result =
        await entitySearchProcessorService.processSearchWithEntityDetection(
          input,
          keywordSearchFeatureFlags
        );

      expect(
        queryUnderstandingServiceClient.getEntitiesForQuery
      ).toHaveBeenCalledWith(input.query, "eng");
      expect(result.shouldExecuteSearch).toBe(true);
      expect(result.detectedEntities?.work_institution).toEqual([
        "Massachusetts General Hospital"
      ]);

      expect(result.modifiedInput.suppliedFilters.institution.values).toContain(
        "Massachusetts General Hospital"
      );

      expect(result.modifiedInput.query).toBe("cardiology");
    });

    it("should handle trial information when detected", async () => {
      const input = generateKeywordSearchInput({
        query: "find oncologists with completed phase 3 trials"
      });
      const mockResponse = createMockEntitiesV2Response(
        "people",
        ["oncology"],
        [],
        [],
        [],
        [],
        [],
        [],
        [],
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        "Completed",
        "Phase 3"
      );

      queryUnderstandingServiceClient.getEntitiesForQuery.mockResolvedValue(
        mockResponse
      );

      const result =
        await entitySearchProcessorService.processSearchWithEntityDetection(
          input,
          keywordSearchFeatureFlags
        );

      expect(
        queryUnderstandingServiceClient.getEntitiesForQuery
      ).toHaveBeenCalledWith(input.query, "eng");
      expect(result.shouldExecuteSearch).toBe(true);
      expect(result.detectedEntities?.trial?.status).toBe("Completed");
      expect(result.detectedEntities?.trial?.phase).toBe("Phase 3");
    });

    it("should handle errors during entity detection", async () => {
      const input = generateKeywordSearchInput({
        query:
          "find oncologists in Boston Massachusetts with publications on lung cancer"
      });
      queryUnderstandingServiceClient.getEntitiesForQuery.mockRejectedValue(
        new Error("Service error")
      );

      const result =
        await entitySearchProcessorService.processSearchWithEntityDetection(
          input,
          keywordSearchFeatureFlags
        );

      expect(
        queryUnderstandingServiceClient.getEntitiesForQuery
      ).toHaveBeenCalledWith(input.query, "eng");
      expect(result.shouldExecuteSearch).toBe(true);
      expect(result.modifiedInput).toEqual(input);
      expect(result.detectedEntities).toBeDefined();
      expect(result.detectedEntities?.search_for).toBe("");
      expect(result.detectedEntities?.indication).toEqual([]);
    });

    it("should set empty query when shouldExecuteSearch is true but no indication exists", async () => {
      const input = generateKeywordSearchInput({
        query: "find top researchers in Boston"
      });
      const mockResponse = createMockEntitiesV2Response(
        "people",
        [],
        [],
        [],
        ["US"],
        [],
        ["Boston"]
      );

      queryUnderstandingServiceClient.getEntitiesForQuery.mockResolvedValue(
        mockResponse
      );

      // Mock msearch response for location validation
      const mockMsearchResponse = createMockMsearchResponse([
        {
          hits: [
            createMockNestedHit(
              "addressesForHCPU",
              "addressesForHCPU.country",
              ["US"]
            )
          ]
        },
        {
          hits: [
            createMockNestedHit("addressesForHCPU", "addressesForHCPU.city", [
              "Boston"
            ])
          ]
        }
      ]);

      elasticSearchService.msearch.mockResolvedValue(mockMsearchResponse);

      const result =
        await entitySearchProcessorService.processSearchWithEntityDetection(
          input,
          keywordSearchFeatureFlags
        );

      expect(
        queryUnderstandingServiceClient.getEntitiesForQuery
      ).toHaveBeenCalledWith(input.query, "eng");
      expect(elasticSearchService.msearch).toHaveBeenCalledTimes(1);
      expect(result.shouldExecuteSearch).toBe(true);
      expect(result.modifiedInput.query).toBe("");

      expect(result.modifiedInput.suppliedFilters.country.values).toContain(
        "US"
      );
      expect(result.modifiedInput.suppliedFilters.city.values).toContain(
        "Boston"
      );
    });

    it("should apply claims filters when detected", async () => {
      const input = generateKeywordSearchInput({
        query: "find doctors with diabetes diagnoses and surgical procedures"
      });
      const mockResponse = createMockEntitiesV2Response(
        "people",
        [],
        [],
        [],
        [],
        [],
        [],
        ["diabetes"],
        ["surgical procedure"],
        5,
        100,
        1,
        50,
        "max"
      );

      queryUnderstandingServiceClient.getEntitiesForQuery.mockResolvedValue(
        mockResponse
      );

      // Mock msearch response for claims validation
      const mockMsearchResponse = createMockMsearchResponse([
        {
          hits: [
            createMockNestedHit(
              "DRG_diagnoses",
              "DRG_diganoses.codeAndDescription_eng",
              ["diabetes"]
            )
          ]
        },
        {
          hits: [
            createMockNestedHit(
              "DRG_procedures",
              "DRG_procedures.codeAndDescription_eng",
              ["surgical procedure"]
            )
          ]
        }
      ]);

      elasticSearchService.msearch.mockResolvedValue(mockMsearchResponse);

      const result =
        await entitySearchProcessorService.processSearchWithEntityDetection(
          input,
          keywordSearchFeatureFlags
        );

      expect(elasticSearchService.msearch).toHaveBeenCalledTimes(1);
      expect(result.shouldExecuteSearch).toBe(true);
      expect(result.detectedEntities?.claims?.diagnoses).toEqual(["diabetes"]);
      expect(result.detectedEntities?.claims?.procedures).toEqual([
        "surgical procedure"
      ]);
      expect(result.detectedEntities?.claims?.min_diagnoses_claim_count).toBe(
        5
      );
      expect(result.detectedEntities?.claims?.max_diagnoses_claim_count).toBe(
        100
      );
      expect(result.detectedEntities?.claims?.min_procedures_claim_count).toBe(
        1
      );
      expect(result.detectedEntities?.claims?.max_procedures_claim_count).toBe(
        50
      );
      expect(result.detectedEntities?.claims?.timeframe).toBe("max");
    });

    it("should apply specialty filters when detected", async () => {
      const input = generateKeywordSearchInput({
        query: "find cardiologists and oncologists"
      });
      const mockResponse = createMockEntitiesV2Response(
        "people",
        [],
        ["cardiology", "oncology"],
        [],
        [],
        [],
        []
      );

      queryUnderstandingServiceClient.getEntitiesForQuery.mockResolvedValue(
        mockResponse
      );

      // Mock msearch response for specialty validation
      const mockMsearchResponse = createMockMsearchResponse([
        {
          hits: [
            createMockNonNestedHit("specialty_eng", [
              "cardiology",
              "cardiology"
            ])
          ]
        },
        {
          hits: [
            createMockNonNestedHit("specialty_eng", ["cardiology", "oncology"])
          ]
        }
      ]);

      elasticSearchService.msearch.mockResolvedValue(mockMsearchResponse);

      const result =
        await entitySearchProcessorService.processSearchWithEntityDetection(
          input,
          keywordSearchFeatureFlags
        );

      expect(elasticSearchService.msearch).toHaveBeenCalledTimes(1);
      expect(result.shouldExecuteSearch).toBe(true);
      expect(result.detectedEntities?.specialty).toEqual([
        "cardiology",
        "oncology"
      ]);
    });

    it("should handle empty msearch results", async () => {
      const input = generateKeywordSearchInput({
        query: "find doctors with rare condition"
      });
      const mockResponse = createMockEntitiesV2Response(
        "people",
        [],
        [],
        [],
        ["US"],
        [],
        []
      );

      queryUnderstandingServiceClient.getEntitiesForQuery.mockResolvedValue(
        mockResponse
      );

      // Mock msearch response with no hits
      const mockMsearchResponse = createMockMsearchResponse([{ hits: [] }]);

      elasticSearchService.msearch.mockResolvedValue(mockMsearchResponse);

      const result =
        await entitySearchProcessorService.processSearchWithEntityDetection(
          input,
          keywordSearchFeatureFlags
        );

      expect(elasticSearchService.msearch).toHaveBeenCalledTimes(1);
      expect(result.shouldExecuteSearch).toBe(true);
      // Should have empty results since no hits were found
      expect(result.detectedEntities?.locations?.country).toEqual([]);
    });
  });

  describe("extractEntitiesFromQueryV2", () => {
    it("should return empty entities when query is empty", async () => {
      queryUnderstandingServiceClient.getEntitiesForQuery.mockResolvedValue(
        createMockEntitiesV2Response()
      );

      const result = await (
        entitySearchProcessorService as any
      ).extractEntitiesFromQueryV2("", "eng");

      expect(result).toEqual({
        search_for: "",
        indication: [],
        locations: {
          country: [],
          state: [],
          city: []
        },
        claims: {
          diagnoses: [],
          min_diagnoses_claim_count: null,
          max_diagnoses_claim_count: null,
          procedures: [],
          min_procedures_claim_count: null,
          max_procedures_claim_count: null,
          timeframe: null
        },
        specialty: [],
        trial: {
          min_count: null,
          max_count: null,
          status: null,
          phase: null
        },
        work_institution: []
      });
      expect(
        queryUnderstandingServiceClient.getEntitiesForQuery
      ).not.toHaveBeenCalled();
    });

    it("should extract all possible entities from a complex query", async () => {
      const query =
        "find oncologists in Boston Massachusetts with lung cancer diagnoses and completed trials";
      const mockResponse = createMockEntitiesV2Response(
        "people",
        ["lung cancer"],
        ["oncology"],
        ["Massachusetts General Hospital"],
        ["US"],
        ["Massachusetts"],
        ["Boston"],
        ["lung cancer"],
        [],
        5,
        100,
        null,
        null,
        null,
        1,
        10,
        "Completed",
        "Phase 3"
      );

      queryUnderstandingServiceClient.getEntitiesForQuery.mockResolvedValue(
        mockResponse
      );

      const result = await (
        entitySearchProcessorService as any
      ).extractEntitiesFromQueryV2(query, "eng");

      expect(result).toEqual({
        search_for: "people",
        indication: ["lung cancer"],
        specialty: ["oncology"],
        work_institution: ["Massachusetts General Hospital"],
        locations: {
          country: ["US"],
          state: ["Massachusetts"],
          city: ["Boston"]
        },
        claims: {
          diagnoses: ["lung cancer"],
          procedures: [],
          min_diagnoses_claim_count: 5,
          max_diagnoses_claim_count: 100,
          min_procedures_claim_count: 0,
          max_procedures_claim_count: 0,
          timeframe: ""
        },
        trial: {
          min_count: 1,
          max_count: 10,
          status: "Completed",
          phase: "Phase 3"
        }
      });
      expect(
        queryUnderstandingServiceClient.getEntitiesForQuery
      ).toHaveBeenCalledWith(query, "eng");
    });
  });
});
