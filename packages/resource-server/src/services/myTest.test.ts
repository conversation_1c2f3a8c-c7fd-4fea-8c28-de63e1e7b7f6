import {
  createMockInstance,
  generateKeywordSearchFeatureFlags
} from "../util/TestUtils";
import { QueryUnderstandingServiceClient } from "./QueryUnderstandingServiceClient";
import { EntitySearchProcessorService } from "./EntitySearchProcessorService";

import {
  EntitiesV2,
  LocationsV2,
  Claims,
  TrialInfo,
  QueryUnderstandingServiceResponse
} from "../proto/query_understanding_service_pb";
import { KeywordSearchFeatureFlags } from "./KeywordSearchResourceServiceRewrite";
import { generateKeywordSearchInput } from "./KeywordSearchResourceServiceRewrite.test";
import { ElasticSearchService } from "./ElasticSearchService";
import { ConfigService } from "./ConfigService";

describe("EntitySearchProcessorService", () => {
  let queryUnderstandingServiceClient: jest.Mocked<QueryUnderstandingServiceClient>;
  let elasticSearchService: jest.Mocked<ElasticSearchService>;
  let entitySearchProcessorService: EntitySearchProcessorService;
  let keywordSearchFeatureFlags: KeywordSearchFeatureFlags;

  beforeEach(() => {
    const configService = createMockInstance(ConfigService);
    queryUnderstandingServiceClient = createMockInstance(
      QueryUnderstandingServiceClient
    );
    elasticSearchService = createMockInstance(ElasticSearchService);

    // Mock the msearch method to return empty responses by default
    elasticSearchService.msearch.mockResolvedValue({
      took: 5,
      responses: []
    });

    entitySearchProcessorService = new EntitySearchProcessorService(
      configService,
      queryUnderstandingServiceClient,
      elasticSearchService
    );

    keywordSearchFeatureFlags = generateKeywordSearchFeatureFlags({
      enableLlmQueryParserForSearch: true
    });
  });

  // Helper function to create mock msearch responses
  function createMockMsearchResponse(responses: any[] = []) {
    return {
      took: 5,
      responses: responses.map(response => {
        if (response.error) {
          return response; // Return error responses as-is
        }
        return {
          hits: {
            total: { value: response.hits?.length || 0, relation: "eq" },
            hits: response.hits || []
          }
        };
      })
    };
  }

  // Helper function to create mock nested query response
  function createMockNestedHit(path: string, field: string, values: string[]) {
    const fieldName = field.split('.').pop();
    return {
      _id: "test-id",
      _source: {},
      inner_hits: {
        [path]: {
          hits: {
            hits: values.map(value => ({
              _source: { [fieldName!]: value }
            }))
          }
        }
      }
    };
  }

  // Helper function to create mock non-nested query response
  // function 
  // createMockNonNestedHit(field: string, values: string[]) {
  //   return {
  //     _id: "test-id",
  //     _source: { [field]: values }
  //   };
  // }

  function createMockEntitiesV2Response(
    searchFor = "",
    indicationList: string[] = [],
    specialtyList: string[] = [],
    workInstitutionList: string[] = [],
    countryList: string[] = [],
    stateList: string[] = [],
    cityList: string[] = [],
    diagnosesClaimsList: string[] = [],
    proceduresClaimsList: string[] = [],
    minDiagnosesClaimCount: number | null = null,
    maxDiagnosesClaimCount: number | null = null,
    minProceduresClaimCount: number | null = null,
    maxProceduresClaimCount: number | null = null,
    claimsTimeframe: string | null = null,
    trialMinCount: number | null = null,
    trialMaxCount: number | null = null,
    trialStatus: string | null = null,
    trialPhase: string | null = null
  ): QueryUnderstandingServiceResponse {
    const response = new QueryUnderstandingServiceResponse();

    const entitiesV2 = new EntitiesV2();

    entitiesV2.setSearchFor(searchFor);
    entitiesV2.setIndicationList(indicationList);
    entitiesV2.setSpecialtyList(specialtyList);
    entitiesV2.setWorkInstitutionList(workInstitutionList);

    // Set locations if any location data is provided
    if (countryList.length > 0 || stateList.length > 0 || cityList.length > 0) {
      const locationsV2 = new LocationsV2();
      locationsV2.setCountryList(countryList);
      locationsV2.setStateList(stateList);
      locationsV2.setCityList(cityList);
      entitiesV2.setLocations(locationsV2);
    }

    // Set claims if any claims data is provided
    if (
      diagnosesClaimsList.length > 0 ||
      proceduresClaimsList.length > 0 ||
      minDiagnosesClaimCount !== null ||
      maxDiagnosesClaimCount !== null ||
      minProceduresClaimCount !== null ||
      maxProceduresClaimCount !== null ||
      claimsTimeframe !== null
    ) {
      const claims = new Claims();
      claims.setDiagnosesList(diagnosesClaimsList);
      claims.setProceduresList(proceduresClaimsList);

      if (minDiagnosesClaimCount !== null) {
        claims.setMinDiagnosesClaimCount(minDiagnosesClaimCount);
      }
      if (maxDiagnosesClaimCount !== null) {
        claims.setMaxDiagnosesClaimCount(maxDiagnosesClaimCount);
      }
      if (minProceduresClaimCount !== null) {
        claims.setMinProceduresClaimCount(minProceduresClaimCount);
      }
      if (maxProceduresClaimCount !== null) {
        claims.setMaxProceduresClaimCount(maxProceduresClaimCount);
      }
      if (claimsTimeframe !== null) {
        claims.setTimeframe(claimsTimeframe);
      }

      entitiesV2.setClaims(claims);
    }

    // Set trial info if any trial data is provided
    if (
      trialMinCount !== null ||
      trialMaxCount !== null ||
      trialStatus !== null ||
      trialPhase !== null
    ) {
      const trialInfo = new TrialInfo();

      if (trialMinCount !== null) {
        trialInfo.setMinCount(trialMinCount);
      }
      if (trialMaxCount !== null) {
        trialInfo.setMaxCount(trialMaxCount);
      }
      if (trialStatus !== null) {
        trialInfo.setStatus(trialStatus);
      }
      if (trialPhase !== null) {
        trialInfo.setPhase(trialPhase);
      }

      entitiesV2.setTrial(trialInfo);
    }

    response.setEntitiesV2(entitiesV2);
    return response;
  }

    it("should extract entities and set shouldExecuteSearch=false when searchFor is not 'people'", async () => {
      const input = generateKeywordSearchInput({
        query: "find hospitals in New York with expertise in oncology"
      });
      const mockResponse = createMockEntitiesV2Response(
        "institution",
        ["oncology"],
        [],
        [],
        ["US"],
        ["New York"],
        []
      );

      const mockMsearchResponse = createMockMsearchResponse([
        { hits: [createMockNestedHit("addressesForHCPU", "addressesForHCPU.country", ["US"])] },
        { hits: [createMockNestedHit("addressesForHCPU", "addressesForHCPU.region", ["New York"])] }
      ]);

      elasticSearchService.msearch.mockResolvedValue(mockMsearchResponse);

      queryUnderstandingServiceClient.getEntitiesForQuery.mockResolvedValue(
        mockResponse
      );

      const result =
        await entitySearchProcessorService.processSearchWithEntityDetection(
          input,
          keywordSearchFeatureFlags
        );

      expect(
        queryUnderstandingServiceClient.getEntitiesForQuery
      ).toHaveBeenCalledWith(input.query, "eng");
      expect(result.shouldExecuteSearch).toBe(false);
      expect(result.detectedEntities).toBeDefined();
      expect(result.detectedEntities?.search_for).toBe("institution");
    });
}
);

