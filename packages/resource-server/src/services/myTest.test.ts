/* eslint-disable  @typescript-eslint/no-non-null-assertion */
/* eslint-disable  @typescript-eslint/no-explicit-any */
import { faker } from "@faker-js/faker";
import {
  createMockInstance,
  generateInstitutionSearchFeatureFlags,
  generateMockElasticsearchResponseWithAggregations,
  mockQueryUnderstandingServerResponse
} from "../util/TestUtils";
import {
  InstitutionsResourceService,
  InstitutionSearchFeatureFlags,
  institutionSearchFeatureFlagTypes,
  featureFlagDefaults,
  priorityBasedScoreScaleRanges,
  assetAggregationValues,
  fieldPriorityMapForNewTLH1DefaultRanking,
  RootFieldsForScoringForH1Default,
  SCRIPT_SCORE_FOR_TRIAL_INVESIGATOR_COUNT
} from "./InstitutionsResourceService";
import { ConfigService } from "./ConfigService";
import {
  InstitutionSortOptions,
  InstitutionsSearchInput} from "@h1nyc/search-sdk";
import { ElasticSearchInstitutionsService } from "./ElasticSearchInstitutionsService";
// import {
//   QueryDslFunctionScoreContainer} from "@elastic/elasticsearch/lib/api/types";
import * as _ from "lodash";
import { QueryUnderstandingServiceClient } from "./QueryUnderstandingServiceClient";
import {
  INSTITUTION_ACCESS_LEVEL,
  InstitutionTagResourceClient} from "@h1nyc/account-sdk";
import { InstitutionClaimsQueryBuilder } from "./queryBuilders/InstitutionClaimsQueryBuilder";
import { QueryParserService } from "./QueryParserService";
import { InstitutionsClaimsResponseAdapterService } from "./InstitutionsClaimsResponseAdapterService";
import { FeatureFlagsService } from "@h1nyc/systems-feature-flags";
import { LDFlagsState } from "launchdarkly-node-server-sdk";
import { when } from "jest-when";
import { RulesParserService } from "./RulesParserService";
import { PatientDiversityStatsService } from "./PatientDiversityStatsService";
import { IndicationsTreeSearchService } from "./IndicationsTreeSearchService";
import { InstitutionsSearchResponseAdapterService } from "./InstitutionsSearchResponseAdapterService";
import { InstitutionNameSearchResourceService } from "./InstitutionNameSearchResourceService";
import {
  CcsrIcdMappingRepository,
  CcsrPxMappingRepository,
  ClaimCodesRepository
} from "@h1nyc/pipeline-repositories";

const INSTITUTION_IS_IOL = {
  term: {
    isIol: true
  }
};

const IS_IOL_DOC = {
  term: {
    join_field: "iol"
  }
};



// function getSaturationScriptFunction(
//   saturationParam: number
// ): QueryDslFunctionScoreContainer {
//   return {
//     script_score: {
//       script: {
//         source: `saturation(_score, params['saturation_param'])`,
//         params: {
//           saturation_param: saturationParam
//         }
//       }
//     }
//   };
// }


// interface TrialsByInstitutionElasticsearchSource {
//   id: number;
//   masterOrganizationId?: number;
//   trials_count: number;
//   person_count: number;
// }


const featureFlagsService = createMockInstance(
  FeatureFlagsService as any
) as FeatureFlagsService;

function generateFeatureFlagsState(
  featureFlagValues: Partial<InstitutionSearchFeatureFlags>
) {
  const featureFlags = {
    ...generateInstitutionSearchFeatureFlags(),
    ...featureFlagValues
  };

  const getFlagValue = jest.fn();

  for (const flag of institutionSearchFeatureFlagTypes) {
    const flagKey = featureFlagDefaults[flag].key;
    when(getFlagValue).calledWith(flagKey).mockReturnValue(featureFlags[flag]);
  }

  return {
    getFlagValue
  } as unknown as LDFlagsState;
}

function buildProjectIdFilter(projectId: string) {
  return {
    bool: {
      should: [
        {
          bool: {
            must: [
              {
                exists: {
                  field: "projectIds"
                }
              },
              {
                terms: {
                  projectIds: [projectId]
                }
              }
            ]
          }
        },
        {
          bool: {
            must_not: [
              {
                exists: {
                  field: "projectIds"
                }
              }
            ]
          }
        }
      ]
    }
  };
}



export const getH1DefaultRankingScriptScoreParams = (
  field: RootFieldsForScoringForH1Default
) => {
  const { max, mean, min } = assetAggregationValues[field];
  const priority = fieldPriorityMapForNewTLH1DefaultRanking[field];
  const { targetMax, targetMin } = priorityBasedScoreScaleRanges[priority];

  return {
    mean,
    max,
    min,
    targetMax,
    targetMin
  };
};

const institutionsSearchResponseAdapterService = createMockInstance(
  InstitutionsSearchResponseAdapterService
);
const institutionNameSearchResourceService = createMockInstance(
  InstitutionNameSearchResourceService
);
const patientDiversityService = createMockInstance(
  PatientDiversityStatsService
);

beforeEach(() => {
  jest.clearAllMocks();

  featureFlagsService.getAllFlags = jest
    .fn()
    .mockResolvedValue(
      generateFeatureFlagsState(generateInstitutionSearchFeatureFlags())
    );
});
describe("ccsr_px", () => {
  it("should should clauses to contain procedure, congress, prescription clauses with 0 score ", async () => {
              const input: InstitutionsSearchInput = {
                query: faker.datatype.string(),
                projectId: faker.datatype.string(),
                accessLevel: INSTITUTION_ACCESS_LEVEL.ALL,
                sortBy: InstitutionSortOptions.H1_DEFAULT
              };
  
              const configService = createMockInstance(ConfigService);
              configService.elasticInstitutionsIndex = faker.datatype.string();
  
              const queryUnderstandingServiceClient = createMockInstance(
                QueryUnderstandingServiceClient
              );
              const queryUnderstandingServerResponse =
                mockQueryUnderstandingServerResponse(input.query!, false);
              queryUnderstandingServiceClient.analyze.mockResolvedValue(
                queryUnderstandingServerResponse
              );
  
              const elasticSearchService = createMockInstance(
                ElasticSearchInstitutionsService
              );
              elasticSearchService.mget.mockResolvedValue({ docs: [] });
              elasticSearchService.query.mockResolvedValue(
                generateMockElasticsearchResponseWithAggregations()
              );
  
              const institutionTagResourceClient = createMockInstance(
                InstitutionTagResourceClient
              );
  
              const institutionClaimsQueryBuilder = createMockInstance(
                InstitutionClaimsQueryBuilder
              );
  
              const queryParserService = createMockInstance(QueryParserService);
  
              const institutionsClaimsResponseAdapterService = createMockInstance(
                InstitutionsClaimsResponseAdapterService
              );
              const indicationTreeSearchService = createMockInstance(
                IndicationsTreeSearchService
              );
  
              featureFlagsService.getAllFlags = jest.fn().mockResolvedValue(
                generateFeatureFlagsState({
                  enableNewIolH1DefaultRanking: true
                })
              );
  
              const rulesParserService = createMockInstance(RulesParserService);
              rulesParserService.parseRulesToEsQueries.mockReturnValue([]);
  
              const ccsrIcdMappingRepository = createMockInstance(
                CcsrIcdMappingRepository
              );
  
              const ccsrPxMappingRepository = createMockInstance(
                CcsrPxMappingRepository
              );
  
              const claimCodesRepository =
                createMockInstance(ClaimCodesRepository);
  
              const institutionsResourceService = new InstitutionsResourceService(
                configService,
                elasticSearchService,
                queryUnderstandingServiceClient,
                institutionTagResourceClient,
                institutionClaimsQueryBuilder,
                queryParserService,
                institutionsClaimsResponseAdapterService,
                featureFlagsService,
                rulesParserService,
                patientDiversityService,
                indicationTreeSearchService,
                institutionsSearchResponseAdapterService,
                institutionNameSearchResourceService,
                ccsrIcdMappingRepository,
                ccsrPxMappingRepository,
                claimCodesRepository
              );
  
              await institutionsResourceService.search(input);
  
              expect(elasticSearchService.query).toHaveBeenCalledWith(
                expect.objectContaining({
                  index: configService.elasticInstitutionsIndex,
                  track_total_hits: true,
                  _source: expect.any(Object),
                  query: {
                    bool: {
                      should: expect.arrayContaining([
                        {
                          function_score: {
                            query: {
                              nested: {
                                path: "diagnoses",
                                score_mode: "sum",
                                query: expect.any(Object),
                                inner_hits: expect.any(Object)
                              }
                            },
                            boost_mode: "replace",
                            functions: [
                              {
                                script_score: {
                                  script: {
                                    source:
                                      "\n            double score = _score;\n\n            // Define the original minimum and maximum score\n            double originalMin = params.min; \n            double originalMax = params.max;\n            \n            // Define the target range\n            double targetMin = params.targetMin;\n            double targetMax = params.targetMax;\n\n            // Scale the score to the range [targetMin, targetMax]\n            double scaledScore = targetMin + ((score - originalMin) / (originalMax - originalMin)) * (targetMax - targetMin);\n            \n            // Return the scaled score\n            return scaledScore;\n        ",
                                    params: getH1DefaultRankingScriptScoreParams(
                                      "totalDiagnosesCount"
                                    )
                                  }
                                }
                              }
                            ]
                          }
                        },
                        {
                          function_score: {
                            query: {
                              nested: {
                                path: "trials",
                                score_mode: "sum",
                                query: {
                                  function_score: {
                                    boost_mode: "replace",
                                    query: {
                                      bool: {
                                        must: [
                                          {
                                            simple_query_string:
                                              expect.any(Object)
                                          }
                                        ],
                                        should: [
                                          {
                                            term: { "trials.status": "Completed" }
                                          }
                                        ]
                                      }
                                    }
                                  }
                                },
                                inner_hits: expect.any(Object)
                              }
                            },
                            boost_mode: "replace",
                            functions: [
                              {
                                script_score: {
                                  script: {
                                    source:
                                      "\n            double score = _score;\n\n            // Define the original minimum and maximum score\n            double originalMin = params.min; \n            double originalMax = params.max;\n            \n            // Define the target range\n            double targetMin = params.targetMin;\n            double targetMax = params.targetMax;\n\n            // Scale the score to the range [targetMin, targetMax]\n            double scaledScore = targetMin + ((score - originalMin) / (originalMax - originalMin)) * (targetMax - targetMin);\n            \n            // Return the scaled score\n            return scaledScore;\n        ",
                                    params:
                                      getH1DefaultRankingScriptScoreParams(
                                        "trials_count"
                                      )
                                  }
                                }
                              }
                            ]
                          }
                        },
                        {
                          function_score: {
                            query: {
                              nested: {
                                path: "payments",
                                score_mode: "sum",
                                query: {
                                  function_score: {
                                    boost_mode: "replace",
                                    query: {
                                      bool: {
                                        filter: [
                                          {
                                            simple_query_string:
                                              expect.any(Object)
                                          }
                                        ]
                                      }
                                    },
                                    functions: [
                                      {
                                        filter: {
                                          term: {
                                            "payments.payment_type": "research"
                                          }
                                        },
                                        field_value_factor: {
                                          field: "payments.amount",
                                          missing: 0
                                        }
                                      }
                                    ]
                                  }
                                },
                                inner_hits: expect.any(Object)
                              }
                            },
                            boost_mode: "replace",
                            functions: [
                              {
                                script_score: {
                                  script: {
                                    source:
                                      "\n            double score = _score;\n\n            // Define the original minimum and maximum score\n            double originalMin = params.min; \n            double originalMax = params.max;\n            \n            // Define the target range\n            double targetMin = params.targetMin;\n            double targetMax = params.targetMax;\n\n            // Scale the score to the range [targetMin, targetMax]\n            double scaledScore = targetMin + ((score - originalMin) / (originalMax - originalMin)) * (targetMax - targetMin);\n            \n            // Return the scaled score\n            return scaledScore;\n        ",
                                    params: {
                                      ...getH1DefaultRankingScriptScoreParams(
                                        "research_institution_payment_total"
                                      )
                                    }
                                  }
                                }
                              }
                            ]
                          }
                        },
                        {
                          function_score: {
                            query: {
                              nested: {
                                path: "trials",
                                score_mode: "sum",
                                inner_hits: {
                                  name: "trialInvestigatorCount",
                                  _source: false
                                },
                                query: {
                                  function_score: {
                                    boost_mode: "replace",
                                    query: {
                                      bool: {
                                        filter: [
                                          {
                                            simple_query_string:
                                              expect.any(Object)
                                          }
                                        ]
                                      }
                                    },
                                    functions: [
                                      {
                                        script_score: {
                                          script:
                                            SCRIPT_SCORE_FOR_TRIAL_INVESIGATOR_COUNT
                                        }
                                      }
                                    ]
                                  }
                                }
                              }
                            },
                            boost_mode: "replace",
                            functions: [
                              {
                                script_score: {
                                  script: {
                                    source:
                                      "\n            double score = _score;\n\n            // Define the original minimum and maximum score\n            double originalMin = params.min; \n            double originalMax = params.max;\n            \n            // Define the target range\n            double targetMin = params.targetMin;\n            double targetMax = params.targetMax;\n\n            // Scale the score to the range [targetMin, targetMax]\n            double scaledScore = targetMin + ((score - originalMin) / (originalMax - originalMin)) * (targetMax - targetMin);\n            \n            // Return the scaled score\n            return scaledScore;\n        ",
                                    params: {
                                      ...getH1DefaultRankingScriptScoreParams(
                                        "trialInvestigatorCount"
                                      )
                                    }
                                  }
                                }
                              }
                            ]
                          }
                        },
                        {
                          nested: {
                            path: "procedures",
                            query: {
                              constant_score: {
                                filter: {
                                  simple_query_string: expect.any(Object)
                                },
                                boost: 0
                              }
                            },
                            inner_hits: {
                              _source: false,
                              docvalue_fields: ["procedures.count"],
                              size: 1000
                            }
                          }
                        },
                        {
                          nested: {
                            path: "prescriptions",
                            query: {
                              constant_score: {
                                filter: {
                                  simple_query_string: expect.any(Object)
                                },
                                boost: 0
                              }
                            },
                            inner_hits: {
                              _source: false,
                              docvalue_fields: [
                                "prescriptions.num_prescriptions"
                              ],
                              size: 1000
                            }
                          }
                        },
                        {
                          nested: {
                            path: "congresses",
                            query: {
                              constant_score: {
                                filter: {
                                  simple_query_string: expect.any(Object)
                                },
                                boost: 0
                              }
                            },
                            inner_hits: { _source: false }
                          }
                        }
                      ]),
                      minimum_should_match: 1,
                      filter: [
                        INSTITUTION_IS_IOL,
                        buildProjectIdFilter(input.projectId),
                        IS_IOL_DOC
                      ]
                    }
                  },
                  aggs: {
                    city: expect.any(Object),
                    country: expect.any(Object),
                    postal_code: expect.any(Object),
                    region: expect.any(Object),
                    orgTypes: expect.any(Object),
                    orgTypesLevel2: expect.any(Object),
                    orgTypesLevel3: expect.any(Object)
                  }
                })
              );
            });
});