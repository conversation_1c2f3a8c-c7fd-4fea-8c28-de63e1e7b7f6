import { ModuleMocker } from "jest-mock";
import {
  AggregationsAggregate,
  SearchHit,
  SearchResponse
} from "@elastic/elasticsearch/lib/api/types";
import { faker } from "@faker-js/faker";
import { KeywordSearchFeatureFlags } from "../services/KeywordSearchResourceServiceRewrite";
import { FilterInterface } from "@h1nyc/search-sdk";
import {
  DocCountBucket,
  ElasticsearchInstitutionDoc,
  InstitutionSearchFeatureFlags,
  LocationFilteredMatchingAggregation
} from "../services/InstitutionsResourceService";
import _ from "lodash";
import { QueryUnderstandingServiceResponse } from "../proto/query_understanding_service_pb";
import { QueryIntent } from "../proto/query_intent_pb";
import { Synonym } from "../proto/synonym_pb";
import { ElasticsearchCongressDoc } from "../services/CongressSearchResourceService";
type ClassConstructor<T> =
  | { new (...args: any[]): T }
  | { (...args: any[]): T };

const mocker = new ModuleMocker(globalThis);

/**
 * Create mock instance of given class or function constructor
 *
 * @param classConstructor Class constructor
 * @returns New instance of given class constructor with all methods being mocked
 */
export function createMockInstance<T>(
  classConstructor: ClassConstructor<T>
): jest.Mocked<T> {
  // get metadata from class constructor or function
  const component = mocker.getMetadata(classConstructor);

  if (component === null) {
    throw Error(
      `createMockInstance failed to getMetadata for: ${classConstructor.name}`
    );
  }

  // use metadata to create new mocked class
  const Mock = mocker.generateFromMetadata<jest.Mocked<any>>(component);

  // instantiate and return mocked class with all methods stubbed
  return new Mock();
}

export function generateMockElasticsearchResponse<T>(
  hits: Array<SearchHit<T>> = []
): SearchResponse<T> {
  return {
    took: faker.datatype.number(),
    timed_out: false,
    _shards: {
      total: faker.datatype.number(),
      successful: faker.datatype.number(),
      skipped: faker.datatype.number(),
      failed: faker.datatype.number()
    },
    hits: {
      total: {
        value: faker.datatype.number(),
        relation: "eq"
      },
      max_score: faker.datatype.number(),
      hits
    }
  };
}

export function generateMockElasticsearchResponseWithAggregations<T>(
  hits: T[] = [],
  aggregations: Record<string, AggregationsAggregate> = {},
  total = faker.datatype.number()
): SearchResponse<T> {
  return {
    took: faker.datatype.number(),
    timed_out: false,
    _shards: {
      total: faker.datatype.number(),
      successful: faker.datatype.number(),
      skipped: faker.datatype.number(),
      failed: faker.datatype.number()
    },
    hits: {
      total: {
        value: total,
        relation: "eq"
      },
      max_score: faker.datatype.number(),
      hits: hits.map((hit) => ({
        _id: faker.datatype.uuid(),
        _index: faker.datatype.string(),
        _source: hit
      }))
    },
    aggregations
  };
}

export function generateMockElasticsearchHit<T>(
  docId: string = faker.datatype.uuid(),
  _source: T
): SearchHit<T> {
  return {
    _index: faker.datatype.string(),
    _id: docId,
    _score: faker.datatype.number(),
    _source
  };
}

export function generateKeywordSearchFeatureFlags(
  overrides?: Partial<KeywordSearchFeatureFlags>
): KeywordSearchFeatureFlags {
  const flags: KeywordSearchFeatureFlags = {
    enableNewClaimsSearch: false,
    enableMinMaxResultCache: false,
    enableQueryContextualPaymentsFiltering: false,
    enableQueryIntent: false,
    enableSpellSuggestion: false,
    enableCompletionSuggestion: false,
    enableSearchHighlights: false,
    enableConferenceIntentFilter: false,
    enableLocationConfidenceRanking: false,
    enableCTMSV2: false,
    enableIntentBasedKeywordSearch: false,
    enableKWSearchPersonalisation: false,
    enableTagsInElasticsearch: false,
    enableUniquePatientCountForClaims: false,
    enableBrazilianClaims: true,
    disableUniquePatientCountForOnlyProcedures: false,
    enableEnhancedHasCTMSDataFilterBehavior: false,
    enableNestedIndicationFilter: false,
    enableAsianPacificIslanderDiversityBreakOut: false,
    enablePenaltyForZeroTrialsDiversityRanking: false,
    enableCcsrExclusionForMatchedCounts: false,
    enableLocationFilterRegionRollup: false,
    enableIndicationSynonymPrescriptionSearch: false,
    enableInternalUsersForRankingDebug: false,
    enableNewH1DefaultRanking: false,
    enableAiqData: false,
    enableBothOperatorForSearch: false,
    enableLlmQueryParserForSearch: false,
    enableNewGlobalLeaderTier: false,
    enableExUSGlobalLeader: false,
    enableCountrySpecificNonIndicationLeaderFilters: false
  };

  return { ...flags, ...overrides };
}

export function generateInstitutionSearchFeatureFlags(
  overrides?: Partial<InstitutionSearchFeatureFlags>
): InstitutionSearchFeatureFlags {
  const flags: InstitutionSearchFeatureFlags = {
    enableCTMSV2: false,
    enableClaimsFilterFunctionScore: true,
    enableClaimsFilteringMatchedCountsUpdate: true,
    enableTagsInElasticsearch: false,
    enableNonIOLsInTrialLandscapeInstitutionSearch: false,
    enableUniquePatientCountForClaims: false,
    enableBrazilianClaims: true,
    enablePatientMatchesForInstitutionProfilePage: false,
    disableUniquePatientCountForOnlyProcedures: false,
    enableEnhancedHasCTMSDataFilterBehavior: false,
    useDocIdForParentPatientClaims: false,
    enableInstitutionNameSuggest: false,
    enableLocationFilterRegionRollup: false,
    enableCcsrExclusionForMatchedCounts: false,
    enableNewIolH1DefaultRanking: false,
    enablePenalizeAutoCreatedIOLs: false,
    enableBothOperatorForSearch: false,
    enableInstitutionOrgHierarchy: false
  };

  return { ...flags, ...overrides };
}

export const getEmptyKeywordSearchFilters = (): FilterInterface => {
  return {
    dateRangePicker: {
      // TODO: figure out why someone decided this can't be nullable
      min: 0,
      max: null,
      active: false
    },
    designations: {
      values: []
    },
    designationsExclusion: {
      values: []
    },
    npi: {
      values: []
    },
    institutionType: {
      values: []
    },
    specialty: {
      values: []
    },
    specialtyExclusion: {
      values: []
    },
    country: {
      values: []
    },
    excludeCountry: {
      values: []
    },
    state: {
      values: []
    },
    excludeState: {
      values: []
    },
    city: {
      values: []
    },
    excludeCity: {
      values: []
    },
    zipCode: {
      values: []
    },
    excludeZipCode: {
      values: []
    },
    institution: {
      values: []
    },
    presentWorkInstitutions: {
      values: []
    },
    presentWorkInstitutionsExclusion: {
      values: []
    },
    pastAndPresentWorkInstitutions: {
      values: []
    },
    studentInstitutions: {
      values: []
    },
    studentInstitutionsExclusion: {
      values: []
    },
    graduationYearRange: {
      min: null,
      max: null
    },
    publications: {
      minCount: {
        value: null
      },
      maxCount: {
        value: null
      },
      socialMediaMinCount: {
        value: null
      },
      socialMediaMaxCount: {
        value: null
      },
      journal: {
        values: []
      },
      type: {
        values: []
      },
      isFirstOrder: {
        value: null
      },
      isLastOrder: {
        value: null
      },
      publicationDate: {
        value: null
      }
    },
    publicationsExclusion: {
      journal: {
        values: []
      },
      type: {
        values: []
      }
    },
    tags: {
      name: {
        values: []
      },
      publicTagIds: [],
      privateTagIds: [],
      programmaticTagIds: []
    },
    exclusionTags: {
      publicTagIds: [],
      privateTagIds: [],
      programmaticTagIds: []
    },
    claims: {
      diagnosesICDMinCount: {
        value: null
      },
      diagnosesICDMaxCount: {
        value: null
      },
      diagnosesICD: {
        values: []
      },
      genericNames: {
        values: []
      },
      drugClasses: {
        values: []
      },
      brandNames: {
        values: []
      },
      brandNameOrGeneric: { values: [] },
      prescriptionsMinCount: {
        value: null
      },
      prescriptionsMaxCount: {
        value: null
      },
      prescriptionsTimeFrame: {
        value: null
      },
      proceduresCPTMinCount: {
        value: null
      },
      proceduresCPTMaxCount: {
        value: null
      },
      proceduresCPT: {
        values: []
      },
      proceduresHCPCMinCount: {
        value: null
      },
      proceduresHCPC: {
        values: []
      },
      timeFrame: {
        value: null
      },
      showUniquePatients: {
        value: null
      },
      ccsr: {
        values: []
      },
      ccsrPx: {
        values: []
      }
    },
    exclusionClaims: {
      diagnosesICD: {
        values: []
      },
      proceduresCPT: {
        values: []
      },
      proceduresHCPC: {
        values: []
      },
      genericNames: {
        values: []
      },
      drugClasses: {
        values: []
      },
      brandNames: {
        values: []
      },
      ccsr: {
        values: []
      }
    },
    trials: {
      minCount: {
        value: null
      },
      maxCount: {
        value: null
      },
      status: {
        values: []
      },
      phase: {
        values: []
      },
      studyType: {
        values: []
      },
      id: {
        values: []
      },
      funderType: {
        values: []
      },
      sponsor: {
        values: []
      },
      sponsorType: {
        values: []
      },
      timeFrame: {
        min: null,
        max: null,
        key: "max"
      },
      biomarkers: {
        values: []
      }
    },
    trialsExclusion: {
      status: {
        values: []
      },
      phase: {
        values: []
      },
      studyType: {
        values: []
      },
      sponsor: {
        values: []
      },
      sponsorType: {
        values: []
      }
    },
    congresses: {
      minCount: {
        value: null
      },
      maxCount: {
        value: null
      },
      name: {
        values: []
      },
      type: {
        values: []
      },
      organizerName: {
        values: []
      },
      sessionType: {
        values: []
      },
      timeFrame: {
        value: null
      }
    },
    congressesExclusion: {
      name: {
        values: []
      },
      organizerName: {
        values: []
      }
    },
    engagements: {
      minCount: {
        value: null
      }
    },
    grants: {
      minAmount: {
        value: null
      },
      funder: {
        values: []
      }
    },
    payments: {
      minAmount: {
        value: null
      },
      maxAmount: {
        value: null
      },
      company: {
        values: []
      },
      drugOrDevice: {
        values: []
      },
      fundingType: {
        values: []
      },
      category: {
        values: []
      }
    },
    paymentsExclusion: {
      company: {
        values: []
      },
      drugOrDevice: {
        values: []
      },
      fundingType: {
        values: []
      },
      category: {
        values: []
      }
    },
    referrals: {
      serviceLine: {
        values: []
      },
      minReferralsReceived: {
        value: null
      },
      maxReferralsReceived: {
        value: null
      },
      minReferralsSent: {
        value: null
      },
      maxReferralsSent: {
        value: null
      }
    },
    referralsExclusion: {
      serviceLine: {
        values: []
      }
    },
    patientsDiversity: {
      ageRange: {
        values: []
      },
      sex: {
        values: []
      },
      race: {
        values: []
      },
      raceMix: {
        thresholdValue: []
      }
    },
    patientsDiversityExclusion: {
      ageRange: {
        values: []
      },
      sex: {
        values: []
      },
      race: {
        values: []
      }
    },
    providerDiversity: {
      languagesSpoken: {
        values: []
      },
      sex: {
        values: []
      },
      race: {
        values: []
      }
    },
    providerDiversityExclusion: {
      languagesSpoken: {
        values: []
      },
      sex: {
        values: []
      },
      race: {
        values: []
      }
    },
    hasLinkedin: {
      value: null
    },
    hasTwitter: {
      value: null
    },
    hasCTMSData: {
      value: null
    },
    isFacultyOpinionsMember: {
      value: null
    },
    geoBoundingBox: {
      value: null
    },
    digitalLeader: {
      value: null
    },
    geoDistance: {
      value: null
    },
    geoShape: {
      value: null
    },
    peopleIds: undefined,
    h1dnIds: undefined,
    indications: {
      values: []
    },
    societyAffiliations: {
      values: []
    }
  };
};

export function generateMockElasticsearchInstitution(
  overrides: Partial<ElasticsearchInstitutionDoc> = {}
): ElasticsearchInstitutionDoc {
  const address = {
    street1: faker.address.streetAddress(),
    street2: faker.address.secondaryAddress(),
    street3: faker.address.secondaryAddress(),
    city: faker.address.city(),
    region: faker.address.state(),
    regionCode: faker.address.stateAbbr(),
    postalCode: faker.address.zipCode(),
    country: faker.address.country(),
    countryCode: faker.address.countryCode()
  };

  const masterOrganizationId = faker.datatype.number();

  const institution = {
    id: masterOrganizationId,
    institutionId: faker.datatype.number(),
    masterOrganizationId,
    groupH1dnOrganizationId: faker.datatype.uuid(),
    name: faker.company.name(),
    institution_type: faker.datatype.string(),
    website: faker.internet.domainName(),
    person_count: faker.datatype.number(),
    affiliation_count: faker.datatype.number(),
    publication_count: faker.datatype.number(),
    citation_count: faker.datatype.number(),
    social_mention_count: faker.datatype.number(),
    conference_count: faker.datatype.number(),
    general_institution_payment_total: faker.datatype.number(),
    research_institution_payment_total: faker.datatype.number(),
    general_institution_payment_count: faker.datatype.number(),
    research_institution_payment_count: faker.datatype.number(),
    sent_referral_count: faker.datatype.number(),
    received_referral_count: faker.datatype.number(),
    diagnosis_count: faker.datatype.number(),
    diagnosesCount_1_year: faker.datatype.number(),
    diagnosesCount_2_year: faker.datatype.number(),
    diagnosesCount_5_year: faker.datatype.number(),
    diagnosesUniqueCount: faker.datatype.number(),
    diagnosesUniqueCount_1_year: faker.datatype.number(),
    diagnosesUniqueCount_2_year: faker.datatype.number(),
    diagnosesUniqueCount_5_year: faker.datatype.number(),
    procedure_count: faker.datatype.number(),
    proceduresCount_1_year: faker.datatype.number(),
    proceduresCount_2_year: faker.datatype.number(),
    proceduresCount_5_year: faker.datatype.number(),
    proceduresUniqueCount: faker.datatype.number(),
    proceduresUniqueCount_1_year: faker.datatype.number(),
    proceduresUniqueCount_2_year: faker.datatype.number(),
    proceduresUniqueCount_5_year: faker.datatype.number(),
    num_prescriptions: faker.datatype.number(),
    num_prescriptions_1_year: faker.datatype.number(),
    num_prescriptions_2_year: faker.datatype.number(),
    num_prescriptions_5_year: faker.datatype.number(),
    trials_count: faker.datatype.number(),
    beds: faker.datatype.number(),
    region: faker.datatype.string(),
    linkedin_url: faker.datatype.string(),
    twitter_url: faker.datatype.string(),
    "address.street1": address.street1,
    "address.street2": address.street2,
    "address.street3": address.street3,
    "address.city": address.city,
    "address.region": address.region,
    "address.region_code": address.regionCode,
    "address.country": address.country,
    "address.country_code": address.countryCode,
    "address.postal_code": address.postalCode,
    "address.token": [
      address.countryCode,
      address.regionCode,
      address.city
    ].join(" ,"),
    "filters.country": address.countryCode,
    "filters.region": [address.countryCode, address.regionCode].join("|"),
    "filters.city": [
      address.countryCode,
      address.regionCode,
      address.city
    ].join("|"),
    "filters.postal_code": [
      address.countryCode,
      address.regionCode,
      address.city,
      address.postalCode
    ].join("|"),
    location: {
      lat: faker.datatype.number().toString(),
      lon: faker.datatype.number().toString()
    },
    patientsDiversityRatio: {
      whiteNonHispanic: faker.datatype.number()
    },
    patientsDiversityRatioUk: {
      white: faker.datatype.number()
    },
    patientsDiversityRatioFrance: {
      franceNative: faker.datatype.number(),
      europeanUnion: faker.datatype.number(),
      otherEuropean: faker.datatype.number(),
      middleEastAndNorthAfrica: faker.datatype.number()
    },
    patientsDiversityRatioSpain: {
      spain: faker.datatype.number(),
      europeanUnion: faker.datatype.number(),
      otherEuropean: faker.datatype.number(),
      middleEastAndNorthAfrica: faker.datatype.number()
    },
    patientDiversity: {
      age: [{ range: faker.datatype.string(), count: faker.datatype.number() }],
      sex: [{ sex: faker.datatype.string(), count: faker.datatype.number() }]
    },
    hasCtms: faker.datatype.boolean(),
    inCtmsNetwork: faker.datatype.boolean(),
    nci_designated_cancer_center: faker.datatype.boolean(),
    national_comprehensive_cancer_network_member: faker.datatype.boolean(),
    teaching_hospital: faker.datatype.boolean(),
    tagIds: [faker.datatype.uuid(), faker.datatype.uuid()],
    orgTypes: faker.datatype.string(),
    orgTypesLevel2: faker.datatype.string(),
    orgTypesLevel3: faker.datatype.string()
  };

  return { ...institution, ...overrides };
}

export function generateMockElasticsearchCongressHit(
  overrides: Partial<ElasticsearchCongressDoc> = {}
): ElasticsearchCongressDoc {
  const addresses = [
    {
      street1: faker.address.streetAddress(),
      street2: faker.address.secondaryAddress(),
      street3: faker.address.secondaryAddress(),
      city: faker.address.city(),
      region: faker.address.state(),
      regionCode: faker.address.stateAbbr(),
      postalCode: faker.address.zipCode(),
      country: faker.address.country(),
      countryCode: faker.address.countryCode()
    }
  ];

  const speakers = [
    {
      id: faker.datatype.number().toString(),
      name: faker.name.fullName(),
      role: faker.datatype.string()
    }
  ];

  const translations = [
    {
      name: faker.datatype.string(),
      description: faker.datatype.string(),
      society: faker.datatype.string(),
      language_code: faker.datatype.string()
    }
  ];

  const congress: ElasticsearchCongressDoc = {
    h1_conference_id: faker.datatype.number().toString(),
    h1_series_id: faker.datatype.number().toString(),
    name: faker.datatype.string(),
    series_name: faker.datatype.string(),
    society: faker.datatype.string(),
    "filters.congress_type": faker.datatype.string(),
    "filters.start_date": faker.date.recent().getTime(),
    "filters.end_date": faker.date.recent().getTime(),
    speakers,
    addresses,
    translations
  };

  return { ...congress, ...overrides };
}

export function generateMockAggregations(
  max = faker.datatype.number() % 10
): DocCountBucket[] {
  return _.range(1, max).map(() => ({
    key: faker.datatype.string(),
    doc_count: faker.datatype.number()
  }));
}

export function generateMockLocationAggregationsForInstitutions(
  max = faker.datatype.number() % 10
): LocationFilteredMatchingAggregation {
  return {
    filtered_matching: {
      buckets: _.range(1, max).map(() => ({
        key: faker.datatype.string(),
        doc_count: faker.datatype.number(),
        regions_in: {
          buckets: _.range(1, max).map(() => ({
            key: faker.datatype.string(),
            doc_count: faker.datatype.number()
          }))
        }
      }))
    }
  };
}

export function generateMockDiversityDashboardAggregation(
  max = faker.datatype.number() % 10
) {
  return {
    location_filtered: {
      education_counts: {
        total_education_count: {
          value: faker.datatype.number()
        },
        by_education: {
          buckets: _.range(1, max).map(() => ({
            key: faker.datatype.string(),
            doc_count: faker.datatype.number(),
            count: {
              value: faker.datatype.number()
            }
          }))
        }
      },
      gender_counts: {
        total_gender_count: {
          value: faker.datatype.number()
        },
        by_gender: {
          buckets: _.range(1, max).map(() => ({
            key: faker.datatype.string(),
            doc_count: faker.datatype.number(),
            count: {
              value: faker.datatype.number()
            }
          }))
        }
      },
      age_counts: {
        total_age_count: {
          value: faker.datatype.number()
        },
        by_age: {
          buckets: _.range(1, max).map(() => ({
            key: faker.datatype.string(),
            doc_count: faker.datatype.number(),
            count: {
              value: faker.datatype.number()
            }
          }))
        }
      },
      nationality_counts: {
        total_nationality_count: {
          value: faker.datatype.number()
        },
        by_nationality: {
          buckets: _.range(1, max).map(() => ({
            key: faker.datatype.string(),
            doc_count: faker.datatype.number(),
            count: {
              value: faker.datatype.number()
            }
          }))
        }
      }
    }
  };
}

export function generateMockFranceHeatmapAggregation(
  max = faker.datatype.number() % 10
) {
  return _.range(1, max).map(() => ({
    key: faker.datatype.string(),
    doc_count: faker.datatype.number(),
    nested_agg: {
      doc_count: faker.datatype.number(),
      filtered_matching: {
        doc_count: faker.datatype.number(),
        patientCountOceania: {
          value: faker.datatype.number()
        },
        patientCountCentralAndSouthAfrica: {
          value: faker.datatype.number()
        },
        patientCountCentralAmerica: {
          value: faker.datatype.number()
        },
        patientCountSoutheastAsia: {
          value: faker.datatype.number()
        },
        patientCountSouthAmerica: {
          value: faker.datatype.number()
        },
        patientCountOtherEuropean: {
          value: faker.datatype.number()
        },
        patientCountEuropeanUnion: {
          value: faker.datatype.number()
        },
        patientCountMiddleEastAndNorthAfrica: {
          value: faker.datatype.number()
        },
        patientCountNorthAmerica: {
          value: faker.datatype.number()
        },
        patientCountSouthAsia: {
          value: faker.datatype.number()
        },
        patientCountEastAsia: {
          value: faker.datatype.number()
        },
        patientCountFranceNative: {
          value: faker.datatype.number()
        }
      }
    }
  }));
}

export function generateMockSpainHeatmapAggregation(
  max = faker.datatype.number() % 10
) {
  return _.range(1, max).map(() => ({
    key: faker.datatype.string(),
    doc_count: faker.datatype.number(),
    nested_agg: {
      doc_count: faker.datatype.number(),
      filtered_matching: {
        doc_count: faker.datatype.number(),
        patientCountOceania: {
          value: faker.datatype.number()
        },
        patientCountCentralAndSouthAfrica: {
          value: faker.datatype.number()
        },
        patientCountCentralAmerica: {
          value: faker.datatype.number()
        },
        patientCountSoutheastAsia: {
          value: faker.datatype.number()
        },
        patientCountSouthAmerica: {
          value: faker.datatype.number()
        },
        patientCountOtherEuropean: {
          value: faker.datatype.number()
        },
        patientCountEuropeanUnion: {
          value: faker.datatype.number()
        },
        patientCountMiddleEastAndNorthAfrica: {
          value: faker.datatype.number()
        },
        patientCountNorthAmerica: {
          value: faker.datatype.number()
        },
        patientCountSouthAsia: {
          value: faker.datatype.number()
        },
        patientCountEastAsia: {
          value: faker.datatype.number()
        },
        patientCountCentralAsia: {
          value: faker.datatype.number()
        },
        patientCountSpain: {
          value: faker.datatype.number()
        },
        patientCountOther: {
          value: faker.datatype.number()
        },
        patientCountUnknown: {
          value: faker.datatype.number()
        }
      }
    }
  }));
}

export function generateMockAggregationsForCcsrDiagnoses(
  max = faker.datatype.number() % 10
): DocCountBucket[] {
  return _.range(1, max).map(() => ({
    key: faker.datatype.string(),
    doc_count: faker.datatype.number(),
    matching_desc: {
      buckets: [
        {
          key: faker.datatype.string(),
          doc_count: faker.datatype.number()
        }
      ]
    }
  }));
}

export function generateMockReverseNestedAggregationsForInstitutions(
  max = faker.datatype.number() % 10
): DocCountBucket[] {
  return _.range(1, max).map(() => ({
    key: faker.datatype.string(),
    doc_count: faker.datatype.number(),
    institutions: {
      doc_count: faker.datatype.number()
    }
  }));
}

/**
 * Utility function to generate a mocked query understanding server response proto
 * @param augmentedQuery The synonymized query
 * @param isIntitutionQuery Flag used to populate query intent in the response proto
 * @param query The query string that is the input to the query understanding service
 * @param synonyms A list of phrasal synonyms
 * @returns Returns a QueryUnderstandingServiceResponse that contains the synonymized query and query intent
 */
export function mockQueryUnderstandingServerResponse(
  augmentedQuery: string,
  isInstitutionQuery: boolean,
  score = 1.0,
  query?: string,
  synonyms?: string[]
): QueryUnderstandingServiceResponse {
  const response = new QueryUnderstandingServiceResponse();
  response.setAugmentedQuery(augmentedQuery);
  if (isInstitutionQuery) {
    const intent = new QueryIntent.Intent();
    intent.setIntentType(QueryIntent.IntentType.INSTITUTION);
    intent.setScore(score);
    const queryIntent = new QueryIntent();
    queryIntent.setInstitutionIntent(intent);
    response.setQueryIntent(queryIntent);
  }

  if (synonyms && query) {
    // phrasal synonyms are synonyms of the whole query
    const phrasalSynonyms = new Synonym();
    phrasalSynonyms.setOrig(query);
    for (const synonym of synonyms) {
      const synonymAndScore = new Synonym.SynScore();
      synonymAndScore.setScore(1.0);
      synonymAndScore.setSyn(synonym);
      phrasalSynonyms.addContextFreeSyn(synonymAndScore);
    }
    response.setUnigramSynonymList([phrasalSynonyms]);
  }
  return response;
}
