// package:
// file: query_understanding_service.proto

/* tslint:disable */
/* eslint-disable */

import * as jspb from "google-protobuf";
import * as synonym_pb from "./synonym_pb";
import * as query_intent_pb from "./query_intent_pb";

export class QueryUnderstandingServiceRequest extends jspb.Message { 
    getQuery(): string;
    setQuery(value: string): QueryUnderstandingServiceRequest;
    getLanguagecode(): LanguageCode;
    setLanguagecode(value: LanguageCode): QueryUnderstandingServiceRequest;

    serializeBinary(): Uint8Array;
    toObject(includeInstance?: boolean): QueryUnderstandingServiceRequest.AsObject;
    static toObject(includeInstance: boolean, msg: QueryUnderstandingServiceRequest): QueryUnderstandingServiceRequest.AsObject;
    static extensions: {[key: number]: jspb.ExtensionFieldInfo<jspb.Message>};
    static extensionsBinary: {[key: number]: jspb.ExtensionFieldBinaryInfo<jspb.Message>};
    static serializeBinaryToWriter(message: QueryUnderstandingServiceRequest, writer: jspb.BinaryWriter): void;
    static deserializeBinary(bytes: Uint8Array): QueryUnderstandingServiceRequest;
    static deserializeBinaryFromReader(message: QueryUnderstandingServiceRequest, reader: jspb.BinaryReader): QueryUnderstandingServiceRequest;
}

export namespace QueryUnderstandingServiceRequest {
    export type AsObject = {
        query: string,
        languagecode: LanguageCode,
    }
}

export class QueryUnderstandingServiceRequestForIndications extends jspb.Message { 
    clearIndicationsList(): void;
    getIndicationsList(): Array<string>;
    setIndicationsList(value: Array<string>): QueryUnderstandingServiceRequestForIndications;
    addIndications(value: string, index?: number): string;
    getLanguagecode(): LanguageCode;
    setLanguagecode(value: LanguageCode): QueryUnderstandingServiceRequestForIndications;
    getOr(): boolean;
    setOr(value: boolean): QueryUnderstandingServiceRequestForIndications;
    getCountry(): string;
    setCountry(value: string): QueryUnderstandingServiceRequestForIndications;

    serializeBinary(): Uint8Array;
    toObject(includeInstance?: boolean): QueryUnderstandingServiceRequestForIndications.AsObject;
    static toObject(includeInstance: boolean, msg: QueryUnderstandingServiceRequestForIndications): QueryUnderstandingServiceRequestForIndications.AsObject;
    static extensions: {[key: number]: jspb.ExtensionFieldInfo<jspb.Message>};
    static extensionsBinary: {[key: number]: jspb.ExtensionFieldBinaryInfo<jspb.Message>};
    static serializeBinaryToWriter(message: QueryUnderstandingServiceRequestForIndications, writer: jspb.BinaryWriter): void;
    static deserializeBinary(bytes: Uint8Array): QueryUnderstandingServiceRequestForIndications;
    static deserializeBinaryFromReader(message: QueryUnderstandingServiceRequestForIndications, reader: jspb.BinaryReader): QueryUnderstandingServiceRequestForIndications;
}

export namespace QueryUnderstandingServiceRequestForIndications {
    export type AsObject = {
        indicationsList: Array<string>,
        languagecode: LanguageCode,
        or: boolean,
        country: string,
    }
}

export class QueryUnderstandingServiceResponse extends jspb.Message { 
    getAugmentedQuery(): string;
    setAugmentedQuery(value: string): QueryUnderstandingServiceResponse;
    clearUnigramSynonymList(): void;
    getUnigramSynonymList(): Array<synonym_pb.Synonym>;
    setUnigramSynonymList(value: Array<synonym_pb.Synonym>): QueryUnderstandingServiceResponse;
    addUnigramSynonym(value?: synonym_pb.Synonym, index?: number): synonym_pb.Synonym;

    hasQueryIntent(): boolean;
    clearQueryIntent(): void;
    getQueryIntent(): query_intent_pb.QueryIntent | undefined;
    setQueryIntent(value?: query_intent_pb.QueryIntent): QueryUnderstandingServiceResponse;
    clearDiagnosisCodesList(): void;
    getDiagnosisCodesList(): Array<string>;
    setDiagnosisCodesList(value: Array<string>): QueryUnderstandingServiceResponse;
    addDiagnosisCodes(value: string, index?: number): string;
    clearProcedureCodesList(): void;
    getProcedureCodesList(): Array<string>;
    setProcedureCodesList(value: Array<string>): QueryUnderstandingServiceResponse;
    addProcedureCodes(value: string, index?: number): string;

    hasEntities(): boolean;
    clearEntities(): void;
    getEntities(): Entities | undefined;
    setEntities(value?: Entities): QueryUnderstandingServiceResponse;

    serializeBinary(): Uint8Array;
    toObject(includeInstance?: boolean): QueryUnderstandingServiceResponse.AsObject;
    static toObject(includeInstance: boolean, msg: QueryUnderstandingServiceResponse): QueryUnderstandingServiceResponse.AsObject;
    static extensions: {[key: number]: jspb.ExtensionFieldInfo<jspb.Message>};
    static extensionsBinary: {[key: number]: jspb.ExtensionFieldBinaryInfo<jspb.Message>};
    static serializeBinaryToWriter(message: QueryUnderstandingServiceResponse, writer: jspb.BinaryWriter): void;
    static deserializeBinary(bytes: Uint8Array): QueryUnderstandingServiceResponse;
    static deserializeBinaryFromReader(message: QueryUnderstandingServiceResponse, reader: jspb.BinaryReader): QueryUnderstandingServiceResponse;
}

export namespace QueryUnderstandingServiceResponse {
    export type AsObject = {
        augmentedQuery: string,
        unigramSynonymList: Array<synonym_pb.Synonym.AsObject>,
        queryIntent?: query_intent_pb.QueryIntent.AsObject,
        diagnosisCodesList: Array<string>,
        procedureCodesList: Array<string>,
        entities?: Entities.AsObject,
    }
}

export class QueryUnderstandingServiceResponseForIndications extends jspb.Message { 
    getIndicationsParsedQuery(): string;
    setIndicationsParsedQuery(value: string): QueryUnderstandingServiceResponseForIndications;
    getIndicationsIcdCodesQuery(): string;
    setIndicationsIcdCodesQuery(value: string): QueryUnderstandingServiceResponseForIndications;

    serializeBinary(): Uint8Array;
    toObject(includeInstance?: boolean): QueryUnderstandingServiceResponseForIndications.AsObject;
    static toObject(includeInstance: boolean, msg: QueryUnderstandingServiceResponseForIndications): QueryUnderstandingServiceResponseForIndications.AsObject;
    static extensions: {[key: number]: jspb.ExtensionFieldInfo<jspb.Message>};
    static extensionsBinary: {[key: number]: jspb.ExtensionFieldBinaryInfo<jspb.Message>};
    static serializeBinaryToWriter(message: QueryUnderstandingServiceResponseForIndications, writer: jspb.BinaryWriter): void;
    static deserializeBinary(bytes: Uint8Array): QueryUnderstandingServiceResponseForIndications;
    static deserializeBinaryFromReader(message: QueryUnderstandingServiceResponseForIndications, reader: jspb.BinaryReader): QueryUnderstandingServiceResponseForIndications;
}

export namespace QueryUnderstandingServiceResponseForIndications {
    export type AsObject = {
        indicationsParsedQuery: string,
        indicationsIcdCodesQuery: string,
    }
}

export class Entities extends jspb.Message { 
    getSearchFor(): string;
    setSearchFor(value: string): Entities;
    getIndication(): string;
    setIndication(value: string): Entities;

    hasNames(): boolean;
    clearNames(): void;
    getNames(): NameTypes | undefined;
    setNames(value?: NameTypes): Entities;
    getRankBy(): string;
    setRankBy(value: string): Entities;

    hasLocations(): boolean;
    clearLocations(): void;
    getLocations(): Locations | undefined;
    setLocations(value?: Locations): Entities;

    serializeBinary(): Uint8Array;
    toObject(includeInstance?: boolean): Entities.AsObject;
    static toObject(includeInstance: boolean, msg: Entities): Entities.AsObject;
    static extensions: {[key: number]: jspb.ExtensionFieldInfo<jspb.Message>};
    static extensionsBinary: {[key: number]: jspb.ExtensionFieldBinaryInfo<jspb.Message>};
    static serializeBinaryToWriter(message: Entities, writer: jspb.BinaryWriter): void;
    static deserializeBinary(bytes: Uint8Array): Entities;
    static deserializeBinaryFromReader(message: Entities, reader: jspb.BinaryReader): Entities;
}

export namespace Entities {
    export type AsObject = {
        searchFor: string,
        indication: string,
        names?: NameTypes.AsObject,
        rankBy: string,
        locations?: Locations.AsObject,
    }
}

export class NameTypes extends jspb.Message { 
    getPeople(): string;
    setPeople(value: string): NameTypes;
    getInstitutions(): string;
    setInstitutions(value: string): NameTypes;

    serializeBinary(): Uint8Array;
    toObject(includeInstance?: boolean): NameTypes.AsObject;
    static toObject(includeInstance: boolean, msg: NameTypes): NameTypes.AsObject;
    static extensions: {[key: number]: jspb.ExtensionFieldInfo<jspb.Message>};
    static extensionsBinary: {[key: number]: jspb.ExtensionFieldBinaryInfo<jspb.Message>};
    static serializeBinaryToWriter(message: NameTypes, writer: jspb.BinaryWriter): void;
    static deserializeBinary(bytes: Uint8Array): NameTypes;
    static deserializeBinaryFromReader(message: NameTypes, reader: jspb.BinaryReader): NameTypes;
}

export namespace NameTypes {
    export type AsObject = {
        people: string,
        institutions: string,
    }
}

export class Locations extends jspb.Message { 
    getCountry(): string;
    setCountry(value: string): Locations;
    getState(): string;
    setState(value: string): Locations;
    getCity(): string;
    setCity(value: string): Locations;
    getZipcode(): string;
    setZipcode(value: string): Locations;

    serializeBinary(): Uint8Array;
    toObject(includeInstance?: boolean): Locations.AsObject;
    static toObject(includeInstance: boolean, msg: Locations): Locations.AsObject;
    static extensions: {[key: number]: jspb.ExtensionFieldInfo<jspb.Message>};
    static extensionsBinary: {[key: number]: jspb.ExtensionFieldBinaryInfo<jspb.Message>};
    static serializeBinaryToWriter(message: Locations, writer: jspb.BinaryWriter): void;
    static deserializeBinary(bytes: Uint8Array): Locations;
    static deserializeBinaryFromReader(message: Locations, reader: jspb.BinaryReader): Locations;
}

export namespace Locations {
    export type AsObject = {
        country: string,
        state: string,
        city: string,
        zipcode: string,
    }
}

export enum LanguageCode {
    ENGLISH = 0,
    JAPANESE = 1,
    CHINESE = 2,
}
