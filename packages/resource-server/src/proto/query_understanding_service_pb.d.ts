// package:
// file: query_understanding_service.proto

/* tslint:disable */
/* eslint-disable */

import * as jspb from "google-protobuf";
import * as synonym_pb from "./synonym_pb";
import * as query_intent_pb from "./query_intent_pb";

export class QueryUnderstandingServiceRequest extends jspb.Message { 
    getQuery(): string;
    setQuery(value: string): QueryUnderstandingServiceRequest;
    getLanguagecode(): LanguageCode;
    setLanguagecode(value: LanguageCode): QueryUnderstandingServiceRequest;

    serializeBinary(): Uint8Array;
    toObject(includeInstance?: boolean): QueryUnderstandingServiceRequest.AsObject;
    static toObject(includeInstance: boolean, msg: QueryUnderstandingServiceRequest): QueryUnderstandingServiceRequest.AsObject;
    static extensions: {[key: number]: jspb.ExtensionFieldInfo<jspb.Message>};
    static extensionsBinary: {[key: number]: jspb.ExtensionFieldBinaryInfo<jspb.Message>};
    static serializeBinaryToWriter(message: QueryUnderstandingServiceRequest, writer: jspb.BinaryWriter): void;
    static deserializeBinary(bytes: Uint8Array): QueryUnderstandingServiceRequest;
    static deserializeBinaryFromReader(message: QueryUnderstandingServiceRequest, reader: jspb.BinaryReader): QueryUnderstandingServiceRequest;
}

export namespace QueryUnderstandingServiceRequest {
    export type AsObject = {
        query: string,
        languagecode: LanguageCode,
    }
}

export class QueryUnderstandingServiceRequestForIndications extends jspb.Message { 
    clearIndicationsList(): void;
    getIndicationsList(): Array<string>;
    setIndicationsList(value: Array<string>): QueryUnderstandingServiceRequestForIndications;
    addIndications(value: string, index?: number): string;
    getLanguagecode(): LanguageCode;
    setLanguagecode(value: LanguageCode): QueryUnderstandingServiceRequestForIndications;
    getOr(): boolean;
    setOr(value: boolean): QueryUnderstandingServiceRequestForIndications;
    getCountry(): string;
    setCountry(value: string): QueryUnderstandingServiceRequestForIndications;

    serializeBinary(): Uint8Array;
    toObject(includeInstance?: boolean): QueryUnderstandingServiceRequestForIndications.AsObject;
    static toObject(includeInstance: boolean, msg: QueryUnderstandingServiceRequestForIndications): QueryUnderstandingServiceRequestForIndications.AsObject;
    static extensions: {[key: number]: jspb.ExtensionFieldInfo<jspb.Message>};
    static extensionsBinary: {[key: number]: jspb.ExtensionFieldBinaryInfo<jspb.Message>};
    static serializeBinaryToWriter(message: QueryUnderstandingServiceRequestForIndications, writer: jspb.BinaryWriter): void;
    static deserializeBinary(bytes: Uint8Array): QueryUnderstandingServiceRequestForIndications;
    static deserializeBinaryFromReader(message: QueryUnderstandingServiceRequestForIndications, reader: jspb.BinaryReader): QueryUnderstandingServiceRequestForIndications;
}

export namespace QueryUnderstandingServiceRequestForIndications {
    export type AsObject = {
        indicationsList: Array<string>,
        languagecode: LanguageCode,
        or: boolean,
        country: string,
    }
}

export class QueryUnderstandingServiceResponse extends jspb.Message { 
    getAugmentedQuery(): string;
    setAugmentedQuery(value: string): QueryUnderstandingServiceResponse;
    clearUnigramSynonymList(): void;
    getUnigramSynonymList(): Array<synonym_pb.Synonym>;
    setUnigramSynonymList(value: Array<synonym_pb.Synonym>): QueryUnderstandingServiceResponse;
    addUnigramSynonym(value?: synonym_pb.Synonym, index?: number): synonym_pb.Synonym;

    hasQueryIntent(): boolean;
    clearQueryIntent(): void;
    getQueryIntent(): query_intent_pb.QueryIntent | undefined;
    setQueryIntent(value?: query_intent_pb.QueryIntent): QueryUnderstandingServiceResponse;
    clearDiagnosisCodesList(): void;
    getDiagnosisCodesList(): Array<string>;
    setDiagnosisCodesList(value: Array<string>): QueryUnderstandingServiceResponse;
    addDiagnosisCodes(value: string, index?: number): string;
    clearProcedureCodesList(): void;
    getProcedureCodesList(): Array<string>;
    setProcedureCodesList(value: Array<string>): QueryUnderstandingServiceResponse;
    addProcedureCodes(value: string, index?: number): string;

    hasEntities(): boolean;
    clearEntities(): void;
    getEntities(): Entities | undefined;
    setEntities(value?: Entities): QueryUnderstandingServiceResponse;

    hasEntitiesV2(): boolean;
    clearEntitiesV2(): void;
    getEntitiesV2(): EntitiesV2 | undefined;
    setEntitiesV2(value?: EntitiesV2): QueryUnderstandingServiceResponse;

    serializeBinary(): Uint8Array;
    toObject(includeInstance?: boolean): QueryUnderstandingServiceResponse.AsObject;
    static toObject(includeInstance: boolean, msg: QueryUnderstandingServiceResponse): QueryUnderstandingServiceResponse.AsObject;
    static extensions: {[key: number]: jspb.ExtensionFieldInfo<jspb.Message>};
    static extensionsBinary: {[key: number]: jspb.ExtensionFieldBinaryInfo<jspb.Message>};
    static serializeBinaryToWriter(message: QueryUnderstandingServiceResponse, writer: jspb.BinaryWriter): void;
    static deserializeBinary(bytes: Uint8Array): QueryUnderstandingServiceResponse;
    static deserializeBinaryFromReader(message: QueryUnderstandingServiceResponse, reader: jspb.BinaryReader): QueryUnderstandingServiceResponse;
}

export namespace QueryUnderstandingServiceResponse {
    export type AsObject = {
        augmentedQuery: string,
        unigramSynonymList: Array<synonym_pb.Synonym.AsObject>,
        queryIntent?: query_intent_pb.QueryIntent.AsObject,
        diagnosisCodesList: Array<string>,
        procedureCodesList: Array<string>,
        entities?: Entities.AsObject,
        entitiesV2?: EntitiesV2.AsObject,
    }
}

export class QueryUnderstandingServiceResponseForIndications extends jspb.Message { 
    getIndicationsParsedQuery(): string;
    setIndicationsParsedQuery(value: string): QueryUnderstandingServiceResponseForIndications;
    getIndicationsIcdCodesQuery(): string;
    setIndicationsIcdCodesQuery(value: string): QueryUnderstandingServiceResponseForIndications;

    serializeBinary(): Uint8Array;
    toObject(includeInstance?: boolean): QueryUnderstandingServiceResponseForIndications.AsObject;
    static toObject(includeInstance: boolean, msg: QueryUnderstandingServiceResponseForIndications): QueryUnderstandingServiceResponseForIndications.AsObject;
    static extensions: {[key: number]: jspb.ExtensionFieldInfo<jspb.Message>};
    static extensionsBinary: {[key: number]: jspb.ExtensionFieldBinaryInfo<jspb.Message>};
    static serializeBinaryToWriter(message: QueryUnderstandingServiceResponseForIndications, writer: jspb.BinaryWriter): void;
    static deserializeBinary(bytes: Uint8Array): QueryUnderstandingServiceResponseForIndications;
    static deserializeBinaryFromReader(message: QueryUnderstandingServiceResponseForIndications, reader: jspb.BinaryReader): QueryUnderstandingServiceResponseForIndications;
}

export namespace QueryUnderstandingServiceResponseForIndications {
    export type AsObject = {
        indicationsParsedQuery: string,
        indicationsIcdCodesQuery: string,
    }
}

export class Entities extends jspb.Message { 
    getSearchFor(): string;
    setSearchFor(value: string): Entities;
    getIndication(): string;
    setIndication(value: string): Entities;

    hasNames(): boolean;
    clearNames(): void;
    getNames(): NameTypes | undefined;
    setNames(value?: NameTypes): Entities;
    getRankBy(): string;
    setRankBy(value: string): Entities;

    hasLocations(): boolean;
    clearLocations(): void;
    getLocations(): Locations | undefined;
    setLocations(value?: Locations): Entities;

    hasLeadership(): boolean;
    clearLeadership(): void;
    getLeadership(): Leadership | undefined;
    setLeadership(value?: Leadership): Entities;

    serializeBinary(): Uint8Array;
    toObject(includeInstance?: boolean): Entities.AsObject;
    static toObject(includeInstance: boolean, msg: Entities): Entities.AsObject;
    static extensions: {[key: number]: jspb.ExtensionFieldInfo<jspb.Message>};
    static extensionsBinary: {[key: number]: jspb.ExtensionFieldBinaryInfo<jspb.Message>};
    static serializeBinaryToWriter(message: Entities, writer: jspb.BinaryWriter): void;
    static deserializeBinary(bytes: Uint8Array): Entities;
    static deserializeBinaryFromReader(message: Entities, reader: jspb.BinaryReader): Entities;
}

export namespace Entities {
    export type AsObject = {
        searchFor: string,
        indication: string,
        names?: NameTypes.AsObject,
        rankBy: string,
        locations?: Locations.AsObject,
        leadership?: Leadership.AsObject,
    }
}

export class NameTypes extends jspb.Message { 
    getPeople(): string;
    setPeople(value: string): NameTypes;
    getInstitutions(): string;
    setInstitutions(value: string): NameTypes;

    serializeBinary(): Uint8Array;
    toObject(includeInstance?: boolean): NameTypes.AsObject;
    static toObject(includeInstance: boolean, msg: NameTypes): NameTypes.AsObject;
    static extensions: {[key: number]: jspb.ExtensionFieldInfo<jspb.Message>};
    static extensionsBinary: {[key: number]: jspb.ExtensionFieldBinaryInfo<jspb.Message>};
    static serializeBinaryToWriter(message: NameTypes, writer: jspb.BinaryWriter): void;
    static deserializeBinary(bytes: Uint8Array): NameTypes;
    static deserializeBinaryFromReader(message: NameTypes, reader: jspb.BinaryReader): NameTypes;
}

export namespace NameTypes {
    export type AsObject = {
        people: string,
        institutions: string,
    }
}

export class Locations extends jspb.Message { 
    getCountry(): string;
    setCountry(value: string): Locations;
    getState(): string;
    setState(value: string): Locations;
    getCity(): string;
    setCity(value: string): Locations;
    getZipcode(): string;
    setZipcode(value: string): Locations;

    serializeBinary(): Uint8Array;
    toObject(includeInstance?: boolean): Locations.AsObject;
    static toObject(includeInstance: boolean, msg: Locations): Locations.AsObject;
    static extensions: {[key: number]: jspb.ExtensionFieldInfo<jspb.Message>};
    static extensionsBinary: {[key: number]: jspb.ExtensionFieldBinaryInfo<jspb.Message>};
    static serializeBinaryToWriter(message: Locations, writer: jspb.BinaryWriter): void;
    static deserializeBinary(bytes: Uint8Array): Locations;
    static deserializeBinaryFromReader(message: Locations, reader: jspb.BinaryReader): Locations;
}

export namespace Locations {
    export type AsObject = {
        country: string,
        state: string,
        city: string,
        zipcode: string,
    }
}

export class Leadership extends jspb.Message { 
    getIsGlobalLeader(): boolean;
    setIsGlobalLeader(value: boolean): Leadership;
    getIsNationalLeader(): boolean;
    setIsNationalLeader(value: boolean): Leadership;
    getIsRegionalLeader(): boolean;
    setIsRegionalLeader(value: boolean): Leadership;
    getIsLocalLeader(): boolean;
    setIsLocalLeader(value: boolean): Leadership;

    serializeBinary(): Uint8Array;
    toObject(includeInstance?: boolean): Leadership.AsObject;
    static toObject(includeInstance: boolean, msg: Leadership): Leadership.AsObject;
    static extensions: {[key: number]: jspb.ExtensionFieldInfo<jspb.Message>};
    static extensionsBinary: {[key: number]: jspb.ExtensionFieldBinaryInfo<jspb.Message>};
    static serializeBinaryToWriter(message: Leadership, writer: jspb.BinaryWriter): void;
    static deserializeBinary(bytes: Uint8Array): Leadership;
    static deserializeBinaryFromReader(message: Leadership, reader: jspb.BinaryReader): Leadership;
}

export namespace Leadership {
    export type AsObject = {
        isGlobalLeader: boolean,
        isNationalLeader: boolean,
        isRegionalLeader: boolean,
        isLocalLeader: boolean,
    }
}

export class EntitiesV2 extends jspb.Message { 
    getSearchFor(): string;
    setSearchFor(value: string): EntitiesV2;
    clearIndicationList(): void;
    getIndicationList(): Array<string>;
    setIndicationList(value: Array<string>): EntitiesV2;
    addIndication(value: string, index?: number): string;

    hasLocations(): boolean;
    clearLocations(): void;
    getLocations(): LocationsV2 | undefined;
    setLocations(value?: LocationsV2): EntitiesV2;

    hasClaims(): boolean;
    clearClaims(): void;
    getClaims(): Claims | undefined;
    setClaims(value?: Claims): EntitiesV2;
    clearSpecialtyList(): void;
    getSpecialtyList(): Array<string>;
    setSpecialtyList(value: Array<string>): EntitiesV2;
    addSpecialty(value: string, index?: number): string;

    hasTrial(): boolean;
    clearTrial(): void;
    getTrial(): TrialInfo | undefined;
    setTrial(value?: TrialInfo): EntitiesV2;
    clearWorkInstitutionList(): void;
    getWorkInstitutionList(): Array<string>;
    setWorkInstitutionList(value: Array<string>): EntitiesV2;
    addWorkInstitution(value: string, index?: number): string;

    serializeBinary(): Uint8Array;
    toObject(includeInstance?: boolean): EntitiesV2.AsObject;
    static toObject(includeInstance: boolean, msg: EntitiesV2): EntitiesV2.AsObject;
    static extensions: {[key: number]: jspb.ExtensionFieldInfo<jspb.Message>};
    static extensionsBinary: {[key: number]: jspb.ExtensionFieldBinaryInfo<jspb.Message>};
    static serializeBinaryToWriter(message: EntitiesV2, writer: jspb.BinaryWriter): void;
    static deserializeBinary(bytes: Uint8Array): EntitiesV2;
    static deserializeBinaryFromReader(message: EntitiesV2, reader: jspb.BinaryReader): EntitiesV2;
}

export namespace EntitiesV2 {
    export type AsObject = {
        searchFor: string,
        indicationList: Array<string>,
        locations?: LocationsV2.AsObject,
        claims?: Claims.AsObject,
        specialtyList: Array<string>,
        trial?: TrialInfo.AsObject,
        workInstitutionList: Array<string>,
    }
}

export class LocationsV2 extends jspb.Message { 
    clearCountryList(): void;
    getCountryList(): Array<string>;
    setCountryList(value: Array<string>): LocationsV2;
    addCountry(value: string, index?: number): string;
    clearStateList(): void;
    getStateList(): Array<string>;
    setStateList(value: Array<string>): LocationsV2;
    addState(value: string, index?: number): string;
    clearCityList(): void;
    getCityList(): Array<string>;
    setCityList(value: Array<string>): LocationsV2;
    addCity(value: string, index?: number): string;

    serializeBinary(): Uint8Array;
    toObject(includeInstance?: boolean): LocationsV2.AsObject;
    static toObject(includeInstance: boolean, msg: LocationsV2): LocationsV2.AsObject;
    static extensions: {[key: number]: jspb.ExtensionFieldInfo<jspb.Message>};
    static extensionsBinary: {[key: number]: jspb.ExtensionFieldBinaryInfo<jspb.Message>};
    static serializeBinaryToWriter(message: LocationsV2, writer: jspb.BinaryWriter): void;
    static deserializeBinary(bytes: Uint8Array): LocationsV2;
    static deserializeBinaryFromReader(message: LocationsV2, reader: jspb.BinaryReader): LocationsV2;
}

export namespace LocationsV2 {
    export type AsObject = {
        countryList: Array<string>,
        stateList: Array<string>,
        cityList: Array<string>,
    }
}

export class Claims extends jspb.Message { 
    clearDiagnosesList(): void;
    getDiagnosesList(): Array<string>;
    setDiagnosesList(value: Array<string>): Claims;
    addDiagnoses(value: string, index?: number): string;

    hasMinDiagnosesClaimCount(): boolean;
    clearMinDiagnosesClaimCount(): void;
    getMinDiagnosesClaimCount(): number | undefined;
    setMinDiagnosesClaimCount(value: number): Claims;

    hasMaxDiagnosesClaimCount(): boolean;
    clearMaxDiagnosesClaimCount(): void;
    getMaxDiagnosesClaimCount(): number | undefined;
    setMaxDiagnosesClaimCount(value: number): Claims;
    clearProceduresList(): void;
    getProceduresList(): Array<string>;
    setProceduresList(value: Array<string>): Claims;
    addProcedures(value: string, index?: number): string;

    hasMinProceduresClaimCount(): boolean;
    clearMinProceduresClaimCount(): void;
    getMinProceduresClaimCount(): number | undefined;
    setMinProceduresClaimCount(value: number): Claims;

    hasMaxProceduresClaimCount(): boolean;
    clearMaxProceduresClaimCount(): void;
    getMaxProceduresClaimCount(): number | undefined;
    setMaxProceduresClaimCount(value: number): Claims;

    hasTimeframe(): boolean;
    clearTimeframe(): void;
    getTimeframe(): string | undefined;
    setTimeframe(value: string): Claims;

    serializeBinary(): Uint8Array;
    toObject(includeInstance?: boolean): Claims.AsObject;
    static toObject(includeInstance: boolean, msg: Claims): Claims.AsObject;
    static extensions: {[key: number]: jspb.ExtensionFieldInfo<jspb.Message>};
    static extensionsBinary: {[key: number]: jspb.ExtensionFieldBinaryInfo<jspb.Message>};
    static serializeBinaryToWriter(message: Claims, writer: jspb.BinaryWriter): void;
    static deserializeBinary(bytes: Uint8Array): Claims;
    static deserializeBinaryFromReader(message: Claims, reader: jspb.BinaryReader): Claims;
}

export namespace Claims {
    export type AsObject = {
        diagnosesList: Array<string>,
        minDiagnosesClaimCount?: number,
        maxDiagnosesClaimCount?: number,
        proceduresList: Array<string>,
        minProceduresClaimCount?: number,
        maxProceduresClaimCount?: number,
        timeframe?: string,
    }
}

export class TrialInfo extends jspb.Message { 

    hasMinCount(): boolean;
    clearMinCount(): void;
    getMinCount(): number | undefined;
    setMinCount(value: number): TrialInfo;

    hasMaxCount(): boolean;
    clearMaxCount(): void;
    getMaxCount(): number | undefined;
    setMaxCount(value: number): TrialInfo;

    hasStatus(): boolean;
    clearStatus(): void;
    getStatus(): string | undefined;
    setStatus(value: string): TrialInfo;

    hasPhase(): boolean;
    clearPhase(): void;
    getPhase(): string | undefined;
    setPhase(value: string): TrialInfo;

    serializeBinary(): Uint8Array;
    toObject(includeInstance?: boolean): TrialInfo.AsObject;
    static toObject(includeInstance: boolean, msg: TrialInfo): TrialInfo.AsObject;
    static extensions: {[key: number]: jspb.ExtensionFieldInfo<jspb.Message>};
    static extensionsBinary: {[key: number]: jspb.ExtensionFieldBinaryInfo<jspb.Message>};
    static serializeBinaryToWriter(message: TrialInfo, writer: jspb.BinaryWriter): void;
    static deserializeBinary(bytes: Uint8Array): TrialInfo;
    static deserializeBinaryFromReader(message: TrialInfo, reader: jspb.BinaryReader): TrialInfo;
}

export namespace TrialInfo {
    export type AsObject = {
        minCount?: number,
        maxCount?: number,
        status?: string,
        phase?: string,
    }
}

export enum LanguageCode {
    ENGLISH = 0,
    JAPANESE = 1,
    CHINESE = 2,
}
