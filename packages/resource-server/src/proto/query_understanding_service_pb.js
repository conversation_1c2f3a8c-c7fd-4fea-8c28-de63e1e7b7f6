// source: query_understanding_service.proto
/**
 * @fileoverview
 * @enhanceable
 * @suppress {missingRequire} reports error on implicit type usages.
 * @suppress {messageConventions} JS Compiler reports an error if a variable or
 *     field starts with 'MSG_' and isn't a translatable message.
 * @public
 */
// GENERATED CODE -- DO NOT EDIT!
/* eslint-disable */
// @ts-nocheck

var jspb = require('google-protobuf');
var goog = jspb;
var global = (function() {
  if (this) { return this; }
  if (typeof window !== 'undefined') { return window; }
  if (typeof global !== 'undefined') { return global; }
  if (typeof self !== 'undefined') { return self; }
  return Function('return this')();
}.call(null));

var synonym_pb = require('./synonym_pb.js');
goog.object.extend(proto, synonym_pb);
var query_intent_pb = require('./query_intent_pb.js');
goog.object.extend(proto, query_intent_pb);
goog.exportSymbol('proto.Claims', null, global);
goog.exportSymbol('proto.Entities', null, global);
goog.exportSymbol('proto.EntitiesV2', null, global);
goog.exportSymbol('proto.LanguageCode', null, global);
goog.exportSymbol('proto.Leadership', null, global);
goog.exportSymbol('proto.Locations', null, global);
goog.exportSymbol('proto.LocationsV2', null, global);
goog.exportSymbol('proto.NameTypes', null, global);
goog.exportSymbol('proto.QueryUnderstandingServiceRequest', null, global);
goog.exportSymbol('proto.QueryUnderstandingServiceRequestForIndications', null, global);
goog.exportSymbol('proto.QueryUnderstandingServiceResponse', null, global);
goog.exportSymbol('proto.QueryUnderstandingServiceResponseForIndications', null, global);
goog.exportSymbol('proto.TrialInfo', null, global);
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.QueryUnderstandingServiceRequest = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.QueryUnderstandingServiceRequest, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.QueryUnderstandingServiceRequest.displayName = 'proto.QueryUnderstandingServiceRequest';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.QueryUnderstandingServiceRequestForIndications = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, proto.QueryUnderstandingServiceRequestForIndications.repeatedFields_, null);
};
goog.inherits(proto.QueryUnderstandingServiceRequestForIndications, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.QueryUnderstandingServiceRequestForIndications.displayName = 'proto.QueryUnderstandingServiceRequestForIndications';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.QueryUnderstandingServiceResponse = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, proto.QueryUnderstandingServiceResponse.repeatedFields_, null);
};
goog.inherits(proto.QueryUnderstandingServiceResponse, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.QueryUnderstandingServiceResponse.displayName = 'proto.QueryUnderstandingServiceResponse';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.QueryUnderstandingServiceResponseForIndications = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.QueryUnderstandingServiceResponseForIndications, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.QueryUnderstandingServiceResponseForIndications.displayName = 'proto.QueryUnderstandingServiceResponseForIndications';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.Entities = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.Entities, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.Entities.displayName = 'proto.Entities';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.NameTypes = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.NameTypes, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.NameTypes.displayName = 'proto.NameTypes';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.Locations = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.Locations, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.Locations.displayName = 'proto.Locations';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.Leadership = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.Leadership, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.Leadership.displayName = 'proto.Leadership';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.EntitiesV2 = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, proto.EntitiesV2.repeatedFields_, null);
};
goog.inherits(proto.EntitiesV2, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.EntitiesV2.displayName = 'proto.EntitiesV2';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.LocationsV2 = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, proto.LocationsV2.repeatedFields_, null);
};
goog.inherits(proto.LocationsV2, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.LocationsV2.displayName = 'proto.LocationsV2';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.Claims = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, proto.Claims.repeatedFields_, null);
};
goog.inherits(proto.Claims, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.Claims.displayName = 'proto.Claims';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.TrialInfo = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.TrialInfo, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.TrialInfo.displayName = 'proto.TrialInfo';
}



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.QueryUnderstandingServiceRequest.prototype.toObject = function(opt_includeInstance) {
  return proto.QueryUnderstandingServiceRequest.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.QueryUnderstandingServiceRequest} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.QueryUnderstandingServiceRequest.toObject = function(includeInstance, msg) {
  var f, obj = {
    query: jspb.Message.getFieldWithDefault(msg, 1, ""),
    languagecode: jspb.Message.getFieldWithDefault(msg, 2, 0)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.QueryUnderstandingServiceRequest}
 */
proto.QueryUnderstandingServiceRequest.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.QueryUnderstandingServiceRequest;
  return proto.QueryUnderstandingServiceRequest.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.QueryUnderstandingServiceRequest} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.QueryUnderstandingServiceRequest}
 */
proto.QueryUnderstandingServiceRequest.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setQuery(value);
      break;
    case 2:
      var value = /** @type {!proto.LanguageCode} */ (reader.readEnum());
      msg.setLanguagecode(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.QueryUnderstandingServiceRequest.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.QueryUnderstandingServiceRequest.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.QueryUnderstandingServiceRequest} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.QueryUnderstandingServiceRequest.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getQuery();
  if (f.length > 0) {
    writer.writeString(
      1,
      f
    );
  }
  f = message.getLanguagecode();
  if (f !== 0.0) {
    writer.writeEnum(
      2,
      f
    );
  }
};


/**
 * optional string query = 1;
 * @return {string}
 */
proto.QueryUnderstandingServiceRequest.prototype.getQuery = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/**
 * @param {string} value
 * @return {!proto.QueryUnderstandingServiceRequest} returns this
 */
proto.QueryUnderstandingServiceRequest.prototype.setQuery = function(value) {
  return jspb.Message.setProto3StringField(this, 1, value);
};


/**
 * optional LanguageCode languageCode = 2;
 * @return {!proto.LanguageCode}
 */
proto.QueryUnderstandingServiceRequest.prototype.getLanguagecode = function() {
  return /** @type {!proto.LanguageCode} */ (jspb.Message.getFieldWithDefault(this, 2, 0));
};


/**
 * @param {!proto.LanguageCode} value
 * @return {!proto.QueryUnderstandingServiceRequest} returns this
 */
proto.QueryUnderstandingServiceRequest.prototype.setLanguagecode = function(value) {
  return jspb.Message.setProto3EnumField(this, 2, value);
};



/**
 * List of repeated fields within this message type.
 * @private {!Array<number>}
 * @const
 */
proto.QueryUnderstandingServiceRequestForIndications.repeatedFields_ = [1];



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.QueryUnderstandingServiceRequestForIndications.prototype.toObject = function(opt_includeInstance) {
  return proto.QueryUnderstandingServiceRequestForIndications.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.QueryUnderstandingServiceRequestForIndications} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.QueryUnderstandingServiceRequestForIndications.toObject = function(includeInstance, msg) {
  var f, obj = {
    indicationsList: (f = jspb.Message.getRepeatedField(msg, 1)) == null ? undefined : f,
    languagecode: jspb.Message.getFieldWithDefault(msg, 2, 0),
    or: jspb.Message.getBooleanFieldWithDefault(msg, 3, false),
    country: jspb.Message.getFieldWithDefault(msg, 4, "")
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.QueryUnderstandingServiceRequestForIndications}
 */
proto.QueryUnderstandingServiceRequestForIndications.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.QueryUnderstandingServiceRequestForIndications;
  return proto.QueryUnderstandingServiceRequestForIndications.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.QueryUnderstandingServiceRequestForIndications} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.QueryUnderstandingServiceRequestForIndications}
 */
proto.QueryUnderstandingServiceRequestForIndications.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.addIndications(value);
      break;
    case 2:
      var value = /** @type {!proto.LanguageCode} */ (reader.readEnum());
      msg.setLanguagecode(value);
      break;
    case 3:
      var value = /** @type {boolean} */ (reader.readBool());
      msg.setOr(value);
      break;
    case 4:
      var value = /** @type {string} */ (reader.readString());
      msg.setCountry(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.QueryUnderstandingServiceRequestForIndications.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.QueryUnderstandingServiceRequestForIndications.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.QueryUnderstandingServiceRequestForIndications} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.QueryUnderstandingServiceRequestForIndications.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getIndicationsList();
  if (f.length > 0) {
    writer.writeRepeatedString(
      1,
      f
    );
  }
  f = message.getLanguagecode();
  if (f !== 0.0) {
    writer.writeEnum(
      2,
      f
    );
  }
  f = message.getOr();
  if (f) {
    writer.writeBool(
      3,
      f
    );
  }
  f = message.getCountry();
  if (f.length > 0) {
    writer.writeString(
      4,
      f
    );
  }
};


/**
 * repeated string indications = 1;
 * @return {!Array<string>}
 */
proto.QueryUnderstandingServiceRequestForIndications.prototype.getIndicationsList = function() {
  return /** @type {!Array<string>} */ (jspb.Message.getRepeatedField(this, 1));
};


/**
 * @param {!Array<string>} value
 * @return {!proto.QueryUnderstandingServiceRequestForIndications} returns this
 */
proto.QueryUnderstandingServiceRequestForIndications.prototype.setIndicationsList = function(value) {
  return jspb.Message.setField(this, 1, value || []);
};


/**
 * @param {string} value
 * @param {number=} opt_index
 * @return {!proto.QueryUnderstandingServiceRequestForIndications} returns this
 */
proto.QueryUnderstandingServiceRequestForIndications.prototype.addIndications = function(value, opt_index) {
  return jspb.Message.addToRepeatedField(this, 1, value, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.QueryUnderstandingServiceRequestForIndications} returns this
 */
proto.QueryUnderstandingServiceRequestForIndications.prototype.clearIndicationsList = function() {
  return this.setIndicationsList([]);
};


/**
 * optional LanguageCode languageCode = 2;
 * @return {!proto.LanguageCode}
 */
proto.QueryUnderstandingServiceRequestForIndications.prototype.getLanguagecode = function() {
  return /** @type {!proto.LanguageCode} */ (jspb.Message.getFieldWithDefault(this, 2, 0));
};


/**
 * @param {!proto.LanguageCode} value
 * @return {!proto.QueryUnderstandingServiceRequestForIndications} returns this
 */
proto.QueryUnderstandingServiceRequestForIndications.prototype.setLanguagecode = function(value) {
  return jspb.Message.setProto3EnumField(this, 2, value);
};


/**
 * optional bool or = 3;
 * @return {boolean}
 */
proto.QueryUnderstandingServiceRequestForIndications.prototype.getOr = function() {
  return /** @type {boolean} */ (jspb.Message.getBooleanFieldWithDefault(this, 3, false));
};


/**
 * @param {boolean} value
 * @return {!proto.QueryUnderstandingServiceRequestForIndications} returns this
 */
proto.QueryUnderstandingServiceRequestForIndications.prototype.setOr = function(value) {
  return jspb.Message.setProto3BooleanField(this, 3, value);
};


/**
 * optional string country = 4;
 * @return {string}
 */
proto.QueryUnderstandingServiceRequestForIndications.prototype.getCountry = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 4, ""));
};


/**
 * @param {string} value
 * @return {!proto.QueryUnderstandingServiceRequestForIndications} returns this
 */
proto.QueryUnderstandingServiceRequestForIndications.prototype.setCountry = function(value) {
  return jspb.Message.setProto3StringField(this, 4, value);
};



/**
 * List of repeated fields within this message type.
 * @private {!Array<number>}
 * @const
 */
proto.QueryUnderstandingServiceResponse.repeatedFields_ = [2,4,5];



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.QueryUnderstandingServiceResponse.prototype.toObject = function(opt_includeInstance) {
  return proto.QueryUnderstandingServiceResponse.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.QueryUnderstandingServiceResponse} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.QueryUnderstandingServiceResponse.toObject = function(includeInstance, msg) {
  var f, obj = {
    augmentedQuery: jspb.Message.getFieldWithDefault(msg, 1, ""),
    unigramSynonymList: jspb.Message.toObjectList(msg.getUnigramSynonymList(),
    synonym_pb.Synonym.toObject, includeInstance),
    queryIntent: (f = msg.getQueryIntent()) && query_intent_pb.QueryIntent.toObject(includeInstance, f),
    diagnosisCodesList: (f = jspb.Message.getRepeatedField(msg, 4)) == null ? undefined : f,
    procedureCodesList: (f = jspb.Message.getRepeatedField(msg, 5)) == null ? undefined : f,
    entities: (f = msg.getEntities()) && proto.Entities.toObject(includeInstance, f),
    entitiesV2: (f = msg.getEntitiesV2()) && proto.EntitiesV2.toObject(includeInstance, f)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.QueryUnderstandingServiceResponse}
 */
proto.QueryUnderstandingServiceResponse.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.QueryUnderstandingServiceResponse;
  return proto.QueryUnderstandingServiceResponse.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.QueryUnderstandingServiceResponse} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.QueryUnderstandingServiceResponse}
 */
proto.QueryUnderstandingServiceResponse.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setAugmentedQuery(value);
      break;
    case 2:
      var value = new synonym_pb.Synonym;
      reader.readMessage(value,synonym_pb.Synonym.deserializeBinaryFromReader);
      msg.addUnigramSynonym(value);
      break;
    case 3:
      var value = new query_intent_pb.QueryIntent;
      reader.readMessage(value,query_intent_pb.QueryIntent.deserializeBinaryFromReader);
      msg.setQueryIntent(value);
      break;
    case 4:
      var value = /** @type {string} */ (reader.readString());
      msg.addDiagnosisCodes(value);
      break;
    case 5:
      var value = /** @type {string} */ (reader.readString());
      msg.addProcedureCodes(value);
      break;
    case 6:
      var value = new proto.Entities;
      reader.readMessage(value,proto.Entities.deserializeBinaryFromReader);
      msg.setEntities(value);
      break;
    case 7:
      var value = new proto.EntitiesV2;
      reader.readMessage(value,proto.EntitiesV2.deserializeBinaryFromReader);
      msg.setEntitiesV2(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.QueryUnderstandingServiceResponse.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.QueryUnderstandingServiceResponse.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.QueryUnderstandingServiceResponse} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.QueryUnderstandingServiceResponse.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getAugmentedQuery();
  if (f.length > 0) {
    writer.writeString(
      1,
      f
    );
  }
  f = message.getUnigramSynonymList();
  if (f.length > 0) {
    writer.writeRepeatedMessage(
      2,
      f,
      synonym_pb.Synonym.serializeBinaryToWriter
    );
  }
  f = message.getQueryIntent();
  if (f != null) {
    writer.writeMessage(
      3,
      f,
      query_intent_pb.QueryIntent.serializeBinaryToWriter
    );
  }
  f = message.getDiagnosisCodesList();
  if (f.length > 0) {
    writer.writeRepeatedString(
      4,
      f
    );
  }
  f = message.getProcedureCodesList();
  if (f.length > 0) {
    writer.writeRepeatedString(
      5,
      f
    );
  }
  f = message.getEntities();
  if (f != null) {
    writer.writeMessage(
      6,
      f,
      proto.Entities.serializeBinaryToWriter
    );
  }
  f = message.getEntitiesV2();
  if (f != null) {
    writer.writeMessage(
      7,
      f,
      proto.EntitiesV2.serializeBinaryToWriter
    );
  }
};


/**
 * optional string augmented_query = 1;
 * @return {string}
 */
proto.QueryUnderstandingServiceResponse.prototype.getAugmentedQuery = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/**
 * @param {string} value
 * @return {!proto.QueryUnderstandingServiceResponse} returns this
 */
proto.QueryUnderstandingServiceResponse.prototype.setAugmentedQuery = function(value) {
  return jspb.Message.setProto3StringField(this, 1, value);
};


/**
 * repeated Synonym unigram_synonym = 2;
 * @return {!Array<!proto.Synonym>}
 */
proto.QueryUnderstandingServiceResponse.prototype.getUnigramSynonymList = function() {
  return /** @type{!Array<!proto.Synonym>} */ (
    jspb.Message.getRepeatedWrapperField(this, synonym_pb.Synonym, 2));
};


/**
 * @param {!Array<!proto.Synonym>} value
 * @return {!proto.QueryUnderstandingServiceResponse} returns this
*/
proto.QueryUnderstandingServiceResponse.prototype.setUnigramSynonymList = function(value) {
  return jspb.Message.setRepeatedWrapperField(this, 2, value);
};


/**
 * @param {!proto.Synonym=} opt_value
 * @param {number=} opt_index
 * @return {!proto.Synonym}
 */
proto.QueryUnderstandingServiceResponse.prototype.addUnigramSynonym = function(opt_value, opt_index) {
  return jspb.Message.addToRepeatedWrapperField(this, 2, opt_value, proto.Synonym, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.QueryUnderstandingServiceResponse} returns this
 */
proto.QueryUnderstandingServiceResponse.prototype.clearUnigramSynonymList = function() {
  return this.setUnigramSynonymList([]);
};


/**
 * optional QueryIntent query_intent = 3;
 * @return {?proto.QueryIntent}
 */
proto.QueryUnderstandingServiceResponse.prototype.getQueryIntent = function() {
  return /** @type{?proto.QueryIntent} */ (
    jspb.Message.getWrapperField(this, query_intent_pb.QueryIntent, 3));
};


/**
 * @param {?proto.QueryIntent|undefined} value
 * @return {!proto.QueryUnderstandingServiceResponse} returns this
*/
proto.QueryUnderstandingServiceResponse.prototype.setQueryIntent = function(value) {
  return jspb.Message.setWrapperField(this, 3, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.QueryUnderstandingServiceResponse} returns this
 */
proto.QueryUnderstandingServiceResponse.prototype.clearQueryIntent = function() {
  return this.setQueryIntent(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.QueryUnderstandingServiceResponse.prototype.hasQueryIntent = function() {
  return jspb.Message.getField(this, 3) != null;
};


/**
 * repeated string diagnosis_codes = 4;
 * @return {!Array<string>}
 */
proto.QueryUnderstandingServiceResponse.prototype.getDiagnosisCodesList = function() {
  return /** @type {!Array<string>} */ (jspb.Message.getRepeatedField(this, 4));
};


/**
 * @param {!Array<string>} value
 * @return {!proto.QueryUnderstandingServiceResponse} returns this
 */
proto.QueryUnderstandingServiceResponse.prototype.setDiagnosisCodesList = function(value) {
  return jspb.Message.setField(this, 4, value || []);
};


/**
 * @param {string} value
 * @param {number=} opt_index
 * @return {!proto.QueryUnderstandingServiceResponse} returns this
 */
proto.QueryUnderstandingServiceResponse.prototype.addDiagnosisCodes = function(value, opt_index) {
  return jspb.Message.addToRepeatedField(this, 4, value, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.QueryUnderstandingServiceResponse} returns this
 */
proto.QueryUnderstandingServiceResponse.prototype.clearDiagnosisCodesList = function() {
  return this.setDiagnosisCodesList([]);
};


/**
 * repeated string procedure_codes = 5;
 * @return {!Array<string>}
 */
proto.QueryUnderstandingServiceResponse.prototype.getProcedureCodesList = function() {
  return /** @type {!Array<string>} */ (jspb.Message.getRepeatedField(this, 5));
};


/**
 * @param {!Array<string>} value
 * @return {!proto.QueryUnderstandingServiceResponse} returns this
 */
proto.QueryUnderstandingServiceResponse.prototype.setProcedureCodesList = function(value) {
  return jspb.Message.setField(this, 5, value || []);
};


/**
 * @param {string} value
 * @param {number=} opt_index
 * @return {!proto.QueryUnderstandingServiceResponse} returns this
 */
proto.QueryUnderstandingServiceResponse.prototype.addProcedureCodes = function(value, opt_index) {
  return jspb.Message.addToRepeatedField(this, 5, value, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.QueryUnderstandingServiceResponse} returns this
 */
proto.QueryUnderstandingServiceResponse.prototype.clearProcedureCodesList = function() {
  return this.setProcedureCodesList([]);
};


/**
 * optional Entities entities = 6;
 * @return {?proto.Entities}
 */
proto.QueryUnderstandingServiceResponse.prototype.getEntities = function() {
  return /** @type{?proto.Entities} */ (
    jspb.Message.getWrapperField(this, proto.Entities, 6));
};


/**
 * @param {?proto.Entities|undefined} value
 * @return {!proto.QueryUnderstandingServiceResponse} returns this
*/
proto.QueryUnderstandingServiceResponse.prototype.setEntities = function(value) {
  return jspb.Message.setWrapperField(this, 6, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.QueryUnderstandingServiceResponse} returns this
 */
proto.QueryUnderstandingServiceResponse.prototype.clearEntities = function() {
  return this.setEntities(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.QueryUnderstandingServiceResponse.prototype.hasEntities = function() {
  return jspb.Message.getField(this, 6) != null;
};


/**
 * optional EntitiesV2 entities_v2 = 7;
 * @return {?proto.EntitiesV2}
 */
proto.QueryUnderstandingServiceResponse.prototype.getEntitiesV2 = function() {
  return /** @type{?proto.EntitiesV2} */ (
    jspb.Message.getWrapperField(this, proto.EntitiesV2, 7));
};


/**
 * @param {?proto.EntitiesV2|undefined} value
 * @return {!proto.QueryUnderstandingServiceResponse} returns this
*/
proto.QueryUnderstandingServiceResponse.prototype.setEntitiesV2 = function(value) {
  return jspb.Message.setWrapperField(this, 7, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.QueryUnderstandingServiceResponse} returns this
 */
proto.QueryUnderstandingServiceResponse.prototype.clearEntitiesV2 = function() {
  return this.setEntitiesV2(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.QueryUnderstandingServiceResponse.prototype.hasEntitiesV2 = function() {
  return jspb.Message.getField(this, 7) != null;
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.QueryUnderstandingServiceResponseForIndications.prototype.toObject = function(opt_includeInstance) {
  return proto.QueryUnderstandingServiceResponseForIndications.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.QueryUnderstandingServiceResponseForIndications} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.QueryUnderstandingServiceResponseForIndications.toObject = function(includeInstance, msg) {
  var f, obj = {
    indicationsParsedQuery: jspb.Message.getFieldWithDefault(msg, 1, ""),
    indicationsIcdCodesQuery: jspb.Message.getFieldWithDefault(msg, 2, "")
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.QueryUnderstandingServiceResponseForIndications}
 */
proto.QueryUnderstandingServiceResponseForIndications.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.QueryUnderstandingServiceResponseForIndications;
  return proto.QueryUnderstandingServiceResponseForIndications.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.QueryUnderstandingServiceResponseForIndications} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.QueryUnderstandingServiceResponseForIndications}
 */
proto.QueryUnderstandingServiceResponseForIndications.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setIndicationsParsedQuery(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setIndicationsIcdCodesQuery(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.QueryUnderstandingServiceResponseForIndications.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.QueryUnderstandingServiceResponseForIndications.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.QueryUnderstandingServiceResponseForIndications} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.QueryUnderstandingServiceResponseForIndications.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getIndicationsParsedQuery();
  if (f.length > 0) {
    writer.writeString(
      1,
      f
    );
  }
  f = message.getIndicationsIcdCodesQuery();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
};


/**
 * optional string indications_parsed_query = 1;
 * @return {string}
 */
proto.QueryUnderstandingServiceResponseForIndications.prototype.getIndicationsParsedQuery = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/**
 * @param {string} value
 * @return {!proto.QueryUnderstandingServiceResponseForIndications} returns this
 */
proto.QueryUnderstandingServiceResponseForIndications.prototype.setIndicationsParsedQuery = function(value) {
  return jspb.Message.setProto3StringField(this, 1, value);
};


/**
 * optional string indications_icd_codes_query = 2;
 * @return {string}
 */
proto.QueryUnderstandingServiceResponseForIndications.prototype.getIndicationsIcdCodesQuery = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/**
 * @param {string} value
 * @return {!proto.QueryUnderstandingServiceResponseForIndications} returns this
 */
proto.QueryUnderstandingServiceResponseForIndications.prototype.setIndicationsIcdCodesQuery = function(value) {
  return jspb.Message.setProto3StringField(this, 2, value);
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.Entities.prototype.toObject = function(opt_includeInstance) {
  return proto.Entities.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.Entities} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.Entities.toObject = function(includeInstance, msg) {
  var f, obj = {
    searchFor: jspb.Message.getFieldWithDefault(msg, 1, ""),
    indication: jspb.Message.getFieldWithDefault(msg, 2, ""),
    names: (f = msg.getNames()) && proto.NameTypes.toObject(includeInstance, f),
    rankBy: jspb.Message.getFieldWithDefault(msg, 4, ""),
    locations: (f = msg.getLocations()) && proto.Locations.toObject(includeInstance, f),
    leadership: (f = msg.getLeadership()) && proto.Leadership.toObject(includeInstance, f)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.Entities}
 */
proto.Entities.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.Entities;
  return proto.Entities.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.Entities} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.Entities}
 */
proto.Entities.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setSearchFor(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setIndication(value);
      break;
    case 3:
      var value = new proto.NameTypes;
      reader.readMessage(value,proto.NameTypes.deserializeBinaryFromReader);
      msg.setNames(value);
      break;
    case 4:
      var value = /** @type {string} */ (reader.readString());
      msg.setRankBy(value);
      break;
    case 5:
      var value = new proto.Locations;
      reader.readMessage(value,proto.Locations.deserializeBinaryFromReader);
      msg.setLocations(value);
      break;
    case 6:
      var value = new proto.Leadership;
      reader.readMessage(value,proto.Leadership.deserializeBinaryFromReader);
      msg.setLeadership(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.Entities.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.Entities.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.Entities} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.Entities.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getSearchFor();
  if (f.length > 0) {
    writer.writeString(
      1,
      f
    );
  }
  f = message.getIndication();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
  f = message.getNames();
  if (f != null) {
    writer.writeMessage(
      3,
      f,
      proto.NameTypes.serializeBinaryToWriter
    );
  }
  f = message.getRankBy();
  if (f.length > 0) {
    writer.writeString(
      4,
      f
    );
  }
  f = message.getLocations();
  if (f != null) {
    writer.writeMessage(
      5,
      f,
      proto.Locations.serializeBinaryToWriter
    );
  }
  f = message.getLeadership();
  if (f != null) {
    writer.writeMessage(
      6,
      f,
      proto.Leadership.serializeBinaryToWriter
    );
  }
};


/**
 * optional string search_for = 1;
 * @return {string}
 */
proto.Entities.prototype.getSearchFor = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/**
 * @param {string} value
 * @return {!proto.Entities} returns this
 */
proto.Entities.prototype.setSearchFor = function(value) {
  return jspb.Message.setProto3StringField(this, 1, value);
};


/**
 * optional string indication = 2;
 * @return {string}
 */
proto.Entities.prototype.getIndication = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/**
 * @param {string} value
 * @return {!proto.Entities} returns this
 */
proto.Entities.prototype.setIndication = function(value) {
  return jspb.Message.setProto3StringField(this, 2, value);
};


/**
 * optional NameTypes names = 3;
 * @return {?proto.NameTypes}
 */
proto.Entities.prototype.getNames = function() {
  return /** @type{?proto.NameTypes} */ (
    jspb.Message.getWrapperField(this, proto.NameTypes, 3));
};


/**
 * @param {?proto.NameTypes|undefined} value
 * @return {!proto.Entities} returns this
*/
proto.Entities.prototype.setNames = function(value) {
  return jspb.Message.setWrapperField(this, 3, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.Entities} returns this
 */
proto.Entities.prototype.clearNames = function() {
  return this.setNames(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.Entities.prototype.hasNames = function() {
  return jspb.Message.getField(this, 3) != null;
};


/**
 * optional string rank_by = 4;
 * @return {string}
 */
proto.Entities.prototype.getRankBy = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 4, ""));
};


/**
 * @param {string} value
 * @return {!proto.Entities} returns this
 */
proto.Entities.prototype.setRankBy = function(value) {
  return jspb.Message.setProto3StringField(this, 4, value);
};


/**
 * optional Locations locations = 5;
 * @return {?proto.Locations}
 */
proto.Entities.prototype.getLocations = function() {
  return /** @type{?proto.Locations} */ (
    jspb.Message.getWrapperField(this, proto.Locations, 5));
};


/**
 * @param {?proto.Locations|undefined} value
 * @return {!proto.Entities} returns this
*/
proto.Entities.prototype.setLocations = function(value) {
  return jspb.Message.setWrapperField(this, 5, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.Entities} returns this
 */
proto.Entities.prototype.clearLocations = function() {
  return this.setLocations(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.Entities.prototype.hasLocations = function() {
  return jspb.Message.getField(this, 5) != null;
};


/**
 * optional Leadership leadership = 6;
 * @return {?proto.Leadership}
 */
proto.Entities.prototype.getLeadership = function() {
  return /** @type{?proto.Leadership} */ (
    jspb.Message.getWrapperField(this, proto.Leadership, 6));
};


/**
 * @param {?proto.Leadership|undefined} value
 * @return {!proto.Entities} returns this
*/
proto.Entities.prototype.setLeadership = function(value) {
  return jspb.Message.setWrapperField(this, 6, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.Entities} returns this
 */
proto.Entities.prototype.clearLeadership = function() {
  return this.setLeadership(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.Entities.prototype.hasLeadership = function() {
  return jspb.Message.getField(this, 6) != null;
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.NameTypes.prototype.toObject = function(opt_includeInstance) {
  return proto.NameTypes.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.NameTypes} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.NameTypes.toObject = function(includeInstance, msg) {
  var f, obj = {
    people: jspb.Message.getFieldWithDefault(msg, 1, ""),
    institutions: jspb.Message.getFieldWithDefault(msg, 2, "")
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.NameTypes}
 */
proto.NameTypes.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.NameTypes;
  return proto.NameTypes.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.NameTypes} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.NameTypes}
 */
proto.NameTypes.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setPeople(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setInstitutions(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.NameTypes.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.NameTypes.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.NameTypes} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.NameTypes.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getPeople();
  if (f.length > 0) {
    writer.writeString(
      1,
      f
    );
  }
  f = message.getInstitutions();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
};


/**
 * optional string people = 1;
 * @return {string}
 */
proto.NameTypes.prototype.getPeople = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/**
 * @param {string} value
 * @return {!proto.NameTypes} returns this
 */
proto.NameTypes.prototype.setPeople = function(value) {
  return jspb.Message.setProto3StringField(this, 1, value);
};


/**
 * optional string institutions = 2;
 * @return {string}
 */
proto.NameTypes.prototype.getInstitutions = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/**
 * @param {string} value
 * @return {!proto.NameTypes} returns this
 */
proto.NameTypes.prototype.setInstitutions = function(value) {
  return jspb.Message.setProto3StringField(this, 2, value);
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.Locations.prototype.toObject = function(opt_includeInstance) {
  return proto.Locations.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.Locations} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.Locations.toObject = function(includeInstance, msg) {
  var f, obj = {
    country: jspb.Message.getFieldWithDefault(msg, 1, ""),
    state: jspb.Message.getFieldWithDefault(msg, 2, ""),
    city: jspb.Message.getFieldWithDefault(msg, 3, ""),
    zipcode: jspb.Message.getFieldWithDefault(msg, 4, "")
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.Locations}
 */
proto.Locations.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.Locations;
  return proto.Locations.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.Locations} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.Locations}
 */
proto.Locations.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setCountry(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setState(value);
      break;
    case 3:
      var value = /** @type {string} */ (reader.readString());
      msg.setCity(value);
      break;
    case 4:
      var value = /** @type {string} */ (reader.readString());
      msg.setZipcode(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.Locations.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.Locations.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.Locations} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.Locations.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getCountry();
  if (f.length > 0) {
    writer.writeString(
      1,
      f
    );
  }
  f = message.getState();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
  f = message.getCity();
  if (f.length > 0) {
    writer.writeString(
      3,
      f
    );
  }
  f = message.getZipcode();
  if (f.length > 0) {
    writer.writeString(
      4,
      f
    );
  }
};


/**
 * optional string country = 1;
 * @return {string}
 */
proto.Locations.prototype.getCountry = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/**
 * @param {string} value
 * @return {!proto.Locations} returns this
 */
proto.Locations.prototype.setCountry = function(value) {
  return jspb.Message.setProto3StringField(this, 1, value);
};


/**
 * optional string state = 2;
 * @return {string}
 */
proto.Locations.prototype.getState = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/**
 * @param {string} value
 * @return {!proto.Locations} returns this
 */
proto.Locations.prototype.setState = function(value) {
  return jspb.Message.setProto3StringField(this, 2, value);
};


/**
 * optional string city = 3;
 * @return {string}
 */
proto.Locations.prototype.getCity = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 3, ""));
};


/**
 * @param {string} value
 * @return {!proto.Locations} returns this
 */
proto.Locations.prototype.setCity = function(value) {
  return jspb.Message.setProto3StringField(this, 3, value);
};


/**
 * optional string zipcode = 4;
 * @return {string}
 */
proto.Locations.prototype.getZipcode = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 4, ""));
};


/**
 * @param {string} value
 * @return {!proto.Locations} returns this
 */
proto.Locations.prototype.setZipcode = function(value) {
  return jspb.Message.setProto3StringField(this, 4, value);
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.Leadership.prototype.toObject = function(opt_includeInstance) {
  return proto.Leadership.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.Leadership} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.Leadership.toObject = function(includeInstance, msg) {
  var f, obj = {
    isGlobalLeader: jspb.Message.getBooleanFieldWithDefault(msg, 1, false),
    isNationalLeader: jspb.Message.getBooleanFieldWithDefault(msg, 2, false),
    isRegionalLeader: jspb.Message.getBooleanFieldWithDefault(msg, 3, false),
    isLocalLeader: jspb.Message.getBooleanFieldWithDefault(msg, 4, false)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.Leadership}
 */
proto.Leadership.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.Leadership;
  return proto.Leadership.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.Leadership} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.Leadership}
 */
proto.Leadership.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {boolean} */ (reader.readBool());
      msg.setIsGlobalLeader(value);
      break;
    case 2:
      var value = /** @type {boolean} */ (reader.readBool());
      msg.setIsNationalLeader(value);
      break;
    case 3:
      var value = /** @type {boolean} */ (reader.readBool());
      msg.setIsRegionalLeader(value);
      break;
    case 4:
      var value = /** @type {boolean} */ (reader.readBool());
      msg.setIsLocalLeader(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.Leadership.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.Leadership.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.Leadership} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.Leadership.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getIsGlobalLeader();
  if (f) {
    writer.writeBool(
      1,
      f
    );
  }
  f = message.getIsNationalLeader();
  if (f) {
    writer.writeBool(
      2,
      f
    );
  }
  f = message.getIsRegionalLeader();
  if (f) {
    writer.writeBool(
      3,
      f
    );
  }
  f = message.getIsLocalLeader();
  if (f) {
    writer.writeBool(
      4,
      f
    );
  }
};


/**
 * optional bool is_global_leader = 1;
 * @return {boolean}
 */
proto.Leadership.prototype.getIsGlobalLeader = function() {
  return /** @type {boolean} */ (jspb.Message.getBooleanFieldWithDefault(this, 1, false));
};


/**
 * @param {boolean} value
 * @return {!proto.Leadership} returns this
 */
proto.Leadership.prototype.setIsGlobalLeader = function(value) {
  return jspb.Message.setProto3BooleanField(this, 1, value);
};


/**
 * optional bool is_national_leader = 2;
 * @return {boolean}
 */
proto.Leadership.prototype.getIsNationalLeader = function() {
  return /** @type {boolean} */ (jspb.Message.getBooleanFieldWithDefault(this, 2, false));
};


/**
 * @param {boolean} value
 * @return {!proto.Leadership} returns this
 */
proto.Leadership.prototype.setIsNationalLeader = function(value) {
  return jspb.Message.setProto3BooleanField(this, 2, value);
};


/**
 * optional bool is_regional_leader = 3;
 * @return {boolean}
 */
proto.Leadership.prototype.getIsRegionalLeader = function() {
  return /** @type {boolean} */ (jspb.Message.getBooleanFieldWithDefault(this, 3, false));
};


/**
 * @param {boolean} value
 * @return {!proto.Leadership} returns this
 */
proto.Leadership.prototype.setIsRegionalLeader = function(value) {
  return jspb.Message.setProto3BooleanField(this, 3, value);
};


/**
 * optional bool is_local_leader = 4;
 * @return {boolean}
 */
proto.Leadership.prototype.getIsLocalLeader = function() {
  return /** @type {boolean} */ (jspb.Message.getBooleanFieldWithDefault(this, 4, false));
};


/**
 * @param {boolean} value
 * @return {!proto.Leadership} returns this
 */
proto.Leadership.prototype.setIsLocalLeader = function(value) {
  return jspb.Message.setProto3BooleanField(this, 4, value);
};



/**
 * List of repeated fields within this message type.
 * @private {!Array<number>}
 * @const
 */
proto.EntitiesV2.repeatedFields_ = [2,5,7];



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.EntitiesV2.prototype.toObject = function(opt_includeInstance) {
  return proto.EntitiesV2.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.EntitiesV2} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.EntitiesV2.toObject = function(includeInstance, msg) {
  var f, obj = {
    searchFor: jspb.Message.getFieldWithDefault(msg, 1, ""),
    indicationList: (f = jspb.Message.getRepeatedField(msg, 2)) == null ? undefined : f,
    locations: (f = msg.getLocations()) && proto.LocationsV2.toObject(includeInstance, f),
    claims: (f = msg.getClaims()) && proto.Claims.toObject(includeInstance, f),
    specialtyList: (f = jspb.Message.getRepeatedField(msg, 5)) == null ? undefined : f,
    trial: (f = msg.getTrial()) && proto.TrialInfo.toObject(includeInstance, f),
    workInstitutionList: (f = jspb.Message.getRepeatedField(msg, 7)) == null ? undefined : f
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.EntitiesV2}
 */
proto.EntitiesV2.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.EntitiesV2;
  return proto.EntitiesV2.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.EntitiesV2} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.EntitiesV2}
 */
proto.EntitiesV2.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setSearchFor(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.addIndication(value);
      break;
    case 3:
      var value = new proto.LocationsV2;
      reader.readMessage(value,proto.LocationsV2.deserializeBinaryFromReader);
      msg.setLocations(value);
      break;
    case 4:
      var value = new proto.Claims;
      reader.readMessage(value,proto.Claims.deserializeBinaryFromReader);
      msg.setClaims(value);
      break;
    case 5:
      var value = /** @type {string} */ (reader.readString());
      msg.addSpecialty(value);
      break;
    case 6:
      var value = new proto.TrialInfo;
      reader.readMessage(value,proto.TrialInfo.deserializeBinaryFromReader);
      msg.setTrial(value);
      break;
    case 7:
      var value = /** @type {string} */ (reader.readString());
      msg.addWorkInstitution(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.EntitiesV2.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.EntitiesV2.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.EntitiesV2} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.EntitiesV2.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getSearchFor();
  if (f.length > 0) {
    writer.writeString(
      1,
      f
    );
  }
  f = message.getIndicationList();
  if (f.length > 0) {
    writer.writeRepeatedString(
      2,
      f
    );
  }
  f = message.getLocations();
  if (f != null) {
    writer.writeMessage(
      3,
      f,
      proto.LocationsV2.serializeBinaryToWriter
    );
  }
  f = message.getClaims();
  if (f != null) {
    writer.writeMessage(
      4,
      f,
      proto.Claims.serializeBinaryToWriter
    );
  }
  f = message.getSpecialtyList();
  if (f.length > 0) {
    writer.writeRepeatedString(
      5,
      f
    );
  }
  f = message.getTrial();
  if (f != null) {
    writer.writeMessage(
      6,
      f,
      proto.TrialInfo.serializeBinaryToWriter
    );
  }
  f = message.getWorkInstitutionList();
  if (f.length > 0) {
    writer.writeRepeatedString(
      7,
      f
    );
  }
};


/**
 * optional string search_for = 1;
 * @return {string}
 */
proto.EntitiesV2.prototype.getSearchFor = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/**
 * @param {string} value
 * @return {!proto.EntitiesV2} returns this
 */
proto.EntitiesV2.prototype.setSearchFor = function(value) {
  return jspb.Message.setProto3StringField(this, 1, value);
};


/**
 * repeated string indication = 2;
 * @return {!Array<string>}
 */
proto.EntitiesV2.prototype.getIndicationList = function() {
  return /** @type {!Array<string>} */ (jspb.Message.getRepeatedField(this, 2));
};


/**
 * @param {!Array<string>} value
 * @return {!proto.EntitiesV2} returns this
 */
proto.EntitiesV2.prototype.setIndicationList = function(value) {
  return jspb.Message.setField(this, 2, value || []);
};


/**
 * @param {string} value
 * @param {number=} opt_index
 * @return {!proto.EntitiesV2} returns this
 */
proto.EntitiesV2.prototype.addIndication = function(value, opt_index) {
  return jspb.Message.addToRepeatedField(this, 2, value, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.EntitiesV2} returns this
 */
proto.EntitiesV2.prototype.clearIndicationList = function() {
  return this.setIndicationList([]);
};


/**
 * optional LocationsV2 locations = 3;
 * @return {?proto.LocationsV2}
 */
proto.EntitiesV2.prototype.getLocations = function() {
  return /** @type{?proto.LocationsV2} */ (
    jspb.Message.getWrapperField(this, proto.LocationsV2, 3));
};


/**
 * @param {?proto.LocationsV2|undefined} value
 * @return {!proto.EntitiesV2} returns this
*/
proto.EntitiesV2.prototype.setLocations = function(value) {
  return jspb.Message.setWrapperField(this, 3, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.EntitiesV2} returns this
 */
proto.EntitiesV2.prototype.clearLocations = function() {
  return this.setLocations(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.EntitiesV2.prototype.hasLocations = function() {
  return jspb.Message.getField(this, 3) != null;
};


/**
 * optional Claims claims = 4;
 * @return {?proto.Claims}
 */
proto.EntitiesV2.prototype.getClaims = function() {
  return /** @type{?proto.Claims} */ (
    jspb.Message.getWrapperField(this, proto.Claims, 4));
};


/**
 * @param {?proto.Claims|undefined} value
 * @return {!proto.EntitiesV2} returns this
*/
proto.EntitiesV2.prototype.setClaims = function(value) {
  return jspb.Message.setWrapperField(this, 4, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.EntitiesV2} returns this
 */
proto.EntitiesV2.prototype.clearClaims = function() {
  return this.setClaims(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.EntitiesV2.prototype.hasClaims = function() {
  return jspb.Message.getField(this, 4) != null;
};


/**
 * repeated string specialty = 5;
 * @return {!Array<string>}
 */
proto.EntitiesV2.prototype.getSpecialtyList = function() {
  return /** @type {!Array<string>} */ (jspb.Message.getRepeatedField(this, 5));
};


/**
 * @param {!Array<string>} value
 * @return {!proto.EntitiesV2} returns this
 */
proto.EntitiesV2.prototype.setSpecialtyList = function(value) {
  return jspb.Message.setField(this, 5, value || []);
};


/**
 * @param {string} value
 * @param {number=} opt_index
 * @return {!proto.EntitiesV2} returns this
 */
proto.EntitiesV2.prototype.addSpecialty = function(value, opt_index) {
  return jspb.Message.addToRepeatedField(this, 5, value, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.EntitiesV2} returns this
 */
proto.EntitiesV2.prototype.clearSpecialtyList = function() {
  return this.setSpecialtyList([]);
};


/**
 * optional TrialInfo trial = 6;
 * @return {?proto.TrialInfo}
 */
proto.EntitiesV2.prototype.getTrial = function() {
  return /** @type{?proto.TrialInfo} */ (
    jspb.Message.getWrapperField(this, proto.TrialInfo, 6));
};


/**
 * @param {?proto.TrialInfo|undefined} value
 * @return {!proto.EntitiesV2} returns this
*/
proto.EntitiesV2.prototype.setTrial = function(value) {
  return jspb.Message.setWrapperField(this, 6, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.EntitiesV2} returns this
 */
proto.EntitiesV2.prototype.clearTrial = function() {
  return this.setTrial(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.EntitiesV2.prototype.hasTrial = function() {
  return jspb.Message.getField(this, 6) != null;
};


/**
 * repeated string work_institution = 7;
 * @return {!Array<string>}
 */
proto.EntitiesV2.prototype.getWorkInstitutionList = function() {
  return /** @type {!Array<string>} */ (jspb.Message.getRepeatedField(this, 7));
};


/**
 * @param {!Array<string>} value
 * @return {!proto.EntitiesV2} returns this
 */
proto.EntitiesV2.prototype.setWorkInstitutionList = function(value) {
  return jspb.Message.setField(this, 7, value || []);
};


/**
 * @param {string} value
 * @param {number=} opt_index
 * @return {!proto.EntitiesV2} returns this
 */
proto.EntitiesV2.prototype.addWorkInstitution = function(value, opt_index) {
  return jspb.Message.addToRepeatedField(this, 7, value, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.EntitiesV2} returns this
 */
proto.EntitiesV2.prototype.clearWorkInstitutionList = function() {
  return this.setWorkInstitutionList([]);
};



/**
 * List of repeated fields within this message type.
 * @private {!Array<number>}
 * @const
 */
proto.LocationsV2.repeatedFields_ = [1,2,3];



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.LocationsV2.prototype.toObject = function(opt_includeInstance) {
  return proto.LocationsV2.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.LocationsV2} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.LocationsV2.toObject = function(includeInstance, msg) {
  var f, obj = {
    countryList: (f = jspb.Message.getRepeatedField(msg, 1)) == null ? undefined : f,
    stateList: (f = jspb.Message.getRepeatedField(msg, 2)) == null ? undefined : f,
    cityList: (f = jspb.Message.getRepeatedField(msg, 3)) == null ? undefined : f
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.LocationsV2}
 */
proto.LocationsV2.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.LocationsV2;
  return proto.LocationsV2.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.LocationsV2} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.LocationsV2}
 */
proto.LocationsV2.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.addCountry(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.addState(value);
      break;
    case 3:
      var value = /** @type {string} */ (reader.readString());
      msg.addCity(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.LocationsV2.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.LocationsV2.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.LocationsV2} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.LocationsV2.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getCountryList();
  if (f.length > 0) {
    writer.writeRepeatedString(
      1,
      f
    );
  }
  f = message.getStateList();
  if (f.length > 0) {
    writer.writeRepeatedString(
      2,
      f
    );
  }
  f = message.getCityList();
  if (f.length > 0) {
    writer.writeRepeatedString(
      3,
      f
    );
  }
};


/**
 * repeated string country = 1;
 * @return {!Array<string>}
 */
proto.LocationsV2.prototype.getCountryList = function() {
  return /** @type {!Array<string>} */ (jspb.Message.getRepeatedField(this, 1));
};


/**
 * @param {!Array<string>} value
 * @return {!proto.LocationsV2} returns this
 */
proto.LocationsV2.prototype.setCountryList = function(value) {
  return jspb.Message.setField(this, 1, value || []);
};


/**
 * @param {string} value
 * @param {number=} opt_index
 * @return {!proto.LocationsV2} returns this
 */
proto.LocationsV2.prototype.addCountry = function(value, opt_index) {
  return jspb.Message.addToRepeatedField(this, 1, value, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.LocationsV2} returns this
 */
proto.LocationsV2.prototype.clearCountryList = function() {
  return this.setCountryList([]);
};


/**
 * repeated string state = 2;
 * @return {!Array<string>}
 */
proto.LocationsV2.prototype.getStateList = function() {
  return /** @type {!Array<string>} */ (jspb.Message.getRepeatedField(this, 2));
};


/**
 * @param {!Array<string>} value
 * @return {!proto.LocationsV2} returns this
 */
proto.LocationsV2.prototype.setStateList = function(value) {
  return jspb.Message.setField(this, 2, value || []);
};


/**
 * @param {string} value
 * @param {number=} opt_index
 * @return {!proto.LocationsV2} returns this
 */
proto.LocationsV2.prototype.addState = function(value, opt_index) {
  return jspb.Message.addToRepeatedField(this, 2, value, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.LocationsV2} returns this
 */
proto.LocationsV2.prototype.clearStateList = function() {
  return this.setStateList([]);
};


/**
 * repeated string city = 3;
 * @return {!Array<string>}
 */
proto.LocationsV2.prototype.getCityList = function() {
  return /** @type {!Array<string>} */ (jspb.Message.getRepeatedField(this, 3));
};


/**
 * @param {!Array<string>} value
 * @return {!proto.LocationsV2} returns this
 */
proto.LocationsV2.prototype.setCityList = function(value) {
  return jspb.Message.setField(this, 3, value || []);
};


/**
 * @param {string} value
 * @param {number=} opt_index
 * @return {!proto.LocationsV2} returns this
 */
proto.LocationsV2.prototype.addCity = function(value, opt_index) {
  return jspb.Message.addToRepeatedField(this, 3, value, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.LocationsV2} returns this
 */
proto.LocationsV2.prototype.clearCityList = function() {
  return this.setCityList([]);
};



/**
 * List of repeated fields within this message type.
 * @private {!Array<number>}
 * @const
 */
proto.Claims.repeatedFields_ = [1,4];



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.Claims.prototype.toObject = function(opt_includeInstance) {
  return proto.Claims.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.Claims} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.Claims.toObject = function(includeInstance, msg) {
  var f, obj = {
    diagnosesList: (f = jspb.Message.getRepeatedField(msg, 1)) == null ? undefined : f,
    minDiagnosesClaimCount: jspb.Message.getFieldWithDefault(msg, 2, 0),
    maxDiagnosesClaimCount: jspb.Message.getFieldWithDefault(msg, 3, 0),
    proceduresList: (f = jspb.Message.getRepeatedField(msg, 4)) == null ? undefined : f,
    minProceduresClaimCount: jspb.Message.getFieldWithDefault(msg, 5, 0),
    maxProceduresClaimCount: jspb.Message.getFieldWithDefault(msg, 6, 0),
    timeframe: jspb.Message.getFieldWithDefault(msg, 7, "")
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.Claims}
 */
proto.Claims.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.Claims;
  return proto.Claims.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.Claims} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.Claims}
 */
proto.Claims.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.addDiagnoses(value);
      break;
    case 2:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setMinDiagnosesClaimCount(value);
      break;
    case 3:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setMaxDiagnosesClaimCount(value);
      break;
    case 4:
      var value = /** @type {string} */ (reader.readString());
      msg.addProcedures(value);
      break;
    case 5:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setMinProceduresClaimCount(value);
      break;
    case 6:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setMaxProceduresClaimCount(value);
      break;
    case 7:
      var value = /** @type {string} */ (reader.readString());
      msg.setTimeframe(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.Claims.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.Claims.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.Claims} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.Claims.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getDiagnosesList();
  if (f.length > 0) {
    writer.writeRepeatedString(
      1,
      f
    );
  }
  f = /** @type {number} */ (jspb.Message.getField(message, 2));
  if (f != null) {
    writer.writeInt32(
      2,
      f
    );
  }
  f = /** @type {number} */ (jspb.Message.getField(message, 3));
  if (f != null) {
    writer.writeInt32(
      3,
      f
    );
  }
  f = message.getProceduresList();
  if (f.length > 0) {
    writer.writeRepeatedString(
      4,
      f
    );
  }
  f = /** @type {number} */ (jspb.Message.getField(message, 5));
  if (f != null) {
    writer.writeInt32(
      5,
      f
    );
  }
  f = /** @type {number} */ (jspb.Message.getField(message, 6));
  if (f != null) {
    writer.writeInt32(
      6,
      f
    );
  }
  f = /** @type {string} */ (jspb.Message.getField(message, 7));
  if (f != null) {
    writer.writeString(
      7,
      f
    );
  }
};


/**
 * repeated string diagnoses = 1;
 * @return {!Array<string>}
 */
proto.Claims.prototype.getDiagnosesList = function() {
  return /** @type {!Array<string>} */ (jspb.Message.getRepeatedField(this, 1));
};


/**
 * @param {!Array<string>} value
 * @return {!proto.Claims} returns this
 */
proto.Claims.prototype.setDiagnosesList = function(value) {
  return jspb.Message.setField(this, 1, value || []);
};


/**
 * @param {string} value
 * @param {number=} opt_index
 * @return {!proto.Claims} returns this
 */
proto.Claims.prototype.addDiagnoses = function(value, opt_index) {
  return jspb.Message.addToRepeatedField(this, 1, value, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.Claims} returns this
 */
proto.Claims.prototype.clearDiagnosesList = function() {
  return this.setDiagnosesList([]);
};


/**
 * optional int32 min_diagnoses_claim_count = 2;
 * @return {number}
 */
proto.Claims.prototype.getMinDiagnosesClaimCount = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 2, 0));
};


/**
 * @param {number} value
 * @return {!proto.Claims} returns this
 */
proto.Claims.prototype.setMinDiagnosesClaimCount = function(value) {
  return jspb.Message.setField(this, 2, value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.Claims} returns this
 */
proto.Claims.prototype.clearMinDiagnosesClaimCount = function() {
  return jspb.Message.setField(this, 2, undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.Claims.prototype.hasMinDiagnosesClaimCount = function() {
  return jspb.Message.getField(this, 2) != null;
};


/**
 * optional int32 max_diagnoses_claim_count = 3;
 * @return {number}
 */
proto.Claims.prototype.getMaxDiagnosesClaimCount = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 3, 0));
};


/**
 * @param {number} value
 * @return {!proto.Claims} returns this
 */
proto.Claims.prototype.setMaxDiagnosesClaimCount = function(value) {
  return jspb.Message.setField(this, 3, value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.Claims} returns this
 */
proto.Claims.prototype.clearMaxDiagnosesClaimCount = function() {
  return jspb.Message.setField(this, 3, undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.Claims.prototype.hasMaxDiagnosesClaimCount = function() {
  return jspb.Message.getField(this, 3) != null;
};


/**
 * repeated string procedures = 4;
 * @return {!Array<string>}
 */
proto.Claims.prototype.getProceduresList = function() {
  return /** @type {!Array<string>} */ (jspb.Message.getRepeatedField(this, 4));
};


/**
 * @param {!Array<string>} value
 * @return {!proto.Claims} returns this
 */
proto.Claims.prototype.setProceduresList = function(value) {
  return jspb.Message.setField(this, 4, value || []);
};


/**
 * @param {string} value
 * @param {number=} opt_index
 * @return {!proto.Claims} returns this
 */
proto.Claims.prototype.addProcedures = function(value, opt_index) {
  return jspb.Message.addToRepeatedField(this, 4, value, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.Claims} returns this
 */
proto.Claims.prototype.clearProceduresList = function() {
  return this.setProceduresList([]);
};


/**
 * optional int32 min_procedures_claim_count = 5;
 * @return {number}
 */
proto.Claims.prototype.getMinProceduresClaimCount = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 5, 0));
};


/**
 * @param {number} value
 * @return {!proto.Claims} returns this
 */
proto.Claims.prototype.setMinProceduresClaimCount = function(value) {
  return jspb.Message.setField(this, 5, value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.Claims} returns this
 */
proto.Claims.prototype.clearMinProceduresClaimCount = function() {
  return jspb.Message.setField(this, 5, undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.Claims.prototype.hasMinProceduresClaimCount = function() {
  return jspb.Message.getField(this, 5) != null;
};


/**
 * optional int32 max_procedures_claim_count = 6;
 * @return {number}
 */
proto.Claims.prototype.getMaxProceduresClaimCount = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 6, 0));
};


/**
 * @param {number} value
 * @return {!proto.Claims} returns this
 */
proto.Claims.prototype.setMaxProceduresClaimCount = function(value) {
  return jspb.Message.setField(this, 6, value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.Claims} returns this
 */
proto.Claims.prototype.clearMaxProceduresClaimCount = function() {
  return jspb.Message.setField(this, 6, undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.Claims.prototype.hasMaxProceduresClaimCount = function() {
  return jspb.Message.getField(this, 6) != null;
};


/**
 * optional string timeframe = 7;
 * @return {string}
 */
proto.Claims.prototype.getTimeframe = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 7, ""));
};


/**
 * @param {string} value
 * @return {!proto.Claims} returns this
 */
proto.Claims.prototype.setTimeframe = function(value) {
  return jspb.Message.setField(this, 7, value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.Claims} returns this
 */
proto.Claims.prototype.clearTimeframe = function() {
  return jspb.Message.setField(this, 7, undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.Claims.prototype.hasTimeframe = function() {
  return jspb.Message.getField(this, 7) != null;
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.TrialInfo.prototype.toObject = function(opt_includeInstance) {
  return proto.TrialInfo.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.TrialInfo} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.TrialInfo.toObject = function(includeInstance, msg) {
  var f, obj = {
    minCount: jspb.Message.getFieldWithDefault(msg, 1, 0),
    maxCount: jspb.Message.getFieldWithDefault(msg, 2, 0),
    status: jspb.Message.getFieldWithDefault(msg, 3, ""),
    phase: jspb.Message.getFieldWithDefault(msg, 4, "")
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.TrialInfo}
 */
proto.TrialInfo.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.TrialInfo;
  return proto.TrialInfo.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.TrialInfo} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.TrialInfo}
 */
proto.TrialInfo.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setMinCount(value);
      break;
    case 2:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setMaxCount(value);
      break;
    case 3:
      var value = /** @type {string} */ (reader.readString());
      msg.setStatus(value);
      break;
    case 4:
      var value = /** @type {string} */ (reader.readString());
      msg.setPhase(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.TrialInfo.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.TrialInfo.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.TrialInfo} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.TrialInfo.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = /** @type {number} */ (jspb.Message.getField(message, 1));
  if (f != null) {
    writer.writeInt32(
      1,
      f
    );
  }
  f = /** @type {number} */ (jspb.Message.getField(message, 2));
  if (f != null) {
    writer.writeInt32(
      2,
      f
    );
  }
  f = /** @type {string} */ (jspb.Message.getField(message, 3));
  if (f != null) {
    writer.writeString(
      3,
      f
    );
  }
  f = /** @type {string} */ (jspb.Message.getField(message, 4));
  if (f != null) {
    writer.writeString(
      4,
      f
    );
  }
};


/**
 * optional int32 min_count = 1;
 * @return {number}
 */
proto.TrialInfo.prototype.getMinCount = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/**
 * @param {number} value
 * @return {!proto.TrialInfo} returns this
 */
proto.TrialInfo.prototype.setMinCount = function(value) {
  return jspb.Message.setField(this, 1, value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.TrialInfo} returns this
 */
proto.TrialInfo.prototype.clearMinCount = function() {
  return jspb.Message.setField(this, 1, undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.TrialInfo.prototype.hasMinCount = function() {
  return jspb.Message.getField(this, 1) != null;
};


/**
 * optional int32 max_count = 2;
 * @return {number}
 */
proto.TrialInfo.prototype.getMaxCount = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 2, 0));
};


/**
 * @param {number} value
 * @return {!proto.TrialInfo} returns this
 */
proto.TrialInfo.prototype.setMaxCount = function(value) {
  return jspb.Message.setField(this, 2, value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.TrialInfo} returns this
 */
proto.TrialInfo.prototype.clearMaxCount = function() {
  return jspb.Message.setField(this, 2, undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.TrialInfo.prototype.hasMaxCount = function() {
  return jspb.Message.getField(this, 2) != null;
};


/**
 * optional string status = 3;
 * @return {string}
 */
proto.TrialInfo.prototype.getStatus = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 3, ""));
};


/**
 * @param {string} value
 * @return {!proto.TrialInfo} returns this
 */
proto.TrialInfo.prototype.setStatus = function(value) {
  return jspb.Message.setField(this, 3, value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.TrialInfo} returns this
 */
proto.TrialInfo.prototype.clearStatus = function() {
  return jspb.Message.setField(this, 3, undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.TrialInfo.prototype.hasStatus = function() {
  return jspb.Message.getField(this, 3) != null;
};


/**
 * optional string phase = 4;
 * @return {string}
 */
proto.TrialInfo.prototype.getPhase = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 4, ""));
};


/**
 * @param {string} value
 * @return {!proto.TrialInfo} returns this
 */
proto.TrialInfo.prototype.setPhase = function(value) {
  return jspb.Message.setField(this, 4, value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.TrialInfo} returns this
 */
proto.TrialInfo.prototype.clearPhase = function() {
  return jspb.Message.setField(this, 4, undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.TrialInfo.prototype.hasPhase = function() {
  return jspb.Message.getField(this, 4) != null;
};


/**
 * @enum {number}
 */
proto.LanguageCode = {
  ENGLISH: 0,
  JAPANESE: 1,
  CHINESE: 2
};

goog.object.extend(exports, proto);
