// source: query_understanding_service.proto
/**
 * @fileoverview
 * @enhanceable
 * @suppress {missingRequire} reports error on implicit type usages.
 * @suppress {messageConventions} JS Compiler reports an error if a variable or
 *     field starts with 'MSG_' and isn't a translatable message.
 * @public
 */
// GENERATED CODE -- DO NOT EDIT!
/* eslint-disable */
// @ts-nocheck

var jspb = require('google-protobuf');
var goog = jspb;
var global = (function() {
  if (this) { return this; }
  if (typeof window !== 'undefined') { return window; }
  if (typeof global !== 'undefined') { return global; }
  if (typeof self !== 'undefined') { return self; }
  return Function('return this')();
}.call(null));

var synonym_pb = require('./synonym_pb.js');
goog.object.extend(proto, synonym_pb);
var query_intent_pb = require('./query_intent_pb.js');
goog.object.extend(proto, query_intent_pb);
goog.exportSymbol('proto.Entities', null, global);
goog.exportSymbol('proto.LanguageCode', null, global);
goog.exportSymbol('proto.Locations', null, global);
goog.exportSymbol('proto.NameTypes', null, global);
goog.exportSymbol('proto.QueryUnderstandingServiceRequest', null, global);
goog.exportSymbol('proto.QueryUnderstandingServiceRequestForIndications', null, global);
goog.exportSymbol('proto.QueryUnderstandingServiceResponse', null, global);
goog.exportSymbol('proto.QueryUnderstandingServiceResponseForIndications', null, global);
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.QueryUnderstandingServiceRequest = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.QueryUnderstandingServiceRequest, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.QueryUnderstandingServiceRequest.displayName = 'proto.QueryUnderstandingServiceRequest';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.QueryUnderstandingServiceRequestForIndications = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, proto.QueryUnderstandingServiceRequestForIndications.repeatedFields_, null);
};
goog.inherits(proto.QueryUnderstandingServiceRequestForIndications, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.QueryUnderstandingServiceRequestForIndications.displayName = 'proto.QueryUnderstandingServiceRequestForIndications';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.QueryUnderstandingServiceResponse = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, proto.QueryUnderstandingServiceResponse.repeatedFields_, null);
};
goog.inherits(proto.QueryUnderstandingServiceResponse, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.QueryUnderstandingServiceResponse.displayName = 'proto.QueryUnderstandingServiceResponse';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.QueryUnderstandingServiceResponseForIndications = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.QueryUnderstandingServiceResponseForIndications, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.QueryUnderstandingServiceResponseForIndications.displayName = 'proto.QueryUnderstandingServiceResponseForIndications';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.Entities = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.Entities, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.Entities.displayName = 'proto.Entities';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.NameTypes = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.NameTypes, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.NameTypes.displayName = 'proto.NameTypes';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.Locations = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.Locations, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.Locations.displayName = 'proto.Locations';
}



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.QueryUnderstandingServiceRequest.prototype.toObject = function(opt_includeInstance) {
  return proto.QueryUnderstandingServiceRequest.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.QueryUnderstandingServiceRequest} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.QueryUnderstandingServiceRequest.toObject = function(includeInstance, msg) {
  var f, obj = {
    query: jspb.Message.getFieldWithDefault(msg, 1, ""),
    languagecode: jspb.Message.getFieldWithDefault(msg, 2, 0)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.QueryUnderstandingServiceRequest}
 */
proto.QueryUnderstandingServiceRequest.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.QueryUnderstandingServiceRequest;
  return proto.QueryUnderstandingServiceRequest.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.QueryUnderstandingServiceRequest} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.QueryUnderstandingServiceRequest}
 */
proto.QueryUnderstandingServiceRequest.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setQuery(value);
      break;
    case 2:
      var value = /** @type {!proto.LanguageCode} */ (reader.readEnum());
      msg.setLanguagecode(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.QueryUnderstandingServiceRequest.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.QueryUnderstandingServiceRequest.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.QueryUnderstandingServiceRequest} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.QueryUnderstandingServiceRequest.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getQuery();
  if (f.length > 0) {
    writer.writeString(
      1,
      f
    );
  }
  f = message.getLanguagecode();
  if (f !== 0.0) {
    writer.writeEnum(
      2,
      f
    );
  }
};


/**
 * optional string query = 1;
 * @return {string}
 */
proto.QueryUnderstandingServiceRequest.prototype.getQuery = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/**
 * @param {string} value
 * @return {!proto.QueryUnderstandingServiceRequest} returns this
 */
proto.QueryUnderstandingServiceRequest.prototype.setQuery = function(value) {
  return jspb.Message.setProto3StringField(this, 1, value);
};


/**
 * optional LanguageCode languageCode = 2;
 * @return {!proto.LanguageCode}
 */
proto.QueryUnderstandingServiceRequest.prototype.getLanguagecode = function() {
  return /** @type {!proto.LanguageCode} */ (jspb.Message.getFieldWithDefault(this, 2, 0));
};


/**
 * @param {!proto.LanguageCode} value
 * @return {!proto.QueryUnderstandingServiceRequest} returns this
 */
proto.QueryUnderstandingServiceRequest.prototype.setLanguagecode = function(value) {
  return jspb.Message.setProto3EnumField(this, 2, value);
};



/**
 * List of repeated fields within this message type.
 * @private {!Array<number>}
 * @const
 */
proto.QueryUnderstandingServiceRequestForIndications.repeatedFields_ = [1];



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.QueryUnderstandingServiceRequestForIndications.prototype.toObject = function(opt_includeInstance) {
  return proto.QueryUnderstandingServiceRequestForIndications.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.QueryUnderstandingServiceRequestForIndications} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.QueryUnderstandingServiceRequestForIndications.toObject = function(includeInstance, msg) {
  var f, obj = {
    indicationsList: (f = jspb.Message.getRepeatedField(msg, 1)) == null ? undefined : f,
    languagecode: jspb.Message.getFieldWithDefault(msg, 2, 0),
    or: jspb.Message.getBooleanFieldWithDefault(msg, 3, false),
    country: jspb.Message.getFieldWithDefault(msg, 4, "")
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.QueryUnderstandingServiceRequestForIndications}
 */
proto.QueryUnderstandingServiceRequestForIndications.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.QueryUnderstandingServiceRequestForIndications;
  return proto.QueryUnderstandingServiceRequestForIndications.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.QueryUnderstandingServiceRequestForIndications} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.QueryUnderstandingServiceRequestForIndications}
 */
proto.QueryUnderstandingServiceRequestForIndications.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.addIndications(value);
      break;
    case 2:
      var value = /** @type {!proto.LanguageCode} */ (reader.readEnum());
      msg.setLanguagecode(value);
      break;
    case 3:
      var value = /** @type {boolean} */ (reader.readBool());
      msg.setOr(value);
      break;
    case 4:
      var value = /** @type {string} */ (reader.readString());
      msg.setCountry(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.QueryUnderstandingServiceRequestForIndications.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.QueryUnderstandingServiceRequestForIndications.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.QueryUnderstandingServiceRequestForIndications} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.QueryUnderstandingServiceRequestForIndications.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getIndicationsList();
  if (f.length > 0) {
    writer.writeRepeatedString(
      1,
      f
    );
  }
  f = message.getLanguagecode();
  if (f !== 0.0) {
    writer.writeEnum(
      2,
      f
    );
  }
  f = message.getOr();
  if (f) {
    writer.writeBool(
      3,
      f
    );
  }
  f = message.getCountry();
  if (f.length > 0) {
    writer.writeString(
      4,
      f
    );
  }
};


/**
 * repeated string indications = 1;
 * @return {!Array<string>}
 */
proto.QueryUnderstandingServiceRequestForIndications.prototype.getIndicationsList = function() {
  return /** @type {!Array<string>} */ (jspb.Message.getRepeatedField(this, 1));
};


/**
 * @param {!Array<string>} value
 * @return {!proto.QueryUnderstandingServiceRequestForIndications} returns this
 */
proto.QueryUnderstandingServiceRequestForIndications.prototype.setIndicationsList = function(value) {
  return jspb.Message.setField(this, 1, value || []);
};


/**
 * @param {string} value
 * @param {number=} opt_index
 * @return {!proto.QueryUnderstandingServiceRequestForIndications} returns this
 */
proto.QueryUnderstandingServiceRequestForIndications.prototype.addIndications = function(value, opt_index) {
  return jspb.Message.addToRepeatedField(this, 1, value, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.QueryUnderstandingServiceRequestForIndications} returns this
 */
proto.QueryUnderstandingServiceRequestForIndications.prototype.clearIndicationsList = function() {
  return this.setIndicationsList([]);
};


/**
 * optional LanguageCode languageCode = 2;
 * @return {!proto.LanguageCode}
 */
proto.QueryUnderstandingServiceRequestForIndications.prototype.getLanguagecode = function() {
  return /** @type {!proto.LanguageCode} */ (jspb.Message.getFieldWithDefault(this, 2, 0));
};


/**
 * @param {!proto.LanguageCode} value
 * @return {!proto.QueryUnderstandingServiceRequestForIndications} returns this
 */
proto.QueryUnderstandingServiceRequestForIndications.prototype.setLanguagecode = function(value) {
  return jspb.Message.setProto3EnumField(this, 2, value);
};


/**
 * optional bool or = 3;
 * @return {boolean}
 */
proto.QueryUnderstandingServiceRequestForIndications.prototype.getOr = function() {
  return /** @type {boolean} */ (jspb.Message.getBooleanFieldWithDefault(this, 3, false));
};


/**
 * @param {boolean} value
 * @return {!proto.QueryUnderstandingServiceRequestForIndications} returns this
 */
proto.QueryUnderstandingServiceRequestForIndications.prototype.setOr = function(value) {
  return jspb.Message.setProto3BooleanField(this, 3, value);
};


/**
 * optional string country = 4;
 * @return {string}
 */
proto.QueryUnderstandingServiceRequestForIndications.prototype.getCountry = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 4, ""));
};


/**
 * @param {string} value
 * @return {!proto.QueryUnderstandingServiceRequestForIndications} returns this
 */
proto.QueryUnderstandingServiceRequestForIndications.prototype.setCountry = function(value) {
  return jspb.Message.setProto3StringField(this, 4, value);
};



/**
 * List of repeated fields within this message type.
 * @private {!Array<number>}
 * @const
 */
proto.QueryUnderstandingServiceResponse.repeatedFields_ = [2,4,5];



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.QueryUnderstandingServiceResponse.prototype.toObject = function(opt_includeInstance) {
  return proto.QueryUnderstandingServiceResponse.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.QueryUnderstandingServiceResponse} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.QueryUnderstandingServiceResponse.toObject = function(includeInstance, msg) {
  var f, obj = {
    augmentedQuery: jspb.Message.getFieldWithDefault(msg, 1, ""),
    unigramSynonymList: jspb.Message.toObjectList(msg.getUnigramSynonymList(),
    synonym_pb.Synonym.toObject, includeInstance),
    queryIntent: (f = msg.getQueryIntent()) && query_intent_pb.QueryIntent.toObject(includeInstance, f),
    diagnosisCodesList: (f = jspb.Message.getRepeatedField(msg, 4)) == null ? undefined : f,
    procedureCodesList: (f = jspb.Message.getRepeatedField(msg, 5)) == null ? undefined : f,
    entities: (f = msg.getEntities()) && proto.Entities.toObject(includeInstance, f)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.QueryUnderstandingServiceResponse}
 */
proto.QueryUnderstandingServiceResponse.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.QueryUnderstandingServiceResponse;
  return proto.QueryUnderstandingServiceResponse.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.QueryUnderstandingServiceResponse} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.QueryUnderstandingServiceResponse}
 */
proto.QueryUnderstandingServiceResponse.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setAugmentedQuery(value);
      break;
    case 2:
      var value = new synonym_pb.Synonym;
      reader.readMessage(value,synonym_pb.Synonym.deserializeBinaryFromReader);
      msg.addUnigramSynonym(value);
      break;
    case 3:
      var value = new query_intent_pb.QueryIntent;
      reader.readMessage(value,query_intent_pb.QueryIntent.deserializeBinaryFromReader);
      msg.setQueryIntent(value);
      break;
    case 4:
      var value = /** @type {string} */ (reader.readString());
      msg.addDiagnosisCodes(value);
      break;
    case 5:
      var value = /** @type {string} */ (reader.readString());
      msg.addProcedureCodes(value);
      break;
    case 6:
      var value = new proto.Entities;
      reader.readMessage(value,proto.Entities.deserializeBinaryFromReader);
      msg.setEntities(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.QueryUnderstandingServiceResponse.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.QueryUnderstandingServiceResponse.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.QueryUnderstandingServiceResponse} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.QueryUnderstandingServiceResponse.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getAugmentedQuery();
  if (f.length > 0) {
    writer.writeString(
      1,
      f
    );
  }
  f = message.getUnigramSynonymList();
  if (f.length > 0) {
    writer.writeRepeatedMessage(
      2,
      f,
      synonym_pb.Synonym.serializeBinaryToWriter
    );
  }
  f = message.getQueryIntent();
  if (f != null) {
    writer.writeMessage(
      3,
      f,
      query_intent_pb.QueryIntent.serializeBinaryToWriter
    );
  }
  f = message.getDiagnosisCodesList();
  if (f.length > 0) {
    writer.writeRepeatedString(
      4,
      f
    );
  }
  f = message.getProcedureCodesList();
  if (f.length > 0) {
    writer.writeRepeatedString(
      5,
      f
    );
  }
  f = message.getEntities();
  if (f != null) {
    writer.writeMessage(
      6,
      f,
      proto.Entities.serializeBinaryToWriter
    );
  }
};


/**
 * optional string augmented_query = 1;
 * @return {string}
 */
proto.QueryUnderstandingServiceResponse.prototype.getAugmentedQuery = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/**
 * @param {string} value
 * @return {!proto.QueryUnderstandingServiceResponse} returns this
 */
proto.QueryUnderstandingServiceResponse.prototype.setAugmentedQuery = function(value) {
  return jspb.Message.setProto3StringField(this, 1, value);
};


/**
 * repeated Synonym unigram_synonym = 2;
 * @return {!Array<!proto.Synonym>}
 */
proto.QueryUnderstandingServiceResponse.prototype.getUnigramSynonymList = function() {
  return /** @type{!Array<!proto.Synonym>} */ (
    jspb.Message.getRepeatedWrapperField(this, synonym_pb.Synonym, 2));
};


/**
 * @param {!Array<!proto.Synonym>} value
 * @return {!proto.QueryUnderstandingServiceResponse} returns this
*/
proto.QueryUnderstandingServiceResponse.prototype.setUnigramSynonymList = function(value) {
  return jspb.Message.setRepeatedWrapperField(this, 2, value);
};


/**
 * @param {!proto.Synonym=} opt_value
 * @param {number=} opt_index
 * @return {!proto.Synonym}
 */
proto.QueryUnderstandingServiceResponse.prototype.addUnigramSynonym = function(opt_value, opt_index) {
  return jspb.Message.addToRepeatedWrapperField(this, 2, opt_value, proto.Synonym, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.QueryUnderstandingServiceResponse} returns this
 */
proto.QueryUnderstandingServiceResponse.prototype.clearUnigramSynonymList = function() {
  return this.setUnigramSynonymList([]);
};


/**
 * optional QueryIntent query_intent = 3;
 * @return {?proto.QueryIntent}
 */
proto.QueryUnderstandingServiceResponse.prototype.getQueryIntent = function() {
  return /** @type{?proto.QueryIntent} */ (
    jspb.Message.getWrapperField(this, query_intent_pb.QueryIntent, 3));
};


/**
 * @param {?proto.QueryIntent|undefined} value
 * @return {!proto.QueryUnderstandingServiceResponse} returns this
*/
proto.QueryUnderstandingServiceResponse.prototype.setQueryIntent = function(value) {
  return jspb.Message.setWrapperField(this, 3, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.QueryUnderstandingServiceResponse} returns this
 */
proto.QueryUnderstandingServiceResponse.prototype.clearQueryIntent = function() {
  return this.setQueryIntent(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.QueryUnderstandingServiceResponse.prototype.hasQueryIntent = function() {
  return jspb.Message.getField(this, 3) != null;
};


/**
 * repeated string diagnosis_codes = 4;
 * @return {!Array<string>}
 */
proto.QueryUnderstandingServiceResponse.prototype.getDiagnosisCodesList = function() {
  return /** @type {!Array<string>} */ (jspb.Message.getRepeatedField(this, 4));
};


/**
 * @param {!Array<string>} value
 * @return {!proto.QueryUnderstandingServiceResponse} returns this
 */
proto.QueryUnderstandingServiceResponse.prototype.setDiagnosisCodesList = function(value) {
  return jspb.Message.setField(this, 4, value || []);
};


/**
 * @param {string} value
 * @param {number=} opt_index
 * @return {!proto.QueryUnderstandingServiceResponse} returns this
 */
proto.QueryUnderstandingServiceResponse.prototype.addDiagnosisCodes = function(value, opt_index) {
  return jspb.Message.addToRepeatedField(this, 4, value, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.QueryUnderstandingServiceResponse} returns this
 */
proto.QueryUnderstandingServiceResponse.prototype.clearDiagnosisCodesList = function() {
  return this.setDiagnosisCodesList([]);
};


/**
 * repeated string procedure_codes = 5;
 * @return {!Array<string>}
 */
proto.QueryUnderstandingServiceResponse.prototype.getProcedureCodesList = function() {
  return /** @type {!Array<string>} */ (jspb.Message.getRepeatedField(this, 5));
};


/**
 * @param {!Array<string>} value
 * @return {!proto.QueryUnderstandingServiceResponse} returns this
 */
proto.QueryUnderstandingServiceResponse.prototype.setProcedureCodesList = function(value) {
  return jspb.Message.setField(this, 5, value || []);
};


/**
 * @param {string} value
 * @param {number=} opt_index
 * @return {!proto.QueryUnderstandingServiceResponse} returns this
 */
proto.QueryUnderstandingServiceResponse.prototype.addProcedureCodes = function(value, opt_index) {
  return jspb.Message.addToRepeatedField(this, 5, value, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.QueryUnderstandingServiceResponse} returns this
 */
proto.QueryUnderstandingServiceResponse.prototype.clearProcedureCodesList = function() {
  return this.setProcedureCodesList([]);
};


/**
 * optional Entities entities = 6;
 * @return {?proto.Entities}
 */
proto.QueryUnderstandingServiceResponse.prototype.getEntities = function() {
  return /** @type{?proto.Entities} */ (
    jspb.Message.getWrapperField(this, proto.Entities, 6));
};


/**
 * @param {?proto.Entities|undefined} value
 * @return {!proto.QueryUnderstandingServiceResponse} returns this
*/
proto.QueryUnderstandingServiceResponse.prototype.setEntities = function(value) {
  return jspb.Message.setWrapperField(this, 6, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.QueryUnderstandingServiceResponse} returns this
 */
proto.QueryUnderstandingServiceResponse.prototype.clearEntities = function() {
  return this.setEntities(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.QueryUnderstandingServiceResponse.prototype.hasEntities = function() {
  return jspb.Message.getField(this, 6) != null;
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.QueryUnderstandingServiceResponseForIndications.prototype.toObject = function(opt_includeInstance) {
  return proto.QueryUnderstandingServiceResponseForIndications.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.QueryUnderstandingServiceResponseForIndications} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.QueryUnderstandingServiceResponseForIndications.toObject = function(includeInstance, msg) {
  var f, obj = {
    indicationsParsedQuery: jspb.Message.getFieldWithDefault(msg, 1, ""),
    indicationsIcdCodesQuery: jspb.Message.getFieldWithDefault(msg, 2, "")
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.QueryUnderstandingServiceResponseForIndications}
 */
proto.QueryUnderstandingServiceResponseForIndications.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.QueryUnderstandingServiceResponseForIndications;
  return proto.QueryUnderstandingServiceResponseForIndications.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.QueryUnderstandingServiceResponseForIndications} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.QueryUnderstandingServiceResponseForIndications}
 */
proto.QueryUnderstandingServiceResponseForIndications.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setIndicationsParsedQuery(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setIndicationsIcdCodesQuery(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.QueryUnderstandingServiceResponseForIndications.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.QueryUnderstandingServiceResponseForIndications.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.QueryUnderstandingServiceResponseForIndications} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.QueryUnderstandingServiceResponseForIndications.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getIndicationsParsedQuery();
  if (f.length > 0) {
    writer.writeString(
      1,
      f
    );
  }
  f = message.getIndicationsIcdCodesQuery();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
};


/**
 * optional string indications_parsed_query = 1;
 * @return {string}
 */
proto.QueryUnderstandingServiceResponseForIndications.prototype.getIndicationsParsedQuery = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/**
 * @param {string} value
 * @return {!proto.QueryUnderstandingServiceResponseForIndications} returns this
 */
proto.QueryUnderstandingServiceResponseForIndications.prototype.setIndicationsParsedQuery = function(value) {
  return jspb.Message.setProto3StringField(this, 1, value);
};


/**
 * optional string indications_icd_codes_query = 2;
 * @return {string}
 */
proto.QueryUnderstandingServiceResponseForIndications.prototype.getIndicationsIcdCodesQuery = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/**
 * @param {string} value
 * @return {!proto.QueryUnderstandingServiceResponseForIndications} returns this
 */
proto.QueryUnderstandingServiceResponseForIndications.prototype.setIndicationsIcdCodesQuery = function(value) {
  return jspb.Message.setProto3StringField(this, 2, value);
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.Entities.prototype.toObject = function(opt_includeInstance) {
  return proto.Entities.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.Entities} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.Entities.toObject = function(includeInstance, msg) {
  var f, obj = {
    searchFor: jspb.Message.getFieldWithDefault(msg, 1, ""),
    indication: jspb.Message.getFieldWithDefault(msg, 2, ""),
    names: (f = msg.getNames()) && proto.NameTypes.toObject(includeInstance, f),
    rankBy: jspb.Message.getFieldWithDefault(msg, 4, ""),
    locations: (f = msg.getLocations()) && proto.Locations.toObject(includeInstance, f)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.Entities}
 */
proto.Entities.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.Entities;
  return proto.Entities.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.Entities} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.Entities}
 */
proto.Entities.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setSearchFor(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setIndication(value);
      break;
    case 3:
      var value = new proto.NameTypes;
      reader.readMessage(value,proto.NameTypes.deserializeBinaryFromReader);
      msg.setNames(value);
      break;
    case 4:
      var value = /** @type {string} */ (reader.readString());
      msg.setRankBy(value);
      break;
    case 5:
      var value = new proto.Locations;
      reader.readMessage(value,proto.Locations.deserializeBinaryFromReader);
      msg.setLocations(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.Entities.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.Entities.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.Entities} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.Entities.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getSearchFor();
  if (f.length > 0) {
    writer.writeString(
      1,
      f
    );
  }
  f = message.getIndication();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
  f = message.getNames();
  if (f != null) {
    writer.writeMessage(
      3,
      f,
      proto.NameTypes.serializeBinaryToWriter
    );
  }
  f = message.getRankBy();
  if (f.length > 0) {
    writer.writeString(
      4,
      f
    );
  }
  f = message.getLocations();
  if (f != null) {
    writer.writeMessage(
      5,
      f,
      proto.Locations.serializeBinaryToWriter
    );
  }
};


/**
 * optional string search_for = 1;
 * @return {string}
 */
proto.Entities.prototype.getSearchFor = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/**
 * @param {string} value
 * @return {!proto.Entities} returns this
 */
proto.Entities.prototype.setSearchFor = function(value) {
  return jspb.Message.setProto3StringField(this, 1, value);
};


/**
 * optional string indication = 2;
 * @return {string}
 */
proto.Entities.prototype.getIndication = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/**
 * @param {string} value
 * @return {!proto.Entities} returns this
 */
proto.Entities.prototype.setIndication = function(value) {
  return jspb.Message.setProto3StringField(this, 2, value);
};


/**
 * optional NameTypes names = 3;
 * @return {?proto.NameTypes}
 */
proto.Entities.prototype.getNames = function() {
  return /** @type{?proto.NameTypes} */ (
    jspb.Message.getWrapperField(this, proto.NameTypes, 3));
};


/**
 * @param {?proto.NameTypes|undefined} value
 * @return {!proto.Entities} returns this
*/
proto.Entities.prototype.setNames = function(value) {
  return jspb.Message.setWrapperField(this, 3, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.Entities} returns this
 */
proto.Entities.prototype.clearNames = function() {
  return this.setNames(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.Entities.prototype.hasNames = function() {
  return jspb.Message.getField(this, 3) != null;
};


/**
 * optional string rank_by = 4;
 * @return {string}
 */
proto.Entities.prototype.getRankBy = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 4, ""));
};


/**
 * @param {string} value
 * @return {!proto.Entities} returns this
 */
proto.Entities.prototype.setRankBy = function(value) {
  return jspb.Message.setProto3StringField(this, 4, value);
};


/**
 * optional Locations locations = 5;
 * @return {?proto.Locations}
 */
proto.Entities.prototype.getLocations = function() {
  return /** @type{?proto.Locations} */ (
    jspb.Message.getWrapperField(this, proto.Locations, 5));
};


/**
 * @param {?proto.Locations|undefined} value
 * @return {!proto.Entities} returns this
*/
proto.Entities.prototype.setLocations = function(value) {
  return jspb.Message.setWrapperField(this, 5, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.Entities} returns this
 */
proto.Entities.prototype.clearLocations = function() {
  return this.setLocations(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.Entities.prototype.hasLocations = function() {
  return jspb.Message.getField(this, 5) != null;
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.NameTypes.prototype.toObject = function(opt_includeInstance) {
  return proto.NameTypes.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.NameTypes} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.NameTypes.toObject = function(includeInstance, msg) {
  var f, obj = {
    people: jspb.Message.getFieldWithDefault(msg, 1, ""),
    institutions: jspb.Message.getFieldWithDefault(msg, 2, "")
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.NameTypes}
 */
proto.NameTypes.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.NameTypes;
  return proto.NameTypes.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.NameTypes} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.NameTypes}
 */
proto.NameTypes.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setPeople(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setInstitutions(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.NameTypes.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.NameTypes.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.NameTypes} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.NameTypes.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getPeople();
  if (f.length > 0) {
    writer.writeString(
      1,
      f
    );
  }
  f = message.getInstitutions();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
};


/**
 * optional string people = 1;
 * @return {string}
 */
proto.NameTypes.prototype.getPeople = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/**
 * @param {string} value
 * @return {!proto.NameTypes} returns this
 */
proto.NameTypes.prototype.setPeople = function(value) {
  return jspb.Message.setProto3StringField(this, 1, value);
};


/**
 * optional string institutions = 2;
 * @return {string}
 */
proto.NameTypes.prototype.getInstitutions = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/**
 * @param {string} value
 * @return {!proto.NameTypes} returns this
 */
proto.NameTypes.prototype.setInstitutions = function(value) {
  return jspb.Message.setProto3StringField(this, 2, value);
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.Locations.prototype.toObject = function(opt_includeInstance) {
  return proto.Locations.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.Locations} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.Locations.toObject = function(includeInstance, msg) {
  var f, obj = {
    country: jspb.Message.getFieldWithDefault(msg, 1, ""),
    state: jspb.Message.getFieldWithDefault(msg, 2, ""),
    city: jspb.Message.getFieldWithDefault(msg, 3, ""),
    zipcode: jspb.Message.getFieldWithDefault(msg, 4, "")
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.Locations}
 */
proto.Locations.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.Locations;
  return proto.Locations.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.Locations} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.Locations}
 */
proto.Locations.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setCountry(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setState(value);
      break;
    case 3:
      var value = /** @type {string} */ (reader.readString());
      msg.setCity(value);
      break;
    case 4:
      var value = /** @type {string} */ (reader.readString());
      msg.setZipcode(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.Locations.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.Locations.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.Locations} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.Locations.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getCountry();
  if (f.length > 0) {
    writer.writeString(
      1,
      f
    );
  }
  f = message.getState();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
  f = message.getCity();
  if (f.length > 0) {
    writer.writeString(
      3,
      f
    );
  }
  f = message.getZipcode();
  if (f.length > 0) {
    writer.writeString(
      4,
      f
    );
  }
};


/**
 * optional string country = 1;
 * @return {string}
 */
proto.Locations.prototype.getCountry = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/**
 * @param {string} value
 * @return {!proto.Locations} returns this
 */
proto.Locations.prototype.setCountry = function(value) {
  return jspb.Message.setProto3StringField(this, 1, value);
};


/**
 * optional string state = 2;
 * @return {string}
 */
proto.Locations.prototype.getState = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/**
 * @param {string} value
 * @return {!proto.Locations} returns this
 */
proto.Locations.prototype.setState = function(value) {
  return jspb.Message.setProto3StringField(this, 2, value);
};


/**
 * optional string city = 3;
 * @return {string}
 */
proto.Locations.prototype.getCity = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 3, ""));
};


/**
 * @param {string} value
 * @return {!proto.Locations} returns this
 */
proto.Locations.prototype.setCity = function(value) {
  return jspb.Message.setProto3StringField(this, 3, value);
};


/**
 * optional string zipcode = 4;
 * @return {string}
 */
proto.Locations.prototype.getZipcode = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 4, ""));
};


/**
 * @param {string} value
 * @return {!proto.Locations} returns this
 */
proto.Locations.prototype.setZipcode = function(value) {
  return jspb.Message.setProto3StringField(this, 4, value);
};


/**
 * @enum {number}
 */
proto.LanguageCode = {
  ENGLISH: 0,
  JAPANESE: 1,
  CHINESE: 2
};

goog.object.extend(exports, proto);
