{"name": "@h1nyc/search-sdk", "version": "1.26.6", "main": "dist/index.js", "files": ["dist"], "license": "UNLICENSED", "scripts": {"docs": "typedoc --readme none --out docs src", "build": "tsc", "build:dev": "tsc --watch", "prebuild": "yarn clean", "clean": "rimraf ./dist", "lint": "eslint . --ext .ts", "prepublishOnly": "yarn build", "prepack": "yarn build", "precommit": "lint-staged", "type-check": "tsc --noEmit --skipL<PERSON><PERSON><PERSON><PERSON>", "test": "LOG_LEVEL=error jest", "test:ci": "LOG_LEVEL=error JEST_JUNIT_OUTPUT_DIR=../../test-artifacts JEST_JUNIT_OUTPUT_NAME=sdk.xml jest --ci --reporters=default --reporters=jest-junit"}, "lint-staged": {"src/**/*.ts": ["eslint --fix"]}, "dependencies": {"@elastic/elasticsearch": "8.9.0", "@h1nyc/account-sdk": "^1.55.1", "@h1nyc/systems-rpc": "^0.9.1", "date-fns": "^2.29.2", "flat": "5.0.2", "grpc-tools": "^1.12.3", "ioredis": "^4.19.4", "lodash": "^4.17.21", "nice-grpc": "^2.1.0", "ts-proto": "^1.138.0"}, "devDependencies": {"@faker-js/faker": "^7.6.0", "@types/flat": "^5.0.1", "@types/ioredis": "^4.17.9", "@types/lodash": "4.14.191", "rimraf": "^4.0.7", "typedoc": "^0.24.1", "typedoc-plugin-markdown": "^3.4.5"}}