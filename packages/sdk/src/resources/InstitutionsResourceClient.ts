import { RpcResourceClient, RedisOptions } from "@h1nyc/systems-rpc";
import { InstitutionsResource } from "./InstitutionsResource";
import { RPC_NAMESPACE_INSTITUTIONS } from "../constants";
import {
  InstitutionsResponse,
  InstitutionsSearchInput,
  InstitutionFilterAggregation,
  InstitutionsAutocompleteInput,
  TrialsSearchByInstitutionResponse,
  TrialsSearchByInstitutionInput,
  IolClaimsInput,
  IolClaimsResponse,
  TaggedInstitutionsSearchInput,
  TaggedInstitutionsSearchResponse,
  InstitutionDiversityStatsInput,
  PatientDiversityStatsResponse,
  InstitutionLocationFilterAggregation
} from "../interfaces";
import { Entity } from "@h1nyc/account-sdk";

export class InstitutionsResourceClient
  extends RpcResourceClient
  implements InstitutionsResource
{
  constructor(signingSecret: string, redisOptions?: RedisOptions) {
    super(signingSecret, RPC_NAMESPACE_INSTITUTIONS, redisOptions);
  }

  async isReady(): Promise<boolean> {
    return true;
  }

  search(input: InstitutionsSearchInput): Promise<InstitutionsResponse> {
    return this.rpcClient.sendRequest<InstitutionsResponse>("search", [input]);
  }

  patientCount(input: InstitutionsSearchInput): Promise<number> {
    return this.rpcClient.sendRequest<number>("patientCount", [input]);
  }

  bulkInstitutionSearch(
    input: Readonly<InstitutionsSearchInput>
  ): Promise<Array<Entity>> {
    return this.rpcClient.sendRequest<Array<Entity>>("bulkInstitutionSearch", [
      input
    ]);
  }

  searchTrialsByInstitution(
    input: TrialsSearchByInstitutionInput
  ): Promise<TrialsSearchByInstitutionResponse> {
    return this.rpcClient.sendRequest<TrialsSearchByInstitutionResponse>(
      "searchTrialsByInstitution",
      [input]
    );
  }

  searchClaimsForInstitute(input: IolClaimsInput): Promise<IolClaimsResponse> {
    return this.rpcClient.sendRequest<IolClaimsResponse>(
      "searchClaimsForInstitute",
      [input]
    );
  }

  getTaggedInstitutions(
    input: TaggedInstitutionsSearchInput
  ): Promise<TaggedInstitutionsSearchResponse> {
    return this.rpcClient.sendRequest<TaggedInstitutionsSearchResponse>(
      "getTaggedInstitutions",
      [input]
    );
  }

  autocompleteCountries(
    input: InstitutionsAutocompleteInput
  ): Promise<InstitutionFilterAggregation[]> {
    return this.rpcClient.sendRequest<InstitutionLocationFilterAggregation[]>(
      "autocompleteCountries",
      [input]
    );
  }

  autocompleteRegions(
    input: InstitutionsAutocompleteInput
  ): Promise<InstitutionFilterAggregation[]> {
    return this.rpcClient.sendRequest<InstitutionLocationFilterAggregation[]>(
      "autocompleteRegions",
      [input]
    );
  }

  autocompleteCities(
    input: InstitutionsAutocompleteInput
  ): Promise<InstitutionFilterAggregation[]> {
    return this.rpcClient.sendRequest<InstitutionLocationFilterAggregation[]>(
      "autocompleteCities",
      [input]
    );
  }

  autocompletePostalCodes(
    input: InstitutionsAutocompleteInput
  ): Promise<InstitutionFilterAggregation[]> {
    return this.rpcClient.sendRequest<InstitutionLocationFilterAggregation[]>(
      "autocompletePostalCodes",
      [input]
    );
  }

  autocompleteTrialsSponsor(
    input: InstitutionsAutocompleteInput
  ): Promise<InstitutionFilterAggregation[]> {
    return this.rpcClient.sendRequest<InstitutionFilterAggregation[]>(
      "autocompleteTrialsSponsor",
      [input]
    );
  }

  autocompleteTrialsSponsorType(
    input: InstitutionsAutocompleteInput
  ): Promise<InstitutionFilterAggregation[]> {
    return this.rpcClient.sendRequest<InstitutionFilterAggregation[]>(
      "autocompleteTrialsSponsorType",
      [input]
    );
  }

  autocompleteTrialsStatus(
    input: InstitutionsAutocompleteInput
  ): Promise<InstitutionFilterAggregation[]> {
    return this.rpcClient.sendRequest<InstitutionFilterAggregation[]>(
      "autocompleteTrialsStatus",
      [input]
    );
  }

  autocompleteTrialsPhase(
    input: InstitutionsAutocompleteInput
  ): Promise<InstitutionFilterAggregation[]> {
    return this.rpcClient.sendRequest<InstitutionFilterAggregation[]>(
      "autocompleteTrialsPhase",
      [input]
    );
  }

  autocompleteTrialsStudyType(
    input: InstitutionsAutocompleteInput
  ): Promise<InstitutionFilterAggregation[]> {
    return this.rpcClient.sendRequest<InstitutionFilterAggregation[]>(
      "autocompleteTrialsStudyType",
      [input]
    );
  }

  autocompleteTrialsId(
    input: InstitutionsAutocompleteInput
  ): Promise<InstitutionFilterAggregation[]> {
    return this.rpcClient.sendRequest<InstitutionFilterAggregation[]>(
      "autocompleteTrialsId",
      [input]
    );
  }

  autocompleteTrialsBiomarker(
    input: InstitutionsAutocompleteInput
  ): Promise<InstitutionFilterAggregation[]> {
    return this.rpcClient.sendRequest<InstitutionFilterAggregation[]>(
      "autocompleteTrialsBiomarker",
      [input]
    );
  }

  autocompleteOrgTypes(
    input: InstitutionsAutocompleteInput
  ): Promise<InstitutionFilterAggregation[]> {
    return this.rpcClient.sendRequest<InstitutionFilterAggregation[]>(
      "autocompleteOrgTypes",
      [input]
    );
  }

  autocompleteDiagnoses(
    input: InstitutionsAutocompleteInput
  ): Promise<InstitutionFilterAggregation[]> {
    return this.rpcClient.sendRequest<InstitutionFilterAggregation[]>(
      "autocompleteDiagnoses",
      [input]
    );
  }

  autocompleteProcedures(
    input: InstitutionsAutocompleteInput
  ): Promise<InstitutionFilterAggregation[]> {
    return this.rpcClient.sendRequest<InstitutionFilterAggregation[]>(
      "autocompleteProcedures",
      [input]
    );
  }

  autocompleteGenericNames(
    input: InstitutionsAutocompleteInput
  ): Promise<InstitutionFilterAggregation[]> {
    return this.rpcClient.sendRequest<InstitutionFilterAggregation[]>(
      "autocompleteGenericNames",
      [input]
    );
  }

  autocompletePatientAgeRange(
    input: InstitutionsAutocompleteInput
  ): Promise<InstitutionFilterAggregation[]> {
    return this.rpcClient.sendRequest<InstitutionFilterAggregation[]>(
      "autocompletePatientAgeRange",
      [input]
    );
  }

  autocompletePatientRace(
    input: InstitutionsAutocompleteInput
  ): Promise<InstitutionFilterAggregation[]> {
    return this.rpcClient.sendRequest<InstitutionFilterAggregation[]>(
      "autocompletePatientRace",
      [input]
    );
  }

  autocompletePatientSex(
    input: InstitutionsAutocompleteInput
  ): Promise<InstitutionFilterAggregation[]> {
    return this.rpcClient.sendRequest<InstitutionFilterAggregation[]>(
      "autocompletePatientSex",
      [input]
    );
  }

  institutionDiversityStats(
    input: InstitutionDiversityStatsInput
  ): Promise<PatientDiversityStatsResponse> {
    return this.rpcClient.sendRequest<PatientDiversityStatsResponse>(
      "institutionDiversityStats",
      [input]
    );
  }

  autocompleteCcsr(
    input: InstitutionsAutocompleteInput
  ): Promise<InstitutionFilterAggregation[]> {
    return this.rpcClient.sendRequest<InstitutionFilterAggregation[]>(
      "autocompleteCcsr",
      [input]
    );
  }

  autocompleteCcsrPx(
    input: InstitutionsAutocompleteInput
  ): Promise<InstitutionFilterAggregation[]> {
    return this.rpcClient.sendRequest<InstitutionFilterAggregation[]>(
      "autocompleteCcsrPx",
      [input]
    );
  }

  autocompleteCcsrDiagnoses(
    input: InstitutionsAutocompleteInput
  ): Promise<InstitutionFilterAggregation[]> {
    return this.rpcClient.sendRequest<InstitutionFilterAggregation[]>(
      "autocompleteCcsrDiagnoses",
      [input]
    );
  }

  autocompleteCcsrProcedures(
    input: InstitutionsAutocompleteInput
  ): Promise<InstitutionFilterAggregation[]> {
    return this.rpcClient.sendRequest<InstitutionFilterAggregation[]>(
      "autocompleteCcsrProcedures",
      [input]
    );
  }
}
