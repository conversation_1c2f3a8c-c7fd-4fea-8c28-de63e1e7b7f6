import { RpcResourceClient } from "@h1nyc/systems-rpc";
import { RedisOptions } from "ioredis";
import { RPC_NAMESPACE_CLINICAL_TRIALS } from "../constants";
import {
  ClinicalTrialsSearchResponse,
  ClinicalTrialInput,
  ClinicalTrialDocument,
  ClinicalTrialDocumentV2,
  ClinicalTrialDocumentV3,
  ClinicalTrialDocumentV4,
  ClinicalTrialDocumentWithCTMS,
  ClinicalTrialFilterAutocompleteInput,
  KeywordFilterAggregation,
  IndicationsType,
  ClinicalTrialHistogramInput,
  ClinicalTrialHistogramResponse
} from "../interfaces";
import { ClinicalTrialSearchResource } from "./ClinicalTrialSearchResource";
import { Entity } from "@h1nyc/account-sdk";

export class ClinicalTrialSearchResourceClient
  extends RpcResourceClient
  implements ClinicalTrialSearchResource
{
  constructor(signingSecret: string, redisOptions?: RedisOptions) {
    super(signingSecret, RPC_NAMESPACE_CLINICAL_TRIALS, redisOptions);
  }

  async isReady(): Promise<boolean> {
    return this.rpcClient.sendRequest<boolean>("isReady", []);
  }

  async searchTrials(
    input: ClinicalTrialInput
  ): Promise<ClinicalTrialsSearchResponse> {
    return this.rpcClient.sendRequest<ClinicalTrialsSearchResponse>(
      "searchTrials",
      [input]
    );
  }

  async trialCountForIndications(
    searchInput: Readonly<ClinicalTrialInput>,
    indications: string[],
    type: IndicationsType
  ): Promise<_.Dictionary<number>> {
    return this.rpcClient.sendRequest<_.Dictionary<number>>(
      "trialCountForIndications",
      [searchInput, indications, type]
    );
  }

  async searchBulkTrials(input: ClinicalTrialInput): Promise<Array<Entity>> {
    return this.rpcClient.sendRequest<Array<Entity>>("searchBulkTrials", [
      input
    ]);
  }

  async autocompleteForTrialFilters(
    input: ClinicalTrialFilterAutocompleteInput
  ): Promise<Array<KeywordFilterAggregation>> {
    return this.rpcClient.sendRequest<Array<KeywordFilterAggregation>>(
      "autocompleteForTrialFilters",
      [input]
    );
  }

  async trialDocument(trialId: string): Promise<ClinicalTrialDocument | null> {
    console.log(`calling RPC with trial ${trialId}`);
    return this.rpcClient.sendRequest<ClinicalTrialDocument | null>(
      "trialDocument",
      [trialId]
    );
  }

  async trialDocumentV2(
    nctId: string
  ): Promise<ClinicalTrialDocumentV2 | null> {
    return this.rpcClient.sendRequest<ClinicalTrialDocumentV2 | null>(
      "trialDocumentV2",
      [nctId]
    );
  }

  async trialDocumentV3(
    nctId: string
  ): Promise<ClinicalTrialDocumentV3 | null> {
    return this.rpcClient.sendRequest<ClinicalTrialDocumentV3 | null>(
      "trialDocumentV3",
      [nctId]
    );
  }

  async trialDocumentV4(
    nctId: string
  ): Promise<ClinicalTrialDocumentV4 | null> {
    return this.rpcClient.sendRequest<ClinicalTrialDocumentV4 | null>(
      "trialDocumentV4",
      [nctId]
    );
  }

  async trialDocumentWithCTMS(
    trialId: string
  ): Promise<ClinicalTrialDocumentWithCTMS | null> {
    return this.rpcClient.sendRequest<ClinicalTrialDocumentWithCTMS | null>(
      "trialDocumentWithCTMS",
      [trialId]
    );
  }

  /**
   * Generate histogram data for trial metrics
   */
  async generateTrialHistogram(
    input: ClinicalTrialHistogramInput
  ): Promise<ClinicalTrialHistogramResponse> {
    return this.rpcClient.sendRequest<ClinicalTrialHistogramResponse>(
      "generateTrialHistogram",
      [input]
    );
  }
}
