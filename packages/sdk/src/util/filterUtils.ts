import {
  DefaultScoreWeights,
  FilterInterface,
  FilterSearchInterface
} from "../interfaces";

const getEmptyFilters = (): FilterInterface => {
  return {
    dateRangePicker: {
      min: 0,
      max: null,
      active: false
    },
    designations: {
      values: []
    },
    npi: {
      values: []
    },
    /**
     * @unused
     */
    institutionType: {
      values: []
    },
    specialty: {
      values: []
    },
    country: {
      values: []
    },
    state: {
      values: []
    },
    city: {
      values: []
    },
    zipCode: {
      values: []
    },
    institution: {
      values: []
    },
    presentWorkInstitutions: {
      values: []
    },
    pastAndPresentWorkInstitutions: {
      values: []
    },
    studentInstitutions: {
      values: []
    },
    graduationYearRange: {
      min: null,
      max: null
    },
    publications: {
      minCount: {
        value: null
      },
      socialMediaMinCount: {
        value: null
      },
      journal: {
        values: []
      },
      type: {
        values: []
      }
    },
    tags: {
      name: {
        values: []
      },
      publicTagIds: [],
      privateTagIds: [],
      programmaticTagIds: []
    },
    claims: {
      diagnosesICDMinCount: {
        value: null
      },
      diagnosesICD: {
        values: []
      },
      proceduresCPTMinCount: {
        value: null
      },
      proceduresCPT: {
        values: []
      },
      proceduresHCPCMinCount: {
        value: null
      },
      proceduresHCPC: {
        values: []
      },
      genericNames: {
        values: []
      },
      brandNames: {
        values: []
      },
      drugClasses: {
        values: []
      },
      prescriptionsMinCount: {
        value: null
      },
      timeFrame: {
        value: null
      }
    },
    trials: {
      minCount: {
        value: null
      },
      maxCount: {
        value: null
      },
      status: {
        values: []
      },
      phase: {
        values: []
      },
      studyType: {
        values: []
      },
      sponsor: {
        values: []
      },
      sponsorType: {
        values: []
      },
      id: {
        values: []
      },
      timeFrame: {
        min: 0,
        max: null,
        key: "max"
      },
      /**
       * @unused
       */
      funderType: {
        values: []
      },
      biomarkers: {
        values: []
      }
    },
    congresses: {
      minCount: {
        value: null
      },
      name: {
        values: []
      },
      /**
       * @unused
       */
      type: {
        values: []
      },
      organizerName: {
        values: []
      },
      /**
       * @unused
       */
      sessionType: {
        values: []
      }
    },
    engagements: {
      minCount: {
        value: null
      }
    },
    /**
     * @unused
     */
    grants: {
      minAmount: {
        value: null
      },
      funder: {
        values: []
      }
    },
    payments: {
      minAmount: {
        value: null
      },
      company: {
        values: []
      },
      drugOrDevice: {
        values: []
      },
      fundingType: {
        values: []
      },
      category: {
        values: []
      }
    },
    referrals: {
      serviceLine: {
        values: []
      },
      minReferralsReceived: {
        value: null
      },
      minReferralsSent: {
        value: null
      }
    },
    patientsDiversity: {
      ageRange: {
        values: []
      },
      sex: {
        values: []
      },
      race: {
        values: []
      },
      raceMix: {
        thresholdValue: []
      }
    },
    providerDiversity: {
      languagesSpoken: {
        values: []
      },
      sex: {
        values: []
      },
      race: {
        values: []
      }
    },
    hasLinkedin: {
      value: null
    },
    hasTwitter: {
      value: null
    },
    hasCTMSData: {
      value: null
    }
  };
};

type TransformSearchInputFunc = (projectId: string) => FilterSearchInterface;

export const createEmptyFilters: TransformSearchInputFunc = (projectId) => {
  const defaultSort = DefaultScoreWeights;
  const sortBy = defaultSort as any;
  const filters = getEmptyFilters();

  const res = {
    userId: "",
    projectId,
    query: [],
    sortBy,
    pageSize: 10000,
    from: 0,
    filters,
    activeFilters: {
      people: false,
      trials: false,
      pubs: false,
      payments: false,
      congress: false,
      tags: false,
      engagements: false,
      claims: false,
      referrals: false,
      patientsDiversity: false,
      providerDiversity: false
    }
  };

  return res;
};
