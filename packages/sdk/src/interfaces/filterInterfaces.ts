import {
  GeoBounds,
  GeoLocation,
  LatLonGeoLocation
} from "@elastic/elasticsearch/lib/api/types";
import { InclusionExclusionRule, RuleOrRuleGroup } from "./rulesAndGroup";

/**
 * Unfortunately, this a numeric enum; as such, new values must be appended
 * to the end of the list. Otherwise, things WILL break things and you (or someone else)
 * will spend way too much time trying to figure out why search isn't working properly.
 */
export enum FilterTypesEnum {
  clinicalCurrentPhase,
  clinicalPastStages,
  clinicalCurrentStatus,
  clinicalPastStatus,
  clinicalStudyStartDate,
  clinicalPrimaryCompletionDate,
  clinicalFunderType,
  clinincalSponsor,
  institution,
  institutionType,
  therapeuticArea,
  locationRegion,
  locationCity,
  title,
  paymentNature,
  paymentCompany,
  paymentSize,
  paymentDate,
  paymentMinAmount,
  paymentDrugOrDevice,
  publicationYear,
  publicationJournal,
  publicationPmid,
  publicationType,
  publicationCitationCount,
  publicationsSocialMediaCount,
  publicationPublisher,
  clinicalStudyInterventional,
  clinicalStudyObservational,
  clinicalPrinicipleInvestigator,
  locationState,
  publicationPublisherType,
  publicationMinCount,
  locationCountry,
  kolType,
  trialMinCount,
  trialPhase,
  trialStatus,
  trialStudyType,
  trialFunderType,
  trialSponsor,
  congressMinCount,
  congressName,
  congressType,
  congressOrganizerName,
  congressSessionType,
  grantMinAmount,
  grantFunder,
  tagName,
  npi,
  diagnosesICD,
  proceduresCPT,
  proceduresHCPC,
  diagnosesICDMinCount,
  proceduresCPTMinCount,
  proceduresHCPCMinCount,
  locationZipCode,
  presentWorkInstitutions,
  pastAndPresentWorkInstitutions,
  studentInstitutions,
  referralsServiceLine,
  minReferralsReceived,
  minReferralsSent,
  engagementMinCount,
  // TODO: Refactor when legacy claims are removed
  DrgDiagnosesICD,
  DrgProceduresCPT,
  DrgProceduresHCPC,
  DrgDiagnosesICDMinCount,
  DrgProceduresCPTMinCount,
  DrgProceduresHCPCMinCount,
  designations,
  patientsDiversityAgeRange,
  diversityRace,
  diversitySex,
  providerDiversityLanguages,
  providerDiversityRace,
  patientsDiversityRace,
  trialSponsorType,
  hasLinkedin,
  hasTwitter,
  providerDiversitySex,
  trialTimeFrame,
  trialMaxCount,
  patientsDiversityRaceMix,
  hasCTMSData,
  claimsTimeFrame,
  precision,
  geoBoundingBox,
  digitalLeader,
  getMatchedClaims,
  geoDistance,
  geoShape,
  DrgDiagnosesICDMaxCount,
  DrgProceduresCPTMaxCount,
  congressMaxCount,
  paymentMaxAmount,
  publicationMaxCount,
  publicationsSocialMediaMaxCount,
  referralsReceivedMaxCount,
  referralsSentMaxCount,
  geoStatsRegionLevel,
  zoom,
  geoHashGrids,
  isFacultyOpinionsMember,
  indications
}

export interface FilterInterface {
  specialty: ValuesFilter;
  specialtyExclusion?: ValuesFilter;
  country: ValuesFilter;
  excludeCountry?: ValuesFilter;
  state: ValuesFilter;
  excludeState?: ValuesFilter;
  city: ValuesFilter;
  excludeCity?: ValuesFilter;
  zipCode: ValuesFilter;
  excludeZipCode?: ValuesFilter;
  institution: ValuesFilter;
  presentWorkInstitutions: ValuesFilter;
  presentWorkInstitutionsExclusion?: ValuesFilter;
  pastAndPresentWorkInstitutions: ValuesFilter;
  studentInstitutions: ValuesFilter;
  studentInstitutionsExclusion?: ValuesFilter;
  graduationYearRange: YearRangeFilter;
  institutionType: ValuesFilter;
  dateRangePicker: DateRangeFilter;
  npi: ValuesFilter;
  tags: {
    /**
     * @deprecated
     */
    name?: ValuesFilter;
    privateTagIds?: string[] | null;
    publicTagIds?: string[] | null;
    programmaticTagIds?: string[] | null;
  };
  intersectTags?: NullableValueFilter<boolean> | null;
  exclusionTags?: {
    privateTagIds?: string[] | null;
    publicTagIds?: string[] | null;
    programmaticTagIds?: string[] | null;
  };
  publications: {
    socialMediaMinCount: NullableValueFilter<number>;
    socialMediaMaxCount?: NullableValueFilter<number>;
    minCount: NullableValueFilter<number>;
    maxCount?: NullableValueFilter<number>;
    journal: ValuesFilter;
    type: ValuesFilter;
    isFirstOrder?: NullableValueFilter<boolean>;
    isLastOrder?: NullableValueFilter<boolean>;
    publicationDate?: NullableValueFilter<PastDateRangeEnumFilter>;
  };
  publicationsExclusion?: {
    journal: ValuesFilter;
    type: ValuesFilter;
  };
  trials: {
    minCount: NullableValueFilter<number>;
    maxCount?: NullableValueFilter<number>;
    status: ValuesFilter;
    phase: ValuesFilter;
    studyType: ValuesFilter;
    funderType: ValuesFilter;
    sponsor: ValuesFilter;
    sponsorType: ValuesFilter;
    id: ValuesFilter;
    timeFrame?: DateRangeRadioFilter;
    roles?: ValuesFilter;
    biomarkers: ValuesFilter;
  };
  trialsExclusion?: {
    status: ValuesFilter;
    phase: ValuesFilter;
    studyType: ValuesFilter;
    sponsor: ValuesFilter;
    sponsorType: ValuesFilter;
  };
  congresses: {
    minCount: NullableValueFilter<number>;
    maxCount?: NullableValueFilter<number>;
    name: ValuesFilter;
    type: ValuesFilter;
    organizerName: ValuesFilter;
    sessionType: ValuesFilter;
    timeFrame?: NullableValueFilter<PastDateRangeEnumFilter>;
    sessionTimeFrame?: NullableValueFilter<{
      min: number;
      max?: number | null;
    }>;
    contributorRole?: ValuesFilter;
  };
  congressesExclusion?: {
    name: ValuesFilter;
    organizerName: ValuesFilter;
    contributorRole?: ValuesFilter;
  };
  claims: {
    diagnosesICD: ValuesFilter;
    diagnosesICDMinCount: NullableValueFilter<number>;
    diagnosesICDMaxCount?: NullableValueFilter<number>;
    proceduresCPT: ValuesFilter;
    proceduresCPTMinCount: NullableValueFilter<number>;
    proceduresCPTMaxCount?: NullableValueFilter<number>;
    proceduresHCPC: ValuesFilter;
    proceduresHCPCMinCount: NullableValueFilter<number>;
    genericNames: ValuesFilter;
    brandNames: ValuesFilter;
    drugClasses: ValuesFilter;
    brandNameOrGeneric: BrandNameOrGenericValuesFilter;
    prescriptionsMinCount: NullableValueFilter<number>;
    ccsr?: ValuesFilter;
    ccsrPx?: ValuesFilter;
    prescriptionsMaxCount?: NullableValueFilter<number>;
    timeFrame?: NullableValueFilter<number>;
    prescriptionsTimeFrame?: NullableValueFilter<number>;
    showUniquePatients?: NullableValueFilter<boolean>;
  };
  exclusionClaims?: {
    diagnosesICD: ValuesFilter;
    proceduresCPT: ValuesFilter;
    proceduresHCPC: ValuesFilter;
    genericNames: ValuesFilter;
    brandNames: ValuesFilter;
    drugClasses: ValuesFilter;
    ccsr?: ValuesFilter;
    ccsrPx?: ValuesFilter;
  };
  grants: {
    minAmount: NullableValueFilter<number>;
    funder: ValuesFilter;
  };
  payments: {
    minAmount: NullableValueFilter<number>;
    maxAmount?: NullableValueFilter<number>;
    company: ValuesFilter;
    drugOrDevice: ValuesFilter;
    fundingType: ValuesFilter;
    category?: ValuesFilter;
  };
  paymentsExclusion?: {
    company: ValuesFilter;
    drugOrDevice: ValuesFilter;
    fundingType: ValuesFilter;
    category?: ValuesFilter;
  };
  referrals: {
    serviceLine: ValuesFilter;
    minReferralsReceived: NullableValueFilter<number>;
    maxReferralsReceived?: NullableValueFilter<number>;
    minReferralsSent: NullableValueFilter<number>;
    maxReferralsSent?: NullableValueFilter<number>;
  };
  referralsExclusion?: {
    serviceLine: ValuesFilter;
  };
  engagements?: {
    minCount: NullableValueFilter<number>;
  };
  designations?: ValuesFilter;
  designationsExclusion?: ValuesFilter;
  patientsDiversity: {
    race: ValuesFilter;
    ageRange: ValuesFilter;
    sex: ValuesFilter;
    raceMix: RaceThresholdFilter;
  };
  patientsDiversityExclusion?: {
    race: ValuesFilter;
    ageRange: ValuesFilter;
    sex: ValuesFilter;
  };
  providerDiversity: {
    languagesSpoken: ValuesFilter;
    sex: ValuesFilter;
    race: ValuesFilter;
  };
  providerDiversityExclusion?: {
    languagesSpoken: ValuesFilter;
    sex: ValuesFilter;
    race: ValuesFilter;
  };
  hasLinkedin: NullableValueFilter<boolean>;
  hasTwitter: NullableValueFilter<boolean>;
  hasCTMSData?: NullableValueFilter<boolean> | null;
  isFacultyOpinionsMember?: NullableValueFilter<boolean> | null;
  precision?: NullableValueFilter<number>;
  geoBoundingBox?: NullableValueFilter<GeoBounds>;
  digitalLeader?: NullableValueFilter<string>;
  getMatchedClaims?: NullableValueFilter<boolean>;
  geoDistance?: NullableValueFilter<GeoDistance>;
  geoShape?: NullableValueFilter<GeoShape>;
  geoStatsRegionLevel?: NullableValueFilter<GeoChoroplethFilter>;
  geoHashGrids?: NullableValueFilter<Array<string>>;
  zoom?: NullableValueFilter<number>;
  computeResultGeoClusters?: NullableValueFilter<boolean> | null;
  peopleIds?: string[];
  h1dnIds?: string[];
  indications?: ValuesFilter;
  suggestedL2Indications?: ValuesFilter;
  indicationsSpecialtyORed?: NullableValueFilter<boolean> | null;
  patientClaimsFilter?: RuleOrRuleGroup;
  patientClaimsFilterV2?: InclusionExclusionRule;
  societyAffiliations?: ValuesFilter;
  hasSocietyAffiliation?: NullableValueFilter<boolean>;
  isInactive?: NullableValueFilter<boolean>;
  isIndustry?: NullableValueFilter<boolean>;
  territory?: ValuesFilter;
  isGlobalLeader?: NullableValueFilter<boolean>;
  isNationalLeader?: NullableValueFilter<boolean>;
  isRegionalLeader?: NullableValueFilter<boolean>;
  isLocalLeader?: NullableValueFilter<boolean>;
  isPublicationLeader?: NullableValueFilter<boolean>;
  isEducatorLeader?: NullableValueFilter<boolean>;
  isScholarLeader?: NullableValueFilter<boolean>;
  isTrialLeader?: NullableValueFilter<boolean>;
  isDOLeader?: NullableValueFilter<boolean>;
  careerLength?: NullableValueFilter<CareerLengthEnumFilter>;
  isRisingStar?: NullableValueFilter<boolean>;
  trialEnrollmentRate?: {
    min?: number;
    max?: number;
  } | null;
}

export interface WeightedSortBy {
  publication: number;
  citation: number;
  microBloggingCount: number;
  trial: number;
  payment: number;
  congress: number;
  grant: number;
  diagnoses: number;
  procedures: number;
  prescriptions: number;
  referralsReceived: number;
  referralsSent: number;
  patientsDiversityRank: number;
  h1DefaultRank: number;
  twitterFollowersCount?: number;
  twitterTweetCount?: number;
  age?: number;
  trialPerformance?: number;
  /**
   * @deprecated This is deprecated and the value is not used
   */
  digitalRelevance?: number;
  congressContributerRank?: number;
}

export type DateRangeFilter = {
  min: number;
  max: number | null;
  active: boolean | null;
};
type YearRangeFilter = {
  min: number | null;
  max: number | null;
};

type RaceMinMaxThresholdFilter = {
  key: string;
  min: number | undefined;
  max: number | undefined;
};
/**
 * The weights of document types
 *
 * @TODO: grants
 */
export const DefaultScoreWeights: WeightedSortBy = {
  publication: 0.3,
  citation: 0.1,
  trial: 0.3,
  payment: 0.1,
  congress: 0.2,
  grant: 0.1,
  microBloggingCount: 0.2,
  diagnoses: 0.1,
  procedures: 0.2,
  prescriptions: 0.2,
  referralsReceived: 0.1,
  referralsSent: 0.1,
  patientsDiversityRank: 0.1,
  h1DefaultRank: 0.1,
  twitterFollowersCount: 0,
  twitterTweetCount: 0,
  digitalRelevance: 0,
  age: 0
};

//Zero weights sort when leaderboard sorts are applied
export const ZERO_WEIGHTS_SORT_BY: WeightedSortBy = {
  publication: 0,
  citation: 0,
  trial: 0,
  payment: 0,
  congress: 0,
  grant: 0,
  microBloggingCount: 0,
  diagnoses: 0,
  procedures: 0,
  prescriptions: 0,
  referralsReceived: 0,
  referralsSent: 0,
  patientsDiversityRank: 0,
  h1DefaultRank: 0,
  twitterFollowersCount: 0,
  twitterTweetCount: 0,
  digitalRelevance: 0,
  age: 0
};
/**
 * An enum for scoring/sorting functions which may be used when searching for HCPs
 */
export enum HCPSearchScoringFunctions {
  DigitalRelevance = "DigitalRelevance"
}

export interface FilterSearchInterface {
  userId: string;
  query: string[];
  projectId: string;
  filters: FilterInterface;
  pageSize: number;
  from: number;
  /**
   * @deprecated
   */
  pageNum?: number;
  sortBy: WeightedSortBy;
  activeFilters: ActiveFilterBreakDown;

  /**
   * The sorting/scoring function
   */
  scoringFunction?: HCPSearchScoringFunctions | null;
}

interface ActiveFilterBreakDown {
  people: boolean;
  trials: boolean;
  pubs: boolean;
  payments: boolean;
  congress: boolean;
  tags: boolean;
  claims: boolean;
  referrals: boolean;
  patientsDiversity: boolean;
  providerDiversity: boolean;
}

export interface GenericSearchResultInterface {
  id: string;
  type: number;
  internalCount?: number;
}

type ValuesFilter<T = string> = {
  values: Array<T>;
};

export type BrandNameOrGeneric = { name: string; brandName: boolean };

export type BrandNameOrGenericValuesFilter = {
  values: BrandNameOrGeneric[];
};

export type NullableValueFilter<T> = {
  value: T | null;
};

type DateRangeRadioFilter = {
  min: number | null;
  max: number | null;
  key: string | null;
};

export enum PastDateRangeEnumFilter {
  Max = "Max",
  OneYear = "OneYear",
  TwoYear = "TwoYear",
  ThreeYear = "ThreeYear",
  FourYear = "FourYear",
  FiveYear = "FiveYear",
  TenYear = "TenYear"
}

export enum CareerLengthEnumFilter {
  UnderTen = "UnderTen",
  TenToThirty = "TenToThirty",
  OverThirty = "OverThirty"
}

export interface RaceThresholdFilter {
  thresholdValue: RaceMinMaxThresholdFilter[];
}

export interface GeoDistance {
  distance: number;
  distanceUnit: GeoDistanceUnitTypes;
  geoPosition: GeoLocation;
}
export interface GeoShape {
  type: GeoShapeTypes;
  coordinates?: LatLonGeoLocation[] | null;
}

export interface GeoChoroplethFilter {
  claims?: {
    zoomLevel: GeoChoroplethLevelTypes;
  };
  franceClaims?: {
    /**
     * @deprecated Use heatMapZoomLevel in the root input filters object instead
     */
    zoomLevel: FranceGeoChoroplethLevelTypes;
  };
}
/**
 * TextFilter or CheckboxFilter options
 * i.e. anything that has options that are to be selected
 */

export type SearchFilterValue =
  | { [name: string]: SearchFilterValue }
  | string[]
  | number
  | number[]
  | null
  | boolean
  | string
  | undefined;

export interface SavedSearchFilterValues {
  [name: string]: SearchFilterValue;

  specialty?: string[] | null;
  country?: string[] | null;
  state?: string[] | null;
  city?: string[] | null;
  zipCode?: string[] | null;
  institution?: string[] | null;
  presentWorkInstitutions?: string[] | null;
  pastAndPresentWorkInstitutions?: string[] | null;
  studentInstitutions?: string[] | null;
  graduationYearRange?: {
    min?: number | null;
    max?: number | null;
  } | null;
  dateRange?: number[] | null;
  npi?: string[] | null;
  societyAffiliations?: string[] | null;
  hasSocietyAffiliation?: boolean | null;
  isInactive?: boolean | null;
  isIndustry?: boolean | null;
  tags?: {
    privateTagIds?: string[] | null;
    publicTagIds?: string[] | null;
    programmaticTagIds?: string[] | null;
  } | null;
  publications?: {
    socialMediaMinCount?: number | null;
    minCount?: number | null;
    journal?: string[] | null;
    type?: string[] | null;
    isFirstOrder?: boolean | null;
    isLastOrder?: boolean | null;
    publicationDate?: PastDateRangeEnumFilter | null;
  } | null;
  trials?: {
    minCount?: number | null;
    maxCount?: number | null;
    status?: string[] | null;
    phase?: string[] | null;
    studyType?: string[] | null;
    sponsor?: string[] | null;
    sponsorType?: string[] | null;
    timeFrame?: {
      min: number | null;
      max: number | null;
      key: string | null;
    } | null;
  } | null;
  congresses?: {
    minCount?: number | null;
    name?: string[] | null;
    organizerName?: string[] | null;
    timeFrame?: PastDateRangeEnumFilter | null;
  } | null;
  claims?: {
    diagnoses?: string[] | null;
    diagnosesMinCount?: number | null;
    procedures?: string[] | null;
    proceduresMinCount?: number | null;
    timeFrame?: number | null;
  } | null;
  grants?: {
    minAmount?: number | null;
    funder?: string[] | null;
  } | null;
  payments?: {
    minAmount?: number | null;
    company?: string[] | null;
    drugOrDevice?: string[] | null;
    fundingType?: string[] | null;
    category?: string[] | null;
  } | null;
  referrals?: {
    serviceLine?: string[] | null;
    minReferralsReceived?: number | null;
    minReferralsSent?: number | null;
  } | null;
  engagements?: {
    minCount?: number | null;
  } | null;
  designations?: string[] | null;
  patientsDiversity?: {
    race: string[] | null;
    ageRange: string[] | null;
    sex: string[] | null;
  };
  providerDiversity?: {
    languagesSpoken: string[] | null;
    sex: string[] | null;
    race: string[] | null;
  };
  indications?: string[] | null;
  hasLinkedin?: boolean | null;
  hasTwitter?: boolean | null;
  hasCTMSData?: boolean | null;
  trialEnrollmentRate?: {
    max?: number;
    min?: number;
  } | null;
}

export enum SearchTypes {
  KEYWORD = "Keyword",
  NAME = "Name",
  INSTITUTION = "Institution"
}
export enum SearchSliceOptionsEnum {
  InsideSlice = "InsideSlice",
  OutsideSlice = "OutsideSlice",
  FullCorpus = "FullCorpus"
}
export enum DigitalLeaderTypes {
  TopOne = "TopOne",
  TopTen = "TopTen",
  All = "All"
}
export interface SearchFilterBuilderInterface {
  aggs: any;
  filter: any;
}

export enum GeoDistanceUnitTypes {
  Miles = "mi",
  Kilometer = "km"
}

export enum GeoShapeTypes {
  Polygon = "Polygon"
}

export enum GeoChoroplethLevelTypes {
  Country = "country",
  Region = "region",
  County = "county",
  District = "district"
}

export enum FranceGeoChoroplethLevelTypes {
  Region = "region",
  Department = "department",
  Commune = "commune"
}

export interface HighlightCountPerAsset {
  trials: number;
  congresses: number;
  publications: number;
}

export interface ExtractedEntityNames {
  people: string;
  institutions: string;
}

export interface ExtractedEntityLocations {
  country: string;
  state: string;
  city: string;
  zipcode: string;
}

export interface ExtractedEntities {
  searchFor: string;
  indication: string;
  names?: ExtractedEntityNames;
  rankBy: string;
  locations?: ExtractedEntityLocations;
}

export interface ExtractedEntitiesV2 {
  search_for: "people" | "institution" | "";
  indication: string[];

  locations: {
    country: string[]; //addressForHCPU.country
    state: string[];  // addressForHCPU.region
    city: string[]; // addressForHCPU.city
  };

  claims: {
    diagnoses: string[]; //DRG_diagnoses.codeAndDescription_eng
    min_diagnoses_claim_count: number | null;
    max_diagnoses_claim_count: number | null;

    procedures: string[]; //DRG_procedures.codeAndDescription_eng
    min_procedures_claim_count: number | null;
    max_procedures_claim_count: number | null;

    timeframe: "1" | "2" | "5" | "max" | null;
  };

  specialty: string[]; //speciality_eng

  trial: {
    min_count: number | null;
    max_count: number | null;
    status: "Completed" | "Recruiting" | "Active" | "Not Yet Recruiting" | "Terminated or Withdrawn" | null;
    phase: "Phase 1" | "Phase 2" | "Phase 3" | "Phase 4" | null;
  };

  work_institution: string[]; //presentWorkInstitutionNames_eng
}
