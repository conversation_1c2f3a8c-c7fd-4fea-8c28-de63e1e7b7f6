import {
  IndicationSortBy,
  IndicationSource,
  IndicationType
} from "./IndicationsTree";

export interface SearchIndicationsByQueryInput {
  query: string;
  h1Ids?: string[];
  size: number;
  rootId?: string;
  indicationSource: IndicationSource[];
  indicationType?: IndicationType[];
  sortBy?: IndicationSortBy;
  userId?: string;
  projectId: string;
  institutionId?: string;
  includeDuplicates?: boolean;
}

export interface SearchRootIndicationsInput {
  size?: number;
  sortBy?: IndicationSortBy;
  indicationSource: IndicationSource[];
  institutionId?: string;
}

export interface IndicationsGetSubTreesInput {
  rootId: string;
  indicationSource: IndicationSource[];
  sortBy: IndicationSortBy;
  institutionId?: string;
}

export interface SearchIndicationTreesByQueryInput {
  query: string;
  h1Ids?: string[];
  indicationSource: IndicationSource[];
  size?: number;
  userId?: string;
  projectId: string;
  sortBy: IndicationSortBy;
  indicationType?: IndicationType[];
  institutionId?: string;
}
