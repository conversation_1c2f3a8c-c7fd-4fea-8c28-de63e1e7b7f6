export enum RuleOperatorEnum {
  EQUAL = "=",
  NOT_EQUAL = "!=",
  OR = "OR" // New
}

export enum RuleFieldEnum {
  DIAGNOSES_CODE = 0,
  DIAGNOSES_INDICATION = 1,
  PROCEDURE_CODE = 2,
  AGE = 3,
  GENDER = 4,
  DIVERSITY = 5,
  GENERIC_DRUG = 6,
  CCSR = 7,
  CCSR_PX = 8
}

export enum PatientAgeBucketEnum {
  LESS_THAN_18 = "<18",
  FROM_18_TO_24 = "18-24",
  FROM_25_TO_29 = "25-29",
  FROM_30_TO_34 = "30-34",
  FROM_35_TO_39 = "35-39",
  FROM_40_TO_44 = "40-44",
  FROM_45_TO_49 = "45-49",
  FROM_50_TO_54 = "50-54",
  FROM_55_TO_59 = "55-59",
  FROM_60_TO_64 = "60-64",
  FROM_65_TO_69 = "65-69",
  FROM_70_TO_74 = "70-74",
  FROM_75_TO_79 = "75-79",
  FROM_80_TO_84 = "80-84",
  GREATER_THAN_84 = ">84"
}

export enum PatientGenderEnum {
  MALE = "M",
  FEMALE = "F"
}

export enum PatientDiversityEnum {
  HISPANIC = "Hispanic",
  WHITE_NON_HISPANIC = "White Non-Hispanic",
  BLACK_NON_HISPANIC = "Black Non-Hispanic",
  ASIAN_PACIFIC_ISLANDER = "Asian Pacific Islander",
  AMERICAN_INDIAN_OR_ALASKA_NATIVE = "American Indian Or Alaska Native",
  OTHER = "Other",
  NOT_DISCLOSED = "Not Disclosed",
  MIXED_BRAZIL = "Mixed (Brazil Only)",
  INDIGENOUS_BRAZIL = "Indigenous (Brazil Only)"
}

export enum TermsQueryType {
  TERMS = "terms",
  TERMS_SET = "terms_set"
}

export type RuleType = {
  id?: string;
  field: RuleFieldEnum;
  operator: RuleOperatorEnum;
  value: string[];
  termsQueryType?: TermsQueryType;
};

export enum RuleCombinatorEnum {
  AND = "and",
  OR = "or",
  ALL = "ALL", // New
  ANY = "ANY" // New
}

export enum RuleTypeEnum {
  RULE = "rule",
  RULE_GROUP = "ruleGroup"
}

export class RuleOrRuleGroup {
  type: RuleTypeEnum;
  rule?: RuleType;
  ruleGroup?: RuleGroupType;
}

export type RuleGroupType = {
  id?: string;
  combinator: RuleCombinatorEnum;
  rules: RuleOrRuleGroup[];
  not?: boolean;
};

export class InclusionExclusionRule {
  inclusionCriteria?: RuleOrRuleGroup;
  exclusionCriteria?: RuleOrRuleGroup;
}
