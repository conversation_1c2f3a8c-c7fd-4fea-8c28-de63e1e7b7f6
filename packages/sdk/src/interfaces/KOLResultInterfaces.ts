import {
  ProviderDiversity,
  PatientDiversity,
  DiversityRankingEnum
} from "./Diversity";
import { ClaimsDiagnosis, ClaimsProcedure } from "./Claims";

export type AffiliationSignal = {
  name: string;
  value: string;
};

export type Affiliation = {
  id: number;
  titles: Array<string>;
  department?: string;
  school: undefined;
  type: string;
  isPastAffiliation: boolean;
  institution: AffiliationInstitution;
  accuracyScore: number;
  tlAccuracyScore: number;
  otherSignals?: AffiliationSignal[];
  claimsNumbers?: number;
  claimsStates?: Array<string>;
};

export type TLAffiliation = Affiliation & {
  tlAffiliationScore: number;
  isSeeingPatients: boolean;
  institutionHasTrials: boolean;
  isPerformingTrials: boolean;
};

export type AffiliationIol = {
  id: number;
  region: string;
};

export type AffiliationInstitution = {
  id: number;
  name?: string;
  type?: string;
  orgTypes: Array<string>;
  iol?: AffiliationIol;
  ultimateParent?: AffiliationInstitution;
  address?: AffiliationInstitutionAddress;
};

export type AffiliationInstitutionAddress = {
  id: number;
  street1?: string;
  street2?: string;
  street3?: string;
  city?: string;
  region?: string;
  regionCode?: string;
  postalCode?: string;
  country?: string;
  countryCode?: string;
  latitude?: string;
  longitude?: string;
  latLongPrecision?: number;
  country_level_regions?: string[];
  state_level_regions?: string[];
  city_level_regions?: string[];
};

export interface LocationBlock<T> {
  location: string;
  stateLevel?: boolean;
  region?: {
    country: string;
    region: string;
  };
  highestClaimCount: number;
  data: {
    parentInstitution: AffiliationInstitution;
    childrenAffiliation: T[];
    parentAffiliation?: T;
  }[];
}

export interface TLWeightedLocationBlock<T> {
  location: string;
  stateLevel?: boolean;
  region?: {
    country: string;
    region: string;
  };
  highestClaimCount: number;
  data: {
    parentInstitution: AffiliationInstitution;
    childrenAffiliation: T[];
    parentAffiliation?: T;
    tlAffiliationScore: number;
  }[];
  isPerformingTrials: boolean;
  tlAffiliationScore: number;
}

export interface FormattedTopAffiliations {
  locationsToDisplay: LocationBlock<Affiliation>[];
  hasAffiliationMatchingFilter: boolean;
  isSeeingPatients: number;
  hasAboveTheLineAffiliations: boolean;
  formattedRelevantLocations: LocationBlock<Affiliation>[];
  hasPastAffiliations: boolean;
}

export interface FormattedTopTLAffiliations {
  locationsToDisplay: TLWeightedLocationBlock<TLAffiliation>[];
  hasAffiliationMatchingFilter: boolean;
  isSeeingPatients: number;
  hasAboveTheLineAffiliations: boolean;
  formattedRelevantLocations: TLWeightedLocationBlock<TLAffiliation>[];
  hasPastAffiliations: boolean;
  isPerformingTrials: boolean;
}

export interface AffiliationToDisplay {
  institutionName?: string;
  titles: string[];
  locality?: string;
  region?: string;
  country?: string;
  postalCode?: string;
}

export interface PastWorkAffiliations {
  length: number;
}
export interface FormattedTopAffiliationsH1dn {
  affiliation?: AffiliationToDisplay;
  pastAffiliationsText: string;
  pastWorkAffiliations: PastWorkAffiliations;
}

interface NameTranslation {
  firstName?: string;
  middleName?: string;
  lastName?: string;
  fullName?: string;
  languageCode: string;
}

export interface HCPLocation {
  city: string;
  state: string;
  country: string;
  zipCode5: string;
}

export interface FOResultCardData {
  foPersonId: string;
  firstName: string;
  lastName: string;
  languageCode: string;
  middleName?: string;
  firstNameEng?: string;
  lastNameEng?: string;
  middleNameEng?: string;
  affiliations?: Array<Affiliation>;
  specialty: string[];
  specialtyCmn?: string[];
  specialtyJpn?: string[];
  designations?: string[];
  orcidId?: string;
  emails?: string[];
  locations?: Array<HCPLocation>;
  name?: string;
  nameEng?: string;
}

export interface ResultCardData {
  personId: string;
  h1dnId: string | null;
  name: string;
  firstName: string;
  lastName: string;
  middleName?: string | null;
  nameEng?: string;
  firstNameEng?: string;
  lastNameEng?: string;
  middleNameEng?: string | null;
  isFacultyOpinionsMember?: boolean;
  hasCTMSData?: boolean;
  personTranslationEng?: NameTranslation;
  personTranslation?: NameTranslation;
  affiliations?: Array<Affiliation>;
  formattedTopAffiliations?:
    | FormattedTopAffiliations
    | FormattedTopAffiliationsH1dn;
  formattedTopTLAffiliations?:
    | FormattedTopTLAffiliations
    | FormattedTopAffiliationsH1dn;
  score: number;
  trendHistory: number[];
  trendPresent: string;
  countPublications: number;
  countPresentWorkAffiliations: number;
  countClinicalTrials: number;
  socialMediaMentionsTotal: number;
  referralsReceivedCount: number;
  referralsSentCount: number;
  sumPayments: number;
  diagnosesCount?: number;
  proceduresCount?: number;
  prescriptionsCount?: number;
  congresses: number;
  grants: number;
  sumGrants: number;
  specialty: string[];
  specialtyCmn?: string[];
  specialtyJpn?: string[];
  grantDates?: number[];
  congressesDates: number[];
  paymentDates: number[];
  publicationDates: number[];
  trialDates: number[];
  trialOngoingCount?: number;
  trialActivelyRecruitingCount?: number;
  citationCount: number;
  citationCountAvg: number;
  infoRequestsResolved: boolean | null;
  tags: TagInterface[];
  languageCode: string;
  designations?: string[];
  patientsDiversity?: PatientDiversity | null;
  providerDiversity?: ProviderDiversity | null;
  diversityPercentile?: number;
  digitalRank?: number | null;
  top1PercentileDigitalRank?: boolean | null;
  top10PercentileDigitalRank?: boolean | null;
  twitterFollowersCount?: number | null;
  twitterTweetCount?: number | null;
  matchedDiagnoses?: ClaimsDiagnosis[];
  matchedProcedures?: ClaimsProcedure[];
  isOutsideUsersSlice?: boolean | null;
  l1Indications?: string[] | null;
  topL3Indications?: string[] | null;
  locations?: Array<HCPLocation>;
  isInactive?: boolean | null;
  isIndustry?: boolean | null;
  isSocietyMember?: boolean | null;
  diversityRanking?: DiversityRankingEnum | null;
  trialEnrollmentRate?: number;
  congressSessions?: SpeakerCongressSession[] | null;
}

export interface TagInterface {
  id: string;
  name: string;
  isH1: boolean;
  created: Date;
  updated: Date;
  active: boolean;
  userEmail: string | null;
}

export interface SpeakerCongressSession {
  id: string;
  name: string;
  role: string;
}
